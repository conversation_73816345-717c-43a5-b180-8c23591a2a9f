{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(cargo check)", "Bash(cargo check -p wisdom-vault-core)", "Bash(cargo clippy --all-targets --all-features -- -W clippy::all)", "Bash(cargo add base64 --package wisdom-vault-core)", "Bash(cargo add redis --package wisdom-vault-core --features tokio-comp)", "Bash(cargo add sha2 --package wisdom-vault-core)", "Bash(cargo add hex --package wisdom-vault-core)", "Bash(cargo check --package wisdom-vault-core)", "mcp__upstash-context-7-mcp__resolve-library-id", "Bash(cargo check --message-format=short)", "Bash(cargo build)", "<PERSON><PERSON>(cargo check --quiet)", "Bash(cargo check --color=never)", "Bash(cargo check --workspace)", "Bash(cargo build --release)", "<PERSON><PERSON>(find . -name \"*.rs\")", "Bash(./test_server.sh)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(cargo fmt)", "<PERSON><PERSON>(cargo clippy)", "Bash(cp /tmp/models_clean.rs /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/models.rs)", "Bash(cargo check --all)", "Bash(cargo check -p wisdom-vault-api)", "Bash(grep -n \"ErrorResponse {\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/chat.rs)", "Bash(grep -n \"pagination:\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/chat.rs)", "Bash(grep -A 5 -B 1 \"message: Some.*\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/chat.rs)", "Bash(find . -name \"*.rs\" -exec wc -l {} +)", "<PERSON><PERSON>(mkdir -p frontend)", "Bash(mkdir -p src/{components,views,stores,api,router,utils,styles})", "<PERSON><PERSON>(mkdir -p public)", "Bash(npm install)", "Bash(npm run dev)", "Bash(npm run build)", "mcp__github__search_code", "mcp__github__get_file_contents", "Bash(grep -n \"ApiResponse {\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/chat.rs)", "Bash(grep -n \"ErrorResponse\\|ApiResponse {\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/chat.rs)", "Bash(grep -c \"ErrorResponse\\|ApiResponse {\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/chat.rs)", "Bash(find /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers -name \"*.rs\" -exec grep -l \"serde_json::json!\\|ErrorResponse\" {} ;)", "Bash(grep -c \"serde_json::json!\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/task_processor.rs)", "Bash(grep -n \"ApiResponse::<()>::error\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/version.rs)", "Bash(for file in /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/*.rs)", "Bash(do echo \"$(basename $file): $(grep -c ''serde_json::json!'' $file 2>/dev/null || echo 0)\")", "Bash(done)", "Bash(grep -n \"serde_json::json!\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/hybrid_search.rs)", "Bash(grep -A5 -B2 \"Err(\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/hybrid_search.rs)", "Bash(grep -n \"Local::now()\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(cargo check --quiet --message-format=short)", "Bash(grep -c \"serde_json::json!\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/hybrid_search.rs)", "Bash(grep -n \"serde_json::json!\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/vectorization.rs)", "Bash(grep -n \"Local::now()\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(grep -n \"chrono\\|DateTime\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(grep -n \"chrono::now_timestamp()\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(grep -n \"chrono::Duration\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(cargo check --package wisdom-vault-database)", "Bash(cargo check --package wisdom-vault-database --quiet --message-format=short)", "Bash(do echo \"$(basename $file): $(grep -c ''serde_json::json!'' $file 2>/dev/null || echo 0)\")", "Bash(grep -n \"serde_json::json!\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-api/src/handlers/document.rs)", "Bash(cargo check --package wisdom-vault-api --quiet --message-format=short)", "Bash(cargo check --package wisdom-vault-core --quiet --message-format=short)", "Bash(cargo check --package wisdom-vault-api)", "Bash(find /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-core/src/services -name \"*.rs\" -type f)", "Bash(cargo build --package wisdom-vault-api)", "Bash(cargo check --workspace --quiet --message-format=short)", "Bash(cargo check --workspace --quiet)", "Bash(cargo check --package wisdom-vault-core --quiet)", "Bash(cargo check --workspace --message-format=short)", "Bash(grep -n \"409\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-core/src/services/document_classification.rs)", "Bash(cargo check --package wisdom-vault-core --message-format=short)", "Bash(cargo build --offline)", "<PERSON>sh(cargo test --no-run)", "<PERSON><PERSON>(cargo check --bin wisdom-vault)", "Bash(grep -n \"TODO\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-core/src/services/vector_search.rs)", "Bash(grep -n \"struct VectorSearchService\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-core/src/services/vector_search.rs)", "Bash(cargo add mongodb --package wisdom-vault-core)", "Bash(cargo add tokio-util --package wisdom-vault-core)", "Bash(grep -n \"mongodb\" /Users/<USER>/workspaces/code/rust/wisdom-vault/Cargo.toml)", "Bash(cargo add tracing-appender --package wisdom-vault-api)", "Bash(cargo add tracing-subscriber --package wisdom-vault-api --features \"env-filter,fmt,registry\")", "Bash(find /Users/<USER>/workspaces/code/rust/wisdom-vault -name \"*test*\" -o -name \"tests\" -type f -o -type d)", "Bash(find /Users/<USER>/workspaces/code/rust/wisdom-vault -name \"Dockerfile\" -o -name \"docker-compose*\" -o -name \"*.dockerfile\")", "<PERSON><PERSON>(curl -s http://127.0.0.1:5141/health)", "Bash(grep -r \"TODO\\|FIXME\\|HACK\\|XXX\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/)", "Bash(grep -n \"KnowledgeBase Repository\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(grep -n -A 10 \"async fn list\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(grep -n -A 15 \"async fn list.*Result.*Vec.*Role\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(grep -n -A 20 \"impl PermissionRepository for MongoPermissionRepository\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(grep -n -A 30 \"async fn find_by_role\" /Users/<USER>/workspaces/code/rust/wisdom-vault/crates/wisdom-vault-database/src/repositories.rs)", "Bash(cargo check --package wisdom-vault-api --quiet)", "Bash(cargo build --package wisdom-vault-api --quiet)", "Bash(rm -rf target/debug/build/utoipa-swagger-ui-*)", "mcp__upstash-context-7-mcp__get-library-docs", "mcp__smithery-ai-github__search_code", "mcp__smithery-ai-github__search_repositories", "mcp__smithery-ai-github__get_repository", "Bash(cargo clean)", "<PERSON><PERSON>(mkdir:*)"], "deny": []}}