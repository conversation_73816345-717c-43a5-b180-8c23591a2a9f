# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Wisdom Vault is an AI-powered enterprise knowledge base system written in Rust. The project uses a workspace architecture with multiple crates for modular development, implementing a comprehensive document management system with knowledge bases, file upload/parsing, user management, and advanced search capabilities.

## Development Commands

### Build and Run

- `cargo build` - Build all workspace crates
- `cargo run --bin wisdom-vault` - Run the main API server binary
- `cargo check` - Check all crates for compilation errors
- `./test_server.sh` - Quick test script that starts server and tests health endpoints

### Code Quality

- `cargo fmt` - Format code according to Rust standards
- `cargo clippy` - Run linter for all workspace crates
- `cargo test` - Run all unit and integration tests across workspace

### Individual Crate Development

- `cargo build -p wisdom-vault-api` - Build specific crate
- `cargo test -p wisdom-vault-core` - Test specific crate
- `RUST_LOG=debug cargo run --bin wisdom-vault` - Run with debug logging

### Code Formatting

- `cargo fmt` - Format code according to `rustfmt.toml` configuration:
  - Uses Rust edition 2024
  - Features `use_try_shorthand`, `use_field_init_shorthand`
  - Comment formatting with 100-character width, wrap/normalize comments
  - Crate-level import granularity with wildcard suffix condensing

## Project Architecture

This is a Rust workspace project using Cargo edition 2024 with a multi-crate architecture implementing layered service design patterns:

### Workspace Structure

- **`wisdom-vault-api`** - Main HTTP server and API gateway using Actix-web

  - Contains the `wisdom-vault` binary (`src/main.rs`)
  - REST API handlers organized by domain (`handlers/`)
  - Request/Response types with validation (`types/`)
  - Middleware stack for auth, CORS, logging
  - Application state management with shared services
- **`wisdom-vault-core`** - Core business logic and shared types

  - Service layer implementing business rules (`services/`)
  - Document processing and file management services
  - Knowledge base management with categories/tags
  - User management and authentication services
  - Shared error types and utilities
- **`wisdom-vault-database`** - Database abstraction layer

  - SurrealDB connection management and query builders
  - Repository pattern implementations for all entities
  - Data models with rich metadata support
  - Redis caching with invalidation strategies
  - Cache-aware repository decorators
- **`wisdom-vault-common`** - Shared common utilities and types across workspace

  - Internationalization support with rust-i18n
  - Common data structures and utility functions

### API Design Architecture

The system exposes a REST API at `/api/v1` with these main domains:

- **Authentication** (`/auth`) - User login, logout, token refresh
- **Users** (`/users`) - User CRUD operations and role management
- **Knowledge Bases** (`/knowledge-bases`) - KB management with categories/tags
- **Files** (`/files`) - File upload, download, processing status
- **Documents** (`/documents`) - Document CRUD, search, status management
- **Chat** (`/chat`) - RAG-based conversational interface
- **Vectorization** (`/vectorization`) - Document embedding and vector operations
- **Search** (`/search`) - Hybrid search with keyword and semantic capabilities

### Data Layer Architecture

The application uses a repository pattern with the following key entities:

- **User/Role/Permission** - RBAC system with department-based isolation
- **KnowledgeBase/Category/Tag** - Hierarchical knowledge organization
- **Document/DocumentMetadata** - Rich document model with processing states
- **FileStorage** - File management with Apache Tika integration

### Service Layer Patterns

Services implement domain-specific business logic:

- **UserManagementService** - User lifecycle, role assignment, permissions
- **KnowledgeBaseService** - KB operations with access control
- **DocumentService** - Document processing pipeline with status tracking
- **FileStorageService** - File upload/download with metadata extraction
- **DocumentParserService** - Apache Tika integration for content extraction
- **VectorizationService** - Document embedding and vector database operations
- **HybridSearchService** - Combined keyword and semantic search
- **RAGService** - Retrieval-Augmented Generation for chat functionality
- **LLMService** - LLM integration and conversation management
- **ConversationService** - Chat session and context management

### Configuration System

The server configuration is managed through `config/default.toml`:

- Server binding (host: 127.0.0.1, port: 5141)
- MongoDB connection (mongodb://localhost:27017/wisdom_vault) with connection pooling
- Redis cache settings (redis://127.0.0.1:6379) for session storage and caching
- Qdrant vector database (http://localhost:6334) for document embeddings
- Redis session-based authentication with secure cookie management
- Apache Tika integration (http://localhost:9998) for document parsing
- OpenAI API configuration for LLM services
- File storage settings with upload size limits and allowed extensions
- AI vectorization settings with embedding model configuration

### Key Dependencies

- **actix-web** + **tokio** - Async web framework with middleware support
- **mongodb** - Multi-model database client for document storage
- **redis** - Session storage and caching layer with connection pooling
- **actix-session** + **argon2** - Redis session management and secure password hashing
- **serde** + **chrono** - Data serialization and type safety
- **tracing** - Structured logging with span context
- **config** + **validator** - Configuration management and input validation
- **actix-multipart** - File upload handling
- **reqwest** - HTTP client for Apache Tika integration
- **candle-core** + **candle-transformers** - AI/ML model inference
- **openai-api-rs** - OpenAI integration for LLM services
- **utoipa** - OpenAPI/Swagger documentation generation

### Application State Management

The `AppState` struct provides shared access to:

- Database connections (MongoDB primary, Redis session store)
- Service instances with dependency injection
- Redis session management with secure cookie authentication
- Cache invalidation system for data consistency
- Qdrant vector database client for embeddings

### Development Patterns

- **Repository Pattern** - Database abstraction with trait-based interfaces
- **Service Layer** - Business logic separation from HTTP handlers
- **Middleware Chain** - Authentication, CORS, logging, error handling
- **Async/Await** - Full async stack with tokio runtime
- **Error Handling** - Structured errors with `anyhow` and `thiserror`
- **Configuration** - Environment-based config with TOML files
- **Dependency Injection** - Arc-wrapped services in application state
- **Cache Strategy** - Write-through caching with Redis invalidation

### Document Processing Pipeline

The system implements a sophisticated document processing workflow with AI capabilities:

1. **File Upload** - Multipart upload with size/type validation
2. **Metadata Extraction** - Apache Tika integration for content parsing
3. **Document Chunking** - Intelligent text segmentation for embeddings
4. **Vectorization** - AI model-based document embedding generation
5. **Document Creation** - Rich metadata model with processing status
6. **Status Tracking** - Lifecycle states (Uploaded → Processing → Indexed/Failed)
7. **Search Integration** - Full-text and semantic search capabilities

### Database Schema Design

Uses MongoDB's document-based capabilities and Qdrant for vector storage:

- **Document Collections** - User, knowledge base, document entities with rich metadata
- **Session Storage** - Redis for secure session management and authentication
- **Vector Storage** - Qdrant for document embeddings and semantic search
- **Caching Layer** - Redis for frequently accessed data and search results

### AI and Search Features

The system includes advanced AI and search capabilities:

- **Hybrid Search** - Combines keyword and semantic vector search
- **Document Embeddings** - AI-generated vectors using Candle framework stored in Qdrant  
- **RAG (Retrieval-Augmented Generation)** - Context-aware chat responses
- **LLM Integration** - OpenAI API for conversation and content generation
- **Search Result Fusion** - Intelligent ranking of hybrid search results
- **Conversation Memory** - Context-aware chat sessions stored in Redis

## Server Runtime

The main server (`wisdom-vault-api/src/main.rs`) provides:

- Health check endpoint at `/health` with dependency status
- CORS middleware for cross-origin requests
- Request logging with tracing integration
- Graceful shutdown handling
- Application state initialization with error handling
- OpenAPI/Swagger documentation endpoints

## Frontend Integration

The project includes a Vue.js frontend (`frontend/`) with:

- Modern Vue 3 + Vite development setup
- Responsive UI with component-based architecture
- API integration with axios client
- Authentication state management with Pinia
- Key views: Dashboard, Documents, Chat, Knowledge Base, User Management
