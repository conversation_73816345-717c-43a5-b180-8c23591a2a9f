# Wisdom Vault

**企业级 AI 驱动的智能知识库系统**

Wisdom Vault 是一个现代化的企业知识管理平台，融合了人工智能技术，提供智能文档处理、语义搜索、RAG 对话等先进功能，帮助企业高效管理和利用知识资产。

![License: MIT](https://img.shields.io/badge/License-MIT-blue.svg)
![Rust Version](https://img.shields.io/badge/Rust-2024-orange.svg)
![Build Status](https://img.shields.io/badge/Build-Passing-green.svg)

## ✨ 核心特性

### 🧠 AI 驱动的智能功能
- **智能文档处理**：基于 Apache Tika 的多格式文档解析和内容提取
- **语义向量化**：使用先进的嵌入模型生成文档向量表示
- **RAG 对话系统**：基于检索增强生成的智能问答功能
- **混合搜索**：结合关键词搜索和语义搜索的智能检索

### 📚 知识库管理
- **层次化组织**：支持知识库、分类、标签的多层次内容组织
- **版本控制**：文档版本管理和历史追踪
- **批量处理**：支持大批量文档上传和处理
- **智能分类**：AI 驱动的自动文档分类和标签建议

### 🔐 企业级安全
- **RBAC 权限系统**：基于角色的细粒度访问控制
- **部门隔离**：支持多部门数据隔离
- **会话管理**：Redis 会话存储和安全认证
- **数据加密**：敏感数据加密存储和传输

### 🌐 现代化架构
- **微服务设计**：模块化的 Rust workspace 架构
- **异步处理**：基于 Tokio 的高性能异步编程
- **云原生**：Docker 容器化部署和 Kubernetes 支持
- **国际化**：多语言支持和本地化

## 🏗️ 系统架构

### 技术栈

#### 后端
- **语言**：Rust 2024 Edition
- **Web 框架**：Actix-web 4.4
- **数据库**：MongoDB 3.x（主数据库）+ Redis 6.x（缓存和会话）+ Qdrant（向量数据库）
- **AI/ML**：Candle + OpenAI API + HuggingFace Transformers
- **文档处理**：Apache Tika
- **认证**：JWT + Argon2 密码哈希

#### 前端
- **框架**：Vue 3 + Composition API
- **UI 组件**：Element Plus
- **状态管理**：Pinia
- **构建工具**：Vite 5
- **HTTP 客户端**：Axios

#### 基础设施
- **容器化**：Docker + Docker Compose
- **向量数据库**：Qdrant
- **缓存**：Redis
- **日志**：结构化日志记录 (tracing)
- **API 文档**：OpenAPI 3.0 + Swagger UI

### 项目结构

```
wisdom-vault/
├── crates/                    # Rust workspace crates
│   ├── wisdom-vault-api/      # Web API 服务层
│   ├── wisdom-vault-core/     # 核心业务逻辑
│   ├── wisdom-vault-database/ # 数据访问层
│   └── wisdom-vault-common/   # 共享工具和类型
├── frontend/                  # Vue.js 前端应用
├── config/                    # 配置文件
├── locales/                   # 国际化资源
├── apis/                      # API 测试文件
└── docker-compose.yml         # Docker 编排配置
```

### 核心组件

#### API 层 (wisdom-vault-api)
- **REST API**：符合 RESTful 规范的 HTTP 接口
- **认证中间件**：JWT token 验证和会话管理
- **请求验证**：输入参数验证和错误处理
- **OpenAPI 文档**：自动生成 API 文档和 Swagger UI

#### 业务逻辑层 (wisdom-vault-core)
- **用户管理**：用户注册、认证、权限管理
- **知识库服务**：知识库创建、管理、访问控制
- **文档处理**：文档上传、解析、分块、向量化
- **搜索服务**：混合搜索、结果融合、个性化推荐
- **对话服务**：RAG 对话、上下文管理、质量评估

#### 数据访问层 (wisdom-vault-database)
- **Repository 模式**：数据访问抽象和实现
- **缓存策略**：多层缓存和失效策略
- **连接管理**：数据库连接池和事务管理
- **索引优化**：查询性能优化和索引管理

## 🚀 快速开始

### 环境要求

- **Rust**: 1.75+ (推荐使用 rustup 安装)
- **Node.js**: 18+ (前端开发)
- **Docker**: 20+ (推荐使用 Docker Desktop)
- **MongoDB**: 6.0+
- **Redis**: 6.0+
- **Apache Tika Server**: 2.9+ (可选，用于文档解析)

### 1. 克隆项目

```bash
git clone https://github.com/your-org/wisdom-vault.git
cd wisdom-vault
```

### 2. 启动依赖服务

使用 Docker Compose 启动所需的外部服务：

```bash
# 启动 Qdrant 向量数据库
docker-compose up -d

# 或手动启动所有依赖服务
docker run -d --name mongodb -p 27017:27017 mongo:6.0
docker run -d --name redis -p 6379:6379 redis:6-alpine
docker run -d --name tika -p 9998:9998 apache/tika:2.9.1-full
```

### 3. 配置环境

复制并编辑配置文件：

```bash
cp config/default.toml config/local.toml
```

在 `config/local.toml` 中配置：

```toml
[mongodb]
url = "mongodb://localhost:27017/wisdom_vault"

[redis]
url = "redis://127.0.0.1:6379"

[ai]
openai_api_key = "your-openai-api-key"
```

### 4. 构建和运行

```bash
# 构建项目
cargo build

# 运行 API 服务器
cargo run --bin wisdom-vault

# 或者运行快速测试
./test_server.sh
```

### 5. 启动前端 (可选)

```bash
cd frontend
npm install
npm run dev
```

现在可以访问：
- **API 服务器**: http://localhost:5141
- **API 文档**: http://localhost:5141/swagger-ui/
- **健康检查**: http://localhost:5141/health
- **前端应用**: http://localhost:3000 (如果启动了前端)

## 💻 开发指南

### 开发工具

推荐的开发环境设置：

```bash
# 安装 Rust 工具链
rustup install stable
rustup default stable

# 安装开发工具
cargo install cargo-watch
cargo install cargo-edit
rustup component add rustfmt clippy
```

### 常用命令

```bash
# 代码检查
cargo check                    # 快速语法检查
cargo clippy                   # 代码质量检查
cargo fmt                      # 代码格式化

# 运行测试
cargo test                     # 运行所有测试
cargo test -p wisdom-vault-core # 运行特定 crate 的测试

# 开发模式运行
RUST_LOG=debug cargo run --bin wisdom-vault

# 监听文件变化自动重启
cargo watch -x 'run --bin wisdom-vault'
```

### 项目规范

- **代码风格**：使用 `rustfmt.toml` 配置的 Rust 标准格式
- **提交消息**：使用约定式提交 (Conventional Commits)
- **分支策略**：Git Flow 分支管理策略
- **代码审查**：所有代码必须经过 Pull Request 审查

## 📖 API 文档

启动服务器后，可以通过以下地址访问完整的 API 文档：

- **Swagger UI**: http://localhost:5141/swagger-ui/
- **OpenAPI JSON**: http://localhost:5141/api-docs/openapi.json

### 主要 API 端点

#### 认证
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh` - 刷新访问令牌

#### 知识库管理
- `GET /api/v1/knowledge-bases` - 获取知识库列表
- `POST /api/v1/knowledge-bases` - 创建知识库
- `PUT /api/v1/knowledge-bases/{id}` - 更新知识库

#### 文档管理
- `POST /api/v1/files/upload` - 文件上传
- `GET /api/v1/documents` - 获取文档列表
- `POST /api/v1/documents` - 创建文档

#### 搜索和对话
- `GET /api/v1/search` - 混合搜索
- `POST /api/v1/chat` - RAG 对话

## 🔧 配置选项

系统通过 `config/default.toml` 进行配置，支持以下主要配置项：

### 服务器配置
```toml
host = "127.0.0.1"
port = 5141
```

### 数据库配置
```toml
[mongodb]
url = "mongodb://localhost:27017/wisdom_vault"
max_pool_size = 10

[redis]
url = "redis://127.0.0.1:6379"
pool_size = 10

[qdrant]
url = "http://localhost:6334"
```

### AI 配置
```toml
[ai]
openai_api_key = "your-api-key"
default_model = "gpt-4o-mini"
max_tokens = 4000
temperature = 0.7

[vectorization]
embedding_model = "paraphrase-multilingual-MiniLM-L12-v2"
batch_size = 32
```

### 文件存储配置
```toml
[file_storage]
max_file_size = 104857600  # 100MB
allowed_extensions = ["pdf", "doc", "docx", "txt", "md"]

[tika]
server_url = "http://localhost:9998"
```

## 🐳 Docker 部署

### 使用 Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs wisdom-vault

# 停止服务
docker-compose down
```

### 生产环境部署

推荐使用 Docker 进行生产环境部署：

1. **构建生产镜像**：
```bash
# 构建后端镜像
docker build -t wisdom-vault:latest .

# 构建前端镜像
cd frontend
docker build -t wisdom-vault-frontend:latest .
```

2. **环境变量配置**：
```bash
# 设置必要的环境变量
export MONGODB_URL="mongodb://mongo:27017/wisdom_vault"
export REDIS_URL="redis://redis:6379"
export OPENAI_API_KEY="your-api-key"
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
cargo test

# 运行特定模块测试
cargo test -p wisdom-vault-core

# 运行集成测试
cargo test --test integration

# 生成测试覆盖率报告
cargo tarpaulin --out Html
```

### API 测试

使用提供的 HTTP 文件进行 API 测试：

```bash
# 使用 REST Client 扩展或 curl
curl -X GET http://localhost:5141/health
```

## 📊 性能优化

### 数据库优化
- MongoDB 索引优化
- Redis 缓存策略
- Qdrant 向量索引配置

### 应用优化
- 异步处理优化
- 连接池配置
- 内存使用优化

### 监控和观测
- 结构化日志记录
- 性能指标收集
- 健康检查端点

## 🔍 故障排除

### 常见问题

1. **无法连接数据库**
   - 检查 MongoDB 和 Redis 服务是否启动
   - 验证连接字符串配置
   - 检查网络连接和防火墙设置

2. **向量化任务失败**
   - 确认 Qdrant 服务正常运行
   - 检查 OpenAI API 密钥配置
   - 验证文档格式支持

3. **文档解析失败**
   - 确认 Apache Tika 服务启动
   - 检查文件格式和大小限制
   - 查看 Tika 服务日志

### 日志分析

启用详细日志记录：

```bash
RUST_LOG=debug cargo run --bin wisdom-vault
```

日志文件位置：`logs/wisdom_vault.log`

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork** 项目到你的 GitHub 账户
2. **创建特性分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'Add amazing feature'`)
4. **推送到分支** (`git push origin feature/amazing-feature`)
5. **创建 Pull Request**

### 开发规范

- 遵循 Rust 官方代码风格指南
- 为新功能编写测试
- 更新相关文档
- 保持提交消息清晰

### 代码审查

所有 Pull Request 都需要经过代码审查，确保：
- 代码质量和风格一致
- 测试覆盖率充足
- 文档完整准确
- 性能影响可接受

## 📄 许可证

本项目基于 MIT 许可证开源。详见 [LICENSE](LICENSE) 文件。

## 👥 团队

由 Wisdom Vault Team 开发和维护。

## 📞 联系我们

- **GitHub Issues**: [提交问题和建议](https://github.com/your-org/wisdom-vault/issues)
- **讨论区**: [GitHub Discussions](https://github.com/your-org/wisdom-vault/discussions)
- **邮箱**: <EMAIL>

## 🗺️ 路线图

### 近期计划 (Q1 2025)
- [ ] 增强 RAG 对话质量
- [ ] 支持更多文档格式
- [ ] 移动端适配
- [ ] API 限流和监控

### 中期计划 (Q2-Q3 2025)
- [ ] 多租户架构
- [ ] 知识图谱构建
- [ ] 高级分析面板
- [ ] 集成更多 LLM 提供商

### 长期计划 (2025年底)
- [ ] 企业级 SSO 集成
- [ ] 高可用集群部署
- [ ] 智能推荐系统
- [ ] 工作流自动化

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！

**Built with ❤️ using Rust and Vue.js**