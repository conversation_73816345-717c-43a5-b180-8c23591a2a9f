@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局基础样式 */
@layer base {
  html {
    font-family: 'Inter', ui-sans-serif, system-ui;
  }
  
  body {
    @apply bg-light-secondary dark:bg-dark-primary text-gray-900 dark:text-gray-100;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-primary-100 dark:bg-primary-800 text-primary-800 dark:text-primary-100;
  }
}

/* 组件基础样式 */
@layer components {
  /* 按钮基础样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-xl font-medium transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-opacity-30 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-gradient-primary text-white shadow-lg hover:shadow-glow focus:ring-primary-300 hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-gray-300;
  }

  .btn-success {
    @apply bg-gradient-success text-white shadow-lg hover:shadow-xl focus:ring-green-300 hover:-translate-y-0.5;
  }

  .btn-warning {
    @apply bg-gradient-warning text-white shadow-lg hover:shadow-xl focus:ring-yellow-300 hover:-translate-y-0.5;
  }

  .btn-error {
    @apply bg-gradient-error text-white shadow-lg hover:shadow-xl focus:ring-red-300 hover:-translate-y-0.5;
  }

  .btn-ghost {
    @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm rounded-lg;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg rounded-2xl;
  }

  /* 输入框样式 */
  .input-field {
    @apply block w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-700 rounded-xl text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:ring-4 focus:ring-primary-100 dark:focus:ring-primary-800 focus:border-primary-500 transition-all duration-300;
  }

  .input-field:focus {
    @apply outline-none ring-4 ring-primary-100 dark:ring-primary-800 border-primary-500;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-glass border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-glass-lg;
  }

  .card-glass {
    @apply backdrop-blur-xl bg-glass dark:bg-glass-dark border border-white/20 dark:border-gray-700/50;
  }

  /* 导航项样式 */
  .nav-item {
    @apply flex items-center px-4 py-3 rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300;
  }

  .nav-item.active {
    @apply bg-gradient-primary text-white shadow-lg;
  }

  .nav-item-icon {
    @apply w-5 h-5 mr-3 transition-transform duration-300 group-hover:scale-110;
  }

  /* 状态指示器 */
  .status-success {
    @apply border-l-4 border-success bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200;
  }

  .status-warning {
    @apply border-l-4 border-warning bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200;
  }

  .status-error {
    @apply border-l-4 border-error bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200;
  }

  .status-info {
    @apply border-l-4 border-info bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200;
  }

  /* 加载状态 */
  .loading {
    @apply relative overflow-hidden;
  }

  .loading::after {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/40 dark:via-gray-600/40 to-transparent;
    content: '';
    animation: shimmer 1.5s infinite;
  }

  /* 模态框样式 */
  .modal-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-40;
  }

  .modal-content {
    @apply bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform transition-all duration-300;
  }

  /* 下拉菜单样式 */
  .dropdown {
    @apply absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-glass border border-gray-200 dark:border-gray-700 py-2 z-50;
  }

  .dropdown-item {
    @apply flex items-center px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
  }

  /* 标签样式 */
  .tag {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .tag-primary {
    @apply bg-primary-100 dark:bg-primary-800 text-primary-800 dark:text-primary-100;
  }

  .tag-success {
    @apply bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100;
  }

  .tag-warning {
    @apply bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-100;
  }

  .tag-error {
    @apply bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-100;
  }
}

/* 工具类 */
@layer utilities {
  /* 玻璃效果 */
  .glass {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .glass-sm {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* 悬浮效果 */
  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-1;
  }

  .hover-lift-sm {
    @apply transition-transform duration-300 hover:-translate-y-0.5;
  }

  /* 点击反馈 */
  .click-feedback {
    @apply transition-transform duration-100 active:scale-95;
  }

  /* 文本渐变 */
  .text-gradient-primary {
    @apply bg-gradient-primary bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-secondary bg-clip-text text-transparent;
  }

  /* 安全区域适配 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* 滚动区域 */
  .scroll-area {
    @apply overflow-auto;
    scrollbar-width: thin;
    scrollbar-color: rgb(209 213 219) rgb(243 244 246);
  }

  .scroll-area::-webkit-scrollbar {
    @apply w-1.5 h-1.5;
  }

  .scroll-area::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800 rounded-full;
  }

  .scroll-area::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500;
  }
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  @apply transition-all duration-300 ease-in-out;
}

.fade-enter-from {
  @apply opacity-0 translate-y-4;
}

.fade-leave-to {
  @apply opacity-0 translate-y-4;
}

.slide-enter-active,
.slide-leave-active {
  @apply transition-transform duration-300 ease-in-out;
}

.slide-enter-from {
  @apply translate-x-full;
}

.slide-leave-to {
  @apply translate-x-full;
}

/* 响应式文本 */
.responsive-text {
  @apply text-sm sm:text-base md:text-lg lg:text-xl;
}

/* 截断文本 */
.text-truncate {
  @apply truncate;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  @apply overflow-hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  @apply overflow-hidden;
}