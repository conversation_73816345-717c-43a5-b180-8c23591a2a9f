import { defineStore } from 'pinia'
import { authAPI, userAPI } from '@/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false,
    error: null
  }),
  
  getters: {
    // 获取用户角色
    userRole: (state) => state.user?.role || 'user',
    
    // 获取用户权限
    userPermissions: (state) => state.user?.permissions || [],
    
    // 检查是否有特定权限
    hasPermission: (state) => (permission) => {
      return state.user?.permissions?.includes(permission) || false
    },
    
    // 检查是否有特定角色
    hasRole: (state) => (role) => {
      return state.user?.role === role
    },
    
    // 检查是否为管理员
    isAdmin: (state) => state.user?.role === 'admin',
    
    // 获取用户显示名称
    displayName: (state) => state.user?.name || state.user?.email || '用户'
  },
  
  actions: {
    // 初始化认证状态
    async initAuth() {
      const token = localStorage.getItem('auth_token')
      const userData = localStorage.getItem('user_data')
      
      if (token && userData) {
        try {
          const user = JSON.parse(userData)
          
          // 检查token是否过期
          if (user.tokenExpiry && new Date() > new Date(user.tokenExpiry)) {
            this.clearAuth()
            return false
          }
          
          this.token = token
          this.user = user
          this.isAuthenticated = true
          
          // 验证token有效性
          await this.getCurrentUser()
          
          return true
        } catch (error) {
          console.error('Initialize auth failed:', error)
          this.clearAuth()
          return false
        }
      }
      
      return false
    },
    
    // 登录
    async login(credentials) {
      this.loading = true
      this.error = null
      
      try {
        const response = await authAPI.login(credentials)
        
        this.token = response.token
        this.user = response.user
        this.isAuthenticated = true
        
        // 保存到本地存储
        localStorage.setItem('auth_token', response.token)
        localStorage.setItem('user_data', JSON.stringify({
          ...response.user,
          tokenExpiry: response.expires_at
        }))
        
        return response
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    // 登出
    async logout() {
      this.loading = true
      
      try {
        // 调用后端登出接口
        if (this.isAuthenticated) {
          await authAPI.logout()
        }
      } catch (error) {
        console.error('Logout API failed:', error)
      } finally {
        this.clearAuth()
        this.loading = false
      }
    },
    
    // 清除认证信息
    clearAuth() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      this.error = null
      
      // 清除本地存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
    },
    
    // 获取当前用户信息
    async getCurrentUser() {
      try {
        const user = await userAPI.getCurrentUser()
        this.user = { ...this.user, ...user }
        
        // 更新本地存储
        const userData = JSON.parse(localStorage.getItem('user_data') || '{}')
        localStorage.setItem('user_data', JSON.stringify({
          ...userData,
          ...user
        }))
        
        return user
      } catch (error) {
        console.error('Get current user failed:', error)
        
        // 如果是401错误，清除认证信息
        if (error.status === 401) {
          this.clearAuth()
        }
        
        throw error
      }
    },
    
    // 更新用户资料
    async updateProfile(data) {
      this.loading = true
      
      try {
        const updatedUser = await userAPI.updateProfile(data)
        this.user = { ...this.user, ...updatedUser }
        
        // 更新本地存储
        const userData = JSON.parse(localStorage.getItem('user_data') || '{}')
        localStorage.setItem('user_data', JSON.stringify({
          ...userData,
          ...updatedUser
        }))
        
        return updatedUser
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    // 更改密码
    async changePassword(data) {
      this.loading = true
      this.error = null
      
      try {
        await userAPI.changePassword(data)
        return true
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    // 刷新token
    async refreshToken() {
      try {
        const response = await authAPI.refreshToken()
        
        this.token = response.token
        
        // 更新本地存储
        localStorage.setItem('auth_token', response.token)
        const userData = JSON.parse(localStorage.getItem('user_data') || '{}')
        localStorage.setItem('user_data', JSON.stringify({
          ...userData,
          tokenExpiry: response.expires_at
        }))
        
        return response
      } catch (error) {
        console.error('Refresh token failed:', error)
        this.clearAuth()
        throw error
      }
    },
    
    // 重置密码
    async resetPassword(email) {
      this.loading = true
      this.error = null
      
      try {
        await authAPI.resetPassword(email)
        return true
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.loading = false
      }
    },
    
    // 设置错误
    setError(error) {
      this.error = error
    },
    
    // 清除错误
    clearError() {
      this.error = null
    }
  }
})