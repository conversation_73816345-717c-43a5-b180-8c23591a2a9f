import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/components/layout/Layout.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { 
      requiresAuth: false,
      hideLayout: true
    }
  },
  {
    path: '/',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { 
          title: '仪表板',
          icon: 'HomeIcon'
        }
      },
      {
        path: 'documents',
        name: 'Documents',
        component: () => import('@/views/Documents.vue'),
        meta: { 
          title: '文档管理',
          icon: 'DocumentTextIcon'
        }
      },
      {
        path: 'documents/new',
        name: 'NewDocument',
        component: () => import('@/views/DocumentEditor.vue'),
        meta: { 
          title: '新建文档',
          parent: 'Documents'
        }
      },
      {
        path: 'documents/:id',
        name: 'DocumentDetail',
        component: () => import('@/views/DocumentDetail.vue'),
        meta: { 
          title: '文档详情',
          parent: 'Documents'
        }
      },
      {
        path: 'chat',
        name: 'Chat',
        component: () => import('@/views/Chat.vue'),
        meta: { 
          title: 'AI对话',
          icon: 'ChatBubbleLeftIcon'
        }
      },
      {
        path: 'chat/:id',
        name: 'ChatDetail',
        component: () => import('@/views/ChatDetail.vue'),
        meta: { 
          title: '对话详情',
          parent: 'Chat'
        }
      },
      {
        path: 'knowledge-base',
        name: 'KnowledgeBase',
        component: () => import('@/views/KnowledgeBase.vue'),
        meta: { 
          title: '知识库',
          icon: 'BookOpenIcon'
        }
      },
      {
        path: 'knowledge-base/categories',
        name: 'Categories',
        component: () => import('@/views/Categories.vue'),
        meta: { 
          title: '分类管理',
          parent: 'KnowledgeBase'
        }
      },
      {
        path: 'knowledge-base/tags',
        name: 'Tags',
        component: () => import('@/views/Tags.vue'),
        meta: { 
          title: '标签管理',
          parent: 'KnowledgeBase'
        }
      },
      {
        path: 'search',
        name: 'Search',
        component: () => import('@/views/Search.vue'),
        meta: { 
          title: '搜索',
          icon: 'MagnifyingGlassIcon'
        }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/views/UserManagement.vue'),
        meta: { 
          title: '用户管理',
          icon: 'UserGroupIcon',
          requiresRole: ['admin']
        }
      },
      {
        path: 'audit-logs',
        name: 'AuditLogs',
        component: () => import('@/views/AuditLogs.vue'),
        meta: { 
          title: '审计日志',
          icon: 'ClipboardDocumentListIcon',
          requiresRole: ['admin']
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/Settings.vue'),
        meta: { 
          title: '系统设置',
          icon: 'Cog6ToothIcon'
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue'),
        meta: { 
          title: '个人资料'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { 
      hideLayout: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isAuthenticated = checkAuthStatus()
  const userRole = getCurrentUserRole()
  
  // 检查是否需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isAuthenticated) {
      next('/login')
      return
    }
    
    // 检查角色权限
    if (to.meta.requiresRole && !to.meta.requiresRole.includes(userRole)) {
      // 没有权限，重定向到仪表板
      next('/dashboard')
      return
    }
  }
  
  // 如果已登录用户访问登录页，重定向到仪表板
  if (to.path === '/login' && isAuthenticated) {
    next('/dashboard')
    return
  }
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 智慧知识库`
  } else {
    document.title = '智慧知识库'
  }
  
  next()
})

// 检查认证状态
function checkAuthStatus() {
  const token = localStorage.getItem('auth_token')
  const userData = localStorage.getItem('user_data')
  
  if (!token || !userData) {
    return false
  }
  
  try {
    const user = JSON.parse(userData)
    const tokenExpiry = user.tokenExpiry
    
    if (tokenExpiry && new Date() > new Date(tokenExpiry)) {
      // Token已过期
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
      return false
    }
    
    return true
  } catch (error) {
    console.error('Error parsing user data:', error)
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    return false
  }
}

// 获取当前用户角色
function getCurrentUserRole() {
  try {
    const userData = localStorage.getItem('user_data')
    if (userData) {
      const user = JSON.parse(userData)
      return user.role || 'user'
    }
  } catch (error) {
    console.error('Error getting user role:', error)
  }
  return 'user'
}

export default router