import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求ID用于日志追踪
    config.headers['X-Request-ID'] = generateRequestId()
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // 处理网络错误
    if (!error.response) {
      const networkError = {
        message: '网络连接失败，请检查您的网络连接',
        type: 'network_error'
      }
      
      // 显示网络错误通知
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          title: '网络错误',
          message: networkError.message
        }
      }))
      
      return Promise.reject(networkError)
    }
    
    const { status, data } = error.response
    
    // 处理不同的HTTP状态码
    switch (status) {
      case 401:
        // 未授权 - 清除token并跳转到登录页
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_data')
        
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
        
        return Promise.reject({
          message: '登录已过期，请重新登录',
          status,
          type: 'auth_error'
        })
        
      case 403:
        // 权限不足
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'error',
            title: '权限不足',
            message: '您没有权限执行此操作'
          }
        }))
        
        return Promise.reject({
          message: '权限不足',
          status,
          type: 'permission_error'
        })
        
      case 422:
        // 验证错误
        return Promise.reject({
          message: '数据验证失败',
          status,
          errors: data.errors || {},
          type: 'validation_error'
        })
        
      case 429:
        // 请求频率限制
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'warning',
            title: '请求过于频繁',
            message: '请稍后再试'
          }
        }))
        
        return Promise.reject({
          message: '请求过于频繁，请稍后再试',
          status,
          type: 'rate_limit_error'
        })
        
      case 500:
        // 服务器内部错误
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'error',
            title: '服务器错误',
            message: '服务器遇到了问题，请稍后再试'
          }
        }))
        
        return Promise.reject({
          message: '服务器内部错误',
          status,
          type: 'server_error'
        })
        
      case 503:
        // 服务不可用
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'error',
            title: '服务维护中',
            message: '系统正在维护中，请稍后再试'
          }
        }))
        
        return Promise.reject({
          message: '服务暂时不可用',
          status,
          type: 'service_unavailable'
        })
        
      default:
        // 其他错误
        const errorMessage = data?.message || `请求失败 (${status})`
        
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'error',
            title: '请求失败',
            message: errorMessage
          }
        }))
        
        return Promise.reject({
          message: errorMessage,
          status,
          type: 'unknown_error'
        })
    }
  }
)

// 生成请求ID
function generateRequestId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5)
}

// 通用请求方法
export const request = {
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  delete: (url, config = {}) => api.delete(url, config)
}

// 文件上传方法
export const uploadFile = (url, formData, config = {}) => {
  return api.post(url, formData, {
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data',
      ...config.headers
    },
    // 上传进度回调
    onUploadProgress: (progressEvent) => {
      if (config.onProgress) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        config.onProgress(progress)
      }
    }
  })
}

// 下载文件方法
export const downloadFile = async (url, filename, config = {}) => {
  try {
    const response = await api.get(url, {
      ...config,
      responseType: 'blob'
    })
    
    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
    
    return response
  } catch (error) {
    throw error
  }
}

export default api