import { request, uploadFile, downloadFile } from './axios'

// 认证相关API
export const authAPI = {
  // 登录
  login: (credentials) => request.post('/auth/login', credentials),
  
  // 登出
  logout: () => request.post('/auth/logout'),
  
  // 刷新token
  refreshToken: () => request.post('/auth/refresh'),
  
  // 重置密码
  resetPassword: (email) => request.post('/auth/reset-password', { email }),
  
  // 验证重置token
  verifyResetToken: (token) => request.post('/auth/verify-reset-token', { token }),
  
  // 更新密码
  updatePassword: (data) => request.post('/auth/update-password', data)
}

// 用户相关API
export const userAPI = {
  // 获取当前用户信息
  getCurrentUser: () => request.get('/users/me'),
  
  // 更新用户信息
  updateProfile: (data) => request.patch('/users/me', data),
  
  // 更改密码
  changePassword: (data) => request.post('/users/me/change-password', data),
  
  // 获取用户列表
  getUsers: (params = {}) => request.get('/users', { params }),
  
  // 创建用户
  createUser: (data) => request.post('/users', data),
  
  // 更新用户
  updateUser: (id, data) => request.patch(`/users/${id}`, data),
  
  // 删除用户
  deleteUser: (id) => request.delete(`/users/${id}`)
}

// 文档相关API
export const documentAPI = {
  // 获取文档列表
  getDocuments: (params = {}) => request.get('/documents', { params }),
  
  // 获取文档详情
  getDocument: (id) => request.get(`/documents/${id}`),
  
  // 创建文档
  createDocument: (data) => request.post('/documents', data),
  
  // 更新文档
  updateDocument: (id, data) => request.patch(`/documents/${id}`, data),
  
  // 删除文档
  deleteDocument: (id) => request.delete(`/documents/${id}`),
  
  // 批量删除文档
  deleteDocuments: (ids) => request.delete('/documents/batch', { data: { ids } }),
  
  // 获取文档内容
  getDocumentContent: (id) => request.get(`/documents/${id}/content`),
  
  // 文档向量化
  vectorizeDocument: (id) => request.post(`/documents/${id}/vectorize`),
  
  // 批量向量化
  batchVectorize: (ids) => request.post('/documents/batch-vectorize', { ids })
}

// 知识库相关API
export const knowledgeBaseAPI = {
  // 获取知识库列表
  getKnowledgeBases: (params = {}) => request.get('/knowledge-bases', { params }),
  
  // 获取知识库详情
  getKnowledgeBase: (id) => request.get(`/knowledge-bases/${id}`),
  
  // 创建知识库
  createKnowledgeBase: (data) => request.post('/knowledge-bases', data),
  
  // 更新知识库
  updateKnowledgeBase: (id, data) => request.patch(`/knowledge-bases/${id}`, data),
  
  // 删除知识库
  deleteKnowledgeBase: (id) => request.delete(`/knowledge-bases/${id}`),
  
  // 获取知识库统计信息
  getKnowledgeBaseStats: (id) => request.get(`/knowledge-bases/${id}/stats`)
}

// 分类相关API
export const categoryAPI = {
  // 获取分类列表
  getCategories: (params = {}) => request.get('/categories', { params }),
  
  // 获取分类详情
  getCategory: (id) => request.get(`/categories/${id}`),
  
  // 创建分类
  createCategory: (data) => request.post('/categories', data),
  
  // 更新分类
  updateCategory: (id, data) => request.patch(`/categories/${id}`, data),
  
  // 删除分类
  deleteCategory: (id) => request.delete(`/categories/${id}`)
}

// 标签相关API
export const tagAPI = {
  // 获取标签列表
  getTags: (params = {}) => request.get('/tags', { params }),
  
  // 获取标签详情
  getTag: (id) => request.get(`/tags/${id}`),
  
  // 创建标签
  createTag: (data) => request.post('/tags', data),
  
  // 更新标签
  updateTag: (id, data) => request.patch(`/tags/${id}`, data),
  
  // 删除标签
  deleteTag: (id) => request.delete(`/tags/${id}`)
}

// 文件上传相关API
export const fileAPI = {
  // 上传文件
  uploadFile: (formData, config = {}) => uploadFile('/files/upload', formData, config),
  
  // 批量上传文件
  uploadFiles: (formData, config = {}) => uploadFile('/files/upload-batch', formData, config),
  
  // 获取文件信息
  getFile: (id) => request.get(`/files/${id}`),
  
  // 下载文件
  downloadFile: (id, filename) => downloadFile(`/files/${id}/download`, filename),
  
  // 删除文件
  deleteFile: (id) => request.delete(`/files/${id}`)
}

// 搜索相关API
export const searchAPI = {
  // 混合搜索
  hybridSearch: (params) => request.get('/search/hybrid', { params }),
  
  // 关键词搜索
  keywordSearch: (params) => request.get('/search/keyword', { params }),
  
  // 语义搜索
  semanticSearch: (params) => request.get('/search/semantic', { params }),
  
  // 搜索建议
  getSearchSuggestions: (query) => request.get('/search/suggestions', { params: { q: query } }),
  
  // 高级搜索
  advancedSearch: (params) => request.post('/search/advanced', params)
}

// 对话相关API
export const chatAPI = {
  // 获取对话列表
  getConversations: (params = {}) => request.get('/chat/conversations', { params }),
  
  // 获取对话详情
  getConversation: (id) => request.get(`/chat/conversations/${id}`),
  
  // 创建对话
  createConversation: (data) => request.post('/chat/conversations', data),
  
  // 更新对话
  updateConversation: (id, data) => request.patch(`/chat/conversations/${id}`, data),
  
  // 删除对话
  deleteConversation: (id) => request.delete(`/chat/conversations/${id}`),
  
  // 发送消息
  sendMessage: (conversationId, data) => request.post(`/chat/conversations/${conversationId}/messages`, data),
  
  // 获取消息列表
  getMessages: (conversationId, params = {}) => request.get(`/chat/conversations/${conversationId}/messages`, { params })
}

// 审计日志相关API
export const auditAPI = {
  // 获取审计日志
  getAuditLogs: (params = {}) => request.get('/audit/logs', { params }),
  
  // 获取审计统计
  getAuditStats: (params = {}) => request.get('/audit/stats', { params }),
  
  // 导出审计日志
  exportAuditLogs: (params = {}) => request.post('/audit/export', params, { responseType: 'blob' })
}

// 系统相关API
export const systemAPI = {
  // 健康检查
  healthCheck: () => request.get('/health'),
  
  // 获取系统信息
  getSystemInfo: () => request.get('/system/info'),
  
  // 获取系统统计
  getSystemStats: () => request.get('/system/stats'),
  
  // 获取系统设置
  getSettings: () => request.get('/system/settings'),
  
  // 更新系统设置
  updateSettings: (data) => request.patch('/system/settings', data)
}

// 任务相关API
export const taskAPI = {
  // 获取任务列表
  getTasks: (params = {}) => request.get('/tasks', { params }),
  
  // 获取任务详情
  getTask: (id) => request.get(`/tasks/${id}`),
  
  // 取消任务
  cancelTask: (id) => request.post(`/tasks/${id}/cancel`),
  
  // 重试任务
  retryTask: (id) => request.post(`/tasks/${id}/retry`)
}

// 导出所有API
export default {
  auth: authAPI,
  user: userAPI,
  document: documentAPI,
  knowledgeBase: knowledgeBaseAPI,
  category: categoryAPI,
  tag: tagAPI,
  file: fileAPI,
  search: searchAPI,
  chat: chatAPI,
  audit: auditAPI,
  system: systemAPI,
  task: taskAPI
}