<template>
  <div :class="sidebarClasses">
    <!-- Logo区域 -->
    <div class="flex items-center justify-between px-4 py-6">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-gradient-primary rounded-xl flex items-center justify-center">
          <span class="text-white font-bold text-sm">智</span>
        </div>
        <h1
          v-if="!collapsed"
          class="text-xl font-bold text-gray-900 dark:text-gray-100 transition-opacity duration-300"
        >
          智慧知识库
        </h1>
      </div>
      
      <button
        v-if="!collapsed"
        @click="$emit('toggle')"
        class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors lg:hidden"
      >
        <XMarkIcon class="w-5 h-5 text-gray-500" />
      </button>
    </div>
    
    <!-- 用户信息 -->
    <div v-if="!collapsed && user" class="px-4 mb-6">
      <div class="bg-gradient-primary/10 dark:bg-gradient-primary/20 rounded-xl p-4">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
            <UserIcon class="w-5 h-5 text-white" />
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
              {{ user.name || '用户' }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
              {{ user.role || '普通用户' }}
            </p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 快速操作 -->
    <div v-if="!collapsed" class="px-4 mb-6">
      <button
        @click="$emit('new-chat')"
        class="w-full btn btn-primary btn-sm justify-start"
      >
        <ChatBubbleLeftIcon class="w-4 h-4 mr-2" />
        新建对话
      </button>
    </div>
    
    <!-- 导航菜单 -->
    <nav class="px-4 space-y-2 flex-1">
      <div
        v-for="item in navigation"
        :key="item.name"
      >
        <router-link
          v-if="!item.children"
          :to="item.href"
          :class="getNavItemClass(item)"
        >
          <component :is="item.icon" class="nav-item-icon" />
          <span v-if="!collapsed" class="transition-opacity duration-300">
            {{ item.name }}
          </span>
          <span
            v-if="item.badge && !collapsed"
            :class="badgeClass(item.badge)"
          >
            {{ item.badge.text }}
          </span>
        </router-link>
        
        <!-- 有子菜单的项目 -->
        <div v-else>
          <button
            @click="toggleSubmenu(item.name)"
            :class="getNavItemClass(item, true)"
          >
            <component :is="item.icon" class="nav-item-icon" />
            <span v-if="!collapsed" class="flex-1 text-left transition-opacity duration-300">
              {{ item.name }}
            </span>
            <ChevronDownIcon
              v-if="!collapsed"
              :class="[
                'w-4 h-4 transition-transform duration-300',
                expandedMenus.includes(item.name) ? 'rotate-180' : ''
              ]"
            />
          </button>
          
          <Transition name="submenu">
            <div
              v-if="!collapsed && expandedMenus.includes(item.name)"
              class="ml-8 mt-2 space-y-1"
            >
              <router-link
                v-for="child in item.children"
                :key="child.name"
                :to="child.href"
                :class="getNavItemClass(child, false, true)"
              >
                <span>{{ child.name }}</span>
              </router-link>
            </div>
          </Transition>
        </div>
      </div>
    </nav>
    
    <!-- 底部操作区 -->
    <div class="px-4 py-4 mt-auto border-t border-gray-200 dark:border-gray-700">
      <div class="space-y-2">
        <button
          @click="toggleTheme"
          :class="getActionButtonClass()"
        >
          <SunIcon v-if="isDark" class="nav-item-icon" />
          <MoonIcon v-else class="nav-item-icon" />
          <span v-if="!collapsed">
            {{ isDark ? '浅色模式' : '深色模式' }}
          </span>
        </button>
        
        <button
          @click="$emit('logout')"
          :class="getActionButtonClass()"
        >
          <ArrowRightOnRectangleIcon class="nav-item-icon" />
          <span v-if="!collapsed">退出登录</span>
        </button>
      </div>
    </div>
    
    <!-- 移动端遮罩 -->
    <div
      v-if="!collapsed"
      @click="$emit('toggle')"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
    ></div>
  </div>
</template>

<script>
import {
  HomeIcon,
  DocumentTextIcon,
  ChatBubbleLeftIcon,
  UserGroupIcon,
  BookOpenIcon,
  ClipboardDocumentListIcon,
  Cog6ToothIcon,
  UserIcon,
  XMarkIcon,
  ChevronDownIcon,
  SunIcon,
  MoonIcon,
  ArrowRightOnRectangleIcon,
  MagnifyingGlassIcon,
  CloudArrowUpIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'Sidebar',
  components: {
    HomeIcon,
    DocumentTextIcon,
    ChatBubbleLeftIcon,
    UserGroupIcon,
    BookOpenIcon,
    ClipboardDocumentListIcon,
    Cog6ToothIcon,
    UserIcon,
    XMarkIcon,
    ChevronDownIcon,
    SunIcon,
    MoonIcon,
    ArrowRightOnRectangleIcon,
    MagnifyingGlassIcon,
    CloudArrowUpIcon,
    ChartBarIcon
  },
  props: {
    collapsed: {
      type: Boolean,
      default: false
    },
    user: {
      type: Object,
      default: null
    }
  },
  emits: ['toggle', 'logout', 'new-chat'],
  data() {
    return {
      expandedMenus: [],
      isDark: false,
      navigation: [
        {
          name: '仪表板',
          href: '/dashboard',
          icon: HomeIcon
        },
        {
          name: '文档管理',
          href: '/documents',
          icon: DocumentTextIcon,
          badge: { text: '12', type: 'info' }
        },
        {
          name: '智能对话',
          href: '/chat',
          icon: ChatBubbleLeftIcon
        },
        {
          name: '知识库',
          href: '/knowledge-base',
          icon: BookOpenIcon,
          children: [
            { name: '分类管理', href: '/knowledge-base/categories' },
            { name: '标签管理', href: '/knowledge-base/tags' },
            { name: '知识库设置', href: '/knowledge-base/settings' }
          ]
        },
        {
          name: '搜索',
          href: '/search',
          icon: MagnifyingGlassIcon
        },
        {
          name: '用户管理',
          href: '/users',
          icon: UserGroupIcon
        },
        {
          name: '审计日志',
          href: '/audit-logs',
          icon: ClipboardDocumentListIcon
        },
        {
          name: '系统设置',
          href: '/settings',
          icon: Cog6ToothIcon
        }
      ]
    }
  },
  computed: {
    sidebarClasses() {
      const baseClasses = 'fixed left-0 top-0 z-50 h-full bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-300 lg:static lg:translate-x-0'
      const widthClasses = this.collapsed ? 'w-20' : 'w-72'
      const mobileClasses = this.collapsed ? '-translate-x-full lg:translate-x-0' : 'translate-x-0'
      
      return [baseClasses, widthClasses, mobileClasses].join(' ')
    }
  },
  methods: {
    getNavItemClass(item, isParent = false, isChild = false) {
      const baseClasses = 'nav-item w-full justify-start'
      const activeClasses = 'nav-item active'
      const childClasses = isChild ? 'text-sm py-2' : ''
      
      // 检查是否为当前路由
      const isActive = this.$route.path === item.href || 
                      (item.children && item.children.some(child => this.$route.path === child.href))
      
      return isActive ? `${activeClasses} ${childClasses}` : `${baseClasses} ${childClasses}`
    },
    getActionButtonClass() {
      return 'nav-item w-full justify-start text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
    },
    badgeClass(badge) {
      const typeClasses = {
        primary: 'tag tag-primary',
        success: 'tag tag-success',
        warning: 'tag tag-warning',
        error: 'tag tag-error',
        info: 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-100'
      }
      
      return typeClasses[badge.type] || typeClasses.info
    },
    toggleSubmenu(menuName) {
      const index = this.expandedMenus.indexOf(menuName)
      if (index > -1) {
        this.expandedMenus.splice(index, 1)
      } else {
        this.expandedMenus.push(menuName)
      }
    },
    toggleTheme() {
      this.isDark = !this.isDark
      const html = document.documentElement
      if (this.isDark) {
        html.classList.add('dark')
        localStorage.setItem('theme', 'dark')
      } else {
        html.classList.remove('dark')
        localStorage.setItem('theme', 'light')
      }
    }
  },
  mounted() {
    // 初始化主题
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    
    this.isDark = savedTheme === 'dark' || (!savedTheme && prefersDark)
    
    if (this.isDark) {
      document.documentElement.classList.add('dark')
    }
  }
}
</script>

<style scoped>
.submenu-enter-active,
.submenu-leave-active {
  transition: all 0.3s ease;
}

.submenu-enter-from,
.submenu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
}

.submenu-enter-to,
.submenu-leave-from {
  opacity: 1;
  transform: translateY(0);
  max-height: 200px;
}
</style>