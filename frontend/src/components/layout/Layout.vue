<template>
  <div class="min-h-screen bg-light-secondary dark:bg-dark-primary">
    <!-- 侧边栏 -->
    <Sidebar
      :collapsed="sidebarCollapsed"
      :user="user"
      @toggle="toggleSidebar"
      @logout="handleLogout"
      @new-chat="handleNewChat"
    />
    
    <!-- 主内容区域 -->
    <div :class="mainContentClasses">
      <!-- 顶部导航栏 -->
      <Header
        :user="user"
        @toggle-sidebar="toggleSidebar"
        @logout="handleLogout"
        @new-document="handleNewDocument"
        @upload-file="handleUploadFile"
      />
      
      <!-- 页面内容 -->
      <main class="flex-1 overflow-auto">
        <div class="container max-w-8xl mx-auto px-4 lg:px-6 py-6">
          <!-- 页面加载动画 -->
          <Transition name="page" mode="out-in">
            <router-view v-slot="{ Component, route }">
              <component
                :is="Component"
                :key="route.fullPath"
                class="animate-fade-in"
              />
            </router-view>
          </Transition>
        </div>
      </main>
    </div>
    
    <!-- 全局模态框容器 -->
    <div id="modal-container"></div>
    
    <!-- 全局通知容器 -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 space-y-3">
      <TransitionGroup name="notification" appear>
        <Alert
          v-for="notification in globalNotifications"
          :key="notification.id"
          :type="notification.type"
          :title="notification.title"
          :message="notification.message"
          :closable="true"
          @close="dismissNotification(notification.id)"
        />
      </TransitionGroup>
    </div>
    
    <!-- 新建聊天模态框 -->
    <Modal
      v-model="showNewChatModal"
      title="新建对话"
      size="md"
    >
      <div class="space-y-4">
        <Input
          v-model="newChatTitle"
          label="对话标题"
          placeholder="输入对话标题（可选）"
        />
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            选择知识库
          </label>
          <select
            v-model="selectedKnowledgeBase"
            class="input-field"
          >
            <option value="">全部知识库</option>
            <option
              v-for="kb in knowledgeBases"
              :key="kb.id"
              :value="kb.id"
            >
              {{ kb.name }}
            </option>
          </select>
        </div>
      </div>
      
      <template #footer>
        <Button
          variant="secondary"
          @click="showNewChatModal = false"
        >
          取消
        </Button>
        <Button
          variant="primary"
          @click="createNewChat"
          :loading="creatingChat"
        >
          开始对话
        </Button>
      </template>
    </Modal>
    
    <!-- 文件上传模态框 -->
    <Modal
      v-model="showUploadModal"
      title="上传文件"
      size="lg"
    >
      <FileUpload
        @uploaded="handleFileUploaded"
        @close="showUploadModal = false"
      />
    </Modal>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import Sidebar from './Sidebar.vue'
import Header from './Header.vue'
import { Modal, Button, Input, Alert } from '../ui'
import FileUpload from '../common/FileUpload.vue'

export default {
  name: 'Layout',
  components: {
    Sidebar,
    Header,
    Modal,
    Button,
    Input,
    Alert,
    FileUpload
  },
  setup() {
    const router = useRouter()
    const sidebarCollapsed = ref(window.innerWidth < 1024)
    const showNewChatModal = ref(false)
    const showUploadModal = ref(false)
    const newChatTitle = ref('')
    const selectedKnowledgeBase = ref('')
    const creatingChat = ref(false)
    const globalNotifications = ref([])
    
    // 模拟用户数据
    const user = ref({
      id: 1,
      name: '张三',
      email: '<EMAIL>',
      role: '管理员',
      avatar: null
    })
    
    // 模拟知识库数据
    const knowledgeBases = ref([
      { id: 1, name: '技术文档' },
      { id: 2, name: '产品手册' },
      { id: 3, name: '培训资料' }
    ])
    
    // 计算主内容区域的样式
    const mainContentClasses = computed(() => {
      const baseClasses = 'flex flex-col min-h-screen transition-all duration-300'
      const marginClass = sidebarCollapsed.value ? 'lg:ml-20' : 'lg:ml-72'
      
      return [baseClasses, marginClass].join(' ')
    })
    
    // 响应式处理
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        sidebarCollapsed.value = true
      }
    }
    
    // 切换侧边栏
    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }
    
    // 处理登出
    const handleLogout = async () => {
      try {
        // 调用登出API
        await logoutUser()
        
        // 清理本地存储
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_data')
        
        // 重定向到登录页
        router.push('/login')
        
        showNotification({
          type: 'success',
          title: '登出成功',
          message: '您已成功退出系统'
        })
      } catch (error) {
        showNotification({
          type: 'error',
          title: '登出失败',
          message: error.message || '登出时发生错误'
        })
      }
    }
    
    // 处理新建对话
    const handleNewChat = () => {
      showNewChatModal.value = true
    }
    
    // 创建新对话
    const createNewChat = async () => {
      creatingChat.value = true
      try {
        const chatData = {
          title: newChatTitle.value || '新对话',
          knowledgeBaseId: selectedKnowledgeBase.value || null
        }
        
        // 调用创建对话API
        const response = await createChat(chatData)
        
        // 跳转到新对话
        router.push(`/chat/${response.id}`)
        
        // 重置表单
        newChatTitle.value = ''
        selectedKnowledgeBase.value = ''
        showNewChatModal.value = false
        
        showNotification({
          type: 'success',
          title: '对话创建成功',
          message: '新对话已创建，可以开始聊天了'
        })
      } catch (error) {
        showNotification({
          type: 'error',
          title: '创建失败',
          message: error.message || '创建对话时发生错误'
        })
      } finally {
        creatingChat.value = false
      }
    }
    
    // 处理新建文档
    const handleNewDocument = () => {
      router.push('/documents/new')
    }
    
    // 处理文件上传
    const handleUploadFile = () => {
      showUploadModal.value = true
    }
    
    // 处理文件上传完成
    const handleFileUploaded = (files) => {
      showUploadModal.value = false
      
      showNotification({
        type: 'success',
        title: '上传成功',
        message: `成功上传 ${files.length} 个文件`
      })
      
      // 如果当前在文档页面，刷新文档列表
      if (router.currentRoute.value.path.startsWith('/documents')) {
        // 触发文档列表刷新事件
        window.dispatchEvent(new CustomEvent('refresh-documents'))
      }
    }
    
    // 显示全局通知
    const showNotification = (notification) => {
      const id = Date.now() + Math.random()
      globalNotifications.value.push({
        id,
        ...notification
      })
      
      // 5秒后自动消失
      setTimeout(() => {
        dismissNotification(id)
      }, 5000)
    }
    
    // 关闭通知
    const dismissNotification = (id) => {
      const index = globalNotifications.value.findIndex(n => n.id === id)
      if (index > -1) {
        globalNotifications.value.splice(index, 1)
      }
    }
    
    // 模拟API函数
    const logoutUser = async () => {
      return new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    const createChat = async (chatData) => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            id: Date.now(),
            ...chatData
          })
        }, 1000)
      })
    }
    
    onMounted(() => {
      window.addEventListener('resize', handleResize)
      
      // 监听全局通知事件
      window.addEventListener('show-notification', (event) => {
        showNotification(event.detail)
      })
    })
    
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('show-notification', () => {})
    })
    
    return {
      sidebarCollapsed,
      mainContentClasses,
      user,
      showNewChatModal,
      showUploadModal,
      newChatTitle,
      selectedKnowledgeBase,
      knowledgeBases,
      creatingChat,
      globalNotifications,
      toggleSidebar,
      handleLogout,
      handleNewChat,
      createNewChat,
      handleNewDocument,
      handleUploadFile,
      handleFileUploaded,
      dismissNotification
    }
  }
}
</script>

<style scoped>
.page-enter-active,
.page-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.notification-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.notification-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

@media (max-width: 1023px) {
  .lg\:ml-20 {
    margin-left: 0;
  }
  
  .lg\:ml-72 {
    margin-left: 0;
  }
}
</style>