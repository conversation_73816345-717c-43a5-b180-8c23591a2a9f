<template>
  <header class="sticky top-0 z-30 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between px-4 lg:px-6 h-16">
      <!-- 左侧：移动端菜单按钮 + 面包屑 -->
      <div class="flex items-center space-x-4">
        <button
          @click="$emit('toggle-sidebar')"
          class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors lg:hidden"
        >
          <Bars3Icon class="w-6 h-6 text-gray-600 dark:text-gray-400" />
        </button>
        
        <!-- 面包屑导航 -->
        <nav class="hidden sm:flex items-center space-x-2 text-sm">
          <router-link
            v-for="(item, index) in breadcrumbs"
            :key="item.path"
            :to="item.path"
            :class="[
              'flex items-center hover:text-primary-600 dark:hover:text-primary-400 transition-colors',
              index === breadcrumbs.length - 1 
                ? 'text-gray-900 dark:text-gray-100 font-medium' 
                : 'text-gray-500 dark:text-gray-400'
            ]"
          >
            <component
              v-if="item.icon"
              :is="item.icon"
              class="w-4 h-4 mr-1"
            />
            <span>{{ item.name }}</span>
            <ChevronRightIcon
              v-if="index < breadcrumbs.length - 1"
              class="w-4 h-4 mx-2 text-gray-400 dark:text-gray-500"
            />
          </router-link>
        </nav>
      </div>
      
      <!-- 中间：全局搜索 -->
      <div class="flex-1 max-w-2xl mx-4">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
          </div>
          <input
            v-model="searchQuery"
            @keydown.enter="handleSearch"
            @focus="showSearchSuggestions = true"
            @blur="hideSearchSuggestions"
            type="text"
            class="block w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-700 rounded-xl bg-gray-50 dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
            placeholder="搜索文档、知识库、用户..."
          />
          
          <!-- 搜索建议下拉框 -->
          <div
            v-if="showSearchSuggestions && (searchSuggestions.length > 0 || searchQuery)"
            class="absolute top-full mt-2 w-full bg-white dark:bg-gray-800 rounded-xl shadow-glass border border-gray-200 dark:border-gray-700 py-2 z-50"
          >
            <div v-if="searchQuery && searchSuggestions.length === 0" class="px-4 py-3 text-gray-500 dark:text-gray-400 text-sm">
              按回车搜索 "{{ searchQuery }}"
            </div>
            <div v-else>
              <div
                v-for="suggestion in searchSuggestions"
                :key="suggestion.id"
                @mousedown="selectSuggestion(suggestion)"
                class="flex items-center px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
              >
                <component :is="suggestion.icon" class="w-4 h-4 mr-3 text-gray-400" />
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-900 dark:text-gray-100 truncate">
                    {{ suggestion.title }}
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {{ suggestion.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧：通知 + 用户菜单 -->
      <div class="flex items-center space-x-3">
        <!-- 快捷操作按钮 -->
        <div class="hidden md:flex items-center space-x-2">
          <button
            @click="$emit('new-document')"
            class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors group"
            title="新建文档"
          >
            <PlusIcon class="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400" />
          </button>
          
          <button
            @click="$emit('upload-file')"
            class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors group"
            title="上传文件"
          >
            <CloudArrowUpIcon class="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400" />
          </button>
        </div>
        
        <!-- 通知按钮 -->
        <div class="relative">
          <button
            @click="toggleNotifications"
            class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors group relative"
          >
            <BellIcon class="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400" />
            <span
              v-if="unreadCount > 0"
              class="absolute -top-1 -right-1 w-5 h-5 bg-error text-white text-xs rounded-full flex items-center justify-center font-medium"
            >
              {{ unreadCount > 9 ? '9+' : unreadCount }}
            </span>
          </button>
          
          <!-- 通知下拉框 -->
          <div
            v-if="showNotifications"
            class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-glass border border-gray-200 dark:border-gray-700 py-2 z-50"
          >
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">通知</h3>
                <button
                  v-if="unreadCount > 0"
                  @click="markAllAsRead"
                  class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300"
                >
                  全部标记已读
                </button>
              </div>
            </div>
            <div class="max-h-96 overflow-y-auto">
              <div
                v-if="notifications.length === 0"
                class="px-4 py-8 text-center text-gray-500 dark:text-gray-400 text-sm"
              >
                暂无通知
              </div>
              <div v-else>
                <div
                  v-for="notification in notifications"
                  :key="notification.id"
                  :class="[
                    'flex items-start px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer',
                    !notification.read ? 'bg-blue-50/50 dark:bg-blue-900/20' : ''
                  ]"
                  @click="handleNotificationClick(notification)"
                >
                  <div class="flex-shrink-0">
                    <div :class="[
                      'w-2 h-2 rounded-full mt-2',
                      notification.read ? 'bg-gray-300 dark:bg-gray-600' : 'bg-primary-500'
                    ]"></div>
                  </div>
                  <div class="ml-3 flex-1 min-w-0">
                    <p class="text-sm text-gray-900 dark:text-gray-100">
                      {{ notification.title }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {{ notification.message }}
                    </p>
                    <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                      {{ formatTime(notification.time) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 用户菜单 -->
        <div class="relative">
          <button
            @click="toggleUserMenu"
            class="flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <UserIcon class="w-4 h-4 text-white" />
            </div>
            <div class="hidden sm:block text-left">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ user?.name || '用户' }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ user?.role || '普通用户' }}
              </p>
            </div>
            <ChevronDownIcon class="hidden sm:block w-4 h-4 text-gray-400" />
          </button>
          
          <!-- 用户下拉菜单 -->
          <div
            v-if="showUserMenu"
            class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-glass border border-gray-200 dark:border-gray-700 py-2 z-50"
          >
            <div class="px-3 py-2 border-b border-gray-200 dark:border-gray-700 sm:hidden">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ user?.name || '用户' }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ user?.email || '' }}
              </p>
            </div>
            
            <router-link
              to="/profile"
              class="dropdown-item"
              @click="showUserMenu = false"
            >
              <UserIcon class="w-4 h-4 mr-3" />
              个人资料
            </router-link>
            
            <router-link
              to="/settings"
              class="dropdown-item"
              @click="showUserMenu = false"
            >
              <Cog6ToothIcon class="w-4 h-4 mr-3" />
              设置
            </router-link>
            
            <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
            
            <button
              @click="handleLogout"
              class="dropdown-item w-full text-red-600 dark:text-red-400"
            >
              <ArrowRightOnRectangleIcon class="w-4 h-4 mr-3" />
              退出登录
            </button>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import {
  Bars3Icon,
  MagnifyingGlassIcon,
  BellIcon,
  UserIcon,
  PlusIcon,
  CloudArrowUpIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  HomeIcon,
  DocumentTextIcon,
  ChatBubbleLeftIcon,
  BookOpenIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'Header',
  components: {
    Bars3Icon,
    MagnifyingGlassIcon,
    BellIcon,
    UserIcon,
    PlusIcon,
    CloudArrowUpIcon,
    ChevronRightIcon,
    ChevronDownIcon,
    Cog6ToothIcon,
    ArrowRightOnRectangleIcon,
    HomeIcon,
    DocumentTextIcon,
    ChatBubbleLeftIcon,
    BookOpenIcon
  },
  props: {
    user: {
      type: Object,
      default: null
    }
  },
  emits: ['toggle-sidebar', 'logout', 'new-document', 'upload-file'],
  data() {
    return {
      searchQuery: '',
      showSearchSuggestions: false,
      showNotifications: false,
      showUserMenu: false,
      unreadCount: 3,
      notifications: [
        {
          id: 1,
          title: '文档处理完成',
          message: '您上传的 "项目需求文档.pdf" 已成功处理并建立索引',
          time: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
          read: false,
          type: 'success'
        },
        {
          id: 2,
          title: '知识库更新',
          message: '技术文档知识库新增了 5 个文档',
          time: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
          read: false,
          type: 'info'
        },
        {
          id: 3,
          title: '系统维护通知',
          message: '系统将在今晚 23:00-24:00 进行维护升级',
          time: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
          read: false,
          type: 'warning'
        }
      ],
      searchSuggestions: []
    }
  },
  computed: {
    breadcrumbs() {
      const routeMap = {
        '/dashboard': [
          { name: '仪表板', path: '/dashboard', icon: HomeIcon }
        ],
        '/documents': [
          { name: '文档管理', path: '/documents', icon: DocumentTextIcon }
        ],
        '/chat': [
          { name: '智能对话', path: '/chat', icon: ChatBubbleLeftIcon }
        ],
        '/knowledge-base': [
          { name: '知识库', path: '/knowledge-base', icon: BookOpenIcon }
        ],
        '/knowledge-base/categories': [
          { name: '知识库', path: '/knowledge-base', icon: BookOpenIcon },
          { name: '分类管理', path: '/knowledge-base/categories' }
        ]
      }
      
      return routeMap[this.$route.path] || [
        { name: '首页', path: '/dashboard', icon: HomeIcon }
      ]
    }
  },
  watch: {
    searchQuery: {
      handler(newVal) {
        this.debouncedSearch(newVal)
      },
      immediate: false
    }
  },
  methods: {
    handleSearch() {
      if (this.searchQuery.trim()) {
        this.$router.push({
          path: '/search',
          query: { q: this.searchQuery.trim() }
        })
        this.showSearchSuggestions = false
      }
    },
    debouncedSearch: (() => {
      let timeout
      return function(query) {
        clearTimeout(timeout)
        timeout = setTimeout(() => {
          if (query && query.length > 1) {
            this.fetchSearchSuggestions(query)
          } else {
            this.searchSuggestions = []
          }
        }, 300)
      }
    })(),
    async fetchSearchSuggestions(query) {
      // 模拟API调用
      const mockSuggestions = [
        {
          id: 1,
          title: 'Vue.js 开发指南',
          description: '前端开发文档',
          icon: DocumentTextIcon,
          type: 'document'
        },
        {
          id: 2,
          title: '技术文档知识库',
          description: '包含 125 个文档',
          icon: BookOpenIcon,
          type: 'knowledge-base'
        }
      ].filter(item => 
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase())
      )
      
      this.searchSuggestions = mockSuggestions
    },
    selectSuggestion(suggestion) {
      if (suggestion.type === 'document') {
        this.$router.push(`/documents/${suggestion.id}`)
      } else if (suggestion.type === 'knowledge-base') {
        this.$router.push(`/knowledge-base/${suggestion.id}`)
      }
      this.searchQuery = suggestion.title
      this.showSearchSuggestions = false
    },
    hideSearchSuggestions() {
      setTimeout(() => {
        this.showSearchSuggestions = false
      }, 200)
    },
    toggleNotifications() {
      this.showNotifications = !this.showNotifications
      this.showUserMenu = false
    },
    toggleUserMenu() {
      this.showUserMenu = !this.showUserMenu
      this.showNotifications = false
    },
    handleNotificationClick(notification) {
      if (!notification.read) {
        notification.read = true
        this.unreadCount = Math.max(0, this.unreadCount - 1)
      }
      this.showNotifications = false
      
      // 根据通知类型跳转到相应页面
      if (notification.type === 'document') {
        this.$router.push('/documents')
      }
    },
    markAllAsRead() {
      this.notifications.forEach(notification => {
        notification.read = true
      })
      this.unreadCount = 0
    },
    handleLogout() {
      this.showUserMenu = false
      this.$emit('logout')
    },
    formatTime(time) {
      const now = new Date()
      const diff = now - time
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes} 分钟前`
      if (hours < 24) return `${hours} 小时前`
      if (days < 7) return `${days} 天前`
      
      return time.toLocaleDateString('zh-CN')
    }
  },
  mounted() {
    // 点击外部关闭下拉菜单
    document.addEventListener('click', (event) => {
      if (!this.$el.contains(event.target)) {
        this.showNotifications = false
        this.showUserMenu = false
        this.showSearchSuggestions = false
      }
    })
  }
}
</script>