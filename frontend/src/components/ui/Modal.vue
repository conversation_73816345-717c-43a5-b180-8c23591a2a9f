<template>
  <Teleport to="body">
    <Transition name="modal">
      <div
        v-if="modelValue"
        class="modal-overlay fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        @click="handleOverlayClick"
      >
        <div ref="modalContent" :class="modalClasses" @click.stop>
          <header
            v-if="$slots.header || title"
            class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"
          >
            <slot name="header">
              <h3
                v-if="title"
                class="text-xl font-semibold text-gray-900 dark:text-gray-100"
              >
                {{ title }}
              </h3>
            </slot>

            <button
              v-if="closable"
              @click="close"
              class="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <XMarkIcon class="w-5 h-5 text-gray-500" />
            </button>
          </header>

          <div class="flex-1 p-6 overflow-auto">
            <slot />
          </div>

          <footer
            v-if="$slots.footer"
            class="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700"
          >
            <slot name="footer" />
          </footer>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script>
import { XMarkIcon } from "@heroicons/vue/24/outline";

export default {
  name: "Modal",
  components: {
    XMarkIcon,
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "md",
      validator: (value) => ["sm", "md", "lg", "xl", "full"].includes(value),
    },
    closable: {
      type: Boolean,
      default: true,
    },
    closeOnOverlay: {
      type: Boolean,
      default: true,
    },
  },
  emits: ["update:modelValue", "close"],
  computed: {
    modalClasses() {
      const baseClasses = "modal-content max-h-full flex flex-col";

      const sizeClasses = {
        sm: "max-w-md w-full",
        md: "max-w-lg w-full",
        lg: "max-w-2xl w-full",
        xl: "max-w-4xl w-full",
        full: "w-full h-full max-w-none",
      };

      return [baseClasses, sizeClasses[this.size]].join(" ");
    },
  },
  methods: {
    close() {
      this.$emit("update:modelValue", false);
      this.$emit("close");
    },
    handleOverlayClick() {
      if (this.closeOnOverlay) {
        this.close();
      }
    },
  },
  mounted() {
    // 监听ESC键
    const handleEscKey = (event) => {
      if (event.key === "Escape" && this.modelValue && this.closable) {
        this.close();
      }
    };

    document.addEventListener("keydown", handleEscKey);

    // 组件卸载时移除监听器
    this.$once("hook:beforeUnmount", () => {
      document.removeEventListener("keydown", handleEscKey);
    });
  },
};
</script>

<style scoped>
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from {
  opacity: 0;
}

.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.9) translateY(20px);
}
</style>