<template>
  <div :class="cardClasses">
    <header
      v-if="$slots.header || title"
      class="mb-4 pb-4 border-b border-gray-200 dark:border-gray-700"
    >
      <slot name="header">
        <h3
          v-if="title"
          class="text-lg font-semibold text-gray-900 dark:text-gray-100"
        >
          {{ title }}
        </h3>
      </slot>
    </header>
    
    <div class="flex-1">
      <slot />
    </div>
    
    <footer
      v-if="$slots.footer"
      class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
    >
      <slot name="footer" />
    </footer>
  </div>
</template>

<script>
export default {
  name: 'Card',
  props: {
    title: {
      type: String,
      default: ''
    },
    variant: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'glass', 'outlined'].includes(value)
    },
    padding: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
    },
    hoverable: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    cardClasses() {
      const baseClasses = 'card flex flex-col'
      
      const variantClasses = {
        default: '',
        glass: 'card-glass',
        outlined: 'border-2 bg-transparent shadow-none'
      }
      
      const paddingClasses = {
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
        xl: 'p-10'
      }
      
      const hoverClass = this.hoverable ? 'hover-lift cursor-pointer' : ''
      
      return [
        baseClasses,
        variantClasses[this.variant],
        paddingClasses[this.padding],
        hoverClass
      ].filter(Boolean).join(' ')
    }
  }
}
</script>