<template>
  <div class="w-full">
    <label
      v-if="label"
      :for="id"
      class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
    >
      {{ label }}
      <span v-if="required" class="text-error ml-1">*</span>
    </label>
    
    <div class="relative">
      <div
        v-if="$slots.prefix || prefixIcon"
        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
      >
        <component
          v-if="prefixIcon"
          :is="prefixIcon"
          class="h-5 w-5 text-gray-400"
        />
        <slot name="prefix" />
      </div>
      
      <input
        :id="id"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        v-bind="$attrs"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <div
        v-if="$slots.suffix || suffixIcon"
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
      >
        <component
          v-if="suffixIcon"
          :is="suffixIcon"
          class="h-5 w-5 text-gray-400"
        />
        <slot name="suffix" />
      </div>
    </div>
    
    <p
      v-if="error"
      class="mt-2 text-sm text-error"
    >
      {{ error }}
    </p>
    
    <p
      v-else-if="hint"
      class="mt-2 text-sm text-gray-500 dark:text-gray-400"
    >
      {{ hint }}
    </p>
  </div>
</template>

<script>
export default {
  name: 'Input',
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default: () => `input-${Math.random().toString(36).substr(2, 9)}`
    },
    modelValue: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: String,
      default: 'text'
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    hint: {
      type: String,
      default: ''
    },
    error: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg'].includes(value)
    },
    prefixIcon: {
      type: [Object, String],
      default: null
    },
    suffixIcon: {
      type: [Object, String],
      default: null
    }
  },
  emits: ['update:modelValue', 'blur', 'focus'],
  computed: {
    inputClasses() {
      const baseClasses = 'input-field'
      const sizeClasses = {
        sm: 'px-3 py-2 text-sm',
        md: 'px-4 py-3',
        lg: 'px-5 py-4 text-lg'
      }
      
      const prefixPadding = (this.$slots.prefix || this.prefixIcon) ? 'pl-10' : ''
      const suffixPadding = (this.$slots.suffix || this.suffixIcon) ? 'pr-10' : ''
      const errorClass = this.error ? 'border-error focus:border-error focus:ring-red-100 dark:focus:ring-red-800' : ''
      
      return [
        baseClasses,
        sizeClasses[this.size],
        prefixPadding,
        suffixPadding,
        errorClass
      ].filter(Boolean).join(' ')
    }
  },
  methods: {
    handleInput(event) {
      this.$emit('update:modelValue', event.target.value)
    },
    handleBlur(event) {
      this.$emit('blur', event)
    },
    handleFocus(event) {
      this.$emit('focus', event)
    }
  }
}
</script>