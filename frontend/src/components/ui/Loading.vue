<template>
  <div :class="containerClasses">
    <div
      v-if="type === 'spinner'"
      :class="spinnerClasses"
    >
      <svg
        class="animate-spin"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
    </div>
    
    <div
      v-else-if="type === 'dots'"
      class="flex space-x-1"
    >
      <div
        v-for="i in 3"
        :key="i"
        :class="dotClasses"
        :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
      ></div>
    </div>
    
    <div
      v-else-if="type === 'skeleton'"
      class="animate-pulse space-y-3"
    >
      <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded-lg w-3/4"></div>
      <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded-lg w-1/2"></div>
      <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded-lg w-5/6"></div>
    </div>
    
    <p
      v-if="text"
      :class="textClasses"
    >
      {{ text }}
    </p>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  props: {
    type: {
      type: String,
      default: 'spinner',
      validator: (value) => ['spinner', 'dots', 'skeleton'].includes(value)
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
    },
    text: {
      type: String,
      default: ''
    },
    overlay: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: 'primary'
    }
  },
  computed: {
    containerClasses() {
      const baseClasses = 'flex flex-col items-center justify-center'
      
      const overlayClasses = this.overlay 
        ? 'fixed inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50' 
        : ''
      
      return [baseClasses, overlayClasses].filter(Boolean).join(' ')
    },
    spinnerClasses() {
      const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-8 h-8',
        lg: 'w-12 h-12',
        xl: 'w-16 h-16'
      }
      
      const colorClasses = {
        primary: 'text-primary-500',
        secondary: 'text-secondary-500',
        success: 'text-success',
        warning: 'text-warning',
        error: 'text-error'
      }
      
      return [
        sizeClasses[this.size],
        colorClasses[this.color]
      ].join(' ')
    },
    dotClasses() {
      const sizeClasses = {
        sm: 'w-1.5 h-1.5',
        md: 'w-2 h-2',
        lg: 'w-3 h-3',
        xl: 'w-4 h-4'
      }
      
      const colorClasses = {
        primary: 'bg-primary-500',
        secondary: 'bg-secondary-500',
        success: 'bg-success',
        warning: 'bg-warning',
        error: 'bg-error'
      }
      
      return [
        'rounded-full animate-bounce',
        sizeClasses[this.size],
        colorClasses[this.color]
      ].join(' ')
    },
    textClasses() {
      const sizeClasses = {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
        xl: 'text-xl'
      }
      
      return [
        'mt-3 text-gray-600 dark:text-gray-400',
        sizeClasses[this.size]
      ].join(' ')
    }
  }
}
</script>