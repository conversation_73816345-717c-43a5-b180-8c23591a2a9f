<template>
  <div :class="alertClasses">
    <div class="flex">
      <div class="flex-shrink-0">
        <component
          :is="iconComponent"
          :class="iconClasses"
        />
      </div>
      
      <div class="ml-3 flex-1">
        <h3
          v-if="title"
          :class="titleClasses"
        >
          {{ title }}
        </h3>
        
        <div :class="messageClasses">
          <slot>{{ message }}</slot>
        </div>
        
        <div
          v-if="$slots.actions"
          class="mt-3"
        >
          <slot name="actions" />
        </div>
      </div>
      
      <div
        v-if="closable"
        class="ml-auto pl-3"
      >
        <div class="-mx-1.5 -my-1.5">
          <button
            @click="close"
            :class="closeButtonClasses"
          >
            <span class="sr-only">关闭</span>
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  XCircleIcon, 
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'Alert',
  components: {
    CheckCircleIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
    InformationCircleIcon,
    XMarkIcon
  },
  props: {
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['success', 'warning', 'error', 'info'].includes(value)
    },
    title: {
      type: String,
      default: ''
    },
    message: {
      type: String,
      default: ''
    },
    closable: {
      type: Boolean,
      default: false
    },
    variant: {
      type: String,
      default: 'filled',
      validator: (value) => ['filled', 'outlined', 'minimal'].includes(value)
    }
  },
  emits: ['close'],
  computed: {
    alertClasses() {
      const baseClasses = 'rounded-xl p-4 border'
      
      const typeClasses = {
        filled: {
          success: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
          warning: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
          error: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
          info: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
        },
        outlined: {
          success: 'bg-transparent border-green-300 dark:border-green-700',
          warning: 'bg-transparent border-yellow-300 dark:border-yellow-700',
          error: 'bg-transparent border-red-300 dark:border-red-700',
          info: 'bg-transparent border-blue-300 dark:border-blue-700'
        },
        minimal: {
          success: 'bg-transparent border-transparent',
          warning: 'bg-transparent border-transparent',
          error: 'bg-transparent border-transparent',
          info: 'bg-transparent border-transparent'
        }
      }
      
      return [baseClasses, typeClasses[this.variant][this.type]].join(' ')
    },
    iconComponent() {
      const icons = {
        success: CheckCircleIcon,
        warning: ExclamationTriangleIcon,
        error: XCircleIcon,
        info: InformationCircleIcon
      }
      
      return icons[this.type]
    },
    iconClasses() {
      const colorClasses = {
        success: 'text-green-500',
        warning: 'text-yellow-500',
        error: 'text-red-500',
        info: 'text-blue-500'
      }
      
      return ['h-5 w-5', colorClasses[this.type]].join(' ')
    },
    titleClasses() {
      const colorClasses = {
        success: 'text-green-800 dark:text-green-200',
        warning: 'text-yellow-800 dark:text-yellow-200',
        error: 'text-red-800 dark:text-red-200',
        info: 'text-blue-800 dark:text-blue-200'
      }
      
      return ['text-sm font-medium', colorClasses[this.type]].join(' ')
    },
    messageClasses() {
      const colorClasses = {
        success: 'text-green-700 dark:text-green-300',
        warning: 'text-yellow-700 dark:text-yellow-300',
        error: 'text-red-700 dark:text-red-300',
        info: 'text-blue-700 dark:text-blue-300'
      }
      
      const marginClass = this.title ? 'mt-1' : ''
      
      return ['text-sm', colorClasses[this.type], marginClass].filter(Boolean).join(' ')
    },
    closeButtonClasses() {
      const colorClasses = {
        success: 'text-green-500 hover:bg-green-100 dark:hover:bg-green-800 focus:ring-green-600',
        warning: 'text-yellow-500 hover:bg-yellow-100 dark:hover:bg-yellow-800 focus:ring-yellow-600',
        error: 'text-red-500 hover:bg-red-100 dark:hover:bg-red-800 focus:ring-red-600',
        info: 'text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-800 focus:ring-blue-600'
      }
      
      return [
        'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors',
        colorClasses[this.type]
      ].join(' ')
    }
  },
  methods: {
    close() {
      this.$emit('close')
    }
  }
}
</script>