<template>
  <div class="space-y-6">
    <!-- 拖拽上传区域 -->
    <div
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      :class="dropZoneClasses"
    >
      <div class="text-center py-12">
        <CloudArrowUpIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <div class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          拖拽文件到此处，或
          <label class="text-primary-600 dark:text-primary-400 cursor-pointer hover:text-primary-800 dark:hover:text-primary-300">
            <span>点击选择文件</span>
            <input
              ref="fileInput"
              type="file"
              multiple
              :accept="acceptedTypes"
              @change="handleFileSelect"
              class="hidden"
            />
          </label>
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          支持 {{ acceptedTypesText }}，单个文件最大 {{ maxFileSizeText }}
        </p>
      </div>
    </div>
    
    <!-- 选择的文件列表 -->
    <div v-if="selectedFiles.length > 0" class="space-y-3">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
        待上传文件 ({{ selectedFiles.length }})
      </h3>
      
      <div class="space-y-2">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-xl"
        >
          <div class="flex items-center space-x-3 flex-1 min-w-0">
            <div class="flex-shrink-0">
              <component
                :is="getFileIcon(file.type)"
                class="h-8 w-8 text-gray-400"
              />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {{ file.name }}
              </p>
              <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                <span>{{ formatFileSize(file.size) }}</span>
                <span v-if="file.progress !== undefined">
                  • {{ file.status === 'uploading' ? '上传中' : file.status === 'success' ? '上传成功' : file.status === 'error' ? '上传失败' : '等待上传' }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 进度条 -->
          <div v-if="file.progress !== undefined" class="flex items-center space-x-3">
            <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="h-2 rounded-full transition-all duration-300"
                :class="file.status === 'error' ? 'bg-error' : file.status === 'success' ? 'bg-success' : 'bg-primary-500'"
                :style="{ width: `${file.progress}%` }"
              ></div>
            </div>
            <span class="text-xs text-gray-500 dark:text-gray-400 w-10">
              {{ file.progress }}%
            </span>
          </div>
          
          <!-- 移除按钮 -->
          <button
            v-if="file.status !== 'uploading'"
            @click="removeFile(index)"
            class="p-1 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ml-3"
          >
            <XMarkIcon class="h-4 w-4 text-gray-400 hover:text-error" />
          </button>
        </div>
      </div>
    </div>
    
    <!-- 知识库选择 -->
    <div v-if="selectedFiles.length > 0">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        选择目标知识库
      </label>
      <select
        v-model="selectedKnowledgeBase"
        class="input-field"
      >
        <option value="">选择知识库</option>
        <option
          v-for="kb in knowledgeBases"
          :key="kb.id"
          :value="kb.id"
        >
          {{ kb.name }} ({{ kb.documentCount }} 个文档)
        </option>
      </select>
    </div>
    
    <!-- 上传选项 -->
    <div v-if="selectedFiles.length > 0" class="space-y-4">
      <div class="flex items-center space-x-6">
        <label class="flex items-center">
          <input
            v-model="autoExtractContent"
            type="checkbox"
            class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500 focus:ring-offset-0"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
            自动提取文档内容
          </span>
        </label>
        
        <label class="flex items-center">
          <input
            v-model="autoVectorize"
            type="checkbox"
            class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500 focus:ring-offset-0"
          />
          <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
            自动向量化处理
          </span>
        </label>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div v-if="selectedFiles.length > 0" class="flex items-center justify-between pt-4">
      <div class="text-sm text-gray-500 dark:text-gray-400">
        共 {{ selectedFiles.length }} 个文件，总大小 {{ totalSizeText }}
      </div>
      
      <div class="flex items-center space-x-3">
        <Button
          variant="secondary"
          @click="clearFiles"
          :disabled="uploading"
        >
          清空
        </Button>
        <Button
          variant="primary"
          @click="startUpload"
          :loading="uploading"
          :disabled="selectedFiles.length === 0 || !selectedKnowledgeBase"
        >
          {{ uploading ? '上传中...' : '开始上传' }}
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  CloudArrowUpIcon,
  DocumentTextIcon,
  PhotoIcon,
  FilmIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'
import { Button } from '../ui'

export default {
  name: 'FileUpload',
  components: {
    CloudArrowUpIcon,
    DocumentTextIcon,
    PhotoIcon,
    FilmIcon,
    MusicalNoteIcon,
    ArchiveBoxIcon,
    XMarkIcon,
    Button
  },
  emits: ['uploaded', 'close'],
  data() {
    return {
      selectedFiles: [],
      uploading: false,
      dragOver: false,
      selectedKnowledgeBase: '',
      autoExtractContent: true,
      autoVectorize: true,
      maxFileSize: 50 * 1024 * 1024, // 50MB
      acceptedTypes: '.pdf,.doc,.docx,.txt,.md,.ppt,.pptx,.xls,.xlsx',
      knowledgeBases: [
        { id: 1, name: '技术文档', documentCount: 45 },
        { id: 2, name: '产品手册', documentCount: 23 },
        { id: 3, name: '培训资料', documentCount: 67 },
        { id: 4, name: '项目文档', documentCount: 89 }
      ]
    }
  },
  computed: {
    dropZoneClasses() {
      return [
        'border-2 border-dashed rounded-2xl transition-all duration-300 cursor-pointer hover:border-primary-400 dark:hover:border-primary-500',
        this.dragOver 
          ? 'border-primary-500 dark:border-primary-400 bg-primary-50 dark:bg-primary-900/20' 
          : 'border-gray-300 dark:border-gray-600'
      ].join(' ')
    },
    acceptedTypesText() {
      return 'PDF, DOC, DOCX, TXT, MD, PPT, PPTX, XLS, XLSX'
    },
    maxFileSizeText() {
      return this.formatFileSize(this.maxFileSize)
    },
    totalSizeText() {
      const totalSize = this.selectedFiles.reduce((sum, file) => sum + file.size, 0)
      return this.formatFileSize(totalSize)
    }
  },
  methods: {
    handleDragOver(event) {
      event.preventDefault()
    },
    handleDragEnter(event) {
      event.preventDefault()
      this.dragOver = true
    },
    handleDragLeave(event) {
      event.preventDefault()
      // 只有当离开整个拖拽区域时才设置为false
      if (!this.$el.contains(event.relatedTarget)) {
        this.dragOver = false
      }
    },
    handleDrop(event) {
      event.preventDefault()
      this.dragOver = false
      
      const files = Array.from(event.dataTransfer.files)
      this.addFiles(files)
    },
    handleFileSelect(event) {
      const files = Array.from(event.target.files)
      this.addFiles(files)
      
      // 重置文件输入框
      event.target.value = ''
    },
    addFiles(files) {
      const validFiles = files.filter(file => this.validateFile(file))
      
      // 添加文件到列表
      validFiles.forEach(file => {
        // 检查是否已存在相同名称的文件
        if (!this.selectedFiles.some(f => f.name === file.name)) {
          this.selectedFiles.push({
            file,
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'pending'
          })
        }
      })
      
      if (files.length !== validFiles.length) {
        this.showNotification({
          type: 'warning',
          title: '部分文件未添加',
          message: '某些文件不符合要求或已存在'
        })
      }
    },
    validateFile(file) {
      // 检查文件大小
      if (file.size > this.maxFileSize) {
        this.showNotification({
          type: 'error',
          title: '文件过大',
          message: `文件 "${file.name}" 超过了 ${this.maxFileSizeText} 的限制`
        })
        return false
      }
      
      // 检查文件类型
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'text/markdown',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ]
      
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
      const acceptedExtensions = this.acceptedTypes.split(',')
      
      if (!allowedTypes.includes(file.type) && !acceptedExtensions.includes(fileExtension)) {
        this.showNotification({
          type: 'error',
          title: '文件类型不支持',
          message: `文件 "${file.name}" 的类型不在支持范围内`
        })
        return false
      }
      
      return true
    },
    removeFile(index) {
      this.selectedFiles.splice(index, 1)
    },
    clearFiles() {
      this.selectedFiles = []
    },
    async startUpload() {
      if (!this.selectedKnowledgeBase) {
        this.showNotification({
          type: 'warning',
          title: '请选择知识库',
          message: '请先选择要上传到的知识库'
        })
        return
      }
      
      this.uploading = true
      
      try {
        // 并发上传所有文件
        const uploadPromises = this.selectedFiles.map((fileObj, index) => 
          this.uploadFile(fileObj, index)
        )
        
        await Promise.all(uploadPromises)
        
        const successCount = this.selectedFiles.filter(f => f.status === 'success').length
        const failCount = this.selectedFiles.filter(f => f.status === 'error').length
        
        if (successCount > 0) {
          this.showNotification({
            type: 'success',
            title: '上传完成',
            message: `成功上传 ${successCount} 个文件${failCount > 0 ? `，${failCount} 个文件失败` : ''}`
          })
          
          // 通知父组件
          this.$emit('uploaded', this.selectedFiles.filter(f => f.status === 'success'))
        }
        
        if (failCount === this.selectedFiles.length) {
          this.showNotification({
            type: 'error',
            title: '上传失败',
            message: '所有文件上传失败，请重试'
          })
        }
      } catch (error) {
        this.showNotification({
          type: 'error',
          title: '上传出错',
          message: error.message || '上传过程中发生未知错误'
        })
      } finally {
        this.uploading = false
      }
    },
    async uploadFile(fileObj, index) {
      try {
        fileObj.status = 'uploading'
        fileObj.progress = 0
        
        const formData = new FormData()
        formData.append('file', fileObj.file)
        formData.append('knowledge_base_id', this.selectedKnowledgeBase)
        formData.append('auto_extract_content', this.autoExtractContent)
        formData.append('auto_vectorize', this.autoVectorize)
        
        // 模拟上传进度
        const progressInterval = setInterval(() => {
          if (fileObj.progress < 90) {
            fileObj.progress += Math.random() * 20
          }
        }, 200)
        
        // 模拟API调用
        await new Promise((resolve, reject) => {
          setTimeout(() => {
            clearInterval(progressInterval)
            
            // 模拟90%的成功率
            if (Math.random() < 0.9) {
              fileObj.progress = 100
              fileObj.status = 'success'
              resolve()
            } else {
              fileObj.status = 'error'
              fileObj.progress = 0
              reject(new Error('上传失败'))
            }
          }, 2000 + Math.random() * 3000) // 2-5秒随机延迟
        })
        
      } catch (error) {
        fileObj.status = 'error'
        fileObj.progress = 0
        throw error
      }
    },
    getFileIcon(type) {
      const iconMap = {
        'application/pdf': DocumentTextIcon,
        'application/msword': DocumentTextIcon,
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': DocumentTextIcon,
        'text/plain': DocumentTextIcon,
        'text/markdown': DocumentTextIcon,
        'application/vnd.ms-powerpoint': DocumentTextIcon,
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': DocumentTextIcon,
        'application/vnd.ms-excel': DocumentTextIcon,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': DocumentTextIcon
      }
      
      return iconMap[type] || ArchiveBoxIcon
    },
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },
    showNotification(notification) {
      // 通过全局事件发送通知
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: notification
      }))
    }
  }
}
</script>