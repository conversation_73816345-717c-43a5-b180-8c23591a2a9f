<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
        文档管理
      </h1>
      <Button variant="primary" @click="showUpload = true">
        <CloudArrowUpIcon class="w-4 h-4 mr-2" />
        上传文档
      </Button>
    </div>
    
    <Card>
      <div class="text-center py-12">
        <DocumentTextIcon class="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          文档管理页面
        </h3>
        <p class="text-gray-500 dark:text-gray-400">
          此页面正在开发中...
        </p>
      </div>
    </Card>
  </div>
</template>

<script>
import { DocumentTextIcon, CloudArrowUpIcon } from '@heroicons/vue/24/outline'
import { But<PERSON>, Card } from '@/components/ui'

export default {
  name: 'Documents',
  components: {
    DocumentTextIcon,
    CloudArrowUpIcon,
    Button,
    Card
  },
  data() {
    return {
      showUpload: false
    }
  }
}
</script>