<template>
  <div class="space-y-6">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
      用户管理
    </h1>
    
    <Card>
      <div class="text-center py-12">
        <UserGroupIcon class="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          用户管理页面
        </h3>
        <p class="text-gray-500 dark:text-gray-400">
          此页面正在开发中...
        </p>
      </div>
    </Card>
  </div>
</template>

<script>
import { UserGroupIcon } from '@heroicons/vue/24/outline'
import { Card } from '@/components/ui'

export default {
  name: 'UserManagement',
  components: {
    UserGroupIcon,
    Card
  }
}
</script>