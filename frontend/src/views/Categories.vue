<template>
  <div class="space-y-6">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
      页面开发中
    </h1>
    
    <Card>
      <div class="text-center py-12">
        <Cog6ToothIcon class="w-16 h-16 mx-auto text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          ${file}页面
        </h3>
        <p class="text-gray-500 dark:text-gray-400">
          此页面正在开发中...
        </p>
      </div>
    </Card>
  </div>
</template>

<script>
import { Cog6ToothIcon } from '@heroicons/vue/24/outline'
import { Card } from '@/components/ui'

export default {
  name: '${file}',
  components: {
    Cog6ToothIcon,
    Card
  }
}
</script>
