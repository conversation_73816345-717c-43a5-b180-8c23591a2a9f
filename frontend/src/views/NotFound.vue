<template>
  <div class="min-h-screen flex items-center justify-center bg-light-secondary dark:bg-dark-primary px-4">
    <div class="text-center max-w-md">
      <!-- 404 图标 -->
      <div class="mb-8">
        <div class="inline-flex items-center justify-center w-32 h-32 bg-gradient-primary rounded-full mb-6">
          <span class="text-6xl font-bold text-white">404</span>
        </div>
      </div>
      
      <!-- 标题和描述 -->
      <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
        页面未找到
      </h1>
      <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
        抱歉，您访问的页面不存在或已被移动。
      </p>
      
      <!-- 操作按钮 -->
      <div class="space-y-4">
        <Button
          variant="primary"
          size="lg"
          @click="$router.push('/dashboard')"
          class="w-full sm:w-auto"
        >
          <HomeIcon class="w-5 h-5 mr-2" />
          返回首页
        </Button>
        
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            variant="secondary"
            @click="$router.go(-1)"
          >
            <ArrowLeftIcon class="w-4 h-4 mr-2" />
            返回上页
          </Button>
          
          <Button
            variant="secondary"
            @click="$router.push('/search')"
          >
            <MagnifyingGlassIcon class="w-4 h-4 mr-2" />
            搜索内容
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  HomeIcon,
  ArrowLeftIcon,
  MagnifyingGlassIcon
} from '@heroicons/vue/24/outline'
import { Button } from '@/components/ui'

export default {
  name: 'NotFound',
  components: {
    HomeIcon,
    ArrowLeftIcon,
    MagnifyingGlassIcon,
    Button
  }
}
</script>