<template>
  <div class="min-h-screen flex">
    <!-- 左侧背景图区域 -->
    <div class="hidden lg:flex lg:w-1/2 bg-gradient-primary relative overflow-hidden">
      <div class="absolute inset-0 bg-black/20"></div>
      <div class="relative z-10 flex flex-col justify-center px-12 text-white">
        <div class="max-w-md">
          <h1 class="text-4xl font-bold mb-6">
            智慧知识库
          </h1>
          <p class="text-xl opacity-90 mb-8">
            AI驱动的企业级知识管理系统，让知识的获取和分享更加智能高效
          </p>
          <div class="space-y-4">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                <DocumentTextIcon class="w-5 h-5" />
              </div>
              <span>智能文档解析与索引</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                <ChatBubbleLeftIcon class="w-5 h-5" />
              </div>
              <span>AI对话式知识问答</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                <MagnifyingGlassIcon class="w-5 h-5" />
              </div>
              <span>混合搜索引擎</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                <ShieldCheckIcon class="w-5 h-5" />
              </div>
              <span>企业级权限管理</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 装饰性几何图形 -->
      <div class="absolute top-10 right-10 w-32 h-32 bg-white/10 rounded-full"></div>
      <div class="absolute bottom-20 right-20 w-20 h-20 bg-white/10 rounded-xl rotate-45"></div>
      <div class="absolute top-1/2 right-32 w-16 h-16 bg-white/10 rounded-lg rotate-12"></div>
    </div>
    
    <!-- 右侧登录表单区域 -->
    <div class="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 bg-light-primary dark:bg-dark-primary">
      <div class="mx-auto w-full max-w-md">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
          <div class="mx-auto w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mb-4">
            <span class="text-2xl font-bold text-white">智</span>
          </div>
          <h2 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
            欢迎回来
          </h2>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            请登录您的账户以继续使用
          </p>
        </div>
        
        <!-- 登录表单 -->
        <div class="card">
          <form @submit.prevent="handleLogin" class="space-y-6">
            <!-- 用户名输入框 -->
            <div>
              <Input
                v-model="loginForm.username"
                label="用户名或邮箱"
                type="text"
                required
                :prefix-icon="UserIcon"
                placeholder="请输入用户名或邮箱"
                :error="errors.username"
                @input="clearError('username')"
              />
            </div>
            
            <!-- 密码输入框 -->
            <div>
              <Input
                v-model="loginForm.password"
                label="密码"
                :type="showPassword ? 'text' : 'password'"
                required
                :prefix-icon="LockClosedIcon"
                placeholder="请输入密码"
                :error="errors.password"
                @input="clearError('password')"
              >
                <template #suffix>
                  <button
                    type="button"
                    @click="showPassword = !showPassword"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  >
                    <EyeIcon v-if="!showPassword" class="w-5 h-5" />
                    <EyeSlashIcon v-else class="w-5 h-5" />
                  </button>
                </template>
              </Input>
            </div>
            
            <!-- 记住我和忘记密码 -->
            <div class="flex items-center justify-between">
              <label class="flex items-center">
                <input
                  v-model="loginForm.remember"
                  type="checkbox"
                  class="rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500 focus:ring-offset-0"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  记住我
                </span>
              </label>
              
              <button
                type="button"
                @click="showForgotPassword = true"
                class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 transition-colors"
              >
                忘记密码？
              </button>
            </div>
            
            <!-- 登录按钮 -->
            <div>
              <Button
                type="submit"
                variant="primary"
                class="w-full"
                size="lg"
                :loading="logging"
                :disabled="!isFormValid"
              >
                登录
              </Button>
            </div>
            
            <!-- 分割线 -->
            <div class="relative my-6">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-200 dark:border-gray-700"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                  或者
                </span>
              </div>
            </div>
            
            <!-- 第三方登录 -->
            <div class="space-y-3">
              <button
                type="button"
                @click="handleOAuthLogin('github')"
                class="w-full btn btn-secondary justify-center"
                :disabled="logging"
              >
                <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                使用 GitHub 登录
              </button>
              
              <button
                type="button"
                @click="handleOAuthLogin('google')"
                class="w-full btn btn-secondary justify-center"
                :disabled="logging"
              >
                <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                使用 Google 登录
              </button>
            </div>
          </form>
        </div>
        
        <!-- 注册链接 -->
        <p class="mt-6 text-center text-sm text-gray-600 dark:text-gray-400">
          还没有账户？
          <button
            @click="showRegister = true"
            class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium transition-colors"
          >
            立即注册
          </button>
        </p>
      </div>
    </div>
    
    <!-- 忘记密码模态框 -->
    <Modal
      v-model="showForgotPassword"
      title="重置密码"
      size="md"
    >
      <div class="space-y-4">
        <p class="text-gray-600 dark:text-gray-400">
          请输入您的邮箱地址，我们将向您发送重置密码的链接。
        </p>
        <Input
          v-model="resetEmail"
          label="邮箱地址"
          type="email"
          placeholder="请输入注册邮箱"
        />
      </div>
      
      <template #footer>
        <Button
          variant="secondary"
          @click="showForgotPassword = false"
        >
          取消
        </Button>
        <Button
          variant="primary"
          @click="handlePasswordReset"
          :loading="sendingReset"
        >
          发送重置链接
        </Button>
      </template>
    </Modal>
  </div>
</template>

<script>
import {
  UserIcon,
  LockClosedIcon,
  EyeIcon,
  EyeSlashIcon,
  DocumentTextIcon,
  ChatBubbleLeftIcon,
  MagnifyingGlassIcon,
  ShieldCheckIcon
} from '@heroicons/vue/24/outline'
import { Button, Input, Modal } from '@/components/ui'

export default {
  name: 'Login',
  components: {
    UserIcon,
    LockClosedIcon,
    EyeIcon,
    EyeSlashIcon,
    DocumentTextIcon,
    ChatBubbleLeftIcon,
    MagnifyingGlassIcon,
    ShieldCheckIcon,
    Button,
    Input,
    Modal
  },
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
        remember: false
      },
      errors: {},
      logging: false,
      showPassword: false,
      showForgotPassword: false,
      showRegister: false,
      resetEmail: '',
      sendingReset: false
    }
  },
  computed: {
    isFormValid() {
      return this.loginForm.username.trim() && this.loginForm.password.trim()
    }
  },
  methods: {
    async handleLogin() {
      if (!this.isFormValid) return
      
      this.logging = true
      this.errors = {}
      
      try {
        const response = await this.loginUser({
          username: this.loginForm.username.trim(),
          password: this.loginForm.password,
          remember: this.loginForm.remember
        })
        
        // 存储认证信息
        localStorage.setItem('auth_token', response.token)
        localStorage.setItem('user_data', JSON.stringify({
          ...response.user,
          tokenExpiry: response.expires_at
        }))
        
        // 显示成功消息
        this.showNotification({
          type: 'success',
          title: '登录成功',
          message: `欢迎回来，${response.user.name}！`
        })
        
        // 重定向到仪表板
        const redirectTo = this.$route.query.redirect || '/dashboard'
        this.$router.push(redirectTo)
        
      } catch (error) {
        if (error.status === 401) {
          this.errors = {
            password: '用户名或密码错误'
          }
        } else if (error.status === 422) {
          this.errors = error.errors || {}
        } else {
          this.showNotification({
            type: 'error',
            title: '登录失败',
            message: error.message || '登录时发生未知错误'
          })
        }
      } finally {
        this.logging = false
      }
    },
    async handleOAuthLogin(provider) {
      this.logging = true
      
      try {
        // 重定向到OAuth认证页面
        window.location.href = `/api/v1/auth/${provider}`
      } catch (error) {
        this.showNotification({
          type: 'error',
          title: '登录失败',
          message: `${provider} 登录失败`
        })
      } finally {
        this.logging = false
      }
    },
    async handlePasswordReset() {
      if (!this.resetEmail) {
        this.showNotification({
          type: 'warning',
          title: '请输入邮箱',
          message: '请输入您的注册邮箱地址'
        })
        return
      }
      
      this.sendingReset = true
      
      try {
        await this.resetPassword(this.resetEmail)
        
        this.showNotification({
          type: 'success',
          title: '重置链接已发送',
          message: '请查看您的邮箱并点击重置链接'
        })
        
        this.showForgotPassword = false
        this.resetEmail = ''
        
      } catch (error) {
        this.showNotification({
          type: 'error',
          title: '发送失败',
          message: error.message || '发送重置链接时发生错误'
        })
      } finally {
        this.sendingReset = false
      }
    },
    clearError(field) {
      if (this.errors[field]) {
        delete this.errors[field]
      }
    },
    showNotification(notification) {
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: notification
      }))
    },
    // 模拟API调用
    async loginUser(credentials) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          // 模拟登录验证
          if (credentials.username === 'admin' && credentials.password === 'admin123') {
            resolve({
              token: 'mock_jwt_token_' + Date.now(),
              user: {
                id: 1,
                name: '管理员',
                email: '<EMAIL>',
                role: 'admin'
              },
              expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
            })
          } else if (credentials.username === 'user' && credentials.password === 'user123') {
            resolve({
              token: 'mock_jwt_token_' + Date.now(),
              user: {
                id: 2,
                name: '普通用户',
                email: '<EMAIL>',
                role: 'user'
              },
              expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
            })
          } else {
            reject({
              status: 401,
              message: '用户名或密码错误'
            })
          }
        }, 1500)
      })
    },
    async resetPassword(email) {
      return new Promise((resolve) => {
        setTimeout(resolve, 1000)
      })
    }
  },
  mounted() {
    // 如果已经登录，重定向到仪表板
    const token = localStorage.getItem('auth_token')
    if (token) {
      this.$router.replace('/dashboard')
    }
  }
}
</script>