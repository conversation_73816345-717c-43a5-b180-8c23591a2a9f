<template>
  <div class="space-y-6">
    <!-- 页面标题和快速操作 -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
          {{ welcomeMessage }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          {{ currentDateText }}
        </p>
      </div>
      
      <div class="flex items-center space-x-3">
        <Button
          variant="secondary"
          @click="refreshData"
          :loading="refreshing"
          size="sm"
        >
          <ArrowPathIcon class="w-4 h-4 mr-2" />
          刷新数据
        </Button>
        <Button
          variant="primary"
          @click="$router.push('/documents/new')"
          size="sm"
        >
          <PlusIcon class="w-4 h-4 mr-2" />
          新建文档
        </Button>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div
        v-for="stat in statistics"
        :key="stat.name"
        class="card hover-lift cursor-pointer"
        @click="handleStatClick(stat)"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-1">
              {{ stat.name }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ stat.value }}
            </p>
            <div class="flex items-center mt-2">
              <component
                :is="stat.trend === 'up' ? ArrowTrendingUpIcon : ArrowTrendingDownIcon"
                :class="[
                  'w-4 h-4 mr-1',
                  stat.trend === 'up' ? 'text-success' : 'text-error'
                ]"
              />
              <span
                :class="[
                  'text-sm',
                  stat.trend === 'up' ? 'text-success' : 'text-error'
                ]"
              >
                {{ stat.change }}
              </span>
              <span class="text-sm text-gray-500 dark:text-gray-400 ml-1">
                vs 上周
              </span>
            </div>
          </div>
          <div :class="[
            'p-3 rounded-xl',
            stat.bgColor
          ]">
            <component
              :is="stat.icon"
              :class="[
                'w-6 h-6',
                stat.iconColor
              ]"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 数据可视化图表 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 文档上传趋势图 -->
        <Card title="文档上传趋势" padding="md">
          <div class="h-80">
            <canvas ref="uploadChart"></canvas>
          </div>
          <div class="mt-4 flex items-center justify-between text-sm">
            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-primary-500 rounded-full mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">本月上传</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-success rounded-full mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">处理完成</span>
              </div>
            </div>
            <div class="text-gray-500 dark:text-gray-400">
              最近30天数据
            </div>
          </div>
        </Card>
        
        <!-- 知识库活跃度 -->
        <Card title="知识库活跃度" padding="md">
          <div class="space-y-3">
            <div
              v-for="kb in activeKnowledgeBases"
              :key="kb.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer"
              @click="$router.push(`/knowledge-base/${kb.id}`)"
            >
              <div class="flex items-center space-x-3">
                <div :class="[
                  'w-10 h-10 rounded-lg flex items-center justify-center',
                  kb.bgColor
                ]">
                  <BookOpenIcon :class="['w-5 h-5', kb.iconColor]" />
                </div>
                <div>
                  <p class="font-medium text-gray-900 dark:text-gray-100">
                    {{ kb.name }}
                  </p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ kb.documentCount }} 个文档 • {{ kb.accessCount }} 次访问
                  </p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ kb.activityScore }}%
                </p>
                <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                  <div
                    class="bg-primary-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${kb.activityScore}%` }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
      
      <!-- 右侧面板 -->
      <div class="space-y-6">
        <!-- 快速操作 -->
        <Card title="快速操作" padding="md">
          <div class="space-y-3">
            <button
              v-for="action in quickActions"
              :key="action.name"
              @click="handleQuickAction(action)"
              class="w-full flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-all hover:shadow-md group"
            >
              <div :class="[
                'w-10 h-10 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform',
                action.bgColor
              ]">
                <component
                  :is="action.icon"
                  :class="['w-5 h-5', action.iconColor]"
                />
              </div>
              <div class="flex-1 text-left">
                <p class="font-medium text-gray-900 dark:text-gray-100">
                  {{ action.name }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ action.description }}
                </p>
              </div>
              <ChevronRightIcon class="w-5 h-5 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300" />
            </button>
          </div>
        </Card>
        
        <!-- 最近文档 -->
        <Card title="最近文档" padding="md">
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                最近文档
              </h3>
              <router-link
                to="/documents"
                class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300"
              >
                查看全部
              </router-link>
            </div>
          </template>
          
          <div class="space-y-3">
            <div
              v-for="doc in recentDocuments"
              :key="doc.id"
              class="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
              @click="$router.push(`/documents/${doc.id}`)"
            >
              <div class="flex-shrink-0 mt-0.5">
                <div :class="[
                  'w-8 h-8 rounded-lg flex items-center justify-center',
                  getDocumentStatusColor(doc.status).bg
                ]">
                  <component
                    :is="getDocumentIcon(doc.type)"
                    :class="['w-4 h-4', getDocumentStatusColor(doc.status).icon]"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {{ doc.title }}
                </p>
                <div class="flex items-center mt-1 space-x-2">
                  <span :class="[
                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                    getDocumentStatusColor(doc.status).badge
                  ]">
                    {{ getDocumentStatusText(doc.status) }}
                  </span>
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {{ formatRelativeTime(doc.updatedAt) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div
            v-if="recentDocuments.length === 0"
            class="text-center py-8 text-gray-500 dark:text-gray-400"
          >
            <DocumentTextIcon class="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>还没有文档</p>
            <Button
              variant="primary"
              size="sm"
              class="mt-3"
              @click="$router.push('/documents/new')"
            >
              创建第一个文档
            </Button>
          </div>
        </Card>
        
        <!-- 系统状态 -->
        <Card title="系统状态" padding="md">
          <div class="space-y-4">
            <div
              v-for="service in systemStatus"
              :key="service.name"
              class="flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <div :class="[
                  'w-2 h-2 rounded-full',
                  service.status === 'healthy' ? 'bg-success' :
                  service.status === 'warning' ? 'bg-warning' : 'bg-error'
                ]"></div>
                <span class="text-sm text-gray-900 dark:text-gray-100">
                  {{ service.name }}
                </span>
              </div>
              <span :class="[
                'text-xs px-2 py-1 rounded-full',
                service.status === 'healthy' ? 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100' :
                service.status === 'warning' ? 'bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-100' :
                'bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-100'
              ]">
                {{ service.statusText }}
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ArrowPathIcon,
  PlusIcon,
  DocumentTextIcon,
  ChatBubbleLeftIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  BookOpenIcon,
  ChevronRightIcon,
  CloudArrowUpIcon,
  MagnifyingGlassIcon,
  Cog6ToothIcon
} from '@heroicons/vue/24/outline'
import { Button, Card } from '@/components/ui'
import Chart from 'chart.js/auto'

export default {
  name: 'Dashboard',
  components: {
    ArrowPathIcon,
    PlusIcon,
    DocumentTextIcon,
    ChatBubbleLeftIcon,
    UserGroupIcon,
    ClipboardDocumentListIcon,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    BookOpenIcon,
    ChevronRightIcon,
    CloudArrowUpIcon,
    MagnifyingGlassIcon,
    Cog6ToothIcon,
    Button,
    Card
  },
  data() {
    return {
      refreshing: false,
      uploadChart: null,
      statistics: [
        {
          name: '总文档数',
          value: '1,234',
          change: '+12%',
          trend: 'up',
          icon: DocumentTextIcon,
          bgColor: 'bg-blue-100 dark:bg-blue-800',
          iconColor: 'text-blue-600 dark:text-blue-300',
          route: '/documents'
        },
        {
          name: '知识库数',
          value: '45',
          change: '+8%',
          trend: 'up',
          icon: BookOpenIcon,
          bgColor: 'bg-green-100 dark:bg-green-800',
          iconColor: 'text-green-600 dark:text-green-300',
          route: '/knowledge-base'
        },
        {
          name: '用户数',
          value: '89',
          change: '+5%',
          trend: 'up',
          icon: UserGroupIcon,
          bgColor: 'bg-purple-100 dark:bg-purple-800',
          iconColor: 'text-purple-600 dark:text-purple-300',
          route: '/users'
        },
        {
          name: '本周对话',
          value: '567',
          change: '-3%',
          trend: 'down',
          icon: ChatBubbleLeftIcon,
          bgColor: 'bg-yellow-100 dark:bg-yellow-800',
          iconColor: 'text-yellow-600 dark:text-yellow-300',
          route: '/chat'
        }
      ],
      quickActions: [
        {
          name: '上传文档',
          description: '批量上传并处理文档',
          icon: CloudArrowUpIcon,
          bgColor: 'bg-primary-100 dark:bg-primary-800',
          iconColor: 'text-primary-600 dark:text-primary-300',
          action: 'upload'
        },
        {
          name: '开始对话',
          description: '与AI助手开始新对话',
          icon: ChatBubbleLeftIcon,
          bgColor: 'bg-green-100 dark:bg-green-800',
          iconColor: 'text-green-600 dark:text-green-300',
          action: 'chat'
        },
        {
          name: '搜索文档',
          description: '快速查找所需文档',
          icon: MagnifyingGlassIcon,
          bgColor: 'bg-purple-100 dark:bg-purple-800',
          iconColor: 'text-purple-600 dark:text-purple-300',
          action: 'search'
        },
        {
          name: '系统设置',
          description: '配置系统参数',
          icon: Cog6ToothIcon,
          bgColor: 'bg-gray-100 dark:bg-gray-800',
          iconColor: 'text-gray-600 dark:text-gray-300',
          action: 'settings'
        }
      ],
      activeKnowledgeBases: [
        {
          id: 1,
          name: '技术文档',
          documentCount: 156,
          accessCount: 1234,
          activityScore: 95,
          bgColor: 'bg-blue-100 dark:bg-blue-800',
          iconColor: 'text-blue-600 dark:text-blue-300'
        },
        {
          id: 2,
          name: '产品手册',
          documentCount: 89,
          accessCount: 567,
          activityScore: 78,
          bgColor: 'bg-green-100 dark:bg-green-800',
          iconColor: 'text-green-600 dark:text-green-300'
        },
        {
          id: 3,
          name: '培训资料',
          documentCount: 234,
          accessCount: 890,
          activityScore: 85,
          bgColor: 'bg-purple-100 dark:bg-purple-800',
          iconColor: 'text-purple-600 dark:text-purple-300'
        },
        {
          id: 4,
          name: '项目文档',
          documentCount: 123,
          accessCount: 456,
          activityScore: 72,
          bgColor: 'bg-yellow-100 dark:bg-yellow-800',
          iconColor: 'text-yellow-600 dark:text-yellow-300'
        }
      ],
      recentDocuments: [
        {
          id: 1,
          title: 'Vue 3 组件开发指南',
          type: 'pdf',
          status: 'processed',
          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
        },
        {
          id: 2,
          title: '产品需求文档 v2.1',
          type: 'doc',
          status: 'processing',
          updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4小时前
        },
        {
          id: 3,
          title: 'API 接口文档',
          type: 'md',
          status: 'processed',
          updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // 1天前
        },
        {
          id: 4,
          title: '用户操作手册',
          type: 'pdf',
          status: 'failed',
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2天前
        }
      ],
      systemStatus: [
        { name: 'API 服务', status: 'healthy', statusText: '正常' },
        { name: '数据库', status: 'healthy', statusText: '正常' },
        { name: '向量数据库', status: 'healthy', statusText: '正常' },
        { name: '文档解析', status: 'warning', statusText: '负载高' },
        { name: 'AI 服务', status: 'healthy', statusText: '正常' }
      ]
    }
  },
  computed: {
    welcomeMessage() {
      const hour = new Date().getHours()
      let greeting = '早上好'
      
      if (hour >= 12 && hour < 18) {
        greeting = '下午好'
      } else if (hour >= 18) {
        greeting = '晚上好'
      }
      
      return `${greeting}！欢迎回来`
    },
    currentDateText() {
      const now = new Date()
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      }
      return now.toLocaleDateString('zh-CN', options)
    }
  },
  methods: {
    async refreshData() {
      this.refreshing = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        this.showNotification({
          type: 'success',
          title: '数据已刷新',
          message: '最新数据已加载'
        })
      } catch (error) {
        this.showNotification({
          type: 'error',
          title: '刷新失败',
          message: '无法获取最新数据'
        })
      } finally {
        this.refreshing = false
      }
    },
    handleStatClick(stat) {
      if (stat.route) {
        this.$router.push(stat.route)
      }
    },
    handleQuickAction(action) {
      switch (action.action) {
        case 'upload':
          // 触发文件上传
          window.dispatchEvent(new CustomEvent('show-upload-modal'))
          break
        case 'chat':
          this.$router.push('/chat')
          break
        case 'search':
          this.$router.push('/search')
          break
        case 'settings':
          this.$router.push('/settings')
          break
      }
    },
    getDocumentIcon(type) {
      const icons = {
        pdf: DocumentTextIcon,
        doc: DocumentTextIcon,
        docx: DocumentTextIcon,
        md: DocumentTextIcon,
        txt: DocumentTextIcon
      }
      return icons[type] || DocumentTextIcon
    },
    getDocumentStatusColor(status) {
      const colors = {
        processed: {
          bg: 'bg-green-100 dark:bg-green-800',
          icon: 'text-green-600 dark:text-green-300',
          badge: 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100'
        },
        processing: {
          bg: 'bg-yellow-100 dark:bg-yellow-800',
          icon: 'text-yellow-600 dark:text-yellow-300',
          badge: 'bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-100'
        },
        failed: {
          bg: 'bg-red-100 dark:bg-red-800',
          icon: 'text-red-600 dark:text-red-300',
          badge: 'bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-100'
        }
      }
      return colors[status] || colors.processed
    },
    getDocumentStatusText(status) {
      const texts = {
        processed: '已处理',
        processing: '处理中',
        failed: '失败'
      }
      return texts[status] || '未知'
    },
    formatRelativeTime(date) {
      const now = new Date()
      const diff = now - date
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes} 分钟前`
      if (hours < 24) return `${hours} 小时前`
      if (days < 7) return `${days} 天前`
      
      return date.toLocaleDateString('zh-CN')
    },
    initUploadChart() {
      const ctx = this.$refs.uploadChart.getContext('2d')
      
      // 模拟数据
      const labels = []
      const uploadData = []
      const processedData = []
      
      for (let i = 29; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))
        uploadData.push(Math.floor(Math.random() * 50) + 10)
        processedData.push(Math.floor(Math.random() * 45) + 8)
      }
      
      this.uploadChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels,
          datasets: [
            {
              label: '文档上传',
              data: uploadData,
              borderColor: 'rgb(59, 130, 246)',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              fill: true,
              tension: 0.4
            },
            {
              label: '处理完成',
              data: processedData,
              borderColor: 'rgb(16, 185, 129)',
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              fill: true,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(0, 0, 0, 0.1)'
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          },
          elements: {
            point: {
              radius: 3,
              hoverRadius: 6
            }
          }
        }
      })
    },
    showNotification(notification) {
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: notification
      }))
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initUploadChart()
    })
  },
  beforeUnmount() {
    if (this.uploadChart) {
      this.uploadChart.destroy()
    }
  }
}
</script>