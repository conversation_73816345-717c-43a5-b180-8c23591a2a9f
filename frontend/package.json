{"name": "wisdom-vault-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.27", "vue-router": "^4.3.2", "pinia": "^2.1.7", "axios": "^1.7.2", "@heroicons/vue": "^2.1.3", "@headlessui/vue": "^1.7.20", "chart.js": "^4.4.3", "vue-chartjs": "^5.3.1", "@vueuse/core": "^10.9.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "vite": "^5.2.10", "tailwindcss": "^3.4.3", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "prettier": "^3.2.5", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "@tailwindcss/aspect-ratio": "^0.4.2"}}