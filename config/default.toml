host = "127.0.0.1"
port = 5141

[mongodb]
# MongoDB connection string format: ********************************:port/database
url = "mongodb://localhost:27017/wisdom_vault"
# Connection pool settings
max_pool_size = 10
min_pool_size = 2
max_idle_time_ms = 30000
connect_timeout_ms = 10000
server_selection_timeout_ms = 30000

[redis]
url = "redis://127.0.0.1:6379"
pool_size = 10

[qdrant]
# Qdrant vector database configuration
url = "http://localhost:6334"
# API key for authentication (optional)
api_key = ""
# Connection timeouts
connect_timeout_ms = 10000
request_timeout_ms = 30000
# TLS settings
enable_tls = false

[auth]
secret = "a2b006566e47ff428ddf1cf91d8283ed3de980459f50b0719b3a3db66c0b19a5f348eae3c2da7dca3ecae60659fb31188d8301d38834097762be7bd1fa3a9997"
cookie_name = "WV_SID"
session_expire_seconds = 1800

# 系统管理员初始化配置
[admin]
# 管理员用户名（系统首次启动时创建）
username = "admin"
# 管理员邮箱
email = "<EMAIL>"
# 管理员密码（首次启动后请立即修改）
password = "Aa123..."
# 管理员全名
full_name = "系统管理员"
# 是否强制重新初始化（仅开发环境使用）
force_reinit = false

[ai]
openai_base_url = "https://api.openai.com/v1"
default_model = "gpt-4o-mini"
openai_api_key = ""
max_tokens = 4000
temperature = 0.7
timeout_seconds = 30
max_retries = 3

[cors]
allowed_origins = ["http://localhost:3000", "http://localhost:8080"]
allowed_methods = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
allowed_headers = ["Authorization", "Accept", "Content-Type"]
supports_credentials = true
max_age = 3600

[file_storage]
gridfs_bucket = "file_storage"
gridfs_chunk_size_kb = 255
max_file_size = 104857600 # 100MB in bytes
max_batch_files = 10
allowed_extensions = [
    "pdf",
    "doc",
    "docx",
    "txt",
    "md",
    "html",
    "ppt",
    "pptx",
    "xls",
    "xlsx",
]
cleanup_interval_hours = 24

[tika]
server_url = "http://localhost:9998"
timeout_seconds = 300
max_retries = 3
supported_mime_types = [
    "application/pdf",
    "application/msword",
    "text/plain",
    "text/html",
]

[vectorization]
embedding_model = "paraphrase-multilingual-MiniLM-L12-v2"
batch_size = 32
max_concurrent_tasks = 4
quality_threshold = 0.8
memory_limit_mb = 2048
max_retries = 3
timeout_seconds = 600

[task_processing]
max_concurrent_tasks = 5
retry_interval_seconds = 60
max_processing_time_minutes = 30
queue_size = 1000
cleanup_interval_hours = 6

[search]
max_results = 100
cache_ttl_minutes = 30
keyword_index_path = "./data/keyword_index"
vector_similarity_threshold = 0.7
bm25_k1 = 1.5
bm25_b = 0.75

[logging]
level = "info"
file_path = "logs/wisdom_vault.log"
max_file_size_mb = 100
max_files = 5
console_output = true
