use anyhow::Result;
use std::sync::Arc;
use wisdom_vault_common::{db::next_id, time::current_millis};
use wisdom_vault_database::{
    models::{Document, DocumentVersion},
    repositories::{DocumentRepository, DocumentVersionRepository},
};

/// 文档版本服务
/// 提供文档版本控制、历史记录管理和版本对比功能
pub struct DocumentVersionService {
    document_repo: Arc<dyn DocumentRepository + Send + Sync>,
    version_repo: Arc<dyn DocumentVersionRepository + Send + Sync>,
    config: VersionConfig,
}

impl DocumentVersionService {
    fn new(
        document_repo: Arc<dyn DocumentRepository + Send + Sync>,
        version_repo: Arc<dyn DocumentVersionRepository + Send + Sync>,
        config: VersionConfig,
    ) -> Self {
        Self {
            document_repo,
            version_repo,
            config,
        }
    }

    /// 创建文档版本
    pub async fn create_version(
        &self,
        document_id: String,
        changes: Vec<String>,
        created_by: String,
    ) -> Result<DocumentVersion> {
        // 获取当前文档
        let document = self
            .document_repo
            .find_by_id(&document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Document not found"))?;

        // 获取最新版本号
        let next_version = self
            .version_repo
            .get_next_version_number(&document_id)
            .await?;

        // 创建版本记录
        let version = DocumentVersion {
            id: next_id(),
            document_id: document_id.clone(),
            version_number: next_version,
            content: document.content.clone(),
            summary: document.summary.clone(),
            changes,
            created_by,
            created_at: current_millis(),
        };

        // 保存版本记录
        let created_version = self.version_repo.create(&version).await?;

        tracing::info!(
            "Created version {} for document {}",
            created_version.version_number,
            document_id
        );

        Ok(created_version)
    }

    /// 获取文档的所有版本
    pub async fn get_document_versions(&self, document_id: &str) -> Result<Vec<DocumentVersion>> {
        self.version_repo
            .find_by_document(document_id, None, None)
            .await
    }

    /// 获取特定版本的文档
    pub async fn get_document_version(
        &self,
        document_id: &str,
        version_number: i32,
    ) -> Result<Option<DocumentVersion>> {
        self.version_repo
            .find_by_version_number(document_id, version_number)
            .await
    }

    /// 恢复文档到指定版本
    pub async fn restore_to_version(
        &self,
        document_id: String,
        version_number: i32,
        restored_by: String,
    ) -> Result<Document> {
        // 获取指定版本
        let version = self
            .get_document_version(&document_id, version_number)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Version {} not found", version_number))?;

        // 获取当前文档
        let mut document = self
            .document_repo
            .find_by_id(&document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Document not found"))?;

        // 创建恢复前的版本记录
        let restore_changes = vec![format!("Restored to version {}", version_number)];
        self.create_version(document_id.clone(), restore_changes, restored_by)
            .await?;

        // 恢复内容
        document.content = version.content;
        document.summary = version.summary;
        document.updated_at = current_millis();

        // 保存更新后的文档
        let updated_document = self.document_repo.update(&document).await?;

        tracing::info!(
            "Restored document {} to version {}",
            document_id,
            version_number
        );

        Ok(updated_document)
    }

    /// 比较两个版本的差异
    pub async fn compare_versions(
        &self,
        document_id: &str,
        version1: i32,
        version2: i32,
    ) -> Result<VersionDiff> {
        let v1 = self
            .get_document_version(document_id, version1)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Version {} not found", version1))?;

        let v2 = self
            .get_document_version(document_id, version2)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Version {} not found", version2))?;

        let diff = self.calculate_diff(&v1.content, &v2.content);

        Ok(VersionDiff {
            document_id: document_id.to_owned(),
            version1,
            version2,
            content_diff: diff,
            summary_changed: v1.summary != v2.summary,
            summary_diff: if v1.summary != v2.summary {
                Some(VersionSummaryDiff {
                    old_summary: v1.summary,
                    new_summary: v2.summary,
                })
            } else {
                None
            },
            changes1: v1.changes,
            changes2: v2.changes,
        })
    }

    /// 获取文档版本统计
    pub async fn get_version_statistics(&self, document_id: &str) -> Result<VersionStatistics> {
        let versions = self.get_document_versions(document_id).await?;

        if versions.is_empty() {
            return Ok(VersionStatistics {
                total_versions: 0,
                latest_version: 0,
                total_changes: 0,
                first_created_at: None,
                last_updated_at: None,
                contributors: Vec::new(),
            });
        }

        let total_versions = versions.len() as i32;
        let latest_version = versions.iter().map(|v| v.version_number).max().unwrap_or(0);
        let total_changes = versions.iter().map(|v| v.changes.len()).sum::<usize>() as i32;

        let first_created_at = versions
            .iter()
            .min_by_key(|v| v.created_at)
            .map(|v| v.created_at);

        let last_updated_at = versions
            .iter()
            .max_by_key(|v| v.created_at)
            .map(|v| v.created_at);

        let mut contributors: Vec<String> = versions.iter().map(|v| v.created_by.clone()).collect();
        contributors.sort();
        contributors.dedup();

        Ok(VersionStatistics {
            total_versions,
            latest_version,
            total_changes,
            first_created_at,
            last_updated_at,
            contributors,
        })
    }

    /// 删除旧版本（保留指定数量的最新版本）
    pub async fn cleanup_old_versions(&self, document_id: &str, keep_latest: i32) -> Result<u32> {
        let deleted_count = self
            .version_repo
            .delete_old_versions(document_id, keep_latest)
            .await?;

        tracing::info!(
            "Cleaned up {} old versions for document {}",
            deleted_count,
            document_id
        );

        Ok(deleted_count as u32)
    }

    /// 计算文本差异
    fn calculate_diff(&self, old_content: &str, new_content: &str) -> Vec<DiffLine> {
        let old_lines: Vec<&str> = old_content.lines().collect();
        let new_lines: Vec<&str> = new_content.lines().collect();

        let mut diff = Vec::new();
        let mut old_idx = 0;
        let mut new_idx = 0;

        // 简单的行级差异算法
        while old_idx < old_lines.len() || new_idx < new_lines.len() {
            if old_idx >= old_lines.len() {
                // 只剩新内容
                diff.push(DiffLine {
                    line_type: DiffLineType::Added,
                    old_line_number: None,
                    new_line_number: Some(new_idx + 1),
                    content: new_lines[new_idx].to_string(),
                });
                new_idx += 1;
            } else if new_idx >= new_lines.len() {
                // 只剩旧内容
                diff.push(DiffLine {
                    line_type: DiffLineType::Removed,
                    old_line_number: Some(old_idx + 1),
                    new_line_number: None,
                    content: old_lines[old_idx].to_string(),
                });
                old_idx += 1;
            } else if old_lines[old_idx] == new_lines[new_idx] {
                // 内容相同
                diff.push(DiffLine {
                    line_type: DiffLineType::Unchanged,
                    old_line_number: Some(old_idx + 1),
                    new_line_number: Some(new_idx + 1),
                    content: old_lines[old_idx].to_string(),
                });
                old_idx += 1;
                new_idx += 1;
            } else {
                // 内容不同，简单处理为删除旧行，添加新行
                diff.push(DiffLine {
                    line_type: DiffLineType::Removed,
                    old_line_number: Some(old_idx + 1),
                    new_line_number: None,
                    content: old_lines[old_idx].to_string(),
                });
                diff.push(DiffLine {
                    line_type: DiffLineType::Added,
                    old_line_number: None,
                    new_line_number: Some(new_idx + 1),
                    content: new_lines[new_idx].to_string(),
                });
                old_idx += 1;
                new_idx += 1;
            }
        }

        diff
    }
}

/// 版本差异信息
#[derive(Debug, Clone)]
pub struct VersionDiff {
    pub document_id: String,
    pub version1: i32,
    pub version2: i32,
    pub content_diff: Vec<DiffLine>,
    pub summary_changed: bool,
    pub summary_diff: Option<VersionSummaryDiff>,
    pub changes1: Vec<String>,
    pub changes2: Vec<String>,
}

/// 摘要差异信息
#[derive(Debug, Clone)]
pub struct VersionSummaryDiff {
    pub old_summary: Option<String>,
    pub new_summary: Option<String>,
}

/// 差异行信息
#[derive(Debug, Clone)]
pub struct DiffLine {
    pub line_type: DiffLineType,
    pub old_line_number: Option<usize>,
    pub new_line_number: Option<usize>,
    pub content: String,
}

/// 差异行类型
#[derive(Debug, Clone, PartialEq)]
pub enum DiffLineType {
    Unchanged, // 未变化
    Added,     // 新增
    Removed,   // 删除
    Modified,  // 修改
}

/// 版本统计信息
#[derive(Debug, Clone)]
pub struct VersionStatistics {
    pub total_versions: i32,
    pub latest_version: i32,
    pub total_changes: i32,
    pub first_created_at: Option<i64>,
    pub last_updated_at: Option<i64>,
    pub contributors: Vec<String>,
}

/// 版本管理配置
#[derive(Debug, Clone)]
pub struct VersionConfig {
    pub auto_version_on_update: bool,   // 更新时自动创建版本
    pub max_versions_per_document: i32, // 每个文档最大版本数
    pub version_retention_days: i32,    // 版本保留天数
    pub compress_old_versions: bool,    // 压缩旧版本
    pub enable_diff_compression: bool,  // 启用差异压缩存储
}

impl Default for VersionConfig {
    fn default() -> Self {
        Self {
            auto_version_on_update: true,
            max_versions_per_document: 50,
            version_retention_days: 365,
            compress_old_versions: true,
            enable_diff_compression: true,
        }
    }
}

impl DocumentVersionService {
    /// 使用配置创建服务
    pub fn with_config(
        document_repo: Arc<dyn DocumentRepository + Send + Sync>,
        version_repo: Arc<dyn DocumentVersionRepository + Send + Sync>,
        config: VersionConfig,
    ) -> Self {
        Self::new(document_repo, version_repo, config)
    }

    /// 自动创建版本（如果启用了自动版本控制）
    pub async fn auto_create_version_if_enabled(
        &self,
        document_id: String,
        changes: Vec<String>,
        updated_by: String,
        _config: &VersionConfig,
    ) -> Result<Option<DocumentVersion>> {
        // TODO 检查配置决定是否自动创建版本
        if self.config.auto_version_on_update {
            // let version = self
            //     .create_version(document_id, changes, updated_by)
            //     .await?;
            // return Ok(Some(version));
        }

        let version = self
            .create_version(document_id, changes, updated_by)
            .await?;
        Ok(Some(version))
    }

    /// 根据保留策略清理版本
    pub async fn cleanup_versions_by_policy(
        &self,
        document_id: &str,
        config: &VersionConfig,
    ) -> Result<u32> {
        let mut total_deleted = 0;

        // 按数量限制清理
        if config.max_versions_per_document > 0 {
            let deleted = self
                .cleanup_old_versions(document_id, config.max_versions_per_document)
                .await?;
            total_deleted += deleted;
        }

        // TODO: 按时间限制清理
        if config.version_retention_days > 0 {
            //     let cutoff_date = Utc::now() - chrono::Duration::days(config.version_retention_days
            // as i64);     // 实现按日期清理逻辑
        }

        Ok(total_deleted)
    }

    /// 获取版本大小估算
    pub async fn estimate_version_storage_size(
        &self,
        document_id: &str,
    ) -> Result<VersionStorageInfo> {
        let versions = self.get_document_versions(document_id).await?;

        let total_versions = versions.len() as i32;
        let total_content_size = versions.iter().map(|v| v.content.len() as u64).sum();

        // 估算压缩后的大小（假设50%压缩率）
        let estimated_compressed_size = (total_content_size as f64 * 0.5) as u64;

        Ok(VersionStorageInfo {
            total_versions,
            total_content_size,
            estimated_compressed_size,
            average_version_size: if total_versions > 0 {
                total_content_size / total_versions as u64
            } else {
                0
            },
        })
    }
}

/// 版本存储信息
#[derive(Debug, Clone)]
pub struct VersionStorageInfo {
    pub total_versions: i32,
    pub total_content_size: u64,
    pub estimated_compressed_size: u64,
    pub average_version_size: u64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_calculate_diff() {
        let service = DocumentVersionService::new(
            Arc::new(MockDocumentRepository),
            Arc::new(MockDocumentVersionRepository),
        );

        let old_content = "line1\nline2\nline3";
        let new_content = "line1\nline2 modified\nline3\nline4";

        let diff = service.calculate_diff(old_content, new_content);

        assert_eq!(diff.len(), 4);
        assert_eq!(diff[0].line_type, DiffLineType::Unchanged);
        assert_eq!(diff[1].line_type, DiffLineType::Removed);
        assert_eq!(diff[2].line_type, DiffLineType::Added);
        assert_eq!(diff[3].line_type, DiffLineType::Unchanged);
    }

    #[test]
    fn test_version_config_default() {
        let config = VersionConfig::default();
        assert!(config.auto_version_on_update);
        assert_eq!(config.max_versions_per_document, 50);
        assert_eq!(config.version_retention_days, 365);
    }

    // Mock implementation for testing
    struct MockDocumentRepository;
    struct MockDocumentVersionRepository;

    #[async_trait::async_trait]
    impl DocumentRepository for MockDocumentRepository {
        async fn create(&self, _document: &Document) -> Result<Document> {
            unimplemented!()
        }
        async fn find_by_id(&self, _id: &str) -> Result<Option<Document>> {
            unimplemented!()
        }
        async fn find_by_knowledge_base(&self, _kb_id: &str) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn update(&self, _document: &Document) -> Result<Document> {
            unimplemented!()
        }
        async fn delete(&self, _id: &str) -> Result<bool> {
            unimplemented!()
        }
        async fn search(&self, _query: &str, _kb_id: Option<&str>) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_status(
            &self,
            _status: wisdom_vault_database::models::DocumentStatus,
            _kb_id: Option<&str>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_author(
            &self,
            _author: &str,
            _kb_id: Option<&str>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_file_type(
            &self,
            _file_type: &str,
            _kb_id: Option<&str>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn list_with_pagination(
            &self,
            _kb_id: Option<&str>,
            _limit: u32,
            _offset: u32,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn count(&self, _kb_id: Option<&str>) -> Result<i64> {
            unimplemented!()
        }
        async fn count_by_status(
            &self,
            _status: wisdom_vault_database::models::DocumentStatus,
            _kb_id: Option<&str>,
        ) -> Result<i64> {
            unimplemented!()
        }
        async fn update_status(
            &self,
            _id: &str,
            _status: wisdom_vault_database::models::DocumentStatus,
        ) -> Result<bool> {
            unimplemented!()
        }
        async fn update_processing_metadata(
            &self,
            _id: &str,
            _metadata: &wisdom_vault_database::models::DocumentProcessingMetadata,
        ) -> Result<bool> {
            unimplemented!()
        }
        async fn find_by_checksum(&self, _checksum: &str) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn batch_update_status(
            &self,
            _ids: Vec<String>,
            _status: wisdom_vault_database::models::DocumentStatus,
        ) -> Result<u64> {
            unimplemented!()
        }
        async fn find_failed_documents(&self, _kb_id: Option<&str>) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_processing_documents(&self, _kb_id: Option<&str>) -> Result<Vec<Document>> {
            unimplemented!()
        }
    }

    #[async_trait::async_trait]
    impl DocumentVersionRepository for MockDocumentVersionRepository {
        async fn create(&self, _version: &DocumentVersion) -> Result<DocumentVersion> {
            unimplemented!()
        }
        async fn find_by_id(&self, _id: &str) -> Result<Option<DocumentVersion>> {
            unimplemented!()
        }
        async fn find_by_document(
            &self,
            _document_id: &str,
            _limit: Option<u32>,
            _offset: Option<u32>,
        ) -> Result<Vec<DocumentVersion>> {
            unimplemented!()
        }
        async fn find_latest_by_document(
            &self,
            _document_id: &str,
        ) -> Result<Option<DocumentVersion>> {
            unimplemented!()
        }
        async fn find_by_version_number(
            &self,
            _document_id: &str,
            _version_number: i32,
        ) -> Result<Option<DocumentVersion>> {
            unimplemented!()
        }
        async fn delete(&self, _id: &str) -> Result<bool> {
            unimplemented!()
        }
        async fn delete_by_document(&self, _document_id: &str) -> Result<u64> {
            unimplemented!()
        }
        async fn delete_old_versions(&self, _document_id: &str, _keep_latest: i32) -> Result<u64> {
            unimplemented!()
        }
        async fn count_by_document(&self, _document_id: &str) -> Result<i64> {
            unimplemented!()
        }
        async fn get_next_version_number(&self, _document_id: &str) -> Result<i32> {
            unimplemented!()
        }
    }
}
