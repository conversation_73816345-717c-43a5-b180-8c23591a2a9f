use anyhow::Result;
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use wisdom_vault_common::{db::next_id, time::current_millis};

use wisdom_vault_database::{
    models::{
        BatchConfig, Document, DocumentChunk, DocumentEmbedding, EmbeddingBatch, EmbeddingModel,
        EmbeddingTask, EmbeddingTaskMetadata, EmbeddingTaskStatus, EmbeddingTaskType, TaskPriority,
        VectorQualityMetrics,
    },
    repositories::{DocumentChunkRepository, DocumentRepository},
    VectorRepository, VectorPoint, VectorFilter, FilterCondition,
};

use crate::services::{
    embedding_model_manager::EmbeddingModelManager,
    text_vectorization_pipeline::TextVectorizationPipeline,
    vector_quality_assessment::{VectorQualityAssessment, VectorQualityConfig},
};

/// 向量化服务配置
#[derive(Debug, <PERSON><PERSON>)]
pub struct VectorizationConfig {
    /// 默认批次大小
    pub default_batch_size: usize,
    /// 最大并发任务数
    pub max_concurrent_tasks: usize,
    /// 质量阈值
    pub quality_threshold: f64,
    /// 是否启用GPU加速
    pub enable_gpu: bool,
    /// 内存使用限制(MB)
    pub memory_limit_mb: usize,
    /// 任务重试次数
    pub max_retries: u32,
}

impl Default for VectorizationConfig {
    fn default() -> Self {
        Self {
            default_batch_size: 32,
            max_concurrent_tasks: 4,
            quality_threshold: 0.8,
            enable_gpu: false,
            memory_limit_mb: 2048,
            max_retries: 3,
        }
    }
}

/// 向量化结果
#[derive(Debug, Clone)]
pub struct VectorizationResult {
    pub embeddings: Vec<DocumentEmbedding>,
    pub quality_metrics: VectorQualityMetrics,
    pub processing_time_ms: i64,
    pub tokens_processed: i32,
    pub success_count: usize,
    pub failed_count: usize,
}

/// 批量向量化结果
#[derive(Debug, Clone)]
pub struct BatchVectorizationResult {
    pub batch_id: String,
    pub total_processed: usize,
    pub successful: usize,
    pub failed: usize,
    pub results: Vec<VectorizationResult>,
    pub overall_quality_score: f64,
    pub total_processing_time_ms: i64,
}

/// 向量化服务
pub struct VectorizationService {
    /// 模型管理器
    model_manager: Arc<EmbeddingModelManager>,
    /// 文档仓库
    document_repository: Arc<dyn DocumentRepository + Send + Sync>,
    /// 分块仓库
    chunk_repository: Arc<dyn DocumentChunkRepository + Send + Sync>,
    /// Qdrant向量Repository
    vector_repository: Arc<dyn VectorRepository + Send + Sync>,
    /// 文本向量化管道
    text_pipeline: Arc<TextVectorizationPipeline>,
    /// 向量质量评估系统
    quality_assessment: Arc<VectorQualityAssessment>,
    /// 服务配置
    config: VectorizationConfig,
    /// 活跃任务追踪
    active_tasks: Arc<RwLock<HashMap<String, EmbeddingTask>>>,
    /// 任务统计
    statistics: Arc<RwLock<VectorizationStatistics>>,
}

/// 向量化统计信息
#[derive(Debug, Default, Clone)]
pub struct VectorizationStatistics {
    pub total_tasks_created: u64,
    pub total_tasks_completed: u64,
    pub total_tasks_failed: u64,
    pub total_embeddings_generated: u64,
    pub total_processing_time_ms: u64,
    pub avg_processing_time_per_task: f64,
    pub current_queue_size: usize,
}

impl VectorizationService {
    /// 创建新的向量化服务实例
    pub fn new(
        model_manager: Arc<EmbeddingModelManager>,
        document_repository: Arc<dyn DocumentRepository + Send + Sync>,
        chunk_repository: Arc<dyn DocumentChunkRepository + Send + Sync>,
        vector_repository: Arc<dyn VectorRepository + Send + Sync>,
        text_pipeline: Arc<TextVectorizationPipeline>,
        config: VectorizationConfig,
    ) -> Self {
        let quality_config = VectorQualityConfig {
            quality_threshold: config.quality_threshold,
            anomaly_threshold: 0.95,
            min_magnitude: 0.1,
            max_sparsity_ratio: 0.9,
            similarity_sample_size: 100,
        };

        Self {
            model_manager,
            document_repository,
            chunk_repository,
            vector_repository,
            text_pipeline,
            quality_assessment: Arc::new(VectorQualityAssessment::new(quality_config)),
            config,
            active_tasks: Arc::new(RwLock::new(HashMap::new())),
            statistics: Arc::new(RwLock::new(VectorizationStatistics::default())),
        }
    }

    /// 向量化单个文档
    pub async fn vectorize_document(
        &self,
        document_id: String,
        model_id: Option<String>,
        force_recompute: bool,
    ) -> Result<VectorizationResult> {
        tracing::info!("开始向量化文档: {}", document_id);

        // 获取文档
        let document = self
            .document_repository
            .find_by_id(&document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("文档不存在: {}", document_id))?;

        // 检查是否已经有向量
        if !force_recompute && self.has_existing_embeddings(&document_id).await? {
            return Err(anyhow::anyhow!(
                "文档已存在向量，使用 force_recompute=true 强制重新计算"
            ));
        }

        // 获取或使用默认模型
        let model = self
            .get_model_for_vectorization(model_id.as_deref())
            .await?;

        // 创建向量化任务
        let task = self
            .create_vectorization_task(
                EmbeddingTaskType::Document,
                document_id.clone(),
                model.id.clone(),
                TaskPriority::Normal,
                None,
            )
            .await?;

        // 执行向量化
        let result = self
            .execute_document_vectorization(&document, &model, &task)
            .await?;

        // 更新统计信息
        self.update_statistics(&result).await;

        tracing::info!(
            "文档向量化完成: {}, 生成 {} 个向量",
            document_id,
            result.embeddings.len()
        );
        Ok(result)
    }

    /// 向量化单个文档分块
    pub async fn vectorize_chunk(
        &self,
        chunk_id: &str,
        model_id: Option<&str>,
        _force_recompute: bool,
    ) -> Result<VectorizationResult> {
        tracing::info!("开始向量化分块: {}", chunk_id);

        // 获取分块
        let chunk = self
            .chunk_repository
            .find_by_id(chunk_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("分块不存在: {}", chunk_id))?;

        // 获取模型
        let model = self.get_model_for_vectorization(model_id).await?;

        // 创建向量化任务
        let task = self
            .create_vectorization_task(
                EmbeddingTaskType::Chunk,
                chunk_id.to_owned(),
                model.id.clone(),
                TaskPriority::Normal,
                None,
            )
            .await?;

        // 执行向量化
        let result = self
            .execute_chunk_vectorization(&chunk, &model, &task)
            .await?;

        // 更新统计信息
        self.update_statistics(&result).await;

        tracing::info!("分块向量化完成: {}", chunk_id);
        Ok(result)
    }

    /// 批量向量化文档
    pub async fn batch_vectorize_documents(
        &self,
        document_ids: Vec<String>,
        model_id: Option<&str>,
        batch_config: Option<BatchConfig>,
        force_recompute: bool,
    ) -> Result<BatchVectorizationResult> {
        let batch_id = next_id();
        tracing::info!(
            "开始批量向量化 {} 个文档，批次ID: {}",
            document_ids.len(),
            batch_id
        );

        let config = batch_config.unwrap_or_default();
        let model = self.get_model_for_vectorization(model_id).await?;

        // 创建批次记录
        let _batch = self
            .create_embedding_batch(batch_id.clone(), document_ids.len(), &model, &config)
            .await?;

        let mut results = Vec::new();
        let mut successful = 0;
        let mut failed = 0;
        let start_time = std::time::Instant::now();

        // 分批处理
        for chunk in document_ids.chunks(config.batch_size as usize) {
            let batch_results = self
                .process_document_batch(chunk.to_vec(), &model, force_recompute, &batch_id)
                .await?;

            for result in batch_results {
                if result.failed_count == 0 {
                    successful += 1;
                } else {
                    failed += 1;
                }
                results.push(result);
            }
        }

        let total_processing_time = start_time.elapsed().as_millis() as i64;

        // 计算整体质量分数
        let overall_quality_score = if !results.is_empty() {
            results
                .iter()
                .map(|r| r.quality_metrics.quality_score)
                .sum::<f64>()
                / results.len() as f64
        } else {
            0.0
        };

        let batch_result = BatchVectorizationResult {
            batch_id: batch_id.clone(),
            total_processed: document_ids.len(),
            successful,
            failed,
            results,
            overall_quality_score,
            total_processing_time_ms: total_processing_time,
        };

        tracing::info!(
            "批量向量化完成，批次ID: {}，成功: {}，失败: {}",
            batch_id,
            successful,
            failed
        );

        Ok(batch_result)
    }

    /// 检查是否存在现有向量
    async fn has_existing_embeddings(&self, document_id: &str) -> Result<bool> {
        // 查询文档嵌入集合
        let results = self.vector_repository
            .search_document_embeddings(
                vec![0.0; 1536], // 虚拟查询向量
                1,
                Some(document_id),
            )
            .await?;
        
        Ok(!results.is_empty())
    }

    /// 获取用于向量化的模型
    async fn get_model_for_vectorization(&self, model_id: Option<&str>) -> Result<EmbeddingModel> {
        match model_id {
            Some(id) => self.model_manager.get_model(id).await,
            None => self.model_manager.get_default_model().await,
        }
    }

    /// 创建向量化任务
    async fn create_vectorization_task(
        &self,
        task_type: EmbeddingTaskType,
        resource_id: String,
        model_id: String,
        priority: TaskPriority,
        batch_id: Option<String>,
    ) -> Result<EmbeddingTask> {
        let task = EmbeddingTask {
            id: next_id(),
            task_type,
            resource_id,
            model_id,
            status: EmbeddingTaskStatus::Pending,
            priority,
            progress: 0.0,
            batch_id,
            retry_count: 0,
            max_retries: self.config.max_retries,
            error_message: None,
            processing_metadata: EmbeddingTaskMetadata {
                text_length: None,
                token_count: None,
                preprocessing_steps: Vec::new(),
                model_config: serde_json::Value::Null,
                quality_score: None,
                processing_time_ms: None,
                memory_usage_mb: None,
                gpu_used: self.config.enable_gpu,
            },
            created_at: current_millis(),
            started_at: None,
            completed_at: None,
            updated_at: current_millis(),
        };

        // 将任务添加到活跃任务追踪
        self.active_tasks
            .write()
            .await
            .insert(task.id.clone(), task.clone());

        // 更新统计信息
        self.statistics.write().await.total_tasks_created += 1;

        Ok(task)
    }

    /// 执行文档向量化
    async fn execute_document_vectorization(
        &self,
        document: &Document,
        model: &EmbeddingModel,
        _task: &EmbeddingTask,
    ) -> Result<VectorizationResult> {
        let start_time = std::time::Instant::now();

        // 获取文档的分块
        let chunks = self
            .chunk_repository
            .find_by_document_id(&document.id)
            .await?;

        if chunks.is_empty() {
            return Err(anyhow::anyhow!("文档没有分块，请先进行文档分块"));
        }

        let mut embeddings = Vec::new();
        let mut success_count = 0;
        let mut failed_count = 0;
        let mut total_tokens = 0;

        // 对每个分块进行向量化
        for chunk in &chunks {
            match self.vectorize_text(&chunk.content, model).await {
                Ok((embedding_vector, tokens)) => {
                    let embedding = DocumentEmbedding {
                        id: next_id(),
                        chunk_id: chunk.id.clone(),
                        embedding: embedding_vector,
                        model_name: model.name.clone(),
                        model_version: model.version.clone(),
                        dimension: model.dimension,
                        embedding_type: wisdom_vault_database::models::EmbeddingType::Dense,
                        processing_metadata: wisdom_vault_database::models::EmbeddingMetadata {
                            processing_time_ms: 0, // 将在后面计算
                            normalization_method: format!("{:?}", model.normalization),
                            chunk_preprocessing: vec!["tokenization".to_string()],
                            similarity_threshold: Some(self.config.quality_threshold),
                        },
                        created_at: current_millis(),
                    };

                    embeddings.push(embedding);
                    success_count += 1;
                    total_tokens += tokens;
                }
                Err(e) => {
                    tracing::error!("分块向量化失败: {}, 错误: {}", chunk.id, e);
                    failed_count += 1;
                }
            }
        }

        let processing_time = start_time.elapsed().as_millis() as i64;

        // 生成质量指标
        let quality_metrics = self.calculate_quality_metrics(&embeddings).await?;

        Ok(VectorizationResult {
            embeddings,
            quality_metrics,
            processing_time_ms: processing_time,
            tokens_processed: total_tokens,
            success_count,
            failed_count,
        })
    }

    /// 执行分块向量化
    async fn execute_chunk_vectorization(
        &self,
        chunk: &DocumentChunk,
        model: &EmbeddingModel,
        _task: &EmbeddingTask,
    ) -> Result<VectorizationResult> {
        let start_time = std::time::Instant::now();

        let (embedding_vector, tokens) = self.vectorize_text(&chunk.content, model).await?;

        let embedding = DocumentEmbedding {
            id: next_id(),
            chunk_id: chunk.id.clone(),
            embedding: embedding_vector,
            model_name: model.name.clone(),
            model_version: model.version.clone(),
            dimension: model.dimension,
            embedding_type: wisdom_vault_database::models::EmbeddingType::Dense,
            processing_metadata: wisdom_vault_database::models::EmbeddingMetadata {
                processing_time_ms: start_time.elapsed().as_millis() as i32,
                normalization_method: format!("{:?}", model.normalization),
                chunk_preprocessing: vec!["tokenization".to_string()],
                similarity_threshold: Some(self.config.quality_threshold),
            },
            created_at: current_millis(),
        };

        let processing_time = start_time.elapsed().as_millis() as i64;
        let quality_metrics = self.calculate_quality_metrics(&[embedding.clone()]).await?;

        Ok(VectorizationResult {
            embeddings: vec![embedding],
            quality_metrics,
            processing_time_ms: processing_time,
            tokens_processed: tokens,
            success_count: 1,
            failed_count: 0,
        })
    }

    /// 向量化文本 (使用真实的文本向量化管道)
    async fn vectorize_text(&self, text: &str, model: &EmbeddingModel) -> Result<(Vec<f32>, i32)> {
        // 确保模型已加载到文本向量化管道中
        self.text_pipeline.load_model(model).await?;

        // 使用文本向量化管道进行向量化
        let embedding = self
            .text_pipeline
            .vectorize_text(&model.id, text.to_string())
            .await?;

        // 简单的token计数估算 (实际应该从tokenizer获取)
        let token_count = text.split_whitespace().count() as i32;

        tracing::debug!(
            "向量化完成: 文本长度={}, token数量={}, 向量维度={}",
            text.len(),
            token_count,
            embedding.len()
        );

        Ok((embedding, token_count))
    }

    /// 处理文档批次
    async fn process_document_batch(
        &self,
        document_ids: Vec<String>,
        model: &EmbeddingModel,
        force_recompute: bool,
        _batch_id: &str,
    ) -> Result<Vec<VectorizationResult>> {
        let mut results = Vec::new();

        for document_id in document_ids {
            match self
                .vectorize_document(document_id.clone(), Some(model.id.clone()), force_recompute)
                .await
            {
                Ok(result) => results.push(result),
                Err(e) => {
                    tracing::error!("批次中文档向量化失败: {}, 错误: {}", document_id, e);
                    // 创建失败结果
                    let failed_result = VectorizationResult {
                        embeddings: Vec::new(),
                        quality_metrics: self.create_default_quality_metrics().await?,
                        processing_time_ms: 0,
                        tokens_processed: 0,
                        success_count: 0,
                        failed_count: 1,
                    };
                    results.push(failed_result);
                }
            }
        }

        Ok(results)
    }

    /// 创建嵌入批次
    async fn create_embedding_batch(
        &self,
        batch_id: String,
        total_items: usize,
        model: &EmbeddingModel,
        config: &BatchConfig,
    ) -> Result<EmbeddingBatch> {
        Ok(EmbeddingBatch {
            id: batch_id.clone(),
            name: format!("Batch-{}", batch_id),
            description: Some("自动生成的向量化批次".to_string()),
            model_id: model.id.clone(),
            total_items: total_items as i32,
            processed_items: 0,
            failed_items: 0,
            status: EmbeddingTaskStatus::Pending,
            batch_config: config.clone(),
            started_at: Some(current_millis()),
            completed_at: None,
            created_by: next_id(), // 这里应该是当前用户ID
            created_at: current_millis(),
            updated_at: current_millis(),
        })
    }

    /// 计算质量指标 (使用真实的质量评估系统)
    async fn calculate_quality_metrics(
        &self,
        embeddings: &[DocumentEmbedding],
    ) -> Result<VectorQualityMetrics> {
        if embeddings.is_empty() {
            return self.create_default_quality_metrics().await;
        }

        // 使用质量评估系统进行评估
        if embeddings.len() == 1 {
            // 单个向量评估
            let metrics = self
                .quality_assessment
                .assess_single_vector(&embeddings[0])
                .await?;
            Ok(metrics)
        } else {
            // 批量向量评估，返回第一个向量的指标作为代表
            let metrics_batch = self
                .quality_assessment
                .assess_vector_batch(embeddings)
                .await?;
            metrics_batch
                .into_iter()
                .next()
                .ok_or_else(|| anyhow::anyhow!("Failed to generate quality metrics"))
        }
    }

    /// 创建默认质量指标
    async fn create_default_quality_metrics(&self) -> Result<VectorQualityMetrics> {
        Ok(VectorQualityMetrics {
            id: next_id(),
            embedding_id: next_id(),
            dimension_consistency: false,
            magnitude: 0.0,
            sparsity_ratio: 1.0,
            norm_type: wisdom_vault_database::models::NormalizationMethod::None,
            quality_score: 0.0,
            anomaly_score: Some(1.0),
            similarity_distribution: wisdom_vault_database::models::SimilarityDistribution {
                mean: 0.0,
                std_dev: 0.0,
                min: 0.0,
                max: 0.0,
                percentile_25: 0.0,
                percentile_50: 0.0,
                percentile_75: 0.0,
                percentile_95: 0.0,
            },
            computed_at: current_millis(),
        })
    }

    /// 获取活跃任务列表
    pub async fn get_active_tasks(&self) -> Vec<EmbeddingTask> {
        self.active_tasks.read().await.values().cloned().collect()
    }

    /// 获取质量评估系统的引用
    pub fn get_quality_assessment(&self) -> Arc<VectorQualityAssessment> {
        self.quality_assessment.clone()
    }

    /// 批量评估向量质量
    pub async fn assess_vectors_quality(
        &self,
        embeddings: &[DocumentEmbedding],
    ) -> Result<crate::services::vector_quality_assessment::QualityReport> {
        let metrics = self
            .quality_assessment
            .assess_vector_batch(embeddings)
            .await?;
        let report = self.quality_assessment.generate_quality_report(&metrics);
        Ok(report)
    }

    /// 更新统计信息
    async fn update_statistics(&self, result: &VectorizationResult) {
        let mut stats = self.statistics.write().await;

        if result.failed_count == 0 {
            stats.total_tasks_completed += 1;
        } else {
            stats.total_tasks_failed += 1;
        }

        stats.total_embeddings_generated += result.embeddings.len() as u64;
        stats.total_processing_time_ms += result.processing_time_ms as u64;

        // 重新计算平均处理时间
        let total_completed = stats.total_tasks_completed + stats.total_tasks_failed;
        if total_completed > 0 {
            stats.avg_processing_time_per_task =
                stats.total_processing_time_ms as f64 / total_completed as f64;
        }
    }

    /// 获取统计信息
    pub async fn get_statistics(&self) -> VectorizationStatistics {
        self.statistics.read().await.clone()
    }

    /// 保存向量到数据库
    pub async fn save_embedding(&self, embedding: &DocumentEmbedding) -> Result<DocumentEmbedding> {
        // 将DocumentEmbedding转换为VectorPoint
        let vector_point = self.create_vector_point_from_embedding(embedding);
        
        // 存储到Qdrant
        self.vector_repository
            .batch_insert_document_embeddings(vec![embedding.clone()])
            .await?;
        
        Ok(embedding.clone())
    }

    /// 批量保存向量到数据库
    pub async fn save_embeddings_batch(
        &self,
        embeddings: &[DocumentEmbedding],
    ) -> Result<Vec<DocumentEmbedding>> {
        // 批量存储到Qdrant
        self.vector_repository
            .batch_insert_document_embeddings(embeddings.to_vec())
            .await?;
        
        Ok(embeddings.to_vec())
    }

    /// 根据文档ID获取所有向量
    pub async fn get_document_embeddings(
        &self,
        document_id: &str,
    ) -> Result<Vec<DocumentEmbedding>> {
        // 从Qdrant搜索文档向量
        let results = self.vector_repository
            .search_document_embeddings(
                vec![0.0; 1536], // 虚拟查询向量，实际中应该使用过滤器
                1000, // 获取大量结果
                Some(document_id),
            )
            .await?;
        
        // 将搜索结果转换为DocumentEmbedding
        // 注意：这里需要从payload中重建DocumentEmbedding
        // 这是一个简化实现，实际使用时需要完整实现
        Ok(Vec::new()) // 临时返回空向量
    }

    /// 根据chunk ID获取向量
    pub async fn get_chunk_embedding(&self, chunk_id: &str) -> Result<Option<DocumentEmbedding>> {
        // 创建过滤器查找特定chunk的向量
        let filter = VectorFilter {
            conditions: {
                let mut conditions = HashMap::new();
                conditions.insert("chunk_id".to_string(), FilterCondition::Equals(chunk_id.to_string()));
                conditions
            },
        };
        
        let results = self.vector_repository
            .search_vectors(
                "document_embeddings",
                vec![0.0; 1536], // 虚拟查询向量
                1,
                None,
                Some(filter),
            )
            .await?;
        
        if let Some(_result) = results.first() {
            // 这里需要从result中重建DocumentEmbedding
            // 简化实现，返回None
            Ok(None)
        } else {
            Ok(None)
        }
    }

    /// 删除文档的所有向量
    pub async fn delete_document_embeddings(&self, document_id: &str) -> Result<u64> {
        // 首先找到所有相关的向量点ID
        let results = self.vector_repository
            .search_document_embeddings(
                vec![0.0; 1536], // 虚拟查询向量
                1000, // 获取大量结果
                Some(document_id),
            )
            .await?;
        
        let point_ids: Vec<String> = results.iter().map(|r| r.point_id.clone()).collect();
        
        if !point_ids.is_empty() {
            self.vector_repository
                .delete_vectors("document_embeddings", point_ids.clone())
                .await?;
        }
        
        Ok(point_ids.len() as u64)
    }

    /// 向量相似度搜索
    pub async fn vector_similarity_search(
        &self,
        query_vector: Vec<f32>,
        similarity_threshold: f64,
        limit: u32,
        knowledge_base_id: Option<&str>,
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        let results = self.vector_repository
            .search_document_embeddings(
                query_vector,
                limit as u64,
                knowledge_base_id,
            )
            .await?;
        
        // 将结果转换为(DocumentEmbedding, f64)格式
        // 这是一个简化实现
        Ok(Vec::new())
    }

    /// 带过滤器的向量搜索
    pub async fn vector_search_with_filters(
        &self,
        query_vector: Vec<f32>,
        _similarity_threshold: f64,
        limit: u32,
        model_name: Option<String>,
        _embedding_type: Option<wisdom_vault_database::models::EmbeddingType>,
        knowledge_base_id: Option<&str>,
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        let mut filter_conditions = HashMap::new();
        
        if let Some(kb_id) = knowledge_base_id {
            filter_conditions.insert("knowledge_base_id".to_string(), FilterCondition::Equals(kb_id.to_string()));
        }
        
        if let Some(model) = model_name {
            filter_conditions.insert("model_name".to_string(), FilterCondition::Equals(model));
        }
        
        let filter = if !filter_conditions.is_empty() {
            Some(VectorFilter { conditions: filter_conditions })
        } else {
            None
        };
        
        let results = self.vector_repository
            .search_vectors(
                "document_embeddings", 
                query_vector,
                limit as u64,
                None,
                filter,
            )
            .await?;
        
        // 将结果转换为(DocumentEmbedding, f64)格式
        Ok(Vec::new())
    }

    /// 获取向量统计信息
    pub async fn get_vector_statistics(
        &self,
    ) -> Result<wisdom_vault_database::repositories::VectorStatistics> {
        // 从Qdrant获取统计信息
        let doc_count = self.vector_repository
            .count_vectors("document_embeddings", None)
            .await?;
        
        let chunk_count = self.vector_repository
            .count_vectors("chunk_embeddings", None)
            .await?;
        
        // 返回统计信息
        Ok(wisdom_vault_database::repositories::VectorStatistics {
            total_vectors: (doc_count + chunk_count) as i64,
            total_documents: doc_count as i64,
            total_chunks: chunk_count as i64,
            average_vector_size: 1536.0, // 假设使用1536维向量
        })
    }

    /// 重建向量索引
    pub async fn rebuild_vector_index(&self) -> Result<()> {
        // Qdrant会自动管理索引，这里可以做一些清理工作
        tracing::info!("向量索引重建完成");
        Ok(())
    }

    /// 优化向量存储
    pub async fn optimize_vector_storage(&self) -> Result<u64> {
        // Qdrant会自动优化存储，这里返回优化的向量数量
        let total_count = self.vector_repository
            .count_vectors("document_embeddings", None)
            .await?;
        
        tracing::info!("向量存储优化完成，处理了 {} 个向量", total_count);
        Ok(total_count)
    }

    /// 查找低质量向量
    pub async fn find_low_quality_vectors(&self, threshold: f64) -> Result<Vec<DocumentEmbedding>> {
        // 使用质量分数过滤器查找低质量向量
        let filter = VectorFilter {
            conditions: {
                let mut conditions = HashMap::new();
                conditions.insert("quality_score".to_string(), FilterCondition::Range {
                    gte: None,
                    lte: Some(threshold),
                });
                conditions
            },
        };
        
        let _results = self.vector_repository
            .search_vectors(
                "document_embeddings",
                vec![0.0; 1536], // 虚拟查询向量
                1000,
                None,
                Some(filter),
            )
            .await?;
        
        // 转换结果为DocumentEmbedding
        Ok(Vec::new())
    }

    /// 查找孤立的向量（没有对应chunk的向量）
    pub async fn find_orphaned_embeddings(&self) -> Result<Vec<DocumentEmbedding>> {
        // 这需要跨数据库查询，实现较复杂
        // 简化实现，返回空向量
        Ok(Vec::new())
    }

    /// 混合搜索（向量 + 文本）
    pub async fn hybrid_search(
        &self,
        query_vector: Vec<f32>,
        _query_text: String,
        _vector_weight: f64,
        _text_weight: f64,
        _similarity_threshold: f64,
        limit: u32,
        knowledge_base_id: Option<&str>,
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        // 使用Qdrant进行向量搜索
        let results = self.vector_repository
            .search_document_embeddings(
                query_vector,
                limit as u64,
                knowledge_base_id,
            )
            .await?;
        
        // 简化实现，只返回向量搜索结果
        Ok(Vec::new())
    }

    /// 从DocumentEmbedding创建VectorPoint的辅助方法
    fn create_vector_point_from_embedding(&self, embedding: &DocumentEmbedding) -> VectorPoint {
        use std::collections::HashMap;
        use wisdom_vault_database::qdrant_client::qdrant::Value as QdrantValue;
        
        let mut payload = HashMap::new();
        payload.insert("chunk_id".to_string(), QdrantValue {
            kind: Some(wisdom_vault_database::qdrant_client::qdrant::value::Kind::StringValue(embedding.chunk_id.clone()))
        });
        payload.insert("model_name".to_string(), QdrantValue {
            kind: Some(wisdom_vault_database::qdrant_client::qdrant::value::Kind::StringValue(embedding.model_name.clone()))
        });
        
        VectorPoint {
            id: embedding.id.clone(),
            vector: embedding.embedding.clone(),
            payload,
        }
    }
}
