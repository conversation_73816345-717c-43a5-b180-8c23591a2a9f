use anyhow::Result;
use mongodb::Database;
use std::sync::Arc;

use super::{FileStorageServiceTrait, GridFsFileStorageService, GridFsStorageConfig};

#[derive(Debug, Clone)]
pub struct FileStorageConfig {
    pub max_file_size: u64,
    pub allowed_extensions: Vec<String>,
    pub cleanup_interval_hours: u64,
    pub gridfs_bucket: String,
    pub gridfs_chunk_size_kb: Option<u32>,
}

/// 统一的文件存储服务工厂，根据配置创建合适的存储服务
pub struct FileStorageServiceFactory;

impl FileStorageServiceFactory {
    /// 根据配置创建文件存储服务
    pub async fn create(
        config: &FileStorageConfig,
        database: &Database,
    ) -> Result<Arc<dyn FileStorageServiceTrait>> {
        let gridfs_config = GridFsStorageConfig::from_app_config_file_storage(
            &config.gridfs_bucket,
            config.max_file_size,
            &config.allowed_extensions,
            config.gridfs_chunk_size_kb,
        );

        let service = GridFsFileStorageService::new(database, gridfs_config)?;
        Ok(Arc::new(service))
    }
}
