use anyhow::Result;
use std::{collections::HashMap, path::PathBuf, sync::Arc};
use tokio::sync::RwLock;

use wisdom_vault_database::models::{
    EmbeddingModel, NormalizationMethod, PaddingStrategy, PreprocessingConfig, TruncationStrategy,
};

/// 文本向量化管道配置
#[derive(Debug, Clone)]
pub struct TextVectorizationConfig {
    /// 缓存目录
    pub cache_dir: PathBuf,
    /// 设备类型 ("cpu", "cuda", "mps")
    pub device: String,
    /// 模型精度 ("fp32", "fp16", "bf16")
    pub precision: String,
    /// 是否使用缓存
    pub use_cache: bool,
    /// 最大序列长度
    pub max_sequence_length: usize,
    /// 批处理大小
    pub batch_size: usize,
}

impl Default for TextVectorizationConfig {
    fn default() -> Self {
        Self {
            cache_dir: PathBuf::from("./cache/models"),
            device: "cpu".to_string(),
            precision: "fp32".to_string(),
            use_cache: true,
            max_sequence_length: 512,
            batch_size: 32,
        }
    }
}

/// 已加载的模型实例
#[derive(Debug)]
pub struct LoadedModelInstance {
    pub id: String,
    pub config: EmbeddingModel,
    pub load_time: chrono::DateTime<chrono::Utc>,
    pub last_used: chrono::DateTime<chrono::Utc>,
    pub usage_count: u64,
}

/// 文本预处理器
pub struct TextPreprocessor {
    /// 是否转换为小写
    pub lowercase: bool,
    /// 是否移除标点符号
    pub remove_punctuation: bool,
    /// 是否移除停用词
    pub remove_stopwords: bool,
    /// 停用词列表
    pub stopwords: std::collections::HashSet<String>,
}

impl TextPreprocessor {
    /// 创建新的预处理器
    pub fn new(config: &PreprocessingConfig) -> Self {
        let mut stopwords = std::collections::HashSet::new();

        // 添加常见的中英文停用词
        let common_stopwords = [
            "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with",
            "by", "是", "的", "了", "在", "有", "和", "与", "或", "但", "这", "那", "我", "你",
            "他", "她", "它", "们", "个", "中", "上", "下", "来", "去", "说", "对", "就", "都",
            "要", "也", "还", "把", "给",
        ];

        for word in &common_stopwords {
            stopwords.insert(word.to_string());
        }

        Self {
            lowercase: config.lowercase,
            remove_punctuation: config.remove_punctuation,
            remove_stopwords: config.remove_stopwords,
            stopwords,
        }
    }

    /// 预处理文本
    pub fn preprocess(&self, text: &str) -> String {
        let mut processed = text.to_string();

        // 转换为小写
        if self.lowercase {
            processed = processed.to_lowercase();
        }

        // 移除标点符号
        if self.remove_punctuation {
            processed = processed
                .chars()
                .filter(|c| c.is_alphanumeric() || c.is_whitespace() || self.is_chinese_char(*c))
                .collect();
        }

        // 移除停用词
        if self.remove_stopwords {
            let words: Vec<&str> = processed.split_whitespace().collect();
            let filtered_words: Vec<&str> = words
                .into_iter()
                .filter(|word| !self.stopwords.contains(*word))
                .collect();
            processed = filtered_words.join(" ");
        }

        // 清理多余的空白字符
        processed = processed.split_whitespace().collect::<Vec<_>>().join(" ");

        processed
    }

    /// 判断是否为中文字符
    fn is_chinese_char(&self, c: char) -> bool {
        matches!(c as u32, 0x4E00..=0x9FFF | 0x3400..=0x4DBF | 0x20000..=0x2A6DF | 0x2A700..=0x2B73F | 0x2B740..=0x2B81F | 0x2B820..=0x2CEAF | 0x2F800..=0x2FA1F)
    }
}

/// 文本向量化管道
pub struct TextVectorizationPipeline {
    /// 配置
    config: TextVectorizationConfig,
    /// 已加载的模型实例
    loaded_models: Arc<RwLock<HashMap<String, LoadedModelInstance>>>,
    /// 文本预处理器
    preprocessor: Arc<TextPreprocessor>,
}

impl TextVectorizationPipeline {
    /// 创建新的文本向量化管道
    pub fn new(config: TextVectorizationConfig) -> Result<Self> {
        let default_preprocessing = PreprocessingConfig {
            lowercase: true,
            remove_punctuation: false,
            remove_stopwords: false,
            stem_words: false,
            max_length: config.max_sequence_length as i32,
            truncation_strategy: TruncationStrategy::Tail,
            padding_strategy: PaddingStrategy::MaxLength,
        };

        Ok(Self {
            config,
            loaded_models: Arc::new(RwLock::new(HashMap::new())),
            preprocessor: Arc::new(TextPreprocessor::new(&default_preprocessing)),
        })
    }

    /// 加载模型 (当前为占位符实现)
    pub async fn load_model(&self, model_config: &EmbeddingModel) -> Result<()> {
        tracing::info!("加载模型: {}", model_config.name);

        // 检查是否已加载
        {
            let models = self.loaded_models.read().await;
            if models.contains_key(&model_config.id) {
                tracing::info!("模型已加载: {}", model_config.name);
                return Ok(());
            }
        }

        // 模拟加载过程
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // 创建加载的模型实例
        let loaded_instance = LoadedModelInstance {
            id: model_config.id.clone(),
            config: model_config.clone(),
            load_time: chrono::Utc::now(),
            last_used: chrono::Utc::now(),
            usage_count: 0,
        };

        // 存储到已加载模型列表
        {
            let mut models = self.loaded_models.write().await;
            models.insert(model_config.id.clone(), loaded_instance);
        }

        tracing::info!("模型加载完成: {}", model_config.name);
        Ok(())
    }

    /// 向量化文本 (当前为占位符实现，返回随机向量)
    pub async fn vectorize_texts(
        &self,
        model_id: &str,
        texts: Vec<String>,
    ) -> Result<Vec<Vec<f32>>> {
        let models = self.loaded_models.read().await;
        let model_instance = models
            .get(model_id)
            .ok_or_else(|| anyhow::anyhow!("Model not loaded: {}", model_id))?;

        // 预处理文本
        let preprocessed_texts: Vec<String> = texts
            .iter()
            .map(|text| self.preprocessor.preprocess(text))
            .collect();

        // 模拟向量化过程 (当前返回随机向量)
        let mut all_embeddings = Vec::new();

        for _text in &preprocessed_texts {
            // 生成随机向量作为占位符
            use rand::Rng;
            let mut rng = rand::rng();
            let embedding: Vec<f32> = (0..model_instance.config.dimension)
                .map(|_| rng.random_range(-1.0..1.0))
                .collect();

            all_embeddings.push(embedding);
        }

        // 标准化处理
        let normalized_embeddings =
            self.normalize_embeddings(all_embeddings, &model_instance.config.normalization)?;

        // 模拟处理时间
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        Ok(normalized_embeddings)
    }

    /// 向量化单个文本
    pub async fn vectorize_text(&self, model_id: &str, text: String) -> Result<Vec<f32>> {
        let embeddings = self.vectorize_texts(model_id, vec![text]).await?;
        embeddings
            .into_iter()
            .next()
            .ok_or_else(|| anyhow::anyhow!("No embedding generated"))
    }

    /// 计算文本相似度
    pub async fn calculate_similarity(
        &self,
        embedding1: &[f32],
        embedding2: &[f32],
    ) -> Result<f32> {
        if embedding1.len() != embedding2.len() {
            return Err(anyhow::anyhow!("Embedding dimensions do not match"));
        }

        // 计算余弦相似度
        let dot_product: f32 = embedding1
            .iter()
            .zip(embedding2.iter())
            .map(|(a, b)| a * b)
            .sum();

        let norm1: f32 = embedding1.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm2: f32 = embedding2.iter().map(|x| x * x).sum::<f32>().sqrt();

        if norm1 == 0.0 || norm2 == 0.0 {
            return Ok(0.0);
        }

        Ok(dot_product / (norm1 * norm2))
    }

    /// 标准化嵌入向量
    fn normalize_embeddings(
        &self,
        embeddings: Vec<Vec<f32>>,
        normalization: &NormalizationMethod,
    ) -> Result<Vec<Vec<f32>>> {
        let normalized = embeddings
            .into_iter()
            .map(|mut embedding| {
                match normalization {
                    NormalizationMethod::L2 => {
                        let norm: f32 = embedding.iter().map(|x| x * x).sum::<f32>().sqrt();
                        if norm > 0.0 {
                            for x in &mut embedding {
                                *x /= norm;
                            }
                        }
                    }
                    NormalizationMethod::L1 => {
                        let norm: f32 = embedding.iter().map(|x| x.abs()).sum();
                        if norm > 0.0 {
                            for x in &mut embedding {
                                *x /= norm;
                            }
                        }
                    }
                    NormalizationMethod::MinMax => {
                        let min_val = embedding.iter().fold(f32::INFINITY, |a, &b| a.min(b));
                        let max_val = embedding.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));
                        let range = max_val - min_val;
                        if range > 0.0 {
                            for x in &mut embedding {
                                *x = (*x - min_val) / range;
                            }
                        }
                    }
                    NormalizationMethod::None => {
                        // 不进行标准化
                    }
                }
                embedding
            })
            .collect();

        Ok(normalized)
    }

    /// 卸载模型
    pub async fn unload_model(&self, model_id: &str) -> Result<()> {
        let mut models = self.loaded_models.write().await;
        if models.remove(model_id).is_some() {
            tracing::info!("模型卸载成功: {}", model_id);
        }
        Ok(())
    }

    /// 获取已加载的模型列表
    pub async fn list_loaded_models(&self) -> Vec<String> {
        let models = self.loaded_models.read().await;
        models.keys().cloned().collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_text_preprocessor() {
        let config = PreprocessingConfig {
            lowercase: true,
            remove_punctuation: true,
            remove_stopwords: true,
            stem_words: false,
            max_length: 512,
            truncation_strategy: TruncationStrategy::Tail,
            padding_strategy: PaddingStrategy::MaxLength,
        };

        let preprocessor = TextPreprocessor::new(&config);
        let text = "This is a TEST text with punctuation! 这是一个测试文本。";
        let processed = preprocessor.preprocess(text);

        assert!(!processed.contains("!"));
        assert!(processed.chars().all(|c| !c.is_uppercase()));
        println!("Processed text: {}", processed);
    }

    #[test]
    fn test_pipeline_creation() {
        let config = TextVectorizationConfig::default();
        let pipeline = TextVectorizationPipeline::new(config);
        assert!(pipeline.is_ok());
    }
}
