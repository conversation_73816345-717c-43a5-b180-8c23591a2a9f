use anyhow::Result;
use regex::Regex;
use std::sync::Arc;
use wisdom_vault_common::db::next_id;
use wisdom_vault_database::{
    models::{ChunkMetadata, ChunkType, Document, DocumentChunk},
    repositories::DocumentChunkRepository,
};

/// 文档分块策略枚举
#[derive(Debug, Clone)]
pub enum ChunkingStrategy {
    /// 固定长度分块
    FixedLength { max_length: usize, overlap: usize },
    /// 段落分块
    Paragraph,
    /// 句子分块
    Sentence { max_sentences: usize },
    /// 语义边界分块
    SemanticBoundary {
        max_length: usize,
        min_length: usize,
    },
    /// 结构化文档分块（基于标题等）
    Structured,
}

impl Default for ChunkingStrategy {
    fn default() -> Self {
        ChunkingStrategy::FixedLength {
            max_length: 1000,
            overlap: 100,
        }
    }
}

/// 分块配置参数
#[derive(Debug, <PERSON>lone)]
pub struct ChunkingConfig {
    pub strategy: ChunkingStrategy,
    pub preserve_formatting: bool,
    pub include_metadata: bool,
    pub quality_threshold: f64,
}

impl Default for ChunkingConfig {
    fn default() -> Self {
        Self {
            strategy: ChunkingStrategy::default(),
            preserve_formatting: false,
            include_metadata: true,
            quality_threshold: 0.5,
        }
    }
}

/// 分块结果
#[derive(Debug, Clone)]
pub struct ChunkingResult {
    pub chunks: Vec<DocumentChunk>,
    pub statistics: ChunkingStatistics,
    pub warnings: Vec<String>,
}

/// 分块统计信息
#[derive(Debug, Clone)]
pub struct ChunkingStatistics {
    pub total_chunks: usize,
    pub average_chunk_size: f64,
    pub min_chunk_size: usize,
    pub max_chunk_size: usize,
    pub quality_score: f64,
    pub processing_time_ms: u64,
}

/// 文档分块服务
/// 提供智能文档分块功能，支持多种分块策略
pub struct DocumentChunkingService {
    repository: Arc<dyn DocumentChunkRepository + Send + Sync>,
    // 预编译的正则表达式用于文本处理
    sentence_regex: Regex,
    paragraph_regex: Regex,
    heading_regex: Regex,
}

impl DocumentChunkingService {
    pub fn new(repository: Arc<dyn DocumentChunkRepository + Send + Sync>) -> Result<Self> {
        let sentence_regex = Regex::new(r"[.!?]+\s+")?;
        let paragraph_regex = Regex::new(r"\n\s*\n")?;
        let heading_regex = Regex::new(r"^#{1,6}\s+.*$")?;

        Ok(Self {
            repository,
            sentence_regex,
            paragraph_regex,
            heading_regex,
        })
    }

    /// 对文档进行分块处理
    pub async fn chunk_document(
        &self,
        document: &Document,
        config: &ChunkingConfig,
    ) -> Result<ChunkingResult> {
        let start_time = std::time::Instant::now();

        // 预处理文档内容
        let content = self.preprocess_content(&document.content, config)?;

        // 根据策略执行分块
        let chunks = match &config.strategy {
            ChunkingStrategy::FixedLength {
                max_length,
                overlap,
            } => self.chunk_by_fixed_length(&content, *max_length, *overlap, &document.id),
            ChunkingStrategy::Paragraph => self.chunk_by_paragraph(&content, &document.id),
            ChunkingStrategy::Sentence { max_sentences } => {
                self.chunk_by_sentence(&content, *max_sentences, &document.id)
            }
            ChunkingStrategy::SemanticBoundary {
                max_length,
                min_length,
            } => self.chunk_by_semantic_boundary(&content, *max_length, *min_length, &document.id),
            ChunkingStrategy::Structured => self.chunk_by_structure(&content, &document.id),
        }?;

        // 计算统计信息
        let processing_time = start_time.elapsed().as_millis() as u64;
        let statistics = self.calculate_statistics(&chunks, processing_time);

        // 质量评估
        let warnings = self.assess_chunk_quality(&chunks, config.quality_threshold);

        Ok(ChunkingResult {
            chunks,
            statistics,
            warnings,
        })
    }

    /// 预处理文档内容
    fn preprocess_content(&self, content: &str, config: &ChunkingConfig) -> Result<String> {
        let mut processed = content.to_string();

        if !config.preserve_formatting {
            // 标准化空白字符
            processed = processed.replace('\t', " ");
            processed = Regex::new(r" +")?.replace_all(&processed, " ").to_string();

            // 标准化换行符
            processed = processed.replace("\r\n", "\n").replace('\r', "\n");
        }

        // 去除首尾空白
        processed = processed.trim().to_string();

        Ok(processed)
    }

    /// 固定长度分块
    fn chunk_by_fixed_length(
        &self,
        content: &str,
        max_length: usize,
        overlap: usize,
        document_id: &str,
    ) -> Result<Vec<DocumentChunk>> {
        let mut chunks = Vec::new();
        let chars: Vec<char> = content.chars().collect();

        if chars.is_empty() {
            return Ok(chunks);
        }

        let mut start = 0;
        let mut chunk_index = 0;

        while start < chars.len() {
            let end = std::cmp::min(start + max_length, chars.len());
            let chunk_content: String = chars[start..end].iter().collect();

            if chunk_content.trim().is_empty() {
                start = end;
                continue;
            }

            let current_millis = wisdom_vault_common::time::current_millis();
            let chunk = DocumentChunk {
                id: next_id(),
                document_id: document_id.to_owned(),
                content: chunk_content.clone(),
                chunk_index,
                chunk_type: ChunkType::Paragraph,
                token_count: self.estimate_token_count(&chunk_content),
                char_count: chunk_content.len() as i32,
                start_offset: start as i32,
                end_offset: end as i32,
                metadata: ChunkMetadata {
                    heading: None,
                    section_level: None,
                    language: None,
                    quality_score: Some(self.calculate_chunk_quality(&chunk_content)),
                    extraction_confidence: Some(1.0),
                },
                created_at: current_millis,
            };

            chunks.push(chunk);
            chunk_index += 1;

            // 计算下一个块的起始位置，包含重叠
            if end >= chars.len() {
                break;
            }
            start = if overlap > 0 && end > overlap {
                end - overlap
            } else {
                end
            };
        }

        Ok(chunks)
    }

    /// 段落分块
    fn chunk_by_paragraph(&self, content: &str, document_id: &str) -> Result<Vec<DocumentChunk>> {
        let paragraphs: Vec<&str> = self.paragraph_regex.split(content).collect();
        let mut chunks = Vec::new();
        let mut current_offset = 0;

        for (index, paragraph) in paragraphs.iter().enumerate() {
            let trimmed = paragraph.trim();
            if trimmed.is_empty() {
                current_offset += paragraph.len() + 2; // +2 for \n\n
                continue;
            }

            let current_millis = wisdom_vault_common::time::current_millis();
            let chunk = DocumentChunk {
                id: next_id(),
                document_id: document_id.to_owned(),
                content: trimmed.to_string(),
                chunk_index: index as i32,
                chunk_type: ChunkType::Paragraph,
                token_count: self.estimate_token_count(trimmed),
                char_count: trimmed.len() as i32,
                start_offset: current_offset as i32,
                end_offset: (current_offset + paragraph.len()) as i32,
                metadata: ChunkMetadata {
                    heading: self.extract_heading(trimmed),
                    section_level: self.detect_section_level(trimmed),
                    language: None,
                    quality_score: Some(self.calculate_chunk_quality(trimmed)),
                    extraction_confidence: Some(0.95),
                },
                created_at: current_millis,
            };

            chunks.push(chunk);
            current_offset += paragraph.len() + 2;
        }

        Ok(chunks)
    }

    /// 句子分块
    fn chunk_by_sentence(
        &self,
        content: &str,
        max_sentences: usize,
        document_id: &str,
    ) -> Result<Vec<DocumentChunk>> {
        let sentences: Vec<&str> = self.sentence_regex.split(content).collect();
        let mut chunks = Vec::new();
        let mut current_sentences = Vec::new();
        let mut chunk_index = 0;
        let mut current_offset = 0;

        for sentence in sentences {
            let trimmed = sentence.trim();
            if trimmed.is_empty() {
                continue;
            }

            current_sentences.push(trimmed);

            let current_millis = wisdom_vault_common::time::current_millis();
            if current_sentences.len() >= max_sentences {
                let chunk_content = current_sentences.join(". ") + ".";
                let chunk = DocumentChunk {
                    id: next_id(),
                    document_id: document_id.to_owned(),
                    content: chunk_content.clone(),
                    chunk_index,
                    chunk_type: ChunkType::Sentence,
                    token_count: self.estimate_token_count(&chunk_content),
                    char_count: chunk_content.len() as i32,
                    start_offset: current_offset as i32,
                    end_offset: (current_offset + chunk_content.len()) as i32,
                    metadata: ChunkMetadata {
                        heading: None,
                        section_level: None,
                        language: None,
                        quality_score: Some(self.calculate_chunk_quality(&chunk_content)),
                        extraction_confidence: Some(0.9),
                    },
                    created_at: current_millis,
                };

                chunks.push(chunk);
                current_offset += chunk_content.len();
                current_sentences.clear();
                chunk_index += 1;
            }
        }

        // 处理剩余的句子
        if !current_sentences.is_empty() {
            let chunk_content = current_sentences.join(". ") + ".";
            let chunk = DocumentChunk {
                id: next_id(),
                document_id: document_id.to_owned(),
                content: chunk_content.clone(),
                chunk_index,
                chunk_type: ChunkType::Sentence,
                token_count: self.estimate_token_count(&chunk_content),
                char_count: chunk_content.len() as i32,
                start_offset: current_offset as i32,
                end_offset: (current_offset + chunk_content.len()) as i32,
                metadata: ChunkMetadata {
                    heading: None,
                    section_level: None,
                    language: None,
                    quality_score: Some(self.calculate_chunk_quality(&chunk_content)),
                    extraction_confidence: Some(0.9),
                },
                created_at: wisdom_vault_common::time::current_millis(),
            };

            chunks.push(chunk);
        }

        Ok(chunks)
    }

    /// 语义边界分块（简化实现）
    fn chunk_by_semantic_boundary(
        &self,
        content: &str,
        max_length: usize,
        min_length: usize,
        document_id: &str,
    ) -> Result<Vec<DocumentChunk>> {
        // 先按段落分割
        let paragraphs: Vec<&str> = self.paragraph_regex.split(content).collect();
        let mut chunks = Vec::new();
        let mut current_chunk = String::new();
        let mut chunk_index = 0;
        let mut current_offset = 0;

        for paragraph in paragraphs {
            let trimmed = paragraph.trim();
            if trimmed.is_empty() {
                continue;
            }

            // 如果当前块加上这个段落超过最大长度
            if !current_chunk.is_empty() && current_chunk.len() + trimmed.len() > max_length {
                // 如果当前块满足最小长度要求，创建分块
                if current_chunk.len() >= min_length {
                    let chunk = self.create_chunk(
                        &current_chunk,
                        document_id,
                        chunk_index,
                        current_offset,
                        ChunkType::Paragraph,
                    );
                    chunks.push(chunk);
                    current_offset += current_chunk.len();
                    current_chunk.clear();
                    chunk_index += 1;
                }
            }

            // 添加段落到当前块
            if !current_chunk.is_empty() {
                current_chunk.push_str("\n\n");
            }
            current_chunk.push_str(trimmed);
        }

        // 处理最后的块
        if !current_chunk.is_empty() && current_chunk.len() >= min_length {
            let chunk = self.create_chunk(
                &current_chunk,
                document_id,
                chunk_index,
                current_offset,
                ChunkType::Paragraph,
            );
            chunks.push(chunk);
        }

        Ok(chunks)
    }

    /// 结构化文档分块（基于标题）
    fn chunk_by_structure(&self, content: &str, document_id: &str) -> Result<Vec<DocumentChunk>> {
        let lines: Vec<&str> = content.lines().collect();
        let mut chunks = Vec::new();
        let mut current_section = Vec::new();
        let mut chunk_index = 0;
        let mut current_offset = 0;
        let mut current_heading: Option<String> = None;
        let mut current_level: Option<i32> = None;

        for line in lines {
            if self.heading_regex.is_match(line) {
                // 如果有当前section，先创建分块
                if !current_section.is_empty() {
                    let section_content = current_section.join("\n");
                    let chunk = DocumentChunk {
                        id: next_id(),
                        document_id: document_id.to_owned(),
                        content: section_content.clone(),
                        chunk_index,
                        chunk_type: ChunkType::Section,
                        token_count: self.estimate_token_count(&section_content),
                        char_count: section_content.len() as i32,
                        start_offset: current_offset as i32,
                        end_offset: (current_offset + section_content.len()) as i32,
                        metadata: ChunkMetadata {
                            heading: current_heading.clone(),
                            section_level: current_level,
                            language: None,
                            quality_score: Some(self.calculate_chunk_quality(&section_content)),
                            extraction_confidence: Some(0.95),
                        },
                        created_at: wisdom_vault_common::time::current_millis(),
                    };

                    chunks.push(chunk);
                    current_offset += section_content.len();
                    current_section.clear();
                    chunk_index += 1;
                }

                // 开始新的section
                current_heading = Some(line.trim().to_string());
                current_level = self.detect_section_level(line);
            }

            current_section.push(line);
        }

        // 处理最后的section
        if !current_section.is_empty() {
            let section_content = current_section.join("\n");
            let chunk = DocumentChunk {
                id: next_id(),
                document_id: document_id.to_owned(),
                content: section_content.clone(),
                chunk_index,
                chunk_type: ChunkType::Section,
                token_count: self.estimate_token_count(&section_content),
                char_count: section_content.len() as i32,
                start_offset: current_offset as i32,
                end_offset: (current_offset + section_content.len()) as i32,
                metadata: ChunkMetadata {
                    heading: current_heading,
                    section_level: current_level,
                    language: None,
                    quality_score: Some(self.calculate_chunk_quality(&section_content)),
                    extraction_confidence: Some(0.95),
                },
                created_at: wisdom_vault_common::time::current_millis(),
            };

            chunks.push(chunk);
        }

        Ok(chunks)
    }

    /// 创建分块辅助函数
    fn create_chunk(
        &self,
        content: &str,
        document_id: &str,
        chunk_index: i32,
        start_offset: usize,
        chunk_type: ChunkType,
    ) -> DocumentChunk {
        DocumentChunk {
            id: next_id(),
            document_id: document_id.to_owned(),
            content: content.to_string(),
            chunk_index,
            chunk_type,
            token_count: self.estimate_token_count(content),
            char_count: content.len() as i32,
            start_offset: start_offset as i32,
            end_offset: (start_offset + content.len()) as i32,
            metadata: ChunkMetadata {
                heading: self.extract_heading(content),
                section_level: self.detect_section_level(content),
                language: None,
                quality_score: Some(self.calculate_chunk_quality(content)),
                extraction_confidence: Some(0.9),
            },
            created_at: wisdom_vault_common::time::current_millis(),
        }
    }

    /// 估算token数量（简化实现）
    fn estimate_token_count(&self, content: &str) -> i32 {
        // 简单估算：一个token大约4个字符
        (content.len() / 4) as i32
    }

    /// 提取标题
    fn extract_heading(&self, content: &str) -> Option<String> {
        let first_line = content.lines().next()?;
        if self.heading_regex.is_match(first_line) {
            Some(first_line.trim().to_string())
        } else {
            None
        }
    }

    /// 检测章节级别
    fn detect_section_level(&self, content: &str) -> Option<i32> {
        let first_line = content.lines().next()?;
        if first_line.starts_with("# ") {
            Some(1)
        } else if first_line.starts_with("## ") {
            Some(2)
        } else if first_line.starts_with("### ") {
            Some(3)
        } else if first_line.starts_with("#### ") {
            Some(4)
        } else if first_line.starts_with("##### ") {
            Some(5)
        } else if first_line.starts_with("###### ") {
            Some(6)
        } else {
            None
        }
    }

    /// 计算分块质量得分
    pub(crate) fn calculate_chunk_quality(&self, content: &str) -> f64 {
        let mut score: f64 = 1.0;

        // 长度评分
        let length = content.len();
        if length < 50 {
            score -= 0.3; // 太短
        } else if length > 2000 {
            score -= 0.2; // 太长
        }

        // 结构完整性评分
        let sentences = self.sentence_regex.split(content).count();
        if sentences == 0 {
            score -= 0.4; // 没有完整句子
        }

        // 语言连贯性评分（简化）
        let words: Vec<&str> = content.split_whitespace().collect();
        if words.len() < 5 {
            score -= 0.2; // 词汇太少
        }

        score.clamp(0.0, 1.0)
    }

    /// 计算分块统计信息
    fn calculate_statistics(
        &self,
        chunks: &[DocumentChunk],
        processing_time_ms: u64,
    ) -> ChunkingStatistics {
        if chunks.is_empty() {
            return ChunkingStatistics {
                total_chunks: 0,
                average_chunk_size: 0.0,
                min_chunk_size: 0,
                max_chunk_size: 0,
                quality_score: 0.0,
                processing_time_ms,
            };
        }

        let sizes: Vec<usize> = chunks.iter().map(|c| c.content.len()).collect();
        let total_size: usize = sizes.iter().sum();
        let min_size = *sizes.iter().min().unwrap();
        let max_size = *sizes.iter().max().unwrap();
        let avg_size = total_size as f64 / chunks.len() as f64;

        let quality_scores: Vec<f64> = chunks
            .iter()
            .filter_map(|c| c.metadata.quality_score)
            .collect();
        let avg_quality = if quality_scores.is_empty() {
            0.0
        } else {
            quality_scores.iter().sum::<f64>() / quality_scores.len() as f64
        };

        ChunkingStatistics {
            total_chunks: chunks.len(),
            average_chunk_size: avg_size,
            min_chunk_size: min_size,
            max_chunk_size: max_size,
            quality_score: avg_quality,
            processing_time_ms,
        }
    }

    /// 评估分块质量并生成警告
    fn assess_chunk_quality(&self, chunks: &[DocumentChunk], threshold: f64) -> Vec<String> {
        let mut warnings = Vec::new();

        if chunks.is_empty() {
            warnings.push("No chunks were generated".to_string());
            return warnings;
        }

        // 检查质量低的分块
        let low_quality_count = chunks
            .iter()
            .filter(|c| c.metadata.quality_score.unwrap_or(0.0) < threshold)
            .count();

        if low_quality_count > 0 {
            warnings.push(format!(
                "{low_quality_count} chunks have quality below threshold ({threshold:.2})",
            ));
        }

        // 检查过短的分块
        let short_chunks = chunks.iter().filter(|c| c.content.len() < 50).count();
        if short_chunks > 0 {
            warnings.push(format!("{short_chunks} chunks are very short (< 50 chars)",));
        }

        // 检查过长的分块
        let long_chunks = chunks.iter().filter(|c| c.content.len() > 2000).count();
        if long_chunks > 0 {
            warnings.push(format!("{long_chunks} chunks are very long (> 2000 chars)",));
        }

        warnings
    }

    /// 保存分块到数据库
    pub async fn save_chunks(&self, chunks: &[DocumentChunk]) -> Result<Vec<DocumentChunk>> {
        let mut saved_chunks = Vec::new();

        for chunk in chunks {
            let saved_chunk = self.repository.create(chunk).await?;
            saved_chunks.push(saved_chunk);
        }

        Ok(saved_chunks)
    }

    /// 获取文档的所有分块
    pub async fn get_document_chunks(&self, document_id: &str) -> Result<Vec<DocumentChunk>> {
        self.repository.find_by_document_id(document_id).await
    }

    /// 删除文档的所有分块
    pub async fn delete_document_chunks(&self, document_id: &str) -> Result<bool> {
        let deleted_count = self.repository.delete_by_document_id(document_id).await?;
        Ok(deleted_count > 0)
    }

    /// 重新分块文档（删除旧分块并创建新分块）
    pub async fn rechunk_document(
        &self,
        document: &Document,
        config: &ChunkingConfig,
    ) -> Result<ChunkingResult> {
        // 删除现有分块
        self.delete_document_chunks(&document.id).await?;

        // 创建新分块
        let result = self.chunk_document(document, config).await?;

        // 保存到数据库
        self.save_chunks(&result.chunks).await?;

        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use wisdom_vault_database::models::{
        DocumentMetadata, DocumentProcessingMetadata, DocumentStatus,
    };

    // Mock repository for testing
    struct MockDocumentChunkRepository;

    #[async_trait]
    impl DocumentChunkRepository for MockDocumentChunkRepository {
        async fn create(&self, chunk: &DocumentChunk) -> Result<DocumentChunk> {
            Ok(chunk.clone())
        }

        async fn find_by_id(&self, _id: &str) -> Result<Option<DocumentChunk>> {
            Ok(None)
        }

        async fn find_by_document_id(&self, _document_id: &str) -> Result<Vec<DocumentChunk>> {
            Ok(Vec::new())
        }

        async fn update(&self, chunk: &DocumentChunk) -> Result<DocumentChunk> {
            Ok(chunk.clone())
        }

        async fn delete(&self, _id: &str) -> Result<bool> {
            Ok(true)
        }

        async fn delete_by_document_id(&self, _document_id: &str) -> Result<bool> {
            Ok(true)
        }

        async fn list_with_pagination(
            &self,
            _document_id: Option<&str>,
            _limit: u32,
            _offset: u32,
        ) -> Result<Vec<DocumentChunk>> {
            Ok(Vec::new())
        }

        async fn count(&self, _document_id: Option<&str>) -> Result<i64> {
            Ok(0)
        }
    }

    fn create_test_document() -> Document {
        let current_millis = wisdom_vault_common::time::current_millis();
        Document {
            id: next_id(),
            knowledge_base_id: next_id(),
            title: "Test Document".to_string(),
            content: "This is a test document.\n\nIt has multiple paragraphs.\n\nEach paragraph should be chunked separately.".to_string(),
            summary: None,
            file_type: "txt".to_string(),
            file_size: 100,
            file_path: None,
            original_filename: Some("test.txt".to_string()),
            mime_type: "text/plain".to_string(),
            language: Some("en".to_string()),
            metadata: DocumentMetadata {
                author: None,
                subject: None,
                creator: None,
                producer: None,
                keywords: Vec::new(),
                source_url: None,
                page_count: None,
                word_count: None,
                character_count: None,
                creation_date: None,
                modification_date: None,
                content_type: "text/plain".to_string(),
                content_encoding: None,
                content_language: None,
                custom_fields: serde_json::Value::Null,
            },
            processing_metadata: DocumentProcessingMetadata {
                extraction_method: "test".to_string(),
                extraction_quality: 1.0,
                processing_time_ms: 0,
                parsing_errors: Vec::new(),
                parsing_warnings: Vec::new(),
                file_checksum: "test".to_string(),
                structured_content: None,
                processing_attempts: 1,
                last_processing_attempt: Some(current_millis),
            },
            status: DocumentStatus::Indexed,
            uploaded_by: next_id(),
            indexed_at: Some(current_millis),
            created_at: current_millis,
            updated_at: current_millis,
        }
    }

    #[tokio::test]
    async fn test_fixed_length_chunking() {
        let repo = Arc::new(MockDocumentChunkRepository);
        let service = DocumentChunkingService::new(repo).unwrap();
        let document = create_test_document();

        let config = ChunkingConfig {
            strategy: ChunkingStrategy::FixedLength {
                max_length: 50,
                overlap: 10,
            },
            ..Default::default()
        };

        let result = service.chunk_document(&document, &config).await.unwrap();

        assert!(!result.chunks.is_empty());
        assert!(result.chunks.iter().all(|c| c.content.len() <= 50));
    }

    #[tokio::test]
    async fn test_paragraph_chunking() {
        let repo = Arc::new(MockDocumentChunkRepository);
        let service = DocumentChunkingService::new(repo).unwrap();
        let document = create_test_document();

        let config = ChunkingConfig {
            strategy: ChunkingStrategy::Paragraph,
            ..Default::default()
        };

        let result = service.chunk_document(&document, &config).await.unwrap();

        assert_eq!(result.chunks.len(), 3); // 3 paragraphs
        assert!(result
            .chunks
            .iter()
            .all(|c| c.chunk_type == ChunkType::Paragraph));
    }

    #[tokio::test]
    async fn test_quality_assessment() {
        let repo = Arc::new(MockDocumentChunkRepository);
        let service = DocumentChunkingService::new(repo).unwrap();

        let high_quality =
            "This is a well-formed paragraph with sufficient content and proper structure.";
        let low_quality = "Short.";

        let high_score = service.calculate_chunk_quality(high_quality);
        let low_score = service.calculate_chunk_quality(low_quality);

        assert!(high_score > low_score);
        assert!(high_score > 0.5);
        assert!(low_score < 0.8);
    }
}
