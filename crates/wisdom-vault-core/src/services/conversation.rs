use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use tracing::{debug, info};

use crate::services::MessageRole;

/// 对话状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConversationStatus {
    /// 活跃状态
    Active,
    /// 暂停状态
    Paused,
    /// 已结束
    Ended,
    /// 已归档
    Archived,
}

/// 对话元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationMetadata {
    /// 对话标题
    pub title: Option<String>,
    /// 对话标签
    pub tags: Vec<String>,
    /// 对话类型
    pub conversation_type: String,
    /// 自定义属性
    pub custom_attributes: HashMap<String, String>,
}

/// 对话信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Conversation {
    /// 对话 ID
    pub id: String,
    /// 用户 ID
    pub user_id: String,
    /// 知识库 ID
    pub knowledge_base_id: Option<String>,
    /// 对话标题
    pub title: String,
    /// 对话状态
    pub status: ConversationStatus,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    /// 最后活跃时间
    pub last_active_at: DateTime<Utc>,
    /// 消息数量
    pub message_count: u32,
    /// 对话元数据
    pub metadata: ConversationMetadata,
}

/// 消息信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    /// 消息 ID
    pub id: String,
    /// 对话 ID
    pub conversation_id: String,
    /// 消息角色
    pub role: MessageRole,
    /// 消息内容
    pub content: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 消息元数据
    pub metadata: Option<MessageMetadata>,
    /// 消息状态
    pub status: MessageStatus,
}

/// 消息元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageMetadata {
    /// Token 使用情况
    pub token_usage: Option<TokenUsage>,
    /// 响应时间 (毫秒)
    pub response_time_ms: Option<u64>,
    /// 置信度
    pub confidence: Option<f64>,
    /// 引用的文档来源
    pub sources: Option<Vec<String>>,
    /// 模型信息
    pub model: Option<String>,
}

/// 消息状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageStatus {
    /// 正常状态
    Normal,
    /// 已编辑
    Edited,
    /// 已删除
    Deleted,
    /// 已标记为有用
    Helpful,
    /// 已标记为无用
    NotHelpful,
}

/// Token 使用情况
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// 对话创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateConversationRequest {
    /// 用户 ID
    pub user_id: String,
    /// 知识库 ID (可选)
    pub knowledge_base_id: Option<String>,
    /// 对话标题 (可选)
    pub title: Option<String>,
    /// 对话元数据 (可选)
    pub metadata: Option<ConversationMetadata>,
}

/// 添加消息请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddMessageRequest {
    /// 对话 ID
    pub conversation_id: String,
    /// 消息角色
    pub role: MessageRole,
    /// 消息内容
    pub content: String,
    /// 消息元数据 (可选)
    pub metadata: Option<MessageMetadata>,
}

/// 对话查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationQuery {
    /// 用户 ID
    pub user_id: Option<String>,
    /// 知识库 ID
    pub knowledge_base_id: Option<String>,
    /// 对话状态
    pub status: Option<ConversationStatus>,
    /// 分页偏移
    pub offset: Option<u32>,
    /// 分页限制
    pub limit: Option<u32>,
    /// 排序字段
    pub sort_by: Option<String>,
    /// 排序方向
    pub sort_order: Option<String>,
}

/// 对话统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationStats {
    /// 总对话数
    pub total_conversations: u32,
    /// 活跃对话数
    pub active_conversations: u32,
    /// 总消息数
    pub total_messages: u32,
    /// 平均每个对话的消息数
    pub avg_messages_per_conversation: f64,
    /// 最近 24 小时的对话数
    pub conversations_last_24h: u32,
    /// 最近 7 天的对话数
    pub conversations_last_7d: u32,
}

/// 对话摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationSummary {
    /// 对话 ID
    pub conversation_id: String,
    /// 对话标题
    pub title: String,
    /// 主要话题
    pub main_topics: Vec<String>,
    /// 关键问题
    pub key_questions: Vec<String>,
    /// 摘要内容
    pub summary: String,
    /// 生成时间
    pub generated_at: DateTime<Utc>,
}

/// 对话服务 trait
#[async_trait::async_trait]
pub trait ConversationRepository: Send + Sync {
    /// 创建对话
    async fn create_conversation(&self, request: CreateConversationRequest)
        -> Result<Conversation>;

    /// 获取对话
    async fn get_conversation(&self, conversation_id: &str) -> Result<Option<Conversation>>;

    /// 更新对话
    async fn update_conversation(&self, conversation: &Conversation) -> Result<()>;

    /// 删除对话
    async fn delete_conversation(&self, conversation_id: &str) -> Result<()>;

    /// 查询对话列表
    async fn list_conversations(&self, query: ConversationQuery) -> Result<Vec<Conversation>>;

    /// 添加消息
    async fn add_message(&self, request: AddMessageRequest) -> Result<Message>;

    /// 获取消息
    async fn get_message(&self, message_id: &str) -> Result<Option<Message>>;

    /// 获取对话消息列表
    async fn get_conversation_messages(
        &self,
        conversation_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Message>>;

    /// 更新消息状态
    async fn update_message_status(&self, message_id: &str, status: MessageStatus) -> Result<()>;

    /// 删除消息
    async fn delete_message(&self, message_id: &str) -> Result<()>;

    /// 获取对话统计
    async fn get_conversation_stats(&self, user_id: Option<&str>) -> Result<ConversationStats>;
}

/// 对话管理服务
pub struct ConversationService {
    repository: Arc<dyn ConversationRepository>,
    conversation_cache: Arc<RwLock<HashMap<String, Conversation>>>,
    message_cache: Arc<RwLock<HashMap<String, Vec<Message>>>>,
    max_cache_size: usize,
}

impl ConversationService {
    /// 创建新的对话服务
    pub fn new(repository: Arc<dyn ConversationRepository>) -> Self {
        Self {
            repository,
            conversation_cache: Arc::new(RwLock::new(HashMap::new())),
            message_cache: Arc::new(RwLock::new(HashMap::new())),
            max_cache_size: 1000,
        }
    }

    /// 创建新对话
    pub async fn create_conversation(
        &self,
        request: CreateConversationRequest,
    ) -> Result<Conversation> {
        info!("Creating new conversation for user: {}", request.user_id);

        let conversation = self.repository.create_conversation(request).await?;

        // 添加到缓存
        {
            let mut cache = self.conversation_cache.write().await;
            if cache.len() >= self.max_cache_size {
                // 简单的 LRU：清理一半缓存
                let keys_to_remove: Vec<String> =
                    cache.keys().take(cache.len() / 2).cloned().collect();
                for key in keys_to_remove {
                    cache.remove(&key);
                }
            }
            cache.insert(conversation.id.clone(), conversation.clone());
        }

        debug!("Created conversation: {}", conversation.id);
        Ok(conversation)
    }

    /// 获取对话
    pub async fn get_conversation(&self, conversation_id: &str) -> Result<Option<Conversation>> {
        // 先检查缓存
        {
            let cache = self.conversation_cache.read().await;
            if let Some(conversation) = cache.get(conversation_id) {
                return Ok(Some(conversation.clone()));
            }
        }

        // 从数据库获取
        let conversation = self.repository.get_conversation(conversation_id).await?;

        // 添加到缓存
        if let Some(ref conv) = conversation {
            let mut cache = self.conversation_cache.write().await;
            cache.insert(conversation_id.to_string(), conv.clone());
        }

        Ok(conversation)
    }

    /// 更新对话
    pub async fn update_conversation(&self, conversation: &Conversation) -> Result<()> {
        self.repository.update_conversation(conversation).await?;

        // 更新缓存
        {
            let mut cache = self.conversation_cache.write().await;
            cache.insert(conversation.id.clone(), conversation.clone());
        }

        Ok(())
    }

    /// 删除对话
    pub async fn delete_conversation(&self, conversation_id: &str) -> Result<()> {
        self.repository.delete_conversation(conversation_id).await?;

        // 从缓存中移除
        {
            let mut cache = self.conversation_cache.write().await;
            cache.remove(conversation_id);
        }

        {
            let mut msg_cache = self.message_cache.write().await;
            msg_cache.remove(conversation_id);
        }

        info!("Deleted conversation: {}", conversation_id);
        Ok(())
    }

    /// 查询对话列表
    pub async fn list_conversations(&self, query: ConversationQuery) -> Result<Vec<Conversation>> {
        self.repository.list_conversations(query).await
    }

    /// 添加消息
    pub async fn add_message(&self, request: AddMessageRequest) -> Result<Message> {
        let message = self.repository.add_message(request.clone()).await?;

        // 更新对话的最后活跃时间和消息数量
        if let Some(mut conversation) = self.get_conversation(&request.conversation_id).await? {
            conversation.last_active_at = Utc::now();
            conversation.message_count += 1;
            conversation.updated_at = Utc::now();
            self.update_conversation(&conversation).await?;
        }

        // 添加到消息缓存
        {
            let mut cache = self.message_cache.write().await;
            let messages = cache
                .entry(request.conversation_id.clone())
                .or_insert_with(Vec::new);
            messages.push(message.clone());

            // 限制缓存中的消息数量
            if messages.len() > 100 {
                messages.drain(..messages.len() - 100);
            }
        }

        debug!("Added message to conversation: {}", request.conversation_id);
        Ok(message)
    }

    /// 获取对话消息列表
    pub async fn get_conversation_messages(
        &self,
        conversation_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Message>> {
        // 如果是获取最近的消息且缓存中有，直接返回缓存
        if offset.unwrap_or(0) == 0 && limit.unwrap_or(50) <= 100 {
            let cache = self.message_cache.read().await;
            if let Some(cached_messages) = cache.get(conversation_id) {
                let limit = limit.unwrap_or(50) as usize;
                let messages = cached_messages
                    .iter()
                    .rev()
                    .take(limit)
                    .cloned()
                    .collect::<Vec<_>>();
                return Ok(messages);
            }
        }

        // 从数据库获取
        let messages = self
            .repository
            .get_conversation_messages(conversation_id, limit, offset)
            .await?;

        // 更新缓存 (只缓存最近的消息)
        if offset.unwrap_or(0) == 0 {
            let mut cache = self.message_cache.write().await;
            cache.insert(conversation_id.to_string(), messages.clone());
        }

        Ok(messages)
    }

    /// 更新消息状态
    pub async fn update_message_status(
        &self,
        message_id: &str,
        status: MessageStatus,
    ) -> Result<()> {
        self.repository
            .update_message_status(message_id, status)
            .await?;

        // 清理相关缓存 (简单实现：清理所有消息缓存)
        {
            let mut cache = self.message_cache.write().await;
            cache.clear();
        }

        Ok(())
    }

    /// 生成对话摘要
    pub async fn generate_conversation_summary(
        &self,
        conversation_id: &str,
    ) -> Result<ConversationSummary> {
        let messages = self
            .get_conversation_messages(conversation_id, Some(50), None)
            .await?;

        if messages.is_empty() {
            return Err(anyhow::anyhow!("No messages found in conversation"));
        }

        // 简单的摘要生成逻辑
        let user_messages: Vec<&Message> = messages
            .iter()
            .filter(|m| matches!(m.role, MessageRole::User))
            .collect();

        let key_questions: Vec<String> = user_messages
            .iter()
            .take(5)
            .map(|m| m.content.clone())
            .collect();

        let main_topics = self.extract_topics(&messages).await?;

        let summary = format!(
            "这个对话包含了 {} 条消息，主要讨论了 {} 等话题。",
            messages.len(),
            main_topics.join("、")
        );

        Ok(ConversationSummary {
            conversation_id: conversation_id.to_string(),
            title: format!("对话摘要 - {conversation_id}"),
            main_topics,
            key_questions,
            summary,
            generated_at: Utc::now(),
        })
    }

    /// 提取对话主题 (简单实现)
    async fn extract_topics(&self, messages: &[Message]) -> Result<Vec<String>> {
        // 简单的关键词提取
        let mut word_count: HashMap<String, usize> = HashMap::new();

        for message in messages {
            let words: Vec<&str> = message.content.split_whitespace().collect();
            for word in words {
                let word = word.to_lowercase();
                if word.len() > 3 {
                    // 只考虑长度大于3的词
                    *word_count.entry(word).or_insert(0) += 1;
                }
            }
        }

        let mut topics: Vec<(String, usize)> = word_count.into_iter().collect();
        topics.sort_by(|a, b| b.1.cmp(&a.1));

        Ok(topics.into_iter().take(5).map(|(word, _)| word).collect())
    }

    /// 获取对话统计
    pub async fn get_conversation_stats(&self, user_id: Option<&str>) -> Result<ConversationStats> {
        self.repository.get_conversation_stats(user_id).await
    }

    /// 清理缓存
    pub async fn clear_cache(&self) {
        {
            let mut conv_cache = self.conversation_cache.write().await;
            conv_cache.clear();
        }
        {
            let mut msg_cache = self.message_cache.write().await;
            msg_cache.clear();
        }
        info!("Conversation service cache cleared");
    }

    /// 设置对话状态
    pub async fn set_conversation_status(
        &self,
        conversation_id: &str,
        status: ConversationStatus,
    ) -> Result<()> {
        if let Some(mut conversation) = self.get_conversation(conversation_id).await? {
            conversation.status = status;
            conversation.updated_at = Utc::now();
            self.update_conversation(&conversation).await?;
        } else {
            return Err(anyhow::anyhow!(
                "Conversation not found: {}",
                conversation_id
            ));
        }
        Ok(())
    }

    /// 搜索对话
    pub async fn search_conversations(
        &self,
        user_id: &str,
        query: &str,
        limit: Option<u32>,
    ) -> Result<Vec<Conversation>> {
        // 简单的搜索实现：在标题中搜索
        let all_conversations = self
            .list_conversations(ConversationQuery {
                user_id: Some(user_id.to_string()),
                limit,
                ..Default::default()
            })
            .await?;

        let query_lower = query.to_lowercase();
        let filtered: Vec<Conversation> = all_conversations
            .into_iter()
            .filter(|conv| conv.title.to_lowercase().contains(&query_lower))
            .collect();

        Ok(filtered)
    }
}

impl Default for ConversationQuery {
    fn default() -> Self {
        Self {
            user_id: None,
            knowledge_base_id: None,
            status: None,
            offset: Some(0),
            limit: Some(20),
            sort_by: Some("last_active_at".to_string()),
            sort_order: Some("desc".to_string()),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_conversation_serialization() {
        let metadata = ConversationMetadata {
            title: Some("Test Conversation".to_string()),
            tags: vec!["test".to_string()],
            conversation_type: "chat".to_string(),
            custom_attributes: HashMap::new(),
        };

        let conversation = Conversation {
            id: "conv-123".to_string(),
            user_id: "user-456".to_string(),
            knowledge_base_id: Some("kb-789".to_string()),
            title: "Test".to_string(),
            status: ConversationStatus::Active,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            last_active_at: Utc::now(),
            message_count: 0,
            metadata,
        };

        let json = serde_json::to_string(&conversation).unwrap();
        let deserialized: Conversation = serde_json::from_str(&json).unwrap();
        assert_eq!(conversation.id, deserialized.id);
    }

    #[test]
    fn test_message_serialization() {
        let message = Message {
            id: "msg-123".to_string(),
            conversation_id: "conv-456".to_string(),
            role: MessageRole::User,
            content: "Hello".to_string(),
            created_at: Utc::now(),
            metadata: None,
            status: MessageStatus::Normal,
        };

        let json = serde_json::to_string(&message).unwrap();
        let deserialized: Message = serde_json::from_str(&json).unwrap();
        assert_eq!(message.content, deserialized.content);
    }
}
