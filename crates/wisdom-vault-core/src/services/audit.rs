use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::net::IpAddr;
use std::sync::Arc;
use tracing::{error, info};

/// 审计事件类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum AuditAction {
    // 认证相关
    Login,
    Logout,
    LoginFailed,

    // 用户管理
    UserCreate,
    UserUpdate,
    UserDelete,
    UserActivate,
    UserDeactivate,

    // 角色和权限
    RoleCreate,
    RoleUpdate,
    RoleDelete,
    RoleAssign,
    RoleUnassign,

    // 知识库管理
    KnowledgeBaseCreate,
    KnowledgeBaseUpdate,
    KnowledgeBaseDelete,

    // 文档管理
    DocumentCreate,
    DocumentUpdate,
    DocumentDelete,
    DocumentView,
    DocumentDownload,

    // 文件管理
    FileUpload,
    FileDownload,
    FileDelete,

    // 搜索操作
    Search,
    Chat,

    // 系统配置
    ConfigUpdate,

    // 其他
    Unknown(String),
}

impl std::fmt::Display for AuditAction {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Login => write!(f, "login"),
            Self::Logout => write!(f, "logout"),
            Self::LoginFailed => write!(f, "login_failed"),
            Self::UserCreate => write!(f, "user_create"),
            Self::UserUpdate => write!(f, "user_update"),
            Self::UserDelete => write!(f, "user_delete"),
            Self::UserActivate => write!(f, "user_activate"),
            Self::UserDeactivate => write!(f, "user_deactivate"),
            Self::RoleCreate => write!(f, "role_create"),
            Self::RoleUpdate => write!(f, "role_update"),
            Self::RoleDelete => write!(f, "role_delete"),
            Self::RoleAssign => write!(f, "role_assign"),
            Self::RoleUnassign => write!(f, "role_unassign"),
            Self::KnowledgeBaseCreate => write!(f, "knowledge_base_create"),
            Self::KnowledgeBaseUpdate => write!(f, "knowledge_base_update"),
            Self::KnowledgeBaseDelete => write!(f, "knowledge_base_delete"),
            Self::DocumentCreate => write!(f, "document_create"),
            Self::DocumentUpdate => write!(f, "document_update"),
            Self::DocumentDelete => write!(f, "document_delete"),
            Self::DocumentView => write!(f, "document_view"),
            Self::DocumentDownload => write!(f, "document_download"),
            Self::FileUpload => write!(f, "file_upload"),
            Self::FileDownload => write!(f, "file_download"),
            Self::FileDelete => write!(f, "file_delete"),
            Self::Search => write!(f, "search"),
            Self::Chat => write!(f, "chat"),
            Self::ConfigUpdate => write!(f, "config_update"),
            Self::Unknown(action) => write!(f, "{}", action),
        }
    }
}

/// 审计事件结果
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum AuditResult {
    Success,
    Failure,
    Denied,
}

impl std::fmt::Display for AuditResult {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Success => write!(f, "success"),
            Self::Failure => write!(f, "failure"),
            Self::Denied => write!(f, "denied"),
        }
    }
}

/// 审计事件详细信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditEvent {
    /// 事件ID
    pub event_id: String,

    /// 时间戳
    pub timestamp: DateTime<Utc>,

    /// 用户ID（如果已认证）
    pub user_id: Option<String>,

    /// 用户名（如果已认证）
    pub username: Option<String>,

    /// 会话ID
    pub session_id: Option<String>,

    /// 请求ID（用于关联请求）
    pub request_id: Option<String>,

    /// 操作类型
    pub action: AuditAction,

    /// 操作结果
    pub result: AuditResult,

    /// 资源类型
    pub resource_type: Option<String>,

    /// 资源ID
    pub resource_id: Option<String>,

    /// 客户端IP地址
    pub ip_address: Option<IpAddr>,

    /// 用户代理
    pub user_agent: Option<String>,

    /// 请求方法
    pub http_method: Option<String>,

    /// 请求路径
    pub request_path: Option<String>,

    /// HTTP状态码
    pub status_code: Option<u16>,

    /// 错误消息
    pub error_message: Option<String>,

    /// 额外的元数据
    pub metadata: serde_json::Value,

    /// 处理时间（毫秒）
    pub duration_ms: Option<u64>,
}

impl AuditEvent {
    /// 创建新的审计事件
    pub fn new(action: AuditAction, result: AuditResult) -> Self {
        Self {
            event_id: uuid::Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            user_id: None,
            username: None,
            session_id: None,
            request_id: None,
            action,
            result,
            resource_type: None,
            resource_id: None,
            ip_address: None,
            user_agent: None,
            http_method: None,
            request_path: None,
            status_code: None,
            error_message: None,
            metadata: serde_json::Value::Null,
            duration_ms: None,
        }
    }

    /// 设置用户信息
    pub fn with_user(mut self, user_id: Option<String>, username: Option<String>) -> Self {
        self.user_id = user_id;
        self.username = username;
        self
    }

    /// 设置会话信息
    pub fn with_session(mut self, session_id: Option<String>, request_id: Option<String>) -> Self {
        self.session_id = session_id;
        self.request_id = request_id;
        self
    }

    /// 设置资源信息
    pub fn with_resource(
        mut self,
        resource_type: Option<String>,
        resource_id: Option<String>,
    ) -> Self {
        self.resource_type = resource_type;
        self.resource_id = resource_id;
        self
    }

    /// 设置HTTP信息
    pub fn with_http(
        mut self,
        ip_address: Option<IpAddr>,
        user_agent: Option<String>,
        method: Option<String>,
        path: Option<String>,
        status_code: Option<u16>,
    ) -> Self {
        self.ip_address = ip_address;
        self.user_agent = user_agent;
        self.http_method = method;
        self.request_path = path;
        self.status_code = status_code;
        self
    }

    /// 设置错误信息
    pub fn with_error(mut self, error_message: Option<String>) -> Self {
        self.error_message = error_message;
        self
    }

    /// 设置元数据
    pub fn with_metadata(mut self, metadata: serde_json::Value) -> Self {
        self.metadata = metadata;
        self
    }

    /// 设置持续时间
    pub fn with_duration(mut self, duration_ms: Option<u64>) -> Self {
        self.duration_ms = duration_ms;
        self
    }
}

/// 审计日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditConfig {
    /// 是否启用审计
    pub enabled: bool,

    /// 日志级别过滤
    pub log_level: String,

    /// 是否记录成功的操作
    pub log_success: bool,

    /// 是否记录失败的操作
    pub log_failure: bool,

    /// 是否记录敏感操作
    pub log_sensitive: bool,

    /// 排除的路径模式
    pub exclude_paths: Vec<String>,

    /// 包含的操作类型
    pub include_actions: Vec<String>,
}

impl Default for AuditConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            log_level: "info".to_string(),
            log_success: true,
            log_failure: true,
            log_sensitive: true,
            exclude_paths: vec![
                "/health".to_string(),
                "/metrics".to_string(),
                "/swagger".to_string(),
            ],
            include_actions: vec![],
        }
    }
}

/// 审计日志服务
#[derive(Clone)]
pub struct AuditLogger {
    config: Arc<AuditConfig>,
}

impl AuditLogger {
    /// 创建新的审计日志服务
    pub fn new(config: AuditConfig) -> Self {
        Self {
            config: Arc::new(config),
        }
    }

    /// 记录审计事件
    pub async fn log_event(&self, event: AuditEvent) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        // 检查是否应该记录此事件
        if !self.should_log_event(&event) {
            return Ok(());
        }

        // 输出审计日志到专用target
        match event.result {
            AuditResult::Success if self.config.log_success => {
                info!(
                    target: "audit",
                    event = %serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string())
                );
            }
            AuditResult::Failure | AuditResult::Denied if self.config.log_failure => {
                error!(
                    target: "audit",
                    event = %serde_json::to_string(&event).unwrap_or_else(|_| "{}".to_string())
                );
            }
            _ => {}
        }

        Ok(())
    }

    /// 记录用户登录事件
    pub async fn log_login(
        &self,
        user_id: String,
        username: String,
        ip_address: Option<IpAddr>,
        result: AuditResult,
    ) -> Result<()> {
        let event = AuditEvent::new(AuditAction::Login, result)
            .with_user(Some(user_id), Some(username))
            .with_http(
                ip_address,
                None,
                Some("POST".to_string()),
                Some("/api/v1/auth/login".to_string()),
                None,
            );

        self.log_event(event).await
    }

    /// 记录用户登出事件
    pub async fn log_logout(
        &self,
        user_id: String,
        username: String,
        session_id: Option<String>,
    ) -> Result<()> {
        let event = AuditEvent::new(AuditAction::Logout, AuditResult::Success)
            .with_user(Some(user_id), Some(username))
            .with_session(session_id, None);

        self.log_event(event).await
    }

    /// 记录资源操作事件
    pub async fn log_resource_operation(
        &self,
        action: AuditAction,
        result: AuditResult,
        user_id: Option<String>,
        username: Option<String>,
        resource_type: Option<String>,
        resource_id: Option<String>,
        metadata: Option<serde_json::Value>,
    ) -> Result<()> {
        let event = AuditEvent::new(action, result)
            .with_user(user_id, username)
            .with_resource(resource_type, resource_id)
            .with_metadata(metadata.unwrap_or(serde_json::Value::Null));

        self.log_event(event).await
    }

    /// 检查是否应该记录此事件
    fn should_log_event(&self, event: &AuditEvent) -> bool {
        // 检查路径排除
        if let Some(path) = &event.request_path {
            for exclude_path in &self.config.exclude_paths {
                if path.starts_with(exclude_path) {
                    return false;
                }
            }
        }

        // 检查操作类型包含（如果配置了的话）
        if !self.config.include_actions.is_empty() {
            let action_str = event.action.to_string();
            if !self.config.include_actions.contains(&action_str) {
                return false;
            }
        }

        true
    }
}

/// 审计事件构建器
pub struct AuditEventBuilder {
    event: AuditEvent,
}

impl AuditEventBuilder {
    pub fn new(action: AuditAction) -> Self {
        Self {
            event: AuditEvent::new(action, AuditResult::Success),
        }
    }

    pub fn with_result(mut self, result: AuditResult) -> Self {
        self.event.result = result;
        self
    }

    pub fn with_user_info(mut self, user_id: Option<String>, username: Option<String>) -> Self {
        self.event.user_id = user_id;
        self.event.username = username;
        self
    }

    pub fn with_session_info(
        mut self,
        session_id: Option<String>,
        request_id: Option<String>,
    ) -> Self {
        self.event.session_id = session_id;
        self.event.request_id = request_id;
        self
    }

    pub fn with_resource_info(
        mut self,
        resource_type: Option<String>,
        resource_id: Option<String>,
    ) -> Self {
        self.event.resource_type = resource_type;
        self.event.resource_id = resource_id;
        self
    }

    pub fn with_http_info(
        mut self,
        ip_address: Option<IpAddr>,
        user_agent: Option<String>,
        method: Option<String>,
        path: Option<String>,
        status_code: Option<u16>,
    ) -> Self {
        self.event.ip_address = ip_address;
        self.event.user_agent = user_agent;
        self.event.http_method = method;
        self.event.request_path = path;
        self.event.status_code = status_code;
        self
    }

    pub fn with_error_info(mut self, error_message: Option<String>) -> Self {
        self.event.error_message = error_message;
        self
    }

    pub fn with_metadata_value(mut self, metadata: serde_json::Value) -> Self {
        self.event.metadata = metadata;
        self
    }

    pub fn with_duration_ms(mut self, duration_ms: u64) -> Self {
        self.event.duration_ms = Some(duration_ms);
        self
    }

    pub fn build(self) -> AuditEvent {
        self.event
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::Ipv4Addr;

    #[test]
    fn test_audit_event_creation() {
        let event = AuditEvent::new(AuditAction::Login, AuditResult::Success);
        assert_eq!(event.action, AuditAction::Login);
        assert_eq!(event.result, AuditResult::Success);
        assert!(!event.event_id.is_empty());
    }

    #[test]
    fn test_audit_event_builder() {
        let event = AuditEventBuilder::new(AuditAction::DocumentCreate)
            .with_result(AuditResult::Success)
            .with_user_info(Some("user123".to_string()), Some("testuser".to_string()))
            .with_resource_info(Some("document".to_string()), Some("doc123".to_string()))
            .with_http_info(
                Some(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1))),
                Some("Mozilla/5.0".to_string()),
                Some("POST".to_string()),
                Some("/api/v1/documents".to_string()),
                Some(201),
            )
            .build();

        assert_eq!(event.action, AuditAction::DocumentCreate);
        assert_eq!(event.result, AuditResult::Success);
        assert_eq!(event.user_id, Some("user123".to_string()));
        assert_eq!(event.resource_type, Some("document".to_string()));
        assert_eq!(event.status_code, Some(201));
    }

    #[tokio::test]
    async fn test_audit_logger() {
        let config = AuditConfig::default();
        let logger = AuditLogger::new(config);

        let event = AuditEvent::new(AuditAction::Login, AuditResult::Success)
            .with_user(Some("user123".to_string()), Some("testuser".to_string()));

        let result = logger.log_event(event).await;
        assert!(result.is_ok());
    }
}
