use anyhow::Result;
use std::{collections::HashMap, sync::Arc};
use wisdom_vault_database::{
    models::{Category, Document, DocumentCategory},
    repositories::{CategoryRepository, DocumentCategoryRepository, DocumentRepository},
};

/// 分类统计摘要
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ClassificationSummary {
    pub total_categories: i64,
    pub total_classified_documents: i64,
    pub total_documents: i64,
    pub uncategorized_documents: i64,
    pub avg_documents_per_category: f64,
    pub most_used_category: Option<(String, i64)>,
    pub least_used_category: Option<(String, i64)>,
}

/// 分类结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ClassificationResult {
    pub document_id: String,
    pub predicted_category: String,
    pub confidence: f64,
    pub alternative_categories: Vec<(String, f64)>,
}

/// 文档分类服务
/// 提供基于内容的智能文档分类功能
pub struct DocumentClassificationService {
    document_repo: Arc<dyn DocumentRepository + Send + Sync>,
    category_repo: Arc<dyn CategoryRepository + Send + Sync>,
    document_category_repo: Arc<dyn DocumentCategoryRepository + Send + Sync>,
}

impl DocumentClassificationService {
    pub fn new(
        document_repo: Arc<dyn DocumentRepository + Send + Sync>,
        category_repo: Arc<dyn CategoryRepository + Send + Sync>,
        document_category_repo: Arc<dyn DocumentCategoryRepository + Send + Sync>,
    ) -> Self {
        Self {
            document_repo,
            category_repo,
            document_category_repo,
        }
    }

    /// 自动分类文档
    pub async fn classify_document(
        &self,
        document_id: &str,
        user_id: &str,
    ) -> Result<Vec<DocumentCategory>> {
        // 获取文档
        let document = self
            .document_repo
            .find_by_id(document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Document not found"))?;

        // 获取知识库的所有分类
        let categories = self
            .category_repo
            .find_by_knowledge_base(&document.knowledge_base_id)
            .await?;

        if categories.is_empty() {
            return Ok(Vec::new());
        }

        // 基于内容进行分类
        let classification_scores = self
            .classify_by_content(&document, &categories)
            .await?;

        // 转换为DocumentCategory
        let mut document_categories = Vec::new();
        for (category_id, score) in classification_scores {
            if score >= 0.5 {
                // 只保留置信度较高的分类
                let doc_category = self
                    .document_category_repo
                    .assign_category(document_id, &category_id, user_id, Some(score))
                    .await?;
                document_categories.push(doc_category);
            }
        }

        Ok(document_categories)
    }

    /// 基于文档内容的分类（用于文档处理管道）
    pub async fn classify_document_by_content(
        &self,
        document_id: &str,
        title: &str,
        content: &str,
    ) -> Result<ClassificationResult> {
        // 如果没有内容，返回默认结果
        if content.trim().is_empty() {
            return Ok(ClassificationResult {
                document_id: document_id.to_string(),
                predicted_category: "未分类".to_string(),
                confidence: 0.0,
                alternative_categories: Vec::new(),
            });
        }

        // 获取文档所属的知识库
        let document = self
            .document_repo
            .find_by_id(document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Document not found"))?;

        // 获取知识库的所有分类
        let categories = self
            .category_repo
            .find_by_knowledge_base(&document.knowledge_base_id)
            .await?;

        if categories.is_empty() {
            return Ok(ClassificationResult {
                document_id: document_id.to_string(),
                predicted_category: "默认分类".to_string(),
                confidence: 0.5,
                alternative_categories: Vec::new(),
            });
        }

        // 基于标题和内容进行分类
        let mut classification_scores = Vec::new();
        
        for category in &categories {
            let score = self.calculate_classification_score(title, content, category).await;
            classification_scores.push((category.clone(), score));
        }

        // 按分数排序
        classification_scores.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        let (best_category, best_score) = classification_scores
            .first()
            .map(|(cat, score)| (cat.clone(), *score))
            .unwrap_or_else(|| {
                let default_category = categories[0].clone();
                (default_category, 0.5)
            });

        // 提取备选分类（前3个，排除最佳分类）
        let alternative_categories: Vec<(String, f64)> = classification_scores
            .iter()
            .skip(1)
            .take(3)
            .map(|(cat, score)| (cat.name.clone(), *score))
            .collect();

        Ok(ClassificationResult {
            document_id: document_id.to_string(),
            predicted_category: best_category.name,
            confidence: best_score,
            alternative_categories,
        })
    }

    /// 计算分类分数
    async fn calculate_classification_score(&self, title: &str, content: &str, category: &Category) -> f64 {
        // 简单的关键词匹配分类算法
        let combined_text = format!("{} {}", title, content).to_lowercase();
        let category_keywords = category.name.to_lowercase();
        
        // 基础分数：分类名称在文本中的匹配度
        let mut score = 0.0;
        
        // 检查分类名称是否在标题中
        if title.to_lowercase().contains(&category_keywords) {
            score += 0.8;
        }
        
        // 检查分类名称是否在内容中
        if content.to_lowercase().contains(&category_keywords) {
            score += 0.6;
        }
        
        // 如果分类有描述，也进行匹配
        if let Some(ref description) = category.description {
            let desc_lower = description.to_lowercase();
            let desc_keywords: Vec<&str> = desc_lower.split_whitespace().collect();
            let text_words: Vec<&str> = combined_text.split_whitespace().collect();
            
            let matching_words = desc_keywords.iter()
                .filter(|keyword| text_words.contains(keyword))
                .count();
            
            if !desc_keywords.is_empty() {
                score += (matching_words as f64 / desc_keywords.len() as f64) * 0.4;
            }
        }
        
        // 确保分数在0-1范围内
        score.min(1.0)
    }

    /// 批量分类文档
    pub async fn batch_classify_documents(
        &self,
        document_ids: Vec<String>,
        user_id: &str,
    ) -> Result<Vec<DocumentCategory>> {
        let mut all_results = Vec::new();

        for document_id in document_ids {
            match self.classify_document(&document_id, user_id).await {
                Ok(mut results) => all_results.append(&mut results),
                Err(e) => {
                    tracing::warn!("Failed to classify document {}: {}", document_id, e);
                    continue;
                }
            }
        }

        Ok(all_results)
    }

    /// 推荐分类
    pub async fn suggest_categories(
        &self,
        document_id: &str,
        max_suggestions: usize,
    ) -> Result<Vec<(Category, f64)>> {
        // 获取文档
        let document = self
            .document_repo
            .find_by_id(document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Document not found"))?;

        // 获取知识库的所有分类
        let categories = self
            .category_repo
            .find_by_knowledge_base(&document.knowledge_base_id)
            .await?;

        if categories.is_empty() {
            return Ok(Vec::new());
        }

        // 基于内容进行分类
        let classifications = self.classify_by_content(&document, &categories).await?;

        // 按置信度排序并限制数量
        let mut suggestions: Vec<(Category, f64)> = classifications
            .into_iter()
            .filter_map(|(category_id, confidence)| {
                categories
                    .iter()
                    .find(|c| c.id == category_id)
                    .map(|c| (c.clone(), confidence))
            })
            .collect();

        suggestions.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        suggestions.truncate(max_suggestions);

        Ok(suggestions)
    }

    /// 基于相似文档的分类推荐
    pub async fn classify_by_similar_documents(
        &self,
        document_id: &str,
        user_id: &str,
    ) -> Result<Vec<DocumentCategory>> {
        let document = self
            .document_repo
            .find_by_id(document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Document not found"))?;

        // 简单实现：基于文件类型找相似文档
        let similar_documents = self
            .document_repo
            .find_by_file_type(&document.file_type, Some(&document.knowledge_base_id))
            .await?;

        // 统计相似文档的分类
        let mut category_counts: HashMap<String, usize> = HashMap::new();
        let mut total_docs = 0;

        for similar_doc in similar_documents {
            if similar_doc.id == document_id {
                continue; // 跳过自己
            }

            let categories = self
                .document_category_repo
                .find_document_categories(&similar_doc.id)
                .await?;

            total_docs += 1;
            for category in categories {
                *category_counts.entry(category.id).or_insert(0) += 1;
            }
        }

        if total_docs == 0 {
            return Ok(Vec::new());
        }

        // 基于频率计算置信度
        let mut results = Vec::new();
        for (category_id, count) in category_counts {
            let confidence = count as f64 / total_docs as f64;
            if confidence >= 0.3 {
                // 只选择置信度 >= 30% 的分类
                let doc_category = self
                    .document_category_repo
                    .categorize_document(document_id, &category_id, user_id, Some(confidence))
                    .await?;
                results.push(doc_category);
            }
        }

        Ok(results)
    }

    /// 手动为文档分配分类
    pub async fn manually_categorize_document(
        &self,
        document_id: &str,
        category_id: &str,
        user_id: &str,
        confidence_score: Option<f64>,
    ) -> Result<DocumentCategory> {
        self.document_category_repo
            .categorize_document(document_id, category_id, user_id, confidence_score)
            .await
    }

    /// 更新文档分类
    pub async fn update_document_classification(
        &self,
        document_id: &str,
        category_id: &str,
        confidence_score: f64,
    ) -> Result<bool> {
        self.document_category_repo
            .update_category_confidence(document_id, category_id, confidence_score)
            .await
    }

    /// 根据分类查找文档
    pub async fn find_documents_by_categories(
        &self,
        category_ids: &[&str],
        _match_all: bool,
    ) -> Result<Vec<Document>> {
        // 首先获取分类关联的文档ID
        let category_ids_string: Vec<String> =
            category_ids.iter().map(|&s| s.to_string()).collect();
        let document_categories = self
            .document_category_repo
            .find_documents_by_categories(&category_ids_string)
            .await?;

        // 提取文档ID
        let document_ids: Vec<String> = document_categories
            .into_iter()
            .map(|dc| dc.document_id)
            .collect();

        // 获取实际文档
        let mut documents = Vec::new();
        for doc_id in document_ids {
            if let Some(doc) = self.document_repo.find_by_id(&doc_id).await? {
                documents.push(doc);
            }
        }

        Ok(documents)
    }

    /// 移除文档分类
    pub async fn remove_document_classification(
        &self,
        document_id: &str,
        category_id: &str,
    ) -> Result<bool> {
        self.document_category_repo
            .remove_category(document_id, category_id)
            .await
    }

    /// 获取文档的所有分类
    pub async fn get_document_categories(&self, document_id: &str) -> Result<Vec<Category>> {
        let document_categories = self
            .document_category_repo
            .find_document_categories(document_id)
            .await?;

        let mut categories = Vec::new();
        for doc_cat in document_categories {
            if let Ok(Some(category)) = self.category_repo.find_by_id(&doc_cat.category_id).await {
                categories.push(category);
            }
        }
        Ok(categories)
    }

    /// 获取分类统计
    pub async fn get_category_statistics(&self, kb_id: &str) -> Result<Vec<(Category, i64)>> {
        // 获取知识库的所有分类
        let categories = self.category_repo.find_by_knowledge_base(kb_id).await?;
        
        let mut category_stats = Vec::new();
        
        // 为每个分类统计文档数量
        for category in categories {
            // 查找该分类下的所有文档分类关系
            let document_categories = self.document_category_repo
                .find_by_category(&category.id)
                .await?;
            
            let document_count = document_categories.len() as i64;
            category_stats.push((category, document_count));
        }
        
        // 按文档数量降序排序
        category_stats.sort_by(|a, b| b.1.cmp(&a.1));
        
        tracing::info!(
            "分类统计完成: 知识库={}, 分类数={}, 总文档分类关系={}",
            kb_id,
            category_stats.len(),
            category_stats.iter().map(|(_, count)| count).sum::<i64>()
        );
        
        Ok(category_stats)
    }

    /// 获取分类统计摘要
    pub async fn get_classification_summary(&self, kb_id: &str) -> Result<ClassificationSummary> {
        let category_stats = self.get_category_statistics(kb_id).await?;
        
        let total_categories = category_stats.len();
        let total_classified_documents: i64 = category_stats.iter().map(|(_, count)| count).sum();
        
        let most_used_category = category_stats.first().cloned();
        let least_used_category = category_stats.last().cloned();
        
        // 计算平均每个分类的文档数
        let avg_documents_per_category = if total_categories > 0 {
            total_classified_documents as f64 / total_categories as f64
        } else {
            0.0
        };
        
        // 统计未分类的文档数量（这需要额外查询）
        let all_documents = self.document_repo.find_by_knowledge_base(kb_id, None, None).await?;
        let uncategorized_documents = {
            let mut uncategorized_count = 0;
            for document in &all_documents {
                let categories = self.document_category_repo.find_by_document(&document.id).await?;
                if categories.is_empty() {
                    uncategorized_count += 1;
                }
            }
            uncategorized_count
        };
        
        Ok(ClassificationSummary {
            total_categories: total_categories as i64,
            total_classified_documents,
            total_documents: all_documents.len() as i64,
            uncategorized_documents,
            avg_documents_per_category,
            most_used_category: most_used_category.map(|(category, count)| (category.name, count)),
            least_used_category: least_used_category.map(|(category, count)| (category.name, count)),
        })
    }

    /// 基于内容的分类实现
    async fn classify_by_content(
        &self,
        document: &Document,
        categories: &[Category],
    ) -> Result<Vec<(String, f64)>> {
        let mut classifications = Vec::new();

        let content = format!("{} {}", document.title, document.content);
        let content_lower = content.to_lowercase();

        for category in categories {
            let mut score = 0.0;
            let category_name_lower = category.name.to_lowercase();

            // 基于分类名称的匹配
            if content_lower.contains(&category_name_lower) {
                score += 0.8;
            }

            // 基于描述的匹配
            if let Some(ref description) = category.description {
                let desc_lower = description.to_lowercase();
                let desc_words: Vec<&str> = desc_lower.split_whitespace().collect();
                for word in desc_words {
                    if word.len() > 3 && content_lower.contains(word) {
                        score += 0.2;
                    }
                }
            }

            // 基于文件类型的简单规则
            score += self.calculate_file_type_score(&document.file_type, &category.name);

            // 基于关键词的匹配
            score += self.calculate_keyword_score(&document.metadata.keywords, &category.name);

            // 归一化分数
            if score > 1.0 {
                score = 1.0;
            }

            // 只保留有一定置信度的分类
            if score >= 0.3 {
                classifications.push((category.id.clone(), score));
            }
        }

        // 按置信度排序
        classifications.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        Ok(classifications)
    }

    /// 基于文件类型计算分数
    fn calculate_file_type_score(&self, file_type: &str, category_name: &str) -> f64 {
        let file_type_lower = file_type.to_lowercase();
        let category_lower = category_name.to_lowercase();

        // 简单的文件类型匹配规则
        match file_type_lower.as_str() {
            "pdf" | "doc" | "docx" => {
                if category_lower.contains("文档") || category_lower.contains("document") {
                    return 0.3;
                }
            }
            "jpg" | "jpeg" | "png" | "gif" => {
                if category_lower.contains("图片") || category_lower.contains("image") {
                    return 0.3;
                }
            }
            "mp4" | "avi" | "mov" => {
                if category_lower.contains("视频") || category_lower.contains("video") {
                    return 0.3;
                }
            }
            "mp3" | "wav" | "flac" => {
                if category_lower.contains("音频") || category_lower.contains("audio") {
                    return 0.3;
                }
            }
            "txt" | "md" => {
                if category_lower.contains("文本") || category_lower.contains("text") {
                    return 0.3;
                }
            }
            "xls" | "xlsx" | "csv" => {
                if category_lower.contains("表格") || category_lower.contains("spreadsheet") {
                    return 0.3;
                }
            }
            "ppt" | "pptx" => {
                if category_lower.contains("演示") || category_lower.contains("presentation") {
                    return 0.3;
                }
            }
            _ => {}
        }

        0.0
    }

    /// 基于关键词计算分数
    fn calculate_keyword_score(&self, keywords: &[String], category_name: &str) -> f64 {
        if keywords.is_empty() {
            return 0.0;
        }

        let category_lower = category_name.to_lowercase();
        let mut matches = 0;

        for keyword in keywords {
            let keyword_lower = keyword.to_lowercase();
            if category_lower.contains(&keyword_lower) || keyword_lower.contains(&category_lower) {
                matches += 1;
            }
        }

        // 计算匹配比例
        let match_ratio = matches as f64 / keywords.len() as f64;
        match_ratio * 0.5 // 最多贡献0.5分
    }
}

/// 分类统计信息
#[derive(Debug, Clone)]
pub struct ClassificationStatistics {
    pub total_documents: i64,
    pub classified_documents: i64,
    pub unclassified_documents: i64,
    pub categories_with_documents: i64,
    pub average_classifications_per_document: f64,
}

impl DocumentClassificationService {
    /// 获取分类统计信息
    pub async fn get_classification_statistics(
        &self,
        kb_id: &str,
    ) -> Result<ClassificationStatistics> {
        let total_documents = self.document_repo.count().await?;
        let category_counts = self.get_category_statistics(kb_id).await?;

        let categories_with_documents = category_counts
            .iter()
            .filter(|(_, count)| *count > 0)
            .count() as i64;
        let classified_documents = category_counts.iter().map(|(_, count)| *count).sum::<i64>();
        let unclassified_documents = total_documents - classified_documents;

        let average_classifications_per_document = if total_documents > 0 {
            classified_documents as f64 / total_documents as f64
        } else {
            0.0
        };

        Ok(ClassificationStatistics {
            total_documents,
            classified_documents,
            unclassified_documents,
            categories_with_documents,
            average_classifications_per_document,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_calculate_file_type_score() {
        let service = DocumentClassificationService::new(
            Arc::new(MockDocumentRepository),
            Arc::new(MockCategoryRepository),
            Arc::new(MockDocumentCategoryRepository),
        );

        // Test PDF file type matching
        assert_eq!(service.calculate_file_type_score("pdf", "文档分类"), 0.3);
        assert_eq!(service.calculate_file_type_score("pdf", "其他分类"), 0.0);

        // Test image file type matching
        assert_eq!(service.calculate_file_type_score("jpg", "图片分类"), 0.3);
        assert_eq!(service.calculate_file_type_score("png", "image"), 0.3);
    }

    #[test]
    fn test_calculate_keyword_score() {
        let service = DocumentClassificationService::new(
            Arc::new(MockDocumentRepository),
            Arc::new(MockCategoryRepository),
            Arc::new(MockDocumentCategoryRepository),
        );

        let keywords = vec!["技术".to_string(), "文档".to_string()];
        assert!(service.calculate_keyword_score(&keywords, "技术文档") > 0.0);
        assert_eq!(service.calculate_keyword_score(&keywords, "其他分类"), 0.0);
    }

    // Mock implementations for testing
    struct MockDocumentRepository;
    struct MockCategoryRepository;
    struct MockDocumentCategoryRepository;

    #[async_trait::async_trait]
    impl DocumentRepository for MockDocumentRepository {
        async fn create(&self, _document: &Document) -> Result<Document> {
            unimplemented!()
        }
        async fn find_by_id(&self, _id: &str) -> Result<Option<Document>> {
            unimplemented!()
        }
        async fn find_by_knowledge_base(&self, _kb_id: &str) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn update(&self, _document: &Document) -> Result<Document> {
            unimplemented!()
        }
        async fn delete(&self, _id: &str) -> Result<bool> {
            unimplemented!()
        }
        async fn search(&self, _query: &str, _kb_id: Option<&str>) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_status(
            &self,
            _status: wisdom_vault_database::models::DocumentStatus,
            _kb_id: Option<&str>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_author(
            &self,
            _author: &str,
            _kb_id: Option<&str>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_file_type(
            &self,
            _file_type: &str,
            _kb_id: Option<&str>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn list_with_pagination(
            &self,
            _kb_id: Option<&str>,
            _limit: u32,
            _offset: u32,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn count(&self, _kb_id: Option<&str>) -> Result<i64> {
            unimplemented!()
        }
        async fn count_by_status(
            &self,
            _status: wisdom_vault_database::models::DocumentStatus,
            _kb_id: Option<&str>,
        ) -> Result<i64> {
            unimplemented!()
        }
        async fn update_status(
            &self,
            _id: &str,
            _status: wisdom_vault_database::models::DocumentStatus,
        ) -> Result<bool> {
            unimplemented!()
        }
        async fn update_processing_metadata(
            &self,
            _id: &str,
            _metadata: &wisdom_vault_database::models::DocumentProcessingMetadata,
        ) -> Result<bool> {
            unimplemented!()
        }
        async fn find_by_checksum(&self, _checksum: &str) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn batch_update_status(
            &self,
            _ids: Vec<String>,
            _status: wisdom_vault_database::models::DocumentStatus,
        ) -> Result<u64> {
            unimplemented!()
        }
        async fn find_failed_documents(&self, _kb_id: Option<&str>) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_processing_documents(&self, _kb_id: Option<&str>) -> Result<Vec<Document>> {
            unimplemented!()
        }
    }

    #[async_trait::async_trait]
    impl CategoryRepository for MockCategoryRepository {
        async fn create(&self, _category: &Category) -> Result<Category> {
            unimplemented!()
        }
        async fn find_by_id(&self, _id: &str) -> Result<Option<Category>> {
            unimplemented!()
        }
        async fn find_by_knowledge_base(&self, _kb_id: &str) -> Result<Vec<Category>> {
            unimplemented!()
        }
        async fn find_by_parent(&self, _parent_id: &str) -> Result<Vec<Category>> {
            unimplemented!()
        }
        async fn update(&self, _category: &Category) -> Result<Category> {
            unimplemented!()
        }
        async fn delete(&self, _id: &str) -> Result<bool> {
            unimplemented!()
        }
        async fn get_hierarchy(&self, _kb_id: &str) -> Result<Vec<Category>> {
            unimplemented!()
        }
    }

    #[async_trait::async_trait]
    impl DocumentCategoryRepository for MockDocumentCategoryRepository {
        async fn categorize_document(
            &self,
            _document_id: &str,
            _category_id: &str,
            _assigned_by: &str,
            _confidence_score: Option<f64>,
        ) -> Result<DocumentCategory> {
            unimplemented!()
        }
        async fn uncategorize_document(
            &self,
            _document_id: &str,
            _category_id: &str,
        ) -> Result<bool> {
            unimplemented!()
        }
        async fn find_document_categories(&self, _document_id: &str) -> Result<Vec<Category>> {
            unimplemented!()
        }
        async fn find_categorized_documents(&self, _category_id: &str) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn batch_categorize_documents(
            &self,
            _document_ids: &[&str],
            _category_id: &str,
            _assigned_by: &str,
        ) -> Result<Vec<DocumentCategory>> {
            unimplemented!()
        }
        async fn batch_uncategorize_documents(
            &self,
            _document_ids: &[&str],
            _category_id: &str,
        ) -> Result<u64> {
            unimplemented!()
        }
        async fn find_documents_by_categories(
            &self,
            _category_ids: &[&str],
            _match_all: bool,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn get_category_document_counts(&self, _kb_id: &str) -> Result<Vec<(Category, i64)>> {
            unimplemented!()
        }
        async fn update_category_confidence(
            &self,
            _document_id: &str,
            _category_id: &str,
            _confidence_score: f64,
        ) -> Result<bool> {
            unimplemented!()
        }
        async fn cleanup_orphaned_document_categories(&self) -> Result<u64> {
            unimplemented!()
        }
    }
}
