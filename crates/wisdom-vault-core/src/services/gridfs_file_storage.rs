use anyhow::{Result, anyhow};
use async_trait::async_trait;
use futures::{AsyncReadExt, AsyncWriteExt};
use mongodb::gridfs::GridFsBucket;
use mongodb::options::GridFsBucketOptions;
use mongodb::{Database, bson};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::io::AsyncRead;
use wisdom_vault_common::{db::next_id, time::current_millis};

use crate::{FileMetadata, StorageConfigInfo, StorageUsage};

use super::file_storage_trait::{FileStorageServiceTrait, StorageStats};

#[derive(Debug, Clone)]
pub struct GridFsStorageConfig {
    pub bucket_name: String,
    pub max_file_size: u64,
    pub allowed_extensions: Vec<String>,
    pub allowed_mime_types: Vec<String>,
    pub chunk_size: Option<u32>,
}

impl Default for GridFsStorageConfig {
    fn default() -> Self {
        Self {
            bucket_name: "file_storage".to_string(),
            max_file_size: 100 * 1024 * 1024, // 100MB
            allowed_extensions: vec![
                "pdf".to_string(),
                "doc".to_string(),
                "docx".to_string(),
                "txt".to_string(),
                "md".to_string(),
                "html".to_string(),
                "htm".to_string(),
                "rtf".to_string(),
                "odt".to_string(),
                "ppt".to_string(),
                "pptx".to_string(),
                "xls".to_string(),
                "xlsx".to_string(),
            ],
            allowed_mime_types: vec![
                "application/pdf".to_string(),
                "application/msword".to_string(),
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    .to_string(),
                "text/plain".to_string(),
                "text/markdown".to_string(),
                "text/html".to_string(),
                "application/rtf".to_string(),
                "application/vnd.oasis.opendocument.text".to_string(),
                "application/vnd.ms-powerpoint".to_string(),
                "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                    .to_string(),
                "application/vnd.ms-excel".to_string(),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".to_string(),
            ],
            chunk_size: Some(255 * 1024), // 255KB chunks
        }
    }
}

impl GridFsStorageConfig {
    pub fn from_app_config_file_storage(
        bucket_name: &str,
        max_file_size: u64,
        allowed_extensions: &[String],
        chunk_size_kb: Option<u32>,
    ) -> Self {
        Self {
            bucket_name: bucket_name.to_string(),
            max_file_size,
            allowed_extensions: allowed_extensions.to_vec(),
            allowed_mime_types: Self::extensions_to_mime_types(allowed_extensions),
            chunk_size: chunk_size_kb.map(|kb| kb * 1024),
        }
    }

    fn extensions_to_mime_types(extensions: &[String]) -> Vec<String> {
        let mut mime_types = Vec::new();
        for ext in extensions {
            match ext.as_str() {
                "pdf" => mime_types.push("application/pdf".to_string()),
                "doc" => mime_types.push("application/msword".to_string()),
                "docx" => mime_types.push(
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                        .to_string(),
                ),
                "txt" => mime_types.push("text/plain".to_string()),
                "md" => mime_types.push("text/markdown".to_string()),
                "html" => mime_types.push("text/html".to_string()),
                "ppt" => mime_types.push("application/vnd.ms-powerpoint".to_string()),
                "pptx" => mime_types.push(
                    "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                        .to_string(),
                ),
                "xls" => mime_types.push("application/vnd.ms-excel".to_string()),
                "xlsx" => mime_types.push(
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".to_string(),
                ),
                _ => {}
            }
        }
        mime_types
    }
}

pub struct GridFsFileStorageService {
    bucket: GridFsBucket,
    config: GridFsStorageConfig,
}

impl GridFsFileStorageService {
    pub fn new(database: &Database, config: GridFsStorageConfig) -> Result<Self> {
        // let wc = WriteConcern::builder()
        //     .w_timeout(Duration::new(5, 0))
        //     .build();
        let opts = GridFsBucketOptions::builder()
            .bucket_name(config.bucket_name.clone())
            .chunk_size_bytes(config.chunk_size)
            // .write_concern(wc)
            .build();
        let bucket = database.gridfs_bucket(opts);

        Ok(Self { bucket, config })
    }

    fn calculate_checksum(&self, data: &[u8]) -> String {
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{Hash, Hasher},
        };

        let mut hasher = DefaultHasher::new();
        data.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridFsStorageStats {
    pub bucket_name: String,
    pub max_file_size: u64,
    pub allowed_extensions: Vec<String>,
    pub allowed_mime_types: Vec<String>,
    pub chunk_size: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridFsStorageUsage {
    pub total_size: u64,
    pub file_count: u64,
    pub extension_counts: HashMap<String, u64>,
    pub last_calculated: i64,
}

// 为 GridFsFileStorageService 实现统一的存储接口
#[async_trait]
impl FileStorageServiceTrait for GridFsFileStorageService {
    async fn store_file(
        &self,
        file_data: Vec<u8>,
        original_filename: &str,
        mime_type: &str,
        uploaded_by: String,
    ) -> Result<FileMetadata> {
        // 验证文件大小
        if file_data.len() as u64 > self.config.max_file_size {
            return Err(anyhow!("文件大小超过最大允许大小"));
        }

        // 验证文件类型
        self.validate_file_type(original_filename, mime_type)?;

        // 生成唯一文件ID
        let file_id = next_id();
        let checksum = self.calculate_checksum(&file_data);

        // 创建文件元数据
        let mut metadata = bson::Document::new();
        metadata.insert("file_id", &file_id);
        metadata.insert("mime_type", mime_type);
        metadata.insert("uploaded_by", &uploaded_by);
        metadata.insert("uploaded_at", current_millis());
        metadata.insert("checksum", &checksum);

        // 使用上传流方式
        let mut upload_stream = self
            .bucket
            .open_upload_stream(original_filename)
            .metadata(metadata)
            .await?;

        upload_stream
            .write_all(&file_data)
            .await
            .map_err(|e| anyhow!("写入文件数据失败: {}", e))?;

        let gridfs_file_id = upload_stream.id().as_object_id().unwrap();
        upload_stream.close().await?;

        Ok(FileMetadata {
            file_id,
            original_filename: original_filename.to_string(),
            gridfs_file_id,
            file_size: file_data.len() as u64,
            mime_type: mime_type.to_string(),
            checksum,
            uploaded_by,
            uploaded_at: current_millis(),
        })
    }

    async fn retrieve_file(&self, file_metadata: &FileMetadata) -> Result<Vec<u8>> {
        let gridfs_file_id = file_metadata.gridfs_file_id;

        // 使用下载流方式
        let mut download_stream = self
            .bucket
            .open_download_stream(bson::Bson::ObjectId(gridfs_file_id))
            .await
            .map_err(|e| anyhow!("打开 GridFS 下载流失败: {}", e))?;

        let mut buffer = Vec::new();
        download_stream
            .read_to_end(&mut buffer)
            .await
            .map_err(|e| anyhow!("读取文件数据失败: {}", e))?;

        Ok(buffer)
    }

    async fn retrieve_file_stream(
        &self,
        file_metadata: &FileMetadata,
    ) -> Result<Box<dyn AsyncRead + Send + Sync + Unpin>> {
        // 对于 GridFS，我们先下载到内存，然后创建一个 Cursor
        let data = self.retrieve_file(file_metadata).await?;
        use std::io::Cursor;
        Ok(Box::new(Cursor::new(data)))
    }

    async fn delete_file(&self, file_metadata: &FileMetadata) -> Result<bool> {
        let gridfs_file_id = file_metadata.gridfs_file_id;

        match self
            .bucket
            .delete(bson::Bson::ObjectId(gridfs_file_id))
            .await
        {
            Ok(_) => Ok(true),
            Err(e) => {
                // 检查是否是文件不存在的错误
                if e.to_string().contains("FileNotFound") {
                    Ok(false)
                } else {
                    Err(anyhow!("删除GridFS文件失败: {}", e))
                }
            }
        }
    }

    async fn file_exists(&self, file_metadata: &FileMetadata) -> bool {
        let gridfs_file_id = file_metadata.gridfs_file_id;

        match self
            .bucket
            .find_one(bson::doc! { "_id": bson::Bson::ObjectId(gridfs_file_id) })
            .await
        {
            Ok(Some(_)) => true,
            _ => false,
        }
    }

    fn validate_file_type(&self, filename: &str, mime_type: &str) -> Result<()> {
        // 检查扩展名
        let extension = std::path::Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        if !self.config.allowed_extensions.contains(&extension) {
            return Err(anyhow!("不允许的文件扩展名: {}", extension));
        }

        // 检查MIME类型
        if !self
            .config
            .allowed_mime_types
            .contains(&mime_type.to_string())
        {
            return Err(anyhow!("不允许的MIME类型: {}", mime_type));
        }

        Ok(())
    }

    async fn get_storage_usage(&self) -> Result<StorageUsage> {
        let mut total_size = 0u64;
        let mut file_count = 0u64;
        let mut extension_counts: HashMap<String, u64> = HashMap::new();

        // 查询所有文件
        let mut cursor = self
            .bucket
            .find(bson::doc! {})
            .await
            .map_err(|e| anyhow!("查询GridFS文件失败: {}", e))?;

        use futures::TryStreamExt;
        while let Some(file_doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历GridFS文件失败: {}", e))?
        {
            total_size += file_doc.length as u64;
            file_count += 1;

            // 统计扩展名
            if let Some(filename) = &file_doc.filename {
                if let Some(extension) = std::path::Path::new(filename)
                    .extension()
                    .and_then(|ext| ext.to_str())
                {
                    let ext_lower = extension.to_lowercase();
                    *extension_counts.entry(ext_lower).or_insert(0) += 1;
                }
            }
        }

        Ok(StorageUsage {
            total_size,
            file_count,
            extension_counts,
            last_calculated: current_millis(),
        })
    }

    fn get_storage_stats(&self) -> StorageStats {
        StorageStats {
            max_file_size: self.config.max_file_size,
            allowed_extensions: self.config.allowed_extensions.clone(),
            allowed_mime_types: self.config.allowed_mime_types.clone(),
            storage_config: StorageConfigInfo {
                bucket_name: self.config.bucket_name.clone(),
                chunk_size: self.config.chunk_size,
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use mongodb::{Client, options::ClientOptions};

    async fn create_test_storage() -> Result<GridFsFileStorageService> {
        // 这需要一个运行的MongoDB实例进行测试
        let client_options = ClientOptions::parse("mongodb://localhost:27017/test_db").await?;
        let client = Client::with_options(client_options)?;
        let database = client.database("test_db");

        let config = GridFsStorageConfig::default();
        GridFsFileStorageService::new(&database, config)
    }

    #[tokio::test]
    #[ignore] // 需要MongoDB实例才能运行
    async fn test_file_storage_and_retrieval() {
        let storage = create_test_storage().await.unwrap();
        let test_data = b"Hello, GridFS!";
        let filename = "test.txt";
        let mime_type = "text/plain";
        let user_id = next_id();

        // 存储文件
        let metadata = storage
            .store_file(test_data.to_vec(), filename, mime_type, user_id.clone())
            .await
            .unwrap();

        assert_eq!(metadata.original_filename, filename);
        assert_eq!(metadata.file_size, test_data.len() as u64);
        assert_eq!(metadata.mime_type, mime_type);
        assert_eq!(metadata.uploaded_by, user_id);

        // 检索文件
        let retrieved_data = storage.retrieve_file(&metadata).await.unwrap();
        assert_eq!(retrieved_data, test_data);

        // 检查文件存在
        assert!(storage.file_exists(&metadata).await);

        // 删除文件
        assert!(storage.delete_file(&metadata).await.unwrap());
    }

    #[tokio::test]
    async fn test_file_validation() {
        let config = GridFsStorageConfig::default();

        // 创建一个临时数据库用于测试验证逻辑
        let client_options = ClientOptions::parse("mongodb://localhost:27017/test_db")
            .await
            .unwrap();
        let client = Client::with_options(client_options).unwrap();
        let database = client.database("test_validation");
        let storage = GridFsFileStorageService::new(database, config).unwrap();

        // 有效的文件类型
        assert!(
            storage
                .validate_file_type("test.pdf", "application/pdf")
                .is_ok()
        );
        assert!(storage.validate_file_type("test.txt", "text/plain").is_ok());

        // 无效的扩展名
        assert!(
            storage
                .validate_file_type("test.exe", "application/pdf")
                .is_err()
        );

        // 无效的MIME类型
        assert!(
            storage
                .validate_file_type("test.pdf", "application/unknown")
                .is_err()
        );
    }
}
