use anyhow::Result;
use chrono::{DateTime, Utc};
use std::sync::Arc;
use wisdom_vault_common::{db::next_id, time::current_millis};

use wisdom_vault_database::{models::DocumentStatus, repositories::DocumentRepository};

use super::keyword_search::{IndexStatistics, KeywordSearchService};

/// 索引管理器配置
#[derive(Debug, Clone)]
pub struct IndexManagerConfig {
    /// 批量索引的文档数量
    pub batch_size: usize,
    /// 索引更新间隔（秒）
    pub update_interval_seconds: u64,
    /// 是否启用增量索引更新
    pub enable_incremental_updates: bool,
    /// 索引性能监控间隔（秒）
    pub monitoring_interval_seconds: u64,
    /// 最大内存使用量（MB）
    pub max_memory_usage_mb: f64,
}

impl Default for IndexManagerConfig {
    fn default() -> Self {
        Self {
            batch_size: 100,
            update_interval_seconds: 300, // 5分钟
            enable_incremental_updates: true,
            monitoring_interval_seconds: 60, // 1分钟
            max_memory_usage_mb: 512.0,      // 512MB
        }
    }
}

/// 索引更新任务
#[derive(Debug, Clone)]
pub struct IndexUpdateTask {
    /// 任务ID
    pub task_id: String,
    /// 任务类型
    pub task_type: IndexTaskType,
    /// 目标文档ID（可选，全量重建时为None）
    pub document_id: Option<String>,
    /// 知识库ID（可选）
    pub knowledge_base_id: Option<String>,
    /// 任务状态
    pub status: IndexTaskStatus,
    /// 创建时间
    pub created_at: i64,
    /// 开始时间
    pub started_at: Option<i64>,
    /// 完成时间
    pub completed_at: Option<i64>,
    /// 错误信息
    pub error_message: Option<String>,
    /// 处理的文档数量
    pub processed_documents: u32,
}

/// 索引任务类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum IndexTaskType {
    /// 全量重建索引
    FullRebuild,
    /// 增量更新单个文档
    IncrementalUpdate,
    /// 删除文档索引
    RemoveDocument,
    /// 批量更新多个文档
    BatchUpdate,
    /// 知识库索引重建
    KnowledgeBaseRebuild,
}

/// 索引任务状态
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum IndexTaskStatus {
    /// 等待执行
    Pending,
    /// 正在执行
    Running,
    /// 执行成功
    Completed,
    /// 执行失败
    Failed,
    /// 已取消
    Cancelled,
}

/// 索引性能指标
#[derive(Debug, Clone)]
pub struct IndexPerformanceMetrics {
    /// 总索引时间（毫秒）
    pub total_index_time_ms: u64,
    /// 平均每文档索引时间（毫秒）
    pub avg_doc_index_time_ms: f64,
    /// 索引吞吐量（文档/秒）
    pub indexing_throughput: f64,
    /// 内存使用量（MB）
    pub memory_usage_mb: f64,
    /// 最后监控时间
    pub last_monitored: DateTime<Utc>,
    /// 错误计数
    pub error_count: u32,
}

/// 关键词索引管理器
/// 负责管理关键词搜索服务的索引生命周期
pub struct KeywordIndexManager {
    /// 关键词搜索服务
    search_service: Arc<KeywordSearchService>,
    /// 文档仓库
    document_repository: Arc<dyn DocumentRepository + Send + Sync>,
    /// 管理器配置
    config: IndexManagerConfig,
    /// 待处理的索引任务队列
    task_queue: Arc<tokio::sync::RwLock<Vec<IndexUpdateTask>>>,
    /// 性能指标
    performance_metrics: Arc<tokio::sync::RwLock<IndexPerformanceMetrics>>,
    /// 索引管理器状态
    is_running: Arc<tokio::sync::RwLock<bool>>,
}

impl KeywordIndexManager {
    /// 创建新的索引管理器
    pub fn new(
        search_service: Arc<KeywordSearchService>,
        document_repository: Arc<dyn DocumentRepository + Send + Sync>,
        config: IndexManagerConfig,
    ) -> Self {
        Self {
            search_service,
            document_repository,
            config,
            task_queue: Arc::new(tokio::sync::RwLock::new(Vec::new())),
            performance_metrics: Arc::new(tokio::sync::RwLock::new(IndexPerformanceMetrics {
                total_index_time_ms: 0,
                avg_doc_index_time_ms: 0.0,
                indexing_throughput: 0.0,
                memory_usage_mb: 0.0,
                last_monitored: Utc::now(),
                error_count: 0,
            })),
            is_running: Arc::new(tokio::sync::RwLock::new(false)),
        }
    }

    /// 使用默认配置创建索引管理器
    pub fn new_with_defaults(
        search_service: Arc<KeywordSearchService>,
        document_repository: Arc<dyn DocumentRepository + Send + Sync>,
    ) -> Self {
        Self::new(
            search_service,
            document_repository,
            IndexManagerConfig::default(),
        )
    }

    /// 启动索引管理器
    pub async fn start(&self) -> Result<()> {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            return Err(anyhow::anyhow!("索引管理器已经在运行"));
        }
        *is_running = true;
        drop(is_running);

        tracing::info!("启动关键词索引管理器");

        // 启动任务处理器
        let task_processor = self.clone_for_task_processing();
        tokio::spawn(async move {
            task_processor.run_task_processor().await;
        });

        // 启动性能监控器
        if self.config.monitoring_interval_seconds > 0 {
            let performance_monitor = self.clone_for_monitoring();
            tokio::spawn(async move {
                performance_monitor.run_performance_monitor().await;
            });
        }

        Ok(())
    }

    /// 停止索引管理器
    pub async fn stop(&self) -> Result<()> {
        let mut is_running = self.is_running.write().await;
        *is_running = false;
        tracing::info!("停止关键词索引管理器");
        Ok(())
    }

    /// 提交全量索引重建任务
    pub async fn submit_full_rebuild_task(
        &self,
        knowledge_base_id: Option<String>,
    ) -> Result<String> {
        let task = IndexUpdateTask {
            task_id: next_id(),
            task_type: IndexTaskType::FullRebuild,
            document_id: None,
            knowledge_base_id,
            status: IndexTaskStatus::Pending,
            created_at: current_millis(),
            started_at: None,
            completed_at: None,
            error_message: None,
            processed_documents: 0,
        };

        let task_id = task.task_id.clone();
        self.task_queue.write().await.push(task);

        tracing::info!("提交全量索引重建任务: {}", task_id);
        Ok(task_id)
    }

    /// 提交单个文档索引更新任务
    pub async fn submit_document_update_task(&self, document_id: String) -> Result<String> {
        let task = IndexUpdateTask {
            task_id: next_id(),
            task_type: IndexTaskType::IncrementalUpdate,
            document_id: Some(document_id.clone()),
            knowledge_base_id: None,
            status: IndexTaskStatus::Pending,
            created_at: current_millis(),
            started_at: None,
            completed_at: None,
            error_message: None,
            processed_documents: 0,
        };

        let task_id = task.task_id.clone();
        self.task_queue.write().await.push(task);

        tracing::debug!("提交文档索引更新任务: {} (文档: {})", task_id, document_id);
        Ok(task_id)
    }

    /// 提交文档删除任务
    pub async fn submit_document_removal_task(&self, document_id: String) -> Result<String> {
        let task = IndexUpdateTask {
            task_id: next_id(),
            task_type: IndexTaskType::RemoveDocument,
            document_id: Some(document_id.clone()),
            knowledge_base_id: None,
            status: IndexTaskStatus::Pending,
            created_at: current_millis(),
            started_at: None,
            completed_at: None,
            error_message: None,
            processed_documents: 0,
        };

        let task_id = task.task_id.clone();
        self.task_queue.write().await.push(task);

        tracing::debug!("提交文档删除任务: {} (文档: {})", task_id, document_id);
        Ok(task_id)
    }

    /// 批量提交文档更新任务
    pub async fn submit_batch_update_task(
        &self,
        document_ids: Vec<String>,
        knowledge_base_id: Option<String>,
    ) -> Result<String> {
        let task = IndexUpdateTask {
            task_id: next_id(),
            task_type: IndexTaskType::BatchUpdate,
            document_id: None,
            knowledge_base_id,
            status: IndexTaskStatus::Pending,
            created_at: current_millis(),
            started_at: None,
            completed_at: None,
            error_message: None,
            processed_documents: 0,
        };

        let task_id = task.task_id.clone();
        self.task_queue.write().await.push(task);

        tracing::info!(
            "提交批量索引更新任务: {} ({} 个文档)",
            task_id,
            document_ids.len()
        );
        Ok(task_id)
    }

    /// 获取任务状态
    pub async fn get_task_status(&self, task_id: &str) -> Option<IndexUpdateTask> {
        let queue = self.task_queue.read().await;
        queue.iter().find(|task| task.task_id == task_id).cloned()
    }

    /// 获取所有待处理任务
    pub async fn get_pending_tasks(&self) -> Vec<IndexUpdateTask> {
        let queue = self.task_queue.read().await;
        queue
            .iter()
            .filter(|task| task.status == IndexTaskStatus::Pending)
            .cloned()
            .collect()
    }

    /// 获取索引统计信息
    pub async fn get_index_statistics(&self) -> IndexStatistics {
        self.search_service.get_index_statistics().await
    }

    /// 获取性能指标
    pub async fn get_performance_metrics(&self) -> IndexPerformanceMetrics {
        self.performance_metrics.read().await.clone()
    }

    /// 清理已完成的任务
    pub async fn cleanup_completed_tasks(&self) -> Result<u32> {
        let mut queue = self.task_queue.write().await;
        let initial_count = queue.len();

        // 保留未完成的任务和最近24小时内完成的任务
        let cutoff_time = current_millis() - chrono::Duration::hours(24).num_milliseconds();
        queue.retain(|task| match task.status {
            IndexTaskStatus::Pending | IndexTaskStatus::Running => true,
            IndexTaskStatus::Completed | IndexTaskStatus::Failed | IndexTaskStatus::Cancelled => {
                task.completed_at.is_none_or(|time| time > cutoff_time)
            }
        });

        let cleaned_count = initial_count - queue.len();
        if cleaned_count > 0 {
            tracing::info!("清理了 {} 个已完成的索引任务", cleaned_count);
        }

        Ok(cleaned_count as u32)
    }

    /// 取消待处理的任务
    pub async fn cancel_task(&self, task_id: &str) -> Result<bool> {
        let mut queue = self.task_queue.write().await;

        if let Some(task) = queue.iter_mut().find(|t| t.task_id == task_id) {
            if task.status == IndexTaskStatus::Pending {
                task.status = IndexTaskStatus::Cancelled;
                task.completed_at = Some(current_millis());
                tracing::info!("取消索引任务: {}", task_id);
                return Ok(true);
            }
        }

        Ok(false)
    }

    /// 强制执行特定任务
    pub async fn execute_task_immediately(&self, task_id: &str) -> Result<bool> {
        let mut queue = self.task_queue.write().await;

        if let Some(task_index) = queue
            .iter()
            .position(|t| t.task_id == task_id && t.status == IndexTaskStatus::Pending)
        {
            let mut task = queue.remove(task_index);
            drop(queue);

            tracing::info!("立即执行索引任务: {}", task_id);
            let result = self.execute_task(&mut task).await;

            // 将任务放回队列
            self.task_queue.write().await.push(task);

            return Ok(result.is_ok());
        }

        Ok(false)
    }

    /// 克隆用于任务处理
    fn clone_for_task_processing(&self) -> KeywordIndexManager {
        KeywordIndexManager {
            search_service: Arc::clone(&self.search_service),
            document_repository: Arc::clone(&self.document_repository),
            config: self.config.clone(),
            task_queue: Arc::clone(&self.task_queue),
            performance_metrics: Arc::clone(&self.performance_metrics),
            is_running: Arc::clone(&self.is_running),
        }
    }

    /// 克隆用于性能监控
    fn clone_for_monitoring(&self) -> KeywordIndexManager {
        self.clone_for_task_processing()
    }

    /// 运行任务处理器
    async fn run_task_processor(self) {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(
            self.config.update_interval_seconds,
        ));

        while *self.is_running.read().await {
            interval.tick().await;

            // 处理队列中的任务
            if let Err(e) = self.process_pending_tasks().await {
                tracing::error!("处理索引任务时出错: {}", e);
            }

            // 定期清理完成的任务
            if let Err(e) = self.cleanup_completed_tasks().await {
                tracing::error!("清理索引任务时出错: {}", e);
            }
        }

        tracing::info!("索引任务处理器已停止");
    }

    /// 运行性能监控器
    async fn run_performance_monitor(self) {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(
            self.config.monitoring_interval_seconds,
        ));

        while *self.is_running.read().await {
            interval.tick().await;

            if let Err(e) = self.update_performance_metrics().await {
                tracing::error!("更新性能指标时出错: {}", e);
            }
        }

        tracing::debug!("索引性能监控器已停止");
    }

    /// 处理待处理的任务
    async fn process_pending_tasks(&self) -> Result<()> {
        let mut processed_count = 0;

        loop {
            // 获取下一个待处理任务
            let mut task_option = None;
            {
                let mut queue = self.task_queue.write().await;
                if let Some(index) = queue
                    .iter()
                    .position(|t| t.status == IndexTaskStatus::Pending)
                {
                    task_option = Some(queue.remove(index));
                }
            }

            if let Some(mut task) = task_option {
                // 执行任务
                let result = self.execute_task(&mut task).await;

                // 更新任务状态
                match result {
                    Ok(_) => {
                        task.status = IndexTaskStatus::Completed;
                        task.completed_at = Some(current_millis());
                    }
                    Err(e) => {
                        task.status = IndexTaskStatus::Failed;
                        task.completed_at = Some(current_millis());
                        task.error_message = Some(e.to_string());
                        tracing::error!("索引任务执行失败 {}: {}", task.task_id, e);
                    }
                }

                // 将任务放回队列
                self.task_queue.write().await.push(task);
                processed_count += 1;

                // 限制每次处理的任务数量
                if processed_count >= self.config.batch_size {
                    break;
                }
            } else {
                break; // 没有更多待处理任务
            }
        }

        if processed_count > 0 {
            tracing::debug!("处理了 {} 个索引任务", processed_count);
        }

        Ok(())
    }

    /// 执行单个任务
    async fn execute_task(&self, task: &mut IndexUpdateTask) -> Result<()> {
        task.status = IndexTaskStatus::Running;
        task.started_at = Some(current_millis());

        let start_time = std::time::Instant::now();

        match task.task_type {
            IndexTaskType::FullRebuild => {
                let indexed_count = self.search_service.rebuild_index().await?;
                task.processed_documents = indexed_count as u32;
                tracing::info!(
                    "全量索引重建完成: {} 个文档, 任务ID: {}",
                    indexed_count,
                    task.task_id
                );
            }

            IndexTaskType::IncrementalUpdate => {
                if let Some(ref doc_id) = task.document_id {
                    if let Some(document) = self.document_repository.find_by_id(doc_id).await? {
                        if document.status == DocumentStatus::Indexed {
                            self.search_service.index_document(&document).await?;
                            task.processed_documents = 1;
                            tracing::debug!("文档索引更新完成: {}", doc_id);
                        }
                    }
                }
            }

            IndexTaskType::RemoveDocument => {
                if let Some(ref doc_id) = task.document_id {
                    self.search_service.remove_document_index(doc_id).await?;
                    task.processed_documents = 1;
                    tracing::debug!("文档索引删除完成: {}", doc_id);
                }
            }

            IndexTaskType::BatchUpdate => {
                // TODO: 实现批量更新逻辑
                tracing::warn!("批量更新任务尚未实现: {}", task.task_id);
            }

            IndexTaskType::KnowledgeBaseRebuild => {
                // TODO: 实现知识库索引重建逻辑
                tracing::warn!("知识库索引重建任务尚未实现: {}", task.task_id);
            }
        }

        // 更新性能指标
        let execution_time = start_time.elapsed().as_millis() as u64;
        self.update_task_performance_metrics(execution_time, task.processed_documents)
            .await;

        Ok(())
    }

    /// 更新任务性能指标
    async fn update_task_performance_metrics(&self, execution_time_ms: u64, processed_docs: u32) {
        let mut metrics = self.performance_metrics.write().await;

        metrics.total_index_time_ms += execution_time_ms;

        if processed_docs > 0 {
            let avg_time = execution_time_ms as f64 / processed_docs as f64;
            metrics.avg_doc_index_time_ms = (metrics.avg_doc_index_time_ms + avg_time) / 2.0;

            metrics.indexing_throughput =
                processed_docs as f64 / (execution_time_ms as f64 / 1000.0);
        }
    }

    /// 更新性能指标
    async fn update_performance_metrics(&self) -> Result<()> {
        let mut metrics = self.performance_metrics.write().await;

        // 简单的内存使用量估算
        let index_stats = self.search_service.get_index_statistics().await;
        metrics.memory_usage_mb = index_stats.index_size_mb;
        metrics.last_monitored = Utc::now();

        // 检查内存使用量是否超过限制
        if metrics.memory_usage_mb > self.config.max_memory_usage_mb {
            tracing::warn!(
                "索引内存使用量超过限制: {:.2}MB > {:.2}MB",
                metrics.memory_usage_mb,
                self.config.max_memory_usage_mb
            );
        }

        Ok(())
    }
}
