use anyhow::Result;
use async_trait::async_trait;
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::io::AsyncRead;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileMetadata {
    pub file_id: String,
    pub original_filename: String,
    pub gridfs_file_id: ObjectId,
    pub file_size: u64,
    pub mime_type: String,
    pub checksum: String,
    pub uploaded_by: String,
    pub uploaded_at: i64,
}

/// 统一的文件存储接口，支持文件系统和GridFS两种存储方式
#[async_trait]
pub trait FileStorageServiceTrait: Send + Sync {
    /// 存储文件
    async fn store_file(
        &self,
        file_data: Vec<u8>,
        original_filename: &str,
        mime_type: &str,
        uploaded_by: String,
    ) -> Result<FileMetadata>;

    /// 检索文件数据
    async fn retrieve_file(&self, file_metadata: &FileMetadata) -> Result<Vec<u8>>;

    /// 检索文件流（用于大文件）
    async fn retrieve_file_stream(
        &self,
        file_metadata: &FileMetadata,
    ) -> Result<Box<dyn AsyncRead + Send + Sync + Unpin>>;

    /// 删除文件
    async fn delete_file(&self, file_metadata: &FileMetadata) -> Result<bool>;

    /// 检查文件是否存在
    async fn file_exists(&self, file_metadata: &FileMetadata) -> bool;

    /// 验证文件类型
    fn validate_file_type(&self, filename: &str, mime_type: &str) -> Result<()>;

    /// 获取存储使用情况
    async fn get_storage_usage(&self) -> Result<StorageUsage>;

    /// 获取存储统计信息
    fn get_storage_stats(&self) -> StorageStats;
}

/// 统一的存储使用情况
#[derive(Debug, Clone)]
pub struct StorageUsage {
    pub total_size: u64,
    pub file_count: u64,
    pub extension_counts: HashMap<String, u64>,
    pub last_calculated: i64,
}

/// 统一的存储统计信息
#[derive(Debug, Clone)]
pub struct StorageStats {
    pub max_file_size: u64,
    pub allowed_extensions: Vec<String>,
    pub allowed_mime_types: Vec<String>,
    pub storage_config: StorageConfigInfo,
}

#[derive(Debug, Clone)]
pub struct StorageConfigInfo {
    pub bucket_name: String,
    pub chunk_size: Option<u32>,
}
