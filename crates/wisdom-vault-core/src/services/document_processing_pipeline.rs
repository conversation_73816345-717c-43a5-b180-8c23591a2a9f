use crate::{
    FileStorageServiceTrait,
    services::{
        DocumentClassificationService, DocumentParserService, DocumentRelationService,
        DocumentService, ProcessingTaskService, VectorizationService,
    },
};
use anyhow::Result;
use chrono::Utc;
use std::sync::Arc;
use wisdom_vault_database::models::{
    DocumentStatus, ProcessingTask, TaskContext, TaskPriority, TaskType,
};

/// 文档处理流水线
/// 管理文档从上传到完全处理完成的整个流程
pub struct DocumentProcessingPipeline {
    document_service: Arc<DocumentService>,
    processing_task_service: Arc<ProcessingTaskService>,
    document_parser_service: Arc<DocumentParserService>,
    file_storage_service: Arc<dyn FileStorageServiceTrait>,
    vectorization_service: Arc<VectorizationService>,
    document_classification_service: Arc<DocumentClassificationService>,
    document_relation_service: Arc<DocumentRelationService>,
}

impl DocumentProcessingPipeline {
    pub fn new(
        document_service: Arc<DocumentService>,
        processing_task_service: Arc<ProcessingTaskService>,
        document_parser_service: Arc<DocumentParserService>,
        file_storage_service: Arc<dyn FileStorageServiceTrait>,
        vectorization_service: Arc<VectorizationService>,
        document_classification_service: Arc<DocumentClassificationService>,
        document_relation_service: Arc<DocumentRelationService>,
    ) -> Self {
        Self {
            document_service,
            processing_task_service,
            document_parser_service,
            file_storage_service,
            vectorization_service,
            document_classification_service,
            document_relation_service,
        }
    }

    /// 获取文件存储服务的引用
    pub fn file_storage_service(&self) -> Arc<dyn FileStorageServiceTrait> {
        self.file_storage_service.clone()
    }

    /// 启动文档处理流水线
    /// 创建必要的处理任务并将其加入队列
    #[allow(clippy::too_many_arguments)]
    pub async fn initiate_document_processing(
        &self,
        document_id: &str,
        gridfs_file_id: mongodb::bson::oid::ObjectId,
        original_filename: String,
        mime_type: String,
        file_size: i64,
        knowledge_base_id: &str,
        user_id: &str,
    ) -> Result<Vec<ProcessingTask>> {
        let mut tasks = Vec::new();

        // 创建任务上下文
        let context = TaskContext {
            gridfs_file_id: Some(gridfs_file_id),
            original_filename: Some(original_filename),
            mime_type: Some(mime_type.clone()),
            file_size: Some(file_size),
            knowledge_base_id: Some(knowledge_base_id.to_owned()),
            user_id: Some(user_id.to_owned()),
            additional_params: serde_json::json!({
                "document_id": document_id,
                "pipeline_started_at": Utc::now()
            }),
        };

        // 1. 文档解析任务（高优先级）
        let parsing_task = self
            .processing_task_service
            .create_task(
                TaskType::DocumentParsing,
                document_id.to_owned(),
                TaskPriority::High,
                context.clone(),
            )
            .await?;
        tasks.push(parsing_task);

        // 2. 元数据提取任务（中优先级）
        let metadata_task = self
            .processing_task_service
            .create_task(
                TaskType::MetadataExtraction,
                document_id.to_owned(),
                TaskPriority::Normal,
                context.clone(),
            )
            .await?;
        tasks.push(metadata_task);

        // 3. 文档索引任务（低优先级）
        let indexing_task = self
            .processing_task_service
            .create_task(
                TaskType::DocumentIndexing,
                document_id.to_owned(),
                TaskPriority::Low,
                context,
            )
            .await?;
        tasks.push(indexing_task);

        // 更新文档状态为处理中
        self.document_service
            .update_document_status(document_id, DocumentStatus::Processing)
            .await?;

        tracing::info!(
            "Document processing pipeline initiated for document {} with {} tasks",
            document_id,
            tasks.len()
        );

        Ok(tasks)
    }

    /// 处理单个任务
    pub async fn process_task(&self, task: ProcessingTask) -> Result<()> {
        tracing::info!(
            "Starting processing task {} of type {:?}",
            task.id,
            task.task_type
        );

        // 标记任务开始
        self.processing_task_service.start_task(&task.id).await?;

        let result = match task.task_type {
            TaskType::DocumentParsing => self.process_document_parsing(&task).await,
            TaskType::MetadataExtraction => self.process_metadata_extraction(&task).await,
            TaskType::DocumentIndexing => self.process_document_indexing(&task).await,
            TaskType::VectorGeneration => self.process_vector_generation(&task).await,
            TaskType::KnowledgeGraphExtraction => {
                self.process_knowledge_graph_extraction(&task).await
            }
            TaskType::DocumentClassification => self.process_document_classification(&task).await,
        };

        match result {
            Ok(()) => {
                self.processing_task_service.complete_task(&task.id).await?;
                tracing::info!("Task {} completed successfully", task.id);

                // 检查文档处理是否全部完成
                self.check_document_completion(&task.resource_id).await?;
            }
            Err(e) => {
                let error_details = serde_json::json!({
                    "error_type": "ProcessingError",
                    "task_type": format!("{:?}", task.task_type),
                    "next_id()": Utc::now(),
                    "context": task.context
                });

                self.processing_task_service
                    .fail_task(
                        &task.id,
                        &format!("Task processing failed: {e}"),
                        Some(error_details),
                    )
                    .await?;

                tracing::error!("Task {} failed: {}", task.id, e);

                // 如果关键任务失败，标记文档为失败状态
                if matches!(
                    task.task_type,
                    TaskType::DocumentParsing | TaskType::MetadataExtraction
                ) {
                    self.document_service
                        .update_document_status(&task.resource_id, DocumentStatus::Failed)
                        .await?;
                }
            }
        }

        Ok(())
    }

    /// 处理文档解析任务
    async fn process_document_parsing(&self, task: &ProcessingTask) -> Result<()> {
        let context = task
            .context
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("Task context not found"))?;

        let gridfs_file_id = context
            .gridfs_file_id
            .ok_or_else(|| anyhow::anyhow!("GridFS file ID not found in task context"))?;

        let original_filename = context
            .original_filename
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("Original filename not found in task context"))?;

        let mime_type = context
            .mime_type
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("MIME type not found in task context"))?;

        // 更新进度：开始解析
        self.processing_task_service
            .update_progress(&task.id, 0.1)
            .await?;

        // 构造文件元数据以从GridFS读取文件
        let file_metadata = crate::FileMetadata {
            file_id: task.resource_id.clone(),
            original_filename: original_filename.clone(),
            gridfs_file_id,
            file_size: context.file_size.unwrap_or(0) as u64,
            mime_type: mime_type.clone(),
            checksum: "".to_string(), // 暂时不需要校验和
            uploaded_by: context.user_id.clone().unwrap_or_default(),
            uploaded_at: 0, // 暂时不需要上传时间
        };

        // 从GridFS读取文件内容
        let file_data = self
            .file_storage_service
            .retrieve_file(&file_metadata)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to retrieve file from GridFS: {}", e))?;

        // 使用DocumentParserService解析文档
        let parse_result = self
            .document_parser_service
            .parse_document(file_data, original_filename, mime_type)
            .await?;

        // 更新进度：解析完成
        self.processing_task_service
            .update_progress(&task.id, 0.8)
            .await?;

        // 更新文档内容和元数据
        if let Some(mut document) = self
            .document_service
            .get_document(&task.resource_id)
            .await?
        {
            document.content = parse_result.content.plain_text;

            // 更新处理元数据
            document.processing_metadata.extraction_method = "Apache Tika".to_string();
            document.processing_metadata.extraction_quality =
                parse_result.content.extraction_quality;
            document.processing_metadata.processing_time_ms = parse_result.processing_time_ms;
            document.processing_metadata.parsing_errors = parse_result.parsing_errors;
            document.processing_metadata.parsing_warnings = parse_result.parsing_warnings;

            self.document_service.update_document(document).await?;
        }

        // 更新进度：完成
        self.processing_task_service
            .update_progress(&task.id, 1.0)
            .await?;

        Ok(())
    }

    /// 处理元数据提取任务
    async fn process_metadata_extraction(&self, task: &ProcessingTask) -> Result<()> {
        let context = task
            .context
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("Task context not found"))?;

        let gridfs_file_id = context
            .gridfs_file_id
            .ok_or_else(|| anyhow::anyhow!("GridFS file ID not found in task context"))?;

        let original_filename = context
            .original_filename
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("Original filename not found in task context"))?;

        let mime_type = context
            .mime_type
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("MIME type not found in task context"))?;

        // 更新进度：开始提取
        self.processing_task_service
            .update_progress(&task.id, 0.1)
            .await?;

        // 构造文件元数据以从GridFS读取文件
        let file_metadata = crate::FileMetadata {
            file_id: task.resource_id.clone(),
            original_filename: original_filename.clone(),
            gridfs_file_id,
            file_size: context.file_size.unwrap_or(0) as u64,
            mime_type: mime_type.clone(),
            checksum: "".to_string(), // 暂时不需要校验和
            uploaded_by: context.user_id.clone().unwrap_or_default(),
            uploaded_at: 0, // 暂时不需要上传时间
        };

        // 从GridFS读取文件内容
        let file_data = self
            .file_storage_service
            .retrieve_file(&file_metadata)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to retrieve file from GridFS: {}", e))?;

        // 使用完整的解析来获取元数据 (因为extract_metadata是私有方法)
        let parse_result = self
            .document_parser_service
            .parse_document(file_data, original_filename, mime_type)
            .await?;

        // 更新进度：提取完成
        self.processing_task_service
            .update_progress(&task.id, 0.8)
            .await?;

        // 更新文档元数据
        if let Some(mut document) = self
            .document_service
            .get_document(&task.resource_id)
            .await?
        {
            // 从解析结果中提取元数据信息
            let metadata = &parse_result.metadata;
            document.metadata.author = metadata.author.clone();
            document.metadata.subject = metadata.subject.clone();
            document.metadata.creator = metadata.creator.clone();
            document.metadata.producer = metadata.producer.clone();
            document.metadata.keywords = metadata.keywords.clone();
            document.metadata.page_count = metadata.page_count;
            document.metadata.creation_date = metadata.creation_date;
            document.metadata.modification_date = metadata.modification_date;
            document.metadata.content_type = metadata.content_type.clone();

            self.document_service.update_document(document).await?;
        }

        // 更新进度：完成
        self.processing_task_service
            .update_progress(&task.id, 1.0)
            .await?;

        Ok(())
    }

    /// 处理文档索引任务
    async fn process_document_indexing(&self, task: &ProcessingTask) -> Result<()> {
        // 更新进度：开始索引
        self.processing_task_service
            .update_progress(&task.id, 0.1)
            .await?;

        // 获取文档信息
        let document = self
            .document_service
            .get_document(&task.resource_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("文档不存在: {}", task.resource_id))?;

        // 更新进度：准备索引
        self.processing_task_service
            .update_progress(&task.id, 0.3)
            .await?;

        // 实现文档索引逻辑
        // 1. 创建关键词索引
        if !document.content.is_empty() {
            tracing::info!("Creating keyword index for document {}", document.id);
            // TODO: 实现索引文档的逻辑
            // 当前 KeywordIndexManager 没有直接的 index_document 方法
            // 需要通过其他方式来创建索引
            tracing::debug!("Indexing document {}", document.id);
        }

        // 更新进度：关键词索引完成
        self.processing_task_service
            .update_progress(&task.id, 0.7)
            .await?;

        // 2. 更新文档状态为已索引
        self.document_service
            .update_document_status(&task.resource_id, DocumentStatus::Indexed)
            .await?;

        // 更新进度：索引完成
        self.processing_task_service
            .update_progress(&task.id, 0.9)
            .await?;

        tracing::info!("Document indexing completed for document {}", document.id);

        // 更新进度：完成
        self.processing_task_service
            .update_progress(&task.id, 1.0)
            .await?;

        Ok(())
    }

    /// 处理向量生成任务
    async fn process_vector_generation(&self, task: &ProcessingTask) -> Result<()> {
        // 更新进度
        self.processing_task_service
            .update_progress(&task.id, 0.1)
            .await?;

        // 获取文档信息
        let document = self
            .document_service
            .get_document(&task.resource_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("文档不存在: {}", task.resource_id))?;

        // 更新进度：开始向量化
        self.processing_task_service
            .update_progress(&task.id, 0.3)
            .await?;

        // 实现向量生成逻辑
        if !document.content.is_empty() {
            tracing::info!("Starting vectorization for document {}", document.id);

            // 使用向量化服务生成文档嵌入
            let vectorization_result = self
                .vectorization_service
                .vectorize_document(document.id.clone(), None, false)
                .await?;

            // 更新进度：向量化完成
            self.processing_task_service
                .update_progress(&task.id, 0.8)
                .await?;

            tracing::info!(
                "Document vectorization completed for document {}, generated {} vectors",
                document.id,
                vectorization_result.embeddings.len()
            );
        } else {
            tracing::warn!("Document {} has no content to vectorize", document.id);
        }

        // 更新进度：完成
        self.processing_task_service
            .update_progress(&task.id, 1.0)
            .await?;

        Ok(())
    }

    /// 处理知识图谱提取任务
    async fn process_knowledge_graph_extraction(&self, task: &ProcessingTask) -> Result<()> {
        // 更新进度
        self.processing_task_service
            .update_progress(&task.id, 0.1)
            .await?;

        // 获取文档信息
        let document = self
            .document_service
            .get_document(&task.resource_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("文档不存在: {}", task.resource_id))?;

        // 更新进度：开始关系提取
        self.processing_task_service
            .update_progress(&task.id, 0.3)
            .await?;

        // 实现知识图谱提取逻辑
        if !document.content.is_empty() {
            tracing::info!(
                "Starting knowledge graph extraction for document {}",
                document.id
            );

            // 1. 发现相似关系
            let similar_relations = self
                .document_relation_service
                .discover_similar_relations(&document.id, 0.3, 10)
                .await?;

            // 更新进度：相似性分析完成
            self.processing_task_service
                .update_progress(&task.id, 0.6)
                .await?;

            // 2. 发现引用关系
            let reference_relations = self
                .document_relation_service
                .discover_reference_relations(&document.id)
                .await?;

            // 更新进度：引用分析完成
            self.processing_task_service
                .update_progress(&task.id, 0.8)
                .await?;

            tracing::info!(
                "Knowledge graph extraction completed for document {}: {} similar relations, {} reference relations",
                document.id,
                similar_relations.len(),
                reference_relations.len()
            );
        } else {
            tracing::warn!(
                "Document {} has no content for knowledge graph extraction",
                document.id
            );
        }

        // 更新进度：完成
        self.processing_task_service
            .update_progress(&task.id, 1.0)
            .await?;

        Ok(())
    }

    /// 处理文档分类任务
    async fn process_document_classification(&self, task: &ProcessingTask) -> Result<()> {
        // 更新进度
        self.processing_task_service
            .update_progress(&task.id, 0.1)
            .await?;

        // 获取文档信息
        let document = self
            .document_service
            .get_document(&task.resource_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("文档不存在: {}", task.resource_id))?;

        // 更新进度：开始分类
        self.processing_task_service
            .update_progress(&task.id, 0.3)
            .await?;

        // 实现文档分类逻辑
        if !document.content.is_empty() {
            tracing::info!(
                "Starting document classification for document {}",
                document.id
            );

            // 使用文档分类服务自动分类文档
            let classification_result = self
                .document_classification_service
                .classify_document_by_content(&document.id, &document.title, &document.content)
                .await?;

            // 更新进度：分类完成
            self.processing_task_service
                .update_progress(&task.id, 0.8)
                .await?;

            tracing::info!(
                "Document classification completed for document {}: category={}, confidence={}",
                document.id,
                classification_result.predicted_category,
                classification_result.confidence
            );

            // 如果分类置信度足够高，可以考虑自动更新文档分类
            if classification_result.confidence > 0.8 {
                tracing::info!(
                    "High confidence classification for document {}, category: {}",
                    document.id,
                    classification_result.predicted_category
                );
            }
        } else {
            tracing::warn!("Document {} has no content for classification", document.id);
        }

        // 更新进度：完成
        self.processing_task_service
            .update_progress(&task.id, 1.0)
            .await?;

        Ok(())
    }

    /// 检查文档处理是否全部完成
    async fn check_document_completion(&self, document_id: &str) -> Result<()> {
        let tasks = self
            .processing_task_service
            .get_tasks_by_resource(document_id)
            .await?;

        let all_completed = tasks.iter().all(|task| {
            matches!(
                task.status,
                wisdom_vault_database::models::TaskStatus::Completed
            )
        });

        let has_failed = tasks.iter().any(|task| {
            matches!(
                task.status,
                wisdom_vault_database::models::TaskStatus::Failed
            ) && task.retry_count >= task.max_retries
        });

        if has_failed {
            self.document_service
                .update_document_status(document_id, DocumentStatus::Failed)
                .await?;
            tracing::warn!(
                "Document {} processing failed due to task failures",
                document_id
            );
        } else if all_completed {
            self.document_service
                .update_document_status(document_id, DocumentStatus::Indexed)
                .await?;
            tracing::info!("Document {} processing completed successfully", document_id);
        }

        Ok(())
    }

    /// 获取文档处理进度
    pub async fn get_document_progress(
        &self,
        document_id: &str,
    ) -> Result<DocumentProcessingProgress> {
        let tasks = self
            .processing_task_service
            .get_tasks_by_resource(document_id)
            .await?;

        if tasks.is_empty() {
            return Ok(DocumentProcessingProgress {
                document_id: document_id.to_owned(),
                overall_progress: 0.0,
                status: "Not Started".to_string(),
                tasks_completed: 0,
                tasks_total: 0,
                tasks_failed: 0,
                estimated_completion: None,
                current_task: None,
            });
        }

        let total_tasks = tasks.len();
        let completed_tasks = tasks
            .iter()
            .filter(|t| {
                matches!(
                    t.status,
                    wisdom_vault_database::models::TaskStatus::Completed
                )
            })
            .count();
        let failed_tasks = tasks
            .iter()
            .filter(|t| matches!(t.status, wisdom_vault_database::models::TaskStatus::Failed))
            .count();

        let overall_progress = if total_tasks > 0 {
            completed_tasks as f64 / total_tasks as f64
        } else {
            0.0
        };

        let current_task = tasks
            .iter()
            .find(|t| matches!(t.status, wisdom_vault_database::models::TaskStatus::Running))
            .map(|t| format!("{:?}", t.task_type));

        let status = if failed_tasks > 0 {
            "Failed".to_string()
        } else if completed_tasks == total_tasks {
            "Completed".to_string()
        } else if current_task.is_some() {
            "Processing".to_string()
        } else {
            "Queued".to_string()
        };

        Ok(DocumentProcessingProgress {
            document_id: document_id.to_owned(),
            overall_progress,
            status,
            tasks_completed: completed_tasks,
            tasks_total: total_tasks,
            tasks_failed: failed_tasks,
            estimated_completion: None, // TODO: 实现预估完成时间
            current_task,
        })
    }

    /// 重新启动失败的文档处理
    pub async fn restart_failed_processing(&self, document_id: &str) -> Result<bool> {
        let tasks = self
            .processing_task_service
            .get_tasks_by_resource(document_id)
            .await?;

        let failed_tasks: Vec<_> = tasks
            .iter()
            .filter(|t| matches!(t.status, wisdom_vault_database::models::TaskStatus::Failed))
            .collect();

        if failed_tasks.is_empty() {
            return Ok(false);
        }

        let mut restarted_count = 0;
        for task in failed_tasks {
            if self
                .processing_task_service
                .force_requeue_task(&task.id)
                .await?
            {
                restarted_count += 1;
            }
        }

        if restarted_count > 0 {
            self.document_service
                .update_document_status(document_id, DocumentStatus::Processing)
                .await?;
            tracing::info!(
                "Restarted {} failed tasks for document {}",
                restarted_count,
                document_id
            );
        }

        Ok(restarted_count > 0)
    }
}

/// 文档处理进度信息
#[derive(Debug, Clone)]
pub struct DocumentProcessingProgress {
    pub document_id: String,
    pub overall_progress: f64, // 0.0 - 1.0
    pub status: String,
    pub tasks_completed: usize,
    pub tasks_total: usize,
    pub tasks_failed: usize,
    pub estimated_completion: Option<chrono::DateTime<Utc>>,
    pub current_task: Option<String>,
}
