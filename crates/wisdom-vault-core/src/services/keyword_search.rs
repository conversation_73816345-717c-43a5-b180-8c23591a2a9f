use anyhow::Result;
use regex::Regex;
use serde::Serialize;
use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};
use unicode_segmentation::UnicodeSegmentation;
use wisdom_vault_common::{db::next_id, time::current_millis};

use wisdom_vault_database::{
    models::{Document, DocumentStatus},
    repositories::DocumentRepository,
};

/// BM25 算法参数配置
#[derive(Debug, Clone)]
pub struct BM25Config {
    /// k1 参数：控制词频饱和度
    pub k1: f64,
    /// b 参数：控制文档长度归一化程度
    pub b: f64,
    /// 最小词长度
    pub min_term_length: usize,
    /// 最大词长度
    pub max_term_length: usize,
    /// 停用词列表
    pub stop_words: HashSet<String>,
}

impl Default for BM25Config {
    fn default() -> Self {
        let mut stop_words = HashSet::new();
        // 常见中英文停用词
        let common_stop_words = [
            "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with",
            "by", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does",
            "did", "will", "would", "could", "should", "may", "might", "can", "must", "shall",
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上",
            "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这",
            "那", "么", "于",
        ];

        for word in &common_stop_words {
            stop_words.insert(word.to_string());
        }

        Self {
            k1: 1.5,
            b: 0.75,
            min_term_length: 2,
            max_term_length: 50,
            stop_words,
        }
    }
}

/// 关键词检索请求
#[derive(Debug, Clone)]
pub struct KeywordSearchRequest {
    /// 查询文本
    pub query: String,
    /// 知识库ID过滤
    pub knowledge_base_id: Option<String>,
    /// 结果数量限制
    pub limit: Option<u32>,
    /// 结果偏移量
    pub offset: Option<u32>,
    /// 最小相关性得分阈值
    pub min_score: Option<f64>,
    /// 文档状态过滤
    pub status_filter: Option<Vec<DocumentStatus>>,
    /// 是否启用查询扩展
    pub enable_query_expansion: Option<bool>,
}

/// 关键词检索结果项
#[derive(Debug, Clone)]
pub struct KeywordSearchResult {
    /// 文档
    pub document: Document,
    /// BM25 相关性得分
    pub score: f64,
    /// 匹配的关键词
    pub matched_terms: Vec<String>,
    /// 词频统计
    pub term_frequencies: HashMap<String, u32>,
    /// 高亮片段
    pub highlights: Vec<String>,
}

/// 关键词检索结果集
#[derive(Debug, Clone)]
pub struct KeywordSearchResults {
    /// 搜索结果列表
    pub results: Vec<KeywordSearchResult>,
    /// 总结果数量
    pub total_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 查询统计信息
    pub query_stats: QueryStatistics,
}

/// 查询统计信息
#[derive(Debug, Clone, Serialize)]
pub struct QueryStatistics {
    /// 原始查询
    pub original_query: String,
    /// 处理后的查询词
    pub processed_terms: Vec<String>,
    /// 被过滤的停用词
    pub filtered_stop_words: Vec<String>,
    /// 查询词的IDF值
    pub term_idf_scores: HashMap<String, f64>,
    /// 平均文档长度
    pub avg_doc_length: f64,
}

/// 文档索引项
#[derive(Debug, Clone)]
pub struct DocumentIndex {
    /// 文档ID
    pub document_id: String,
    /// 词项频率 (term -> frequency)
    pub term_frequencies: HashMap<String, u32>,
    /// 文档总词数
    pub total_terms: u32,
    /// 文档长度（字符数）
    pub doc_length: usize,
    /// 最后更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// 关键词检索服务
pub struct KeywordSearchService {
    /// 文档仓库
    document_repository: Arc<dyn DocumentRepository + Send + Sync>,
    /// BM25 配置
    config: BM25Config,
    /// 文档索引缓存 (document_id -> DocumentIndex)
    document_indexes: Arc<tokio::sync::RwLock<HashMap<String, DocumentIndex>>>,
    /// 词项文档频率 (term -> document_count)
    term_doc_frequencies: Arc<tokio::sync::RwLock<HashMap<String, u32>>>,
    /// 总文档数
    total_documents: Arc<tokio::sync::RwLock<u32>>,
    /// 平均文档长度
    avg_doc_length: Arc<tokio::sync::RwLock<f64>>,
}

impl KeywordSearchService {
    /// 创建关键词检索服务
    pub fn new(
        document_repository: Arc<dyn DocumentRepository + Send + Sync>,
        config: BM25Config,
    ) -> Self {
        Self {
            document_repository,
            config,
            document_indexes: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            term_doc_frequencies: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            total_documents: Arc::new(tokio::sync::RwLock::new(0)),
            avg_doc_length: Arc::new(tokio::sync::RwLock::new(0.0)),
        }
    }

    /// 使用默认配置创建服务
    pub fn new_with_defaults(
        document_repository: Arc<dyn DocumentRepository + Send + Sync>,
    ) -> Self {
        Self::new(document_repository, BM25Config::default())
    }

    /// 执行关键词搜索
    pub async fn search(&self, request: KeywordSearchRequest) -> Result<KeywordSearchResults> {
        let start_time = std::time::Instant::now();

        // 1. 查询预处理
        let (processed_terms, filtered_stop_words) = self.preprocess_query_with_stats(&request.query);
        if processed_terms.is_empty() {
            return Ok(KeywordSearchResults {
                results: Vec::new(),
                total_count: 0,
                search_time_ms: start_time.elapsed().as_millis() as u64,
                query_stats: QueryStatistics {
                    original_query: request.query.clone(),
                    processed_terms: Vec::new(),
                    filtered_stop_words: filtered_stop_words.clone(),
                    term_idf_scores: HashMap::new(),
                    avg_doc_length: 0.0,
                },
            });
        }

        // 2. 获取候选文档
        let candidate_documents = self.get_candidate_documents(&request).await?;
        if candidate_documents.is_empty() {
            return Ok(KeywordSearchResults {
                results: Vec::new(),
                total_count: 0,
                search_time_ms: start_time.elapsed().as_millis() as u64,
                query_stats: QueryStatistics {
                    original_query: request.query.clone(),
                    processed_terms,
                    filtered_stop_words: filtered_stop_words.clone(),
                    term_idf_scores: HashMap::new(),
                    avg_doc_length: 0.0,
                },
            });
        }

        // 3. 计算BM25得分
        let mut scored_results = Vec::new();
        let term_idf_scores = self.calculate_idf_scores(&processed_terms).await;
        let avg_doc_len = *self.avg_doc_length.read().await;

        for document in candidate_documents {
            let doc_index = self.get_or_create_document_index(&document).await?;
            let score = self.calculate_bm25_score(
                &processed_terms,
                &doc_index,
                &term_idf_scores,
                avg_doc_len,
            );

            // 应用最小得分阈值
            if let Some(min_score) = request.min_score {
                if score < min_score {
                    continue;
                }
            }

            let matched_terms = self.find_matched_terms(&processed_terms, &doc_index);
            let highlights = self.generate_highlights(&document.content, &matched_terms);

            scored_results.push(KeywordSearchResult {
                document,
                score,
                matched_terms,
                term_frequencies: doc_index.term_frequencies.clone(),
                highlights,
            });
        }

        // 4. 按得分排序
        scored_results.sort_by(|a, b| {
            b.score
                .partial_cmp(&a.score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        // 5. 应用分页
        let total_count = scored_results.len() as u64;
        let offset = request.offset.unwrap_or(0) as usize;
        let limit = request.limit.unwrap_or(50) as usize;

        let paginated_results = if offset < scored_results.len() {
            let end = std::cmp::min(offset + limit, scored_results.len());
            scored_results[offset..end].to_vec()
        } else {
            Vec::new()
        };

        // 6. 构建查询统计信息
        let query_stats = QueryStatistics {
            original_query: request.query,
            processed_terms,
            filtered_stop_words,
            term_idf_scores,
            avg_doc_length: avg_doc_len,
        };

        Ok(KeywordSearchResults {
            results: paginated_results,
            total_count,
            search_time_ms: start_time.elapsed().as_millis() as u64,
            query_stats,
        })
    }

    /// 重建所有文档的索引
    pub async fn rebuild_index(&self) -> Result<u64> {
        let start_time = std::time::Instant::now();

        // 清空现有索引
        self.document_indexes.write().await.clear();
        self.term_doc_frequencies.write().await.clear();
        *self.total_documents.write().await = 0;
        *self.avg_doc_length.write().await = 0.0;

        // 获取所有文档
        let documents = self.document_repository.list(Some(10000), Some(0)).await?;
        let mut indexed_count = 0;
        let mut total_length = 0;

        for document in documents {
            if document.status == DocumentStatus::Indexed {
                self.index_document(&document).await?;
                indexed_count += 1;
                total_length += document.content.len();
            }
        }

        // 更新统计信息
        *self.total_documents.write().await = indexed_count;
        if indexed_count > 0 {
            *self.avg_doc_length.write().await = total_length as f64 / indexed_count as f64;
        }

        tracing::info!(
            "索引重建完成: {} 个文档, 用时 {}ms",
            indexed_count,
            start_time.elapsed().as_millis()
        );

        Ok(indexed_count as u64)
    }

    /// 为单个文档建立索引
    pub async fn index_document(&self, document: &Document) -> Result<()> {
        let terms = self.tokenize_text(&document.content);
        let mut term_frequencies = HashMap::new();
        let mut total_terms = 0;

        // 计算词频
        for term in terms {
            if self.is_valid_term(&term) {
                *term_frequencies.entry(term).or_insert(0) += 1;
                total_terms += 1;
            }
        }

        // 创建文档索引
        let doc_index = DocumentIndex {
            document_id: document.id.clone(),
            term_frequencies: term_frequencies.clone(),
            total_terms,
            doc_length: document.content.len(),
            last_updated: chrono::Utc::now(),
        };

        // 更新索引缓存
        self.document_indexes
            .write()
            .await
            .insert(document.id.clone(), doc_index);

        // 更新词项文档频率
        let mut term_doc_freq = self.term_doc_frequencies.write().await;
        for term in term_frequencies.keys() {
            *term_doc_freq.entry(term.clone()).or_insert(0) += 1;
        }

        Ok(())
    }

    /// 删除文档索引
    pub async fn remove_document_index(&self, document_id: &str) -> Result<()> {
        if let Some(doc_index) = self.document_indexes.write().await.remove(document_id) {
            // 更新词项文档频率
            let mut term_doc_freq = self.term_doc_frequencies.write().await;
            for term in doc_index.term_frequencies.keys() {
                if let Some(count) = term_doc_freq.get_mut(term) {
                    *count = count.saturating_sub(1);
                    if *count == 0 {
                        term_doc_freq.remove(term);
                    }
                }
            }

            // 更新总文档数
            *self.total_documents.write().await =
                self.total_documents.read().await.saturating_sub(1);
        }

        Ok(())
    }

    /// 批量更新文档索引
    pub async fn batch_update_index(&self, document_ids: &[String]) -> Result<BatchIndexResult> {
        let start_time = std::time::Instant::now();
        let mut success_count = 0;
        let mut failed_documents = Vec::new();
        
        for document_id in document_ids {
            match self.document_repository.find_by_id(document_id).await {
                Ok(Some(document)) => {
                    match self.index_document(&document).await {
                        Ok(_) => success_count += 1,
                        Err(e) => {
                            tracing::warn!("Failed to index document {}: {}", document_id, e);
                            failed_documents.push(FailedIndexOperation {
                                document_id: document_id.clone(),
                                error_message: e.to_string(),
                            });
                        }
                    }
                }
                Ok(None) => {
                    failed_documents.push(FailedIndexOperation {
                        document_id: document_id.clone(),
                        error_message: "Document not found".to_string(),
                    });
                }
                Err(e) => {
                    tracing::error!("Failed to fetch document {}: {}", document_id, e);
                    failed_documents.push(FailedIndexOperation {
                        document_id: document_id.clone(),
                        error_message: format!("Database error: {}", e),
                    });
                }
            }
        }
        
        // 重新计算平均文档长度
        self.recalculate_avg_document_length().await?;
        
        let elapsed_ms = start_time.elapsed().as_millis() as u64;
        
        tracing::info!(
            "批量索引更新完成: 成功 {} 个，失败 {} 个，用时 {}ms",
            success_count,
            failed_documents.len(),
            elapsed_ms
        );
        
        Ok(BatchIndexResult {
            total_documents: document_ids.len() as u32,
            successful_updates: success_count,
            failed_updates: failed_documents.len() as u32,
            failed_operations: failed_documents,
            elapsed_time_ms: elapsed_ms,
        })
    }
    
    /// 批量删除文档索引
    pub async fn batch_remove_index(&self, document_ids: &[String]) -> Result<BatchIndexResult> {
        let start_time = std::time::Instant::now();
        let mut success_count = 0;
        let mut failed_documents = Vec::new();
        
        for document_id in document_ids {
            match self.remove_document_index(document_id).await {
                Ok(_) => success_count += 1,
                Err(e) => {
                    tracing::warn!("Failed to remove index for document {}: {}", document_id, e);
                    failed_documents.push(FailedIndexOperation {
                        document_id: document_id.clone(),
                        error_message: e.to_string(),
                    });
                }
            }
        }
        
        // 重新计算平均文档长度
        self.recalculate_avg_document_length().await?;
        
        let elapsed_ms = start_time.elapsed().as_millis() as u64;
        
        tracing::info!(
            "批量索引删除完成: 成功 {} 个，失败 {} 个，用时 {}ms",
            success_count,
            failed_documents.len(),
            elapsed_ms
        );
        
        Ok(BatchIndexResult {
            total_documents: document_ids.len() as u32,
            successful_updates: success_count,
            failed_updates: failed_documents.len() as u32,
            failed_operations: failed_documents,
            elapsed_time_ms: elapsed_ms,
        })
    }
    
    /// 重新计算平均文档长度
    async fn recalculate_avg_document_length(&self) -> Result<()> {
        let indexes = self.document_indexes.read().await;
        let total_docs = indexes.len();
        
        if total_docs == 0 {
            *self.avg_doc_length.write().await = 0.0;
            *self.total_documents.write().await = 0;
            return Ok(());
        }
        
        let total_length: usize = indexes.values().map(|idx| idx.doc_length).sum();
        let avg_length = total_length as f64 / total_docs as f64;
        
        *self.avg_doc_length.write().await = avg_length;
        *self.total_documents.write().await = total_docs as u32;
        
        Ok(())
    }
    
    /// 获取停用词列表
    pub fn get_stop_words(&self) -> &HashSet<String> {
        &self.config.stop_words
    }
    
    /// 添加停用词
    pub fn add_stop_words(&mut self, words: &[String]) {
        for word in words {
            self.config.stop_words.insert(word.clone());
        }
    }
    
    /// 移除停用词
    pub fn remove_stop_words(&mut self, words: &[String]) {
        for word in words {
            self.config.stop_words.remove(word);
        }
    }
    
    /// 清空所有停用词
    pub fn clear_stop_words(&mut self) {
        self.config.stop_words.clear();
    }
    
    /// 设置停用词列表
    pub fn set_stop_words(&mut self, words: HashSet<String>) {
        self.config.stop_words = words;
    }
    pub async fn get_index_statistics(&self) -> IndexStatistics {
        let document_indexes = self.document_indexes.read().await;
        let term_doc_frequencies = self.term_doc_frequencies.read().await;

        IndexStatistics {
            total_documents: *self.total_documents.read().await,
            total_terms: term_doc_frequencies.len() as u32,
            avg_doc_length: *self.avg_doc_length.read().await,
            index_size_mb: self.estimate_index_size(&document_indexes) / (1024.0 * 1024.0),
            last_updated: current_millis(), // TODO: 跟踪实际的最后更新时间
        }
    }

    /// 查询预处理
    fn preprocess_query(&self, query: &str) -> Vec<String> {
        let terms = self.tokenize_text(query);
        terms
            .into_iter()
            .filter(|term| self.is_valid_term(term) && !self.config.stop_words.contains(term))
            .collect()
    }
    
    /// 查询预处理（带停用词统计）
    fn preprocess_query_with_stats(&self, query: &str) -> (Vec<String>, Vec<String>) {
        let terms = self.tokenize_text(query);
        let mut processed_terms = Vec::new();
        let mut filtered_stop_words = Vec::new();
        
        for term in terms {
            if !self.is_valid_term(&term) {
                continue; // 跳过无效词项
            }
            
            if self.config.stop_words.contains(&term) {
                filtered_stop_words.push(term);
            } else {
                processed_terms.push(term);
            }
        }
        
        (processed_terms, filtered_stop_words)
    }

    /// 文本分词
    fn tokenize_text(&self, text: &str) -> Vec<String> {
        let mut terms = Vec::new();

        // 清理文本：移除特殊字符，保留字母数字和中文
        let clean_regex = Regex::new(r"[^\p{L}\p{N}\s]").unwrap();
        let cleaned_text = clean_regex.replace_all(text, " ");

        // 按空格分割英文单词
        for word in cleaned_text.split_whitespace() {
            let word = word.to_lowercase();
            if !word.is_empty() {
                terms.push(word);
            }
        }

        // 中文字符分词（简单的字符级分词）
        for grapheme in text.graphemes(true) {
            if grapheme
                .chars()
                .all(|c| c.is_ascii_alphabetic() || c.is_ascii_digit())
            {
                continue; // 跳过已经处理的ASCII字符
            }

            if grapheme.chars().any(|c| {
                c as u32 >= 0x4E00 && c as u32 <= 0x9FFF // 中文字符范围
            }) {
                terms.push(grapheme.to_string());
            }
        }

        terms
    }

    /// 验证词项是否有效
    fn is_valid_term(&self, term: &str) -> bool {
        let len = term.chars().count();
        len >= self.config.min_term_length && len <= self.config.max_term_length
    }

    /// 获取候选文档
    async fn get_candidate_documents(
        &self,
        request: &KeywordSearchRequest,
    ) -> Result<Vec<Document>> {
        // 根据知识库过滤获取文档
        let documents = match request.knowledge_base_id {
            Some(ref kb_id) => {
                self.document_repository
                    .find_by_knowledge_base(kb_id, None, None)
                    .await?
            }
            None => self.document_repository.list(Some(10000), Some(0)).await?,
        };

        // 按状态过滤
        let filtered_documents = if let Some(ref status_filter) = request.status_filter {
            documents
                .into_iter()
                .filter(|doc| status_filter.contains(&doc.status))
                .collect()
        } else {
            // 默认只搜索已索引的文档
            documents
                .into_iter()
                .filter(|doc| doc.status == DocumentStatus::Indexed)
                .collect()
        };

        Ok(filtered_documents)
    }

    /// 获取或创建文档索引
    async fn get_or_create_document_index(&self, document: &Document) -> Result<DocumentIndex> {
        let indexes = self.document_indexes.read().await;
        if let Some(index) = indexes.get(&document.id) {
            return Ok(index.clone());
        }
        drop(indexes);

        // 创建索引
        self.index_document(document).await?;
        let indexes = self.document_indexes.read().await;
        Ok(indexes.get(&document.id).unwrap().clone())
    }

    /// 计算IDF得分
    async fn calculate_idf_scores(&self, terms: &[String]) -> HashMap<String, f64> {
        let term_doc_freq = self.term_doc_frequencies.read().await;
        let total_docs = *self.total_documents.read().await as f64;

        let mut idf_scores = HashMap::new();

        for term in terms {
            let doc_freq = *term_doc_freq.get(term).unwrap_or(&0) as f64;
            let idf = if doc_freq > 0.0 {
                ((total_docs - doc_freq + 0.5) / (doc_freq + 0.5)).ln()
            } else {
                0.0
            };
            idf_scores.insert(term.clone(), idf);
        }

        idf_scores
    }

    /// 计算BM25得分
    fn calculate_bm25_score(
        &self,
        query_terms: &[String],
        doc_index: &DocumentIndex,
        idf_scores: &HashMap<String, f64>,
        avg_doc_length: f64,
    ) -> f64 {
        let mut score = 0.0;
        let doc_length = doc_index.total_terms as f64;

        for term in query_terms {
            let tf = *doc_index.term_frequencies.get(term).unwrap_or(&0) as f64;
            let idf = idf_scores.get(term).unwrap_or(&0.0);

            if tf > 0.0 {
                let normalized_tf = (tf * (self.config.k1 + 1.0))
                    / (tf
                        + self.config.k1
                            * (1.0 - self.config.b
                                + self.config.b * (doc_length / avg_doc_length)));
                score += idf * normalized_tf;
            }
        }

        score
    }

    /// 查找匹配的词项
    fn find_matched_terms(&self, query_terms: &[String], doc_index: &DocumentIndex) -> Vec<String> {
        query_terms
            .iter()
            .filter(|term| doc_index.term_frequencies.contains_key(*term))
            .cloned()
            .collect()
    }

    /// 生成高亮片段
    fn generate_highlights(&self, content: &str, matched_terms: &[String]) -> Vec<String> {
        let mut highlights = Vec::new();

        for term in matched_terms {
            if let Some(pos) = content.to_lowercase().find(&term.to_lowercase()) {
                let start = pos.saturating_sub(50);
                let end = std::cmp::min(pos + term.len() + 50, content.len());
                let snippet = &content[start..end];

                // 简单的高亮标记
                let highlighted = snippet.replace(term, &format!("<mark>{}</mark>", term));

                highlights.push(format!("...{}...", highlighted));
            }
        }

        highlights.truncate(3); // 最多返回3个高亮片段
        highlights
    }

    /// 估算索引大小
    fn estimate_index_size(&self, indexes: &HashMap<String, DocumentIndex>) -> f64 {
        let mut size = 0.0;

        for index in indexes.values() {
            size += std::mem::size_of_val(&next_id()) as f64; // document_id
            size += std::mem::size_of::<u32>() as f64; // total_terms
            size += std::mem::size_of::<usize>() as f64; // doc_length

            for term in index.term_frequencies.keys() {
                size += term.len() as f64; // term string
                size += std::mem::size_of::<u32>() as f64; // frequency
            }
        }

        size
    }
}

/// 索引统计信息
#[derive(Debug, Clone)]
pub struct IndexStatistics {
    /// 总文档数
    pub total_documents: u32,
    /// 总词项数
    pub total_terms: u32,
    /// 平均文档长度
    pub avg_doc_length: f64,
    /// 索引大小（MB）
    pub index_size_mb: f64,
    /// 最后更新时间
    pub last_updated: i64,
}

/// 批量索引操作结果
#[derive(Debug, Clone)]
pub struct BatchIndexResult {
    /// 总文档数
    pub total_documents: u32,
    /// 成功更新的文档数
    pub successful_updates: u32,
    /// 失败更新的文档数
    pub failed_updates: u32,
    /// 失败的操作详情
    pub failed_operations: Vec<FailedIndexOperation>,
    /// 操作耗时（毫秒）
    pub elapsed_time_ms: u64,
}

/// 失败的索引操作
#[derive(Debug, Clone)]
pub struct FailedIndexOperation {
    /// 文档ID
    pub document_id: String,
    /// 错误消息
    pub error_message: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_tokenize_text() {
        let config = BM25Config::default();
        // 这里需要创建一个mock的document_repository来测试
        // 由于涉及异步和Arc，在实际项目中建议创建专门的测试模块
    }

    #[test]
    fn test_preprocess_query() {
        // 测试查询预处理功能
    }

    #[test]
    fn test_bm25_calculation() {
        // 测试BM25得分计算
    }
}
