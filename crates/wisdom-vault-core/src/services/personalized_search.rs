use anyhow::Result;
use chrono::Timelike;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use wisdom_vault_common::time::{current_millis, to_datetime};

use crate::services::{HybridSearchResult, QueryContext, QueryIntent};
use wisdom_vault_database::models::{
    Document, DocumentMetadata, DocumentProcessingMetadata, DocumentStatus,
};

/// 个性化搜索服务
pub struct PersonalizedSearchService {
    /// 用户行为分析器
    user_behavior_analyzer: Arc<UserBehaviorAnalyzer>,
    /// 个性化权重计算器
    personalization_engine: Arc<PersonalizationEngine>,
    /// 推荐算法管理器
    recommendation_manager: Arc<RecommendationManager>,
    /// 配置
    config: PersonalizationConfig,
}

/// 个性化配置
#[derive(Debug, Clone)]
pub struct PersonalizationConfig {
    /// 用户行为历史保留天数
    pub behavior_retention_days: u32,
    /// 最小交互次数阈值（用于建立用户画像）
    pub min_interactions_threshold: u32,
    /// 个性化权重强度
    pub personalization_strength: f64,
    /// 推荐结果数量
    pub recommendation_count: u32,
    /// 启用协同过滤
    pub enable_collaborative_filtering: bool,
    /// 启用内容推荐
    pub enable_content_based_filtering: bool,
    /// 启用混合推荐
    pub enable_hybrid_recommendation: bool,
    /// 冷启动策略
    pub cold_start_strategy: ColdStartStrategy,
}

impl Default for PersonalizationConfig {
    fn default() -> Self {
        Self {
            behavior_retention_days: 90,
            min_interactions_threshold: 5,
            personalization_strength: 0.7,
            recommendation_count: 10,
            enable_collaborative_filtering: true,
            enable_content_based_filtering: true,
            enable_hybrid_recommendation: true,
            cold_start_strategy: ColdStartStrategy::PopularityBased,
        }
    }
}

/// 冷启动策略
#[derive(Debug, Clone, PartialEq)]
pub enum ColdStartStrategy {
    /// 基于流行度
    PopularityBased,
    /// 基于内容相似性
    ContentSimilarity,
    /// 随机推荐
    Random,
    /// 基于用户群体
    DemographicBased,
}

/// 用户行为分析器
pub struct UserBehaviorAnalyzer {
    /// 用户行为存储
    user_behaviors: Arc<RwLock<HashMap<String, UserBehaviorProfile>>>,
    /// 行为权重配置
    behavior_weights: BehaviorWeights,
}

/// 行为权重配置
#[derive(Debug, Clone)]
pub struct BehaviorWeights {
    /// 搜索权重
    pub search_weight: f64,
    /// 点击权重
    pub click_weight: f64,
    /// 阅读时长权重
    pub reading_time_weight: f64,
    /// 下载权重
    pub download_weight: f64,
    /// 收藏权重
    pub bookmark_weight: f64,
    /// 分享权重
    pub share_weight: f64,
    /// 评分权重
    pub rating_weight: f64,
}

impl Default for BehaviorWeights {
    fn default() -> Self {
        Self {
            search_weight: 1.0,
            click_weight: 2.0,
            reading_time_weight: 3.0,
            download_weight: 4.0,
            bookmark_weight: 5.0,
            share_weight: 4.5,
            rating_weight: 6.0,
        }
    }
}

/// 用户行为画像
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserBehaviorProfile {
    /// 用户ID
    pub user_id: String,
    /// 搜索历史
    pub search_history: Vec<SearchBehavior>,
    /// 文档交互历史
    pub document_interactions: HashMap<String, DocumentInteraction>,
    /// 兴趣类别
    pub interest_categories: Vec<String>,
    /// 偏好的文档类型
    pub preferred_document_types: HashMap<String, f64>,
    /// 偏好的知识库
    pub preferred_knowledge_bases: HashMap<String, f64>,
    /// 搜索时间偏好
    pub search_time_preferences: TimePreferences,
    /// 查询复杂度偏好
    pub query_complexity_preference: f64,
    /// 搜索行为统计
    pub search_behavior: SearchBehaviorStats,
    /// 时间偏好
    pub time_preference: TimePreferences,
    /// 最后更新时间
    pub last_updated: i64,
}

/// 搜索行为统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchBehaviorStats {
    /// 平均查询长度
    pub average_query_length: f64,
    /// 平均会话长度
    pub average_session_length: f64,
    /// 偏好最新内容
    pub prefers_recent_content: bool,
    /// 偏好复杂度级别
    pub preferred_complexity_level: String,
}

impl Default for SearchBehaviorStats {
    fn default() -> Self {
        Self {
            average_query_length: 5.0,
            average_session_length: 10.0,
            prefers_recent_content: false,
            preferred_complexity_level: "intermediate".to_string(),
        }
    }
}

/// 搜索行为
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchBehavior {
    /// 查询文本
    pub query_text: String,
    /// 查询时间
    pub query_time: i64,
    /// 查询上下文
    pub query_context: QueryContext,
    /// 结果点击情况
    pub result_clicks: Vec<ResultClick>,
    /// 搜索满意度（用户反馈或推断）
    pub satisfaction_score: Option<f64>,
}

/// 结果点击记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResultClick {
    /// 文档ID
    pub document_id: String,
    /// 点击位置（排名）
    pub click_position: u32,
    /// 点击时间
    pub click_time: u64,
    /// 阅读时长（秒）
    pub reading_duration: Option<u32>,
}

/// 文档交互记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentInteraction {
    /// 文档ID
    pub document_id: String,
    /// 交互类型
    pub interaction_types: Vec<InteractionType>,
    /// 总交互次数
    pub total_interactions: u32,
    /// 总阅读时长
    pub total_reading_time: u32,
    /// 最后交互时间
    pub last_interaction: i64,
    /// 交互评分（综合得分）
    pub interaction_score: f64,
}

/// 交互类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InteractionType {
    /// 查看
    View { duration: u32 },
    /// 下载
    Download { timestamp: i64 },
    /// 收藏
    Bookmark { timestamp: i64 },
    /// 分享
    Share { timestamp: i64 },
    /// 评分
    Rating { score: f32, timestamp: i64 },
    /// 评论
    Comment { timestamp: i64 },
}

/// 时间偏好
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimePreferences {
    /// 偏好的搜索时间段
    pub preferred_hours: Vec<u32>,
    /// 高峰时间段
    pub peak_hours: Vec<u32>,
    /// 工作日vs周末偏好
    pub weekday_preference: f64, // 0.0-1.0，越高越偏好工作日
    /// 搜索频率分布
    pub search_frequency_pattern: HashMap<String, f64>,
}

impl Default for TimePreferences {
    fn default() -> Self {
        Self {
            preferred_hours: vec![9, 10, 11, 14, 15, 16],
            peak_hours: vec![9, 10, 11, 14, 15, 16],
            weekday_preference: 0.7,
            search_frequency_pattern: HashMap::new(),
        }
    }
}

/// 个性化引擎
pub struct PersonalizationEngine {
    /// 用户相似度计算器
    user_similarity_calculator: UserSimilarityCalculator,
    /// 内容特征提取器
    content_feature_extractor: ContentFeatureExtractor,
    /// 个性化评分器
    personalized_scorer: PersonalizedScorer,
}

/// 用户相似度计算器
pub struct UserSimilarityCalculator;

/// 内容特征提取器
pub struct ContentFeatureExtractor;

/// 个性化评分器
pub struct PersonalizedScorer;

/// 推荐算法管理器
pub struct RecommendationManager {
    /// 协同过滤推荐器
    collaborative_filter: CollaborativeFilteringRecommender,
    /// 基于内容的推荐器
    content_based_filter: ContentBasedRecommender,
    /// 混合推荐器
    hybrid_recommender: HybridRecommender,
}

/// 协同过滤推荐器
pub struct CollaborativeFilteringRecommender;

/// 基于内容的推荐器
pub struct ContentBasedRecommender;

/// 混合推荐器
pub struct HybridRecommender;

/// 个性化推荐结果
#[derive(Debug, Clone)]
pub struct PersonalizationResult {
    /// 个性化调整后的搜索结果
    pub personalized_results: Vec<HybridSearchResult>,
    /// 个性化得分调整
    pub personalization_adjustments: Vec<PersonalizationAdjustment>,
    /// 推荐理由
    pub recommendation_reasons: Vec<RecommendationReason>,
    /// 用户画像摘要
    pub user_profile_summary: UserProfileSummary,
}

/// 个性化调整信息
#[derive(Debug, Clone)]
pub struct PersonalizationAdjustment {
    /// 文档ID
    pub document_id: String,
    /// 原始得分
    pub original_score: f64,
    /// 调整后得分
    pub adjusted_score: f64,
    /// 调整因子
    pub adjustment_factors: AdjustmentFactors,
}

/// 调整因子
#[derive(Debug, Clone)]
pub struct AdjustmentFactors {
    /// 兴趣匹配度
    pub interest_match: f64,
    /// 历史偏好
    pub historical_preference: f64,
    /// 时间偏好
    pub time_preference: f64,
    /// 相似用户影响
    pub similar_user_influence: f64,
}

/// 推荐理由
#[derive(Debug, Clone)]
pub struct RecommendationReason {
    /// 文档ID
    pub document_id: String,
    /// 推荐理由类型
    pub reason_type: RecommendationReasonType,
    /// 理由描述
    pub description: String,
    /// 置信度
    pub confidence: f64,
}

/// 推荐理由类型
#[derive(Debug, Clone)]
pub enum RecommendationReasonType {
    /// 基于历史搜索
    HistoricalSearch,
    /// 基于兴趣主题
    InterestTopic,
    /// 基于相似用户
    SimilarUsers,
    /// 基于内容相似性
    ContentSimilarity,
    /// 基于流行度
    Popularity,
    /// 基于时间偏好
    TimePreference,
}

/// 用户画像摘要
#[derive(Debug, Clone)]
pub struct UserProfileSummary {
    /// 主要兴趣主题
    pub top_interests: Vec<(String, f64)>,
    /// 偏好文档类型
    pub preferred_types: Vec<(String, f64)>,
    /// 搜索活跃度
    pub search_activity_level: ActivityLevel,
    /// 专业程度
    pub expertise_level: ExpertiseLevel,
}

/// 活跃度等级
#[derive(Debug, Clone)]
pub enum ActivityLevel {
    Low,
    Medium,
    High,
    VeryHigh,
}

/// 专业程度等级
#[derive(Debug, Clone)]
pub enum ExpertiseLevel {
    Beginner,
    Intermediate,
    Advanced,
    Expert,
}

impl PersonalizedSearchService {
    /// 创建个性化搜索服务
    pub fn new(config: PersonalizationConfig) -> Self {
        let user_behavior_analyzer = Arc::new(UserBehaviorAnalyzer::new());
        let personalization_engine = Arc::new(PersonalizationEngine::new());
        let recommendation_manager = Arc::new(RecommendationManager::new());

        Self {
            user_behavior_analyzer,
            personalization_engine,
            recommendation_manager,
            config,
        }
    }

    /// 记录用户搜索行为
    pub async fn record_search_behavior(
        &self,
        user_id: String,
        search_behavior: SearchBehavior,
    ) -> Result<()> {
        self.user_behavior_analyzer
            .record_search_behavior(user_id, search_behavior)
            .await
    }

    /// 记录文档交互
    pub async fn record_document_interaction(
        &self,
        user_id: String,
        document_id: String,
        interaction_type: InteractionType,
    ) -> Result<()> {
        self.user_behavior_analyzer
            .record_document_interaction(user_id, document_id, interaction_type)
            .await
    }

    /// 对搜索结果进行个性化调整
    pub async fn personalize_search_results(
        &self,
        user_id: &str,
        search_results: Vec<HybridSearchResult>,
        query_context: &QueryContext,
    ) -> Result<PersonalizationResult> {
        // 获取用户行为画像
        let user_profile = self
            .user_behavior_analyzer
            .get_user_profile(user_id)
            .await?;

        // 计算个性化调整
        let adjustments = self
            .personalization_engine
            .calculate_personalization_adjustments(&search_results, &user_profile, query_context)
            .await?;

        // 应用个性化调整
        let mut personalized_results = search_results;
        for (i, adjustment) in adjustments.iter().enumerate() {
            if i < personalized_results.len() {
                personalized_results[i].final_score = adjustment.adjusted_score;
            }
        }

        // 重新排序
        personalized_results.sort_by(|a, b| {
            b.final_score
                .partial_cmp(&a.final_score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        // 生成推荐理由
        let recommendation_reasons = self
            .generate_recommendation_reasons(&personalized_results, &user_profile, &adjustments)
            .await?;

        // 生成用户画像摘要
        let user_profile_summary = self.generate_user_profile_summary(&user_profile).await;

        Ok(PersonalizationResult {
            personalized_results,
            personalization_adjustments: adjustments,
            recommendation_reasons,
            user_profile_summary,
        })
    }

    /// 获取个性化推荐
    pub async fn get_personalized_recommendations(
        &self,
        user_id: &str,
        context: Option<QueryContext>,
    ) -> Result<Vec<Document>> {
        let user_profile = self
            .user_behavior_analyzer
            .get_user_profile(user_id)
            .await?;

        let recommendations = if user_profile.search_history.len()
            < self.config.min_interactions_threshold as usize
        {
            // 冷启动情况
            self.handle_cold_start_recommendations(user_id, context)
                .await?
        } else {
            // 基于用户画像的推荐
            self.generate_profile_based_recommendations(&user_profile, context)
                .await?
        };

        Ok(recommendations)
    }

    /// 更新用户偏好
    pub async fn update_user_preferences(
        &self,
        user_id: &str,
        feedback: UserFeedback,
    ) -> Result<()> {
        self.user_behavior_analyzer
            .update_user_preferences(user_id, feedback)
            .await
    }

    /// 处理冷启动推荐
    async fn handle_cold_start_recommendations(
        &self,
        user_id: &str,
        context: Option<QueryContext>,
    ) -> Result<Vec<Document>> {
        match self.config.cold_start_strategy {
            ColdStartStrategy::PopularityBased => self.get_popular_documents().await,
            ColdStartStrategy::ContentSimilarity => {
                if let Some(ctx) = context {
                    self.get_content_similar_documents(&ctx).await
                } else {
                    self.get_popular_documents().await
                }
            }
            ColdStartStrategy::Random => self.get_random_documents().await,
            ColdStartStrategy::DemographicBased => {
                self.get_demographic_based_documents(user_id).await
            }
        }
    }

    /// 生成基于用户画像的推荐
    async fn generate_profile_based_recommendations(
        &self,
        user_profile: &UserBehaviorProfile,
        context: Option<QueryContext>,
    ) -> Result<Vec<Document>> {
        let mut recommendations = Vec::new();

        if self.config.enable_collaborative_filtering {
            let collab_recs = self
                .recommendation_manager
                .collaborative_filter
                .get_recommendations(user_profile, &*self.user_behavior_analyzer, &self.config)
                .await?;
            recommendations.extend(collab_recs);
        }

        if self.config.enable_content_based_filtering {
            let content_recs = self
                .recommendation_manager
                .content_based_filter
                .get_recommendations(user_profile, &*self.user_behavior_analyzer, &self.config)
                .await?;
            recommendations.extend(content_recs);
        }

        if self.config.enable_hybrid_recommendation {
            let hybrid_recs = self
                .recommendation_manager
                .hybrid_recommender
                .get_recommendations(
                    user_profile,
                    &self.recommendation_manager.collaborative_filter,
                    &self.recommendation_manager.content_based_filter,
                    &*self.user_behavior_analyzer,
                    &self.config,
                    context,
                )
                .await?;
            recommendations.extend(hybrid_recs);
        }

        // 去重并限制数量
        recommendations.sort_by_key(|doc| doc.id.clone());
        recommendations.dedup_by_key(|doc| doc.id.clone());
        recommendations.truncate(self.config.recommendation_count as usize);

        Ok(recommendations)
    }

    /// 获取流行文档
    async fn get_popular_documents(&self) -> Result<Vec<Document>> {
        // 基于用户行为统计实现流行文档推荐
        let user_behaviors = self.user_behavior_analyzer.user_behaviors.read().await;
        let mut document_popularity: HashMap<String, f64> = HashMap::new();

        // 统计所有用户的文档交互分数
        for (_, profile) in user_behaviors.iter() {
            for (doc_id, interaction_score) in &profile.document_interactions {
                *document_popularity.entry(doc_id.clone()).or_insert(0.0) +=
                    interaction_score.interaction_score;
            }
        }

        // 按流行度排序并转换为文档ID列表
        let mut popular_docs: Vec<(String, f64)> = document_popularity.into_iter().collect();
        popular_docs.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        popular_docs.truncate(self.config.recommendation_count as usize);

        // 创建模拟文档（实际应用中应从数据库查询）
        let documents: Vec<Document> = popular_docs
            .into_iter()
            .enumerate()
            .map(|(index, (doc_id, score))| Document {
                id: doc_id.clone(),
                knowledge_base_id: "default".to_string(),
                title: format!("Popular Document #{}", index + 1),
                content: format!("This is a popular document with score: {:.2}", score),
                summary: None,
                file_type: "text".to_string(),
                file_size: 1024,
                file_path: None,
                original_filename: Some(format!("doc_{}.txt", doc_id)),
                mime_type: "text/plain".to_string(),
                language: Some("en".to_string()),
                metadata: DocumentMetadata {
                    author: None,
                    subject: None,
                    creator: None,
                    producer: None,
                    keywords: Vec::new(),
                    source_url: None,
                    page_count: None,
                    word_count: Some(50),
                    character_count: Some(300),
                    creation_date: None,
                    modification_date: None,
                    content_type: "text/plain".to_string(),
                    content_encoding: None,
                    content_language: None,
                    custom_fields: serde_json::Value::Null,
                },
                processing_metadata: DocumentProcessingMetadata {
                    extraction_method: "simulated".to_string(),
                    extraction_quality: 1.0,
                    processing_time_ms: 100,
                    parsing_errors: Vec::new(),
                    parsing_warnings: Vec::new(),
                    file_checksum: format!("hash_{}", doc_id),
                    structured_content: None,
                    processing_attempts: 1,
                    last_processing_attempt: Some(current_millis()),
                },
                status: DocumentStatus::Indexed,
                uploaded_by: "system".to_string(),
                indexed_at: Some(current_millis()),
                created_at: current_millis(),
                updated_at: current_millis(),
            })
            .collect();

        Ok(documents)
    }

    /// 获取内容相似文档
    async fn get_content_similar_documents(&self, context: &QueryContext) -> Result<Vec<Document>> {
        // 基于查询上下文的关键词提取相似内容
        let query_keywords: Vec<String> = context
            .query_text
            .split_whitespace()
            .filter(|word| word.len() > 2)
            .map(|word| word.to_lowercase())
            .collect();

        if query_keywords.is_empty() {
            return Ok(Vec::new());
        }

        let user_behaviors = self.user_behavior_analyzer.user_behaviors.read().await;
        let mut similar_documents: Vec<(String, f64)> = Vec::new();

        // 基于查询关键词计算文档相似性
        for (_, profile) in user_behaviors.iter() {
            for (doc_id, _) in &profile.document_interactions {
                // 简化的相似度计算：基于关键词匹配
                let similarity_score = self.calculate_keyword_similarity(&query_keywords, doc_id);
                if similarity_score > 0.1 {
                    similar_documents.push((doc_id.clone(), similarity_score));
                }
            }
        }

        // 按相似度排序并限制数量
        similar_documents.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        similar_documents.truncate(self.config.recommendation_count as usize);

        // 生成相似文档
        let documents: Vec<Document> = similar_documents
            .into_iter()
            .enumerate()
            .map(|(index, (doc_id, similarity))| Document {
                id: doc_id.clone(),
                knowledge_base_id: context
                    .knowledge_base_id
                    .clone()
                    .unwrap_or("default".to_string()),
                title: format!("Similar Document #{} ({})", index + 1, doc_id),
                content: format!(
                    "Content similar to query '{}' with similarity: {:.2}",
                    context.query_text, similarity
                ),
                summary: None,
                file_type: "text".to_string(),
                file_size: 1024,
                file_path: None,
                original_filename: Some(format!("similar_{}.txt", doc_id)),
                mime_type: "text/plain".to_string(),
                language: Some("en".to_string()),
                metadata: DocumentMetadata {
                    author: None,
                    subject: None,
                    creator: None,
                    producer: None,
                    keywords: Vec::new(),
                    source_url: None,
                    page_count: None,
                    word_count: Some(30),
                    character_count: Some(200),
                    creation_date: None,
                    modification_date: None,
                    content_type: "text/plain".to_string(),
                    content_encoding: None,
                    content_language: None,
                    custom_fields: serde_json::Value::Null,
                },
                processing_metadata: DocumentProcessingMetadata {
                    extraction_method: "similarity_based".to_string(),
                    extraction_quality: 1.0,
                    processing_time_ms: 50,
                    parsing_errors: Vec::new(),
                    parsing_warnings: Vec::new(),
                    file_checksum: format!("sim_hash_{}", doc_id),
                    structured_content: Some(format!("Similar to {}", context.query_text)),
                    processing_attempts: 1,
                    last_processing_attempt: Some(current_millis()),
                },
                status: DocumentStatus::Indexed,
                uploaded_by: "system".to_string(),
                indexed_at: Some(current_millis()),
                created_at: current_millis(),
                updated_at: current_millis(),
            })
            .collect();

        Ok(documents)
    }

    /// 计算关键词相似度（简化实现）
    fn calculate_keyword_similarity(&self, query_keywords: &[String], doc_id: &str) -> f64 {
        // 简化实现：基于文档ID中包含的关键词匹配
        let doc_words: Vec<String> = doc_id
            .split(['_', '-', ' '])
            .map(|word| word.to_lowercase())
            .collect();

        let mut matches = 0;
        for keyword in query_keywords {
            if doc_words.iter().any(|word| word.contains(keyword)) {
                matches += 1;
            }
        }

        matches as f64 / query_keywords.len() as f64
    }

    /// 获取随机文档
    async fn get_random_documents(&self) -> Result<Vec<Document>> {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let user_behaviors = self.user_behavior_analyzer.user_behaviors.read().await;
        let all_doc_ids: Vec<String> = user_behaviors
            .values()
            .flat_map(|profile| profile.document_interactions.keys().cloned())
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();

        if all_doc_ids.is_empty() {
            // 如果没有用户行为数据，生成一些示例随机文档
            let documents: Vec<Document> = (1..=self.config.recommendation_count)
                .map(|i| Document {
                    id: format!("random_doc_{}", i),
                    title: format!("Random Document #{}", i),
                    content: format!("This is a randomly recommended document #{}", i),
                    knowledge_base_id: "default".to_string(),
                    summary: None,
                    file_type: "text".to_string(),
                    file_size: 1024,
                    file_path: None,
                    original_filename: Some(format!("random_doc_{}.txt", i)),
                    mime_type: "text/plain".to_string(),
                    language: Some("en".to_string()),
                    status: DocumentStatus::Indexed,
                    created_at: current_millis(),
                    updated_at: current_millis(),
                    metadata: DocumentMetadata {
                        author: None,
                        subject: None,
                        creator: None,
                        producer: None,
                        keywords: Vec::new(),
                        source_url: None,
                        page_count: None,
                        word_count: Some(50),
                        character_count: Some(300),
                        creation_date: None,
                        modification_date: None,
                        content_type: "text/plain".to_string(),
                        content_encoding: None,
                        content_language: None,
                        custom_fields: serde_json::Value::Null,
                    },
                    processing_metadata: DocumentProcessingMetadata {
                        extraction_method: "random".to_string(),
                        extraction_quality: 1.0,
                        processing_time_ms: 10,
                        parsing_errors: Vec::new(),
                        parsing_warnings: Vec::new(),
                        file_checksum: format!("random_hash_{}", i),
                        structured_content: None,
                        processing_attempts: 1,
                        last_processing_attempt: Some(current_millis()),
                    },
                    uploaded_by: "system".to_string(),
                    indexed_at: Some(current_millis()),
                })
                .collect();
            return Ok(documents);
        }

        // 使用伪随机方式选择文档（基于当前时间戳）
        let current_time = current_millis();
        let mut hasher = DefaultHasher::new();
        current_time.hash(&mut hasher);
        let seed = hasher.finish() as usize;

        let mut selected_docs = Vec::new();
        let max_count = std::cmp::min(self.config.recommendation_count as usize, all_doc_ids.len());

        for i in 0..max_count {
            let index = (seed + i * 7) % all_doc_ids.len(); // 简单的伪随机索引
            let doc_id = &all_doc_ids[index];

            selected_docs.push(Document {
                id: doc_id.clone(),
                title: format!("Random Document: {}", doc_id),
                content: format!("Randomly selected document with ID: {}", doc_id),
                knowledge_base_id: "default".to_string(),
                summary: None,
                file_type: "text".to_string(),
                file_size: 1024,
                file_path: None,
                original_filename: Some(format!("random_{}.txt", doc_id)),
                mime_type: "text/plain".to_string(),
                language: Some("en".to_string()),
                status: DocumentStatus::Indexed,
                uploaded_by: "system".to_string(),
                indexed_at: Some(current_millis()),
                created_at: current_millis(),
                updated_at: current_millis(),
                metadata: DocumentMetadata {
                    author: None,
                    subject: None,
                    creator: None,
                    producer: None,
                    keywords: Vec::new(),
                    source_url: None,
                    page_count: None,
                    word_count: Some(50),
                    character_count: Some(300),
                    creation_date: None,
                    modification_date: None,
                    content_type: "text/plain".to_string(),
                    content_encoding: None,
                    content_language: None,
                    custom_fields: serde_json::Value::Null,
                },
                processing_metadata: DocumentProcessingMetadata {
                    extraction_method: "random".to_string(),
                    extraction_quality: 1.0,
                    processing_time_ms: 10,
                    parsing_errors: Vec::new(),
                    parsing_warnings: Vec::new(),
                    file_checksum: format!("random_hash_{}", doc_id),
                    structured_content: None,
                    processing_attempts: 1,
                    last_processing_attempt: Some(current_millis()),
                },
            });
        }

        Ok(selected_docs)
    }

    /// 获取基于人口统计学的文档
    async fn get_demographic_based_documents(&self, user_id: &str) -> Result<Vec<Document>> {
        let user_behaviors = self.user_behavior_analyzer.user_behaviors.read().await;

        // 获取当前用户的行为画像
        let current_user_profile = match user_behaviors.get(user_id) {
            Some(profile) => profile,
            None => {
                // 用户无历史行为，返回默认推荐
                return self.get_random_documents().await;
            }
        };

        // 简化的人口统计学分析：基于用户的兴趣类别和访问时间模式
        let mut demographic_scores: HashMap<String, f64> = HashMap::new();

        // 分析所有用户的行为，找出与当前用户相似的群体
        for (other_user_id, other_profile) in user_behaviors.iter() {
            if other_user_id == user_id {
                continue;
            }

            // 计算行为相似度
            let similarity =
                self.calculate_behavioral_similarity(current_user_profile, other_profile);

            if similarity > 0.3 {
                // 相似度阈值
                // 为相似用户的文档加权
                for (doc_id, interaction) in &other_profile.document_interactions {
                    *demographic_scores.entry(doc_id.clone()).or_insert(0.0) +=
                        interaction.interaction_score * similarity;
                }
            }
        }

        // 排除用户已经交互过的文档
        for doc_id in current_user_profile.document_interactions.keys() {
            demographic_scores.remove(doc_id);
        }

        // 按分数排序并限制数量
        let mut recommended_docs: Vec<(String, f64)> = demographic_scores.into_iter().collect();
        recommended_docs.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        recommended_docs.truncate(self.config.recommendation_count as usize);

        if recommended_docs.is_empty() {
            // 如果没有人口统计学推荐，回退到流行文档
            return self.get_popular_documents().await;
        }

        // 生成推荐文档
        let documents: Vec<Document> = recommended_docs
            .into_iter()
            .enumerate()
            .map(|(index, (doc_id, score))| Document {
                id: doc_id.clone(),
                title: format!("Demographic Recommendation #{}: {}", index + 1, doc_id),
                content: format!("Recommended based on similar users (score: {:.2})", score),
                knowledge_base_id: "default".to_string(),
                summary: None,
                file_type: "text".to_string(),
                file_size: 1024,
                file_path: None,
                original_filename: Some(format!("demographic_{}.txt", doc_id)),
                mime_type: "text/plain".to_string(),
                language: Some("en".to_string()),
                status: DocumentStatus::Indexed,
                uploaded_by: "system".to_string(),
                indexed_at: Some(current_millis()),
                created_at: current_millis(),
                updated_at: current_millis(),
                metadata: DocumentMetadata {
                    author: None,
                    subject: None,
                    creator: None,
                    producer: None,
                    keywords: Vec::new(),
                    source_url: None,
                    page_count: None,
                    word_count: Some(50),
                    character_count: Some(300),
                    creation_date: None,
                    modification_date: None,
                    content_type: "text/plain".to_string(),
                    content_encoding: None,
                    content_language: None,
                    custom_fields: serde_json::Value::Null,
                },
                processing_metadata: DocumentProcessingMetadata {
                    extraction_method: "demographic".to_string(),
                    extraction_quality: 1.0,
                    processing_time_ms: 50,
                    parsing_errors: Vec::new(),
                    parsing_warnings: Vec::new(),
                    file_checksum: format!("demo_hash_{}", doc_id),
                    structured_content: None,
                    processing_attempts: 1,
                    last_processing_attempt: Some(current_millis()),
                },
            })
            .collect();

        Ok(documents)
    }

    /// 计算行为相似度
    fn calculate_behavioral_similarity(
        &self,
        profile1: &UserBehaviorProfile,
        profile2: &UserBehaviorProfile,
    ) -> f64 {
        // 简化的相似度计算：基于兴趣类别重叠和访问时间模式
        let interests1: std::collections::HashSet<_> =
            profile1.interest_categories.iter().collect();
        let interests2: std::collections::HashSet<_> =
            profile2.interest_categories.iter().collect();

        let intersection_count = interests1.intersection(&interests2).count();
        let union_count = interests1.union(&interests2).count();

        if union_count == 0 {
            return 0.0;
        }

        // Jaccard相似度
        let jaccard_similarity = intersection_count as f64 / union_count as f64;

        // 时间偏好相似度（简化）
        let time_similarity = 1.0
            - (profile1.time_preference.peak_hours.len() as f64
                - profile2.time_preference.peak_hours.len() as f64)
                .abs()
                / 24.0;

        // 综合相似度
        jaccard_similarity * 0.7 + time_similarity * 0.3
    }

    /// 生成推荐理由
    async fn generate_recommendation_reasons(
        &self,
        results: &[HybridSearchResult],
        user_profile: &UserBehaviorProfile,
        adjustments: &[PersonalizationAdjustment],
    ) -> Result<Vec<RecommendationReason>> {
        let mut reasons = Vec::new();

        for (result, adjustment) in results.iter().zip(adjustments.iter()) {
            let mut result_reasons = Vec::new();

            // 基于兴趣匹配的理由
            if adjustment.adjustment_factors.interest_match > 0.5 {
                result_reasons.push(RecommendationReason {
                    document_id: result.document.id.clone(),
                    reason_type: RecommendationReasonType::InterestTopic,
                    description: "基于您的兴趣主题推荐".to_string(),
                    confidence: adjustment.adjustment_factors.interest_match,
                });
            }

            // 基于历史行为的理由
            if adjustment.adjustment_factors.historical_preference > 0.5 {
                result_reasons.push(RecommendationReason {
                    document_id: result.document.id.clone(),
                    reason_type: RecommendationReasonType::HistoricalSearch,
                    description: "基于您的搜索历史推荐".to_string(),
                    confidence: adjustment.adjustment_factors.historical_preference,
                });
            }

            // 基于相似用户的理由
            if adjustment.adjustment_factors.similar_user_influence > 0.5 {
                result_reasons.push(RecommendationReason {
                    document_id: result.document.id.clone(),
                    reason_type: RecommendationReasonType::SimilarUsers,
                    description: "与您兴趣相似的用户也喜欢这个文档".to_string(),
                    confidence: adjustment.adjustment_factors.similar_user_influence,
                });
            }

            reasons.extend(result_reasons);
        }

        Ok(reasons)
    }

    /// 生成用户画像摘要
    async fn generate_user_profile_summary(
        &self,
        user_profile: &UserBehaviorProfile,
    ) -> UserProfileSummary {
        // 提取前5个兴趣类别
        let top_interests: Vec<_> = user_profile
            .interest_categories
            .iter()
            .enumerate()
            .map(|(i, category)| (category.clone(), 1.0 - (i as f64 * 0.1)))
            .take(5)
            .collect();

        // 提取前3个偏好文档类型
        let mut preferred_types: Vec<_> = user_profile
            .preferred_document_types
            .iter()
            .map(|(doc_type, score)| (doc_type.clone(), *score))
            .collect();
        preferred_types.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        preferred_types.truncate(3);

        // 计算搜索活跃度
        let search_activity_level = match user_profile.search_history.len() {
            0..=10 => ActivityLevel::Low,
            11..=30 => ActivityLevel::Medium,
            31..=100 => ActivityLevel::High,
            _ => ActivityLevel::VeryHigh,
        };

        // 估算专业程度
        let expertise_level = if user_profile.query_complexity_preference > 0.8 {
            ExpertiseLevel::Expert
        } else if user_profile.query_complexity_preference > 0.6 {
            ExpertiseLevel::Advanced
        } else if user_profile.query_complexity_preference > 0.4 {
            ExpertiseLevel::Intermediate
        } else {
            ExpertiseLevel::Beginner
        };

        UserProfileSummary {
            top_interests,
            preferred_types,
            search_activity_level,
            expertise_level,
        }
    }

    /// 获取配置
    pub fn get_config(&self) -> &PersonalizationConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, new_config: PersonalizationConfig) {
        self.config = new_config;
    }
}

impl UserBehaviorAnalyzer {
    pub fn new() -> Self {
        Self {
            user_behaviors: Arc::new(RwLock::new(HashMap::new())),
            behavior_weights: BehaviorWeights::default(),
        }
    }

    /// 记录搜索行为
    pub async fn record_search_behavior(
        &self,
        user_id: String,
        search_behavior: SearchBehavior,
    ) -> Result<()> {
        let mut behaviors = self.user_behaviors.write().await;
        let profile = behaviors
            .entry(user_id.clone())
            .or_insert_with(|| UserBehaviorProfile {
                user_id,
                search_history: Vec::new(),
                document_interactions: HashMap::new(),
                interest_categories: Vec::new(),
                preferred_document_types: HashMap::new(),
                preferred_knowledge_bases: HashMap::new(),
                search_time_preferences: TimePreferences::default(),
                query_complexity_preference: 0.5,
                search_behavior: SearchBehaviorStats::default(),
                time_preference: TimePreferences::default(),
                last_updated: current_millis(),
            });

        profile.search_history.push(search_behavior.clone());
        profile.last_updated = current_millis();

        // 更新兴趣主题
        self.update_interest_topics(profile, &search_behavior).await;

        // 更新时间偏好
        self.update_time_preferences(profile, &search_behavior)
            .await;

        Ok(())
    }

    /// 记录文档交互
    pub async fn record_document_interaction(
        &self,
        user_id: String,
        document_id: String,
        interaction_type: InteractionType,
    ) -> Result<()> {
        let mut behaviors = self.user_behaviors.write().await;
        let profile = behaviors
            .entry(user_id.clone())
            .or_insert_with(|| UserBehaviorProfile {
                user_id,
                search_history: Vec::new(),
                document_interactions: HashMap::new(),
                interest_categories: Vec::new(),
                preferred_document_types: HashMap::new(),
                preferred_knowledge_bases: HashMap::new(),
                search_time_preferences: TimePreferences::default(),
                query_complexity_preference: 0.5,
                search_behavior: SearchBehaviorStats::default(),
                time_preference: TimePreferences::default(),
                last_updated: current_millis(),
            });

        let interaction = profile
            .document_interactions
            .entry(document_id.clone())
            .or_insert_with(|| DocumentInteraction {
                document_id,
                interaction_types: Vec::new(),
                total_interactions: 0,
                total_reading_time: 0,
                last_interaction: current_millis(),
                interaction_score: 0.0,
            });

        interaction.interaction_types.push(interaction_type.clone());
        interaction.total_interactions += 1;
        interaction.last_interaction = current_millis();

        // 更新阅读时长
        if let InteractionType::View { duration } = interaction_type {
            interaction.total_reading_time += duration;
        }

        // 重新计算交互评分
        interaction.interaction_score =
            self.calculate_interaction_score(&interaction.interaction_types);

        profile.last_updated = current_millis();

        Ok(())
    }

    /// 获取用户画像
    pub async fn get_user_profile(&self, user_id: &str) -> Result<UserBehaviorProfile> {
        let behaviors = self.user_behaviors.read().await;
        behaviors
            .get(user_id)
            .cloned()
            .ok_or_else(|| anyhow::anyhow!("用户画像不存在"))
    }

    /// 更新用户偏好
    pub async fn update_user_preferences(
        &self,
        user_id: &str,
        feedback: UserFeedback,
    ) -> Result<()> {
        let mut behaviors = self.user_behaviors.write().await;
        if let Some(profile) = behaviors.get_mut(user_id) {
            self.apply_user_feedback(profile, feedback).await;
            profile.last_updated = current_millis();
        }
        Ok(())
    }

    /// 更新兴趣主题
    async fn update_interest_topics(
        &self,
        profile: &mut UserBehaviorProfile,
        search_behavior: &SearchBehavior,
    ) {
        // 从查询文本中提取关键词作为兴趣主题
        let keywords = self.extract_keywords(&search_behavior.query_text);
        for keyword in keywords {
            if !profile.interest_categories.contains(&keyword) {
                profile.interest_categories.push(keyword);
            }
        }
    }

    /// 更新时间偏好
    async fn update_time_preferences(
        &self,
        profile: &mut UserBehaviorProfile,
        search_behavior: &SearchBehavior,
    ) {
        let hour = to_datetime(search_behavior.query_time).hour();
        if !profile
            .search_time_preferences
            .preferred_hours
            .contains(&hour)
        {
            profile.search_time_preferences.preferred_hours.push(hour);
        }
    }

    /// 提取关键词
    fn extract_keywords(&self, text: &str) -> Vec<String> {
        text.split_whitespace()
            .filter(|word| word.len() > 2)
            .map(|word| word.to_lowercase())
            .collect()
    }

    /// 计算交互评分
    fn calculate_interaction_score(&self, interactions: &[InteractionType]) -> f64 {
        let mut score = 0.0;
        for interaction in interactions {
            score += match interaction {
                InteractionType::View { duration } => {
                    self.behavior_weights.click_weight
                        + (*duration as f64 / 60.0) * self.behavior_weights.reading_time_weight
                }
                InteractionType::Download { .. } => self.behavior_weights.download_weight,
                InteractionType::Bookmark { .. } => self.behavior_weights.bookmark_weight,
                InteractionType::Share { .. } => self.behavior_weights.share_weight,
                InteractionType::Rating {
                    score: rating_score,
                    ..
                } => *rating_score as f64 * self.behavior_weights.rating_weight,
                InteractionType::Comment { .. } => 2.0,
            };
        }
        score
    }

    /// 应用用户反馈
    async fn apply_user_feedback(&self, profile: &mut UserBehaviorProfile, feedback: UserFeedback) {
        match feedback {
            UserFeedback::SearchResultRelevance {
                query,
                document_id,
                relevance_score,
            } => {
                // 更新兴趣类别
                let keywords = self.extract_keywords(&query);
                for keyword in keywords {
                    if relevance_score > 0.5 && !profile.interest_categories.contains(&keyword) {
                        profile.interest_categories.push(keyword);
                    } else if relevance_score < 0.5 {
                        profile.interest_categories.retain(|k| k != &keyword);
                    }
                }
            }
            UserFeedback::DocumentRating {
                document_id,
                rating,
            } => {
                // 更新文档交互记录
                if let Some(interaction) = profile.document_interactions.get_mut(&document_id) {
                    interaction.interaction_types.push(InteractionType::Rating {
                        score: rating,
                        timestamp: current_millis(),
                    });
                    interaction.interaction_score =
                        self.calculate_interaction_score(&interaction.interaction_types);
                }
            }
        }
    }
}

impl PersonalizationEngine {
    pub fn new() -> Self {
        Self {
            user_similarity_calculator: UserSimilarityCalculator,
            content_feature_extractor: ContentFeatureExtractor,
            personalized_scorer: PersonalizedScorer,
        }
    }

    /// 计算个性化调整
    pub async fn calculate_personalization_adjustments(
        &self,
        search_results: &[HybridSearchResult],
        user_profile: &UserBehaviorProfile,
        query_context: &QueryContext,
    ) -> Result<Vec<PersonalizationAdjustment>> {
        let mut adjustments = Vec::new();

        for result in search_results {
            let adjustment_factors = self
                .calculate_adjustment_factors(result, user_profile, query_context)
                .await;
            let adjustment_multiplier = self.calculate_adjustment_multiplier(&adjustment_factors);
            let adjusted_score = result.final_score * adjustment_multiplier;

            adjustments.push(PersonalizationAdjustment {
                document_id: result.document.id.clone(),
                original_score: result.final_score,
                adjusted_score,
                adjustment_factors,
            });
        }

        Ok(adjustments)
    }

    /// 计算调整因子
    async fn calculate_adjustment_factors(
        &self,
        result: &HybridSearchResult,
        user_profile: &UserBehaviorProfile,
        query_context: &QueryContext,
    ) -> AdjustmentFactors {
        let interest_match = self.calculate_interest_match(result, user_profile).await;
        let historical_preference = self
            .calculate_historical_preference(result, user_profile)
            .await;
        let time_preference = self
            .calculate_time_preference(query_context, user_profile)
            .await;
        let similar_user_influence = self
            .calculate_similar_user_influence(result, user_profile)
            .await;

        AdjustmentFactors {
            interest_match,
            historical_preference,
            time_preference,
            similar_user_influence,
        }
    }

    /// 计算兴趣匹配度
    async fn calculate_interest_match(
        &self,
        result: &HybridSearchResult,
        user_profile: &UserBehaviorProfile,
    ) -> f64 {
        let document_keywords = self.extract_document_keywords(&result.document);
        let mut match_score = 0.0;
        let total_keywords = document_keywords.len() as f64;

        for keyword in document_keywords {
            if user_profile.interest_categories.contains(&keyword) {
                match_score += 1.0;
            }
        }

        if total_keywords > 0.0 {
            match_score / total_keywords
        } else {
            0.0
        }
    }

    /// 计算历史偏好
    async fn calculate_historical_preference(
        &self,
        result: &HybridSearchResult,
        user_profile: &UserBehaviorProfile,
    ) -> f64 {
        // 检查文档类型偏好
        let doc_type_preference = user_profile
            .preferred_document_types
            .get(&result.document.file_type)
            .unwrap_or(&0.0);

        // 检查知识库偏好
        let kb_preference = user_profile
            .preferred_knowledge_bases
            .get(&result.document.knowledge_base_id)
            .unwrap_or(&0.0);

        (doc_type_preference + kb_preference) / 2.0
    }

    /// 计算时间偏好
    async fn calculate_time_preference(
        &self,
        query_context: &QueryContext,
        user_profile: &UserBehaviorProfile,
    ) -> f64 {
        // 简化实现：总是返回中性值
        0.5
    }

    /// 计算相似用户影响
    async fn calculate_similar_user_influence(
        &self,
        result: &HybridSearchResult,
        user_profile: &UserBehaviorProfile,
    ) -> f64 {
        // 简化实现：基于文档ID和用户兴趣类别的匹配度
        let document_keywords = self.extract_document_keywords(&result.document);
        let mut influence_score = 0.0;

        // 计算文档关键词与用户兴趣的匹配度
        for keyword in document_keywords {
            if user_profile.interest_categories.contains(&keyword) {
                influence_score += 0.1;
            }
        }

        // 基于用户历史交互数量调整影响力
        let interaction_factor = (user_profile.document_interactions.len() as f64).min(10.0) / 10.0;
        influence_score *= 0.5 + interaction_factor * 0.5;

        influence_score.min(1.0)
    }

    /// 计算综合用户相似度
    async fn calculate_comprehensive_user_similarity(
        &self,
        user1: &UserBehaviorProfile,
        user2: &UserBehaviorProfile,
    ) -> f64 {
        let mut similarity_factors = Vec::new();

        // 1. 兴趣类别相似度（Jaccard系数）
        let interests1: std::collections::HashSet<_> = user1.interest_categories.iter().collect();
        let interests2: std::collections::HashSet<_> = user2.interest_categories.iter().collect();

        let interest_similarity = if interests1.is_empty() && interests2.is_empty() {
            1.0
        } else {
            let intersection = interests1.intersection(&interests2).count() as f64;
            let union = interests1.union(&interests2).count() as f64;
            intersection / union
        };
        similarity_factors.push((interest_similarity, 0.35)); // 权重35%

        // 2. 搜索模式相似度
        let search_pattern_similarity = self.calculate_search_pattern_similarity(user1, user2);
        similarity_factors.push((search_pattern_similarity, 0.25)); // 权重25%

        // 3. 时间偏好相似度
        let time_preference_similarity = self
            .calculate_time_preference_similarity(&user1.time_preference, &user2.time_preference);
        similarity_factors.push((time_preference_similarity, 0.20)); // 权重20%

        // 4. 文档类型偏好相似度
        let doc_type_similarity = self.calculate_document_type_preference_similarity(user1, user2);
        similarity_factors.push((doc_type_similarity, 0.20)); // 权重20%

        // 加权平均计算最终相似度
        similarity_factors
            .into_iter()
            .map(|(similarity, weight)| similarity * weight)
            .sum()
    }

    /// 计算搜索模式相似度
    fn calculate_search_pattern_similarity(
        &self,
        user1: &UserBehaviorProfile,
        user2: &UserBehaviorProfile,
    ) -> f64 {
        // 比较用户的查询长度分布、查询频率等
        let avg_query_len1 = user1.search_behavior.average_query_length;
        let avg_query_len2 = user2.search_behavior.average_query_length;

        let length_similarity = 1.0
            - (avg_query_len1 - avg_query_len2).abs()
                / (avg_query_len1.max(avg_query_len2).max(1.0));

        let session_len1 = user1.search_behavior.average_session_length;
        let session_len2 = user2.search_behavior.average_session_length;

        let session_similarity =
            1.0 - (session_len1 - session_len2).abs() / (session_len1.max(session_len2).max(1.0));

        (length_similarity + session_similarity) / 2.0
    }

    /// 计算时间偏好相似度
    fn calculate_time_preference_similarity(
        &self,
        pref1: &TimePreferences,
        pref2: &TimePreferences,
    ) -> f64 {
        let hours1: std::collections::HashSet<_> = pref1.peak_hours.iter().collect();
        let hours2: std::collections::HashSet<_> = pref2.peak_hours.iter().collect();

        if hours1.is_empty() && hours2.is_empty() {
            return 1.0;
        }

        let intersection = hours1.intersection(&hours2).count() as f64;
        let union = hours1.union(&hours2).count() as f64;

        intersection / union
    }

    /// 计算文档类型偏好相似度
    fn calculate_document_type_preference_similarity(
        &self,
        user1: &UserBehaviorProfile,
        user2: &UserBehaviorProfile,
    ) -> f64 {
        // 简化实现：基于用户交互过的文档数量分布
        let doc_count1 = user1.document_interactions.len() as f64;
        let doc_count2 = user2.document_interactions.len() as f64;

        if doc_count1 == 0.0 && doc_count2 == 0.0 {
            return 1.0;
        }

        let max_count = doc_count1.max(doc_count2);
        1.0 - (doc_count1 - doc_count2).abs() / max_count
    }

    /// 计算交互时效性权重
    fn calculate_interaction_recency_weight(
        &self,
        profile: &UserBehaviorProfile,
        doc_id: &str,
    ) -> f64 {
        // 基于最后更新时间计算时效性权重
        let current_time = current_millis();
        let last_updated = profile.last_updated;

        let time_diff_days = (current_time - last_updated) as f64 / (24.0 * 60.0 * 60.0 * 1000.0);

        // 时效性衰减函数：30天半衰期
        let decay_rate = -0.693 / 30.0; // ln(0.5) / 30
        (decay_rate * time_diff_days).exp().max(0.1) // 最小权重0.1
    }

    /// 根据样本数量计算置信度调整
    fn calculate_confidence_from_sample_size(&self, sample_size: usize) -> f64 {
        // 样本数量越大，置信度越高
        let min_sample = 3;
        let optimal_sample = 20;

        if sample_size < min_sample {
            0.3 // 低置信度
        } else if sample_size >= optimal_sample {
            1.0 // 高置信度
        } else {
            // 线性插值
            0.3 + 0.7 * ((sample_size - min_sample) as f64 / (optimal_sample - min_sample) as f64)
        }
    }

    /// 计算调整乘数
    fn calculate_adjustment_multiplier(&self, factors: &AdjustmentFactors) -> f64 {
        let combined_factor = factors.interest_match * 0.4
            + factors.historical_preference * 0.3
            + factors.time_preference * 0.1
            + factors.similar_user_influence * 0.2;

        // 将因子转换为乘数：0.8 到 1.2 的范围
        0.8 + (combined_factor * 0.4)
    }

    /// 提取文档关键词
    fn extract_document_keywords(&self, document: &Document) -> Vec<String> {
        let mut keywords = Vec::new();

        // 从标题提取
        keywords.extend(
            document
                .title
                .split_whitespace()
                .filter(|word| word.len() > 2)
                .map(|word| word.to_lowercase()),
        );

        // 从内容提取（取前100个词）
        keywords.extend(
            document
                .content
                .split_whitespace()
                .take(100)
                .filter(|word| word.len() > 2)
                .map(|word| word.to_lowercase()),
        );

        keywords
    }
}

impl RecommendationManager {
    pub fn new() -> Self {
        Self {
            collaborative_filter: CollaborativeFilteringRecommender,
            content_based_filter: ContentBasedRecommender,
            hybrid_recommender: HybridRecommender,
        }
    }
}

impl CollaborativeFilteringRecommender {
    pub async fn get_recommendations(
        &self,
        user_profile: &UserBehaviorProfile,
        user_behavior_analyzer: &UserBehaviorAnalyzer,
        config: &PersonalizationConfig,
    ) -> Result<Vec<Document>> {
        let user_behaviors = user_behavior_analyzer.user_behaviors.read().await;
        let target_user_id = &user_profile.user_id;

        // Step 1: 计算用户相似度矩阵
        let mut user_similarities = HashMap::new();
        for (other_user_id, other_profile) in user_behaviors.iter() {
            if other_user_id == target_user_id {
                continue;
            }

            let similarity = self.calculate_user_similarity_for_cf(user_profile, other_profile);
            if similarity > 0.1 {
                // 相似度阈值
                user_similarities.insert(other_user_id.clone(), similarity);
            }
        }

        if user_similarities.is_empty() {
            return Ok(Vec::new());
        }

        // Step 2: 基于相似用户的偏好计算推荐分数
        let mut item_scores: HashMap<String, f64> = HashMap::new();
        let mut item_weights: HashMap<String, f64> = HashMap::new();

        for (similar_user_id, similarity) in &user_similarities {
            if let Some(similar_user_profile) = user_behaviors.get(similar_user_id) {
                for (doc_id, interaction) in &similar_user_profile.document_interactions {
                    // 跳过用户已经交互过的文档
                    if user_profile.document_interactions.contains_key(doc_id) {
                        continue;
                    }

                    let weighted_score = interaction.interaction_score * similarity;
                    *item_scores.entry(doc_id.clone()).or_insert(0.0) += weighted_score;
                    *item_weights.entry(doc_id.clone()).or_insert(0.0) += similarity;
                }
            }
        }

        // Step 3: 标准化分数并生成推荐
        let mut recommendations: Vec<(String, f64)> = item_scores
            .into_iter()
            .map(|(doc_id, total_score)| {
                let weight = item_weights.get(&doc_id).unwrap_or(&1.0);
                let normalized_score = if *weight > 0.0 {
                    total_score / weight
                } else {
                    total_score
                };
                (doc_id, normalized_score)
            })
            .collect();

        // Step 4: 排序并限制数量
        recommendations.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        recommendations.truncate(config.recommendation_count as usize);

        // Step 5: 转换为文档对象
        let documents = recommendations
            .into_iter()
            .enumerate()
            .map(|(index, (doc_id, score))| Document {
                id: doc_id.clone(),
                title: format!("CF Recommendation #{}: {}", index + 1, doc_id),
                content: format!(
                    "Document recommended by collaborative filtering (score: {:.3})",
                    score
                ),
                knowledge_base_id: "default".to_string(),
                summary: None,
                file_type: "text".to_string(),
                file_size: 1024,
                file_path: None,
                original_filename: Some(format!("cf_{}.txt", doc_id)),
                mime_type: "text/plain".to_string(),
                language: Some("en".to_string()),
                status: DocumentStatus::Indexed,
                uploaded_by: "system".to_string(),
                indexed_at: Some(current_millis()),
                created_at: current_millis(),
                updated_at: current_millis(),
                metadata: DocumentMetadata {
                    author: None,
                    subject: None,
                    creator: None,
                    producer: None,
                    keywords: Vec::new(),
                    source_url: None,
                    page_count: None,
                    word_count: Some(50),
                    character_count: Some(300),
                    creation_date: None,
                    modification_date: None,
                    content_type: "text/plain".to_string(),
                    content_encoding: None,
                    content_language: None,
                    custom_fields: serde_json::Value::Null,
                },
                processing_metadata: DocumentProcessingMetadata {
                    extraction_method: "collaborative_filtering".to_string(),
                    extraction_quality: 1.0,
                    processing_time_ms: 100,
                    parsing_errors: Vec::new(),
                    parsing_warnings: Vec::new(),
                    file_checksum: format!("cf_hash_{}", doc_id),
                    structured_content: None,
                    processing_attempts: 1,
                    last_processing_attempt: Some(current_millis()),
                },
            })
            .collect();

        Ok(documents)
    }

    /// 计算协同过滤的用户相似度
    fn calculate_user_similarity_for_cf(
        &self,
        user1: &UserBehaviorProfile,
        user2: &UserBehaviorProfile,
    ) -> f64 {
        // 找出两个用户都交互过的文档
        let common_docs: Vec<&String> = user1
            .document_interactions
            .keys()
            .filter(|doc_id| user2.document_interactions.contains_key(*doc_id))
            .collect();

        if common_docs.is_empty() {
            return 0.0;
        }

        // 计算皮尔逊相关系数
        let mut sum1 = 0.0;
        let mut sum2 = 0.0;
        let mut sum1_sq = 0.0;
        let mut sum2_sq = 0.0;
        let mut sum_products = 0.0;
        let n = common_docs.len() as f64;

        for doc_id in &common_docs {
            let interaction1 = user1.document_interactions.get(*doc_id).unwrap();
            let interaction2 = user2.document_interactions.get(*doc_id).unwrap();

            let rating1 = interaction1.interaction_score;
            let rating2 = interaction2.interaction_score;

            sum1 += rating1;
            sum2 += rating2;
            sum1_sq += rating1 * rating1;
            sum2_sq += rating2 * rating2;
            sum_products += rating1 * rating2;
        }

        let numerator = sum_products - (sum1 * sum2 / n);
        let denominator = ((sum1_sq - sum1 * sum1 / n) * (sum2_sq - sum2 * sum2 / n)).sqrt();

        if denominator == 0.0 {
            0.0
        } else {
            (numerator / denominator).max(-1.0).min(1.0) // 限制在[-1,1]范围内
        }
    }
}

impl ContentBasedRecommender {
    pub async fn get_recommendations(
        &self,
        user_profile: &UserBehaviorProfile,
        user_behavior_analyzer: &UserBehaviorAnalyzer,
        config: &PersonalizationConfig,
    ) -> Result<Vec<Document>> {
        let user_behaviors = user_behavior_analyzer.user_behaviors.read().await;

        // Step 1: 构建用户兴趣画像
        let user_interest_vector = self.build_user_interest_vector(user_profile);
        if user_interest_vector.is_empty() {
            return Ok(Vec::new());
        }

        // Step 2: 收集所有可用文档（排除用户已交互过的）
        let mut candidate_docs: HashMap<String, HashMap<String, f64>> = HashMap::new();

        for (_, other_profile) in user_behaviors.iter() {
            for doc_id in other_profile.document_interactions.keys() {
                if !user_profile.document_interactions.contains_key(doc_id) {
                    // 为每个候选文档构建内容特征向量
                    let doc_features = self.build_document_feature_vector(doc_id, &user_behaviors);
                    candidate_docs.insert(doc_id.clone(), doc_features);
                }
            }
        }

        // Step 3: 计算内容相似度并排序
        let mut content_scores: Vec<(String, f64)> = Vec::new();

        for (doc_id, doc_features) in candidate_docs {
            let content_similarity =
                self.calculate_content_similarity(&user_interest_vector, &doc_features);

            // 应用额外的内容过滤策略
            let final_score =
                self.apply_content_filtering_strategies(content_similarity, user_profile, &doc_id);

            if final_score > 0.1 {
                // 相似度阈值
                content_scores.push((doc_id, final_score));
            }
        }

        // Step 4: 排序并限制数量
        content_scores.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        content_scores.truncate(config.recommendation_count as usize);

        // Step 5: 转换为文档对象
        let documents = content_scores
            .into_iter()
            .enumerate()
            .map(|(index, (doc_id, score))| Document {
                id: doc_id.clone(),
                title: format!("Content-Based Rec #{}: {}", index + 1, doc_id),
                content: format!(
                    "Document recommended by content similarity (score: {:.3})",
                    score
                ),
                knowledge_base_id: "default".to_string(),
                summary: None,
                file_type: "text".to_string(),
                file_size: 1024,
                file_path: None,
                original_filename: Some(format!("cb_{}.txt", doc_id)),
                mime_type: "text/plain".to_string(),
                language: Some("en".to_string()),
                status: DocumentStatus::Indexed,
                uploaded_by: "system".to_string(),
                indexed_at: Some(current_millis()),
                created_at: current_millis(),
                updated_at: current_millis(),
                metadata: DocumentMetadata {
                    author: None,
                    subject: None,
                    creator: None,
                    producer: None,
                    keywords: Vec::new(),
                    source_url: None,
                    page_count: None,
                    word_count: Some(50),
                    character_count: Some(300),
                    creation_date: None,
                    modification_date: None,
                    content_type: "text/plain".to_string(),
                    content_encoding: None,
                    content_language: None,
                    custom_fields: serde_json::Value::Null,
                },
                processing_metadata: DocumentProcessingMetadata {
                    extraction_method: "content_based".to_string(),
                    extraction_quality: 1.0,
                    processing_time_ms: 75,
                    parsing_errors: Vec::new(),
                    parsing_warnings: Vec::new(),
                    file_checksum: format!("cb_hash_{}", doc_id),
                    structured_content: None,
                    processing_attempts: 1,
                    last_processing_attempt: Some(current_millis()),
                },
            })
            .collect();

        Ok(documents)
    }

    /// 构建用户兴趣向量
    fn build_user_interest_vector(
        &self,
        user_profile: &UserBehaviorProfile,
    ) -> HashMap<String, f64> {
        let mut interest_vector = HashMap::new();

        // 基于用户的兴趣类别
        for category in &user_profile.interest_categories {
            *interest_vector.entry(category.clone()).or_insert(0.0) += 1.0;
        }

        // 基于用户的文档交互历史提取特征
        for (doc_id, interaction) in &user_profile.document_interactions {
            // 从文档ID中提取特征（简化的特征提取）
            let doc_features = self.extract_features_from_doc_id(doc_id);
            for feature in doc_features {
                *interest_vector.entry(feature).or_insert(0.0) +=
                    interaction.interaction_score * 0.5;
            }
        }

        // 标准化向量
        let total_weight: f64 = interest_vector.values().sum();
        if total_weight > 0.0 {
            for value in interest_vector.values_mut() {
                *value /= total_weight;
            }
        }

        interest_vector
    }

    /// 构建文档特征向量
    fn build_document_feature_vector(
        &self,
        doc_id: &str,
        user_behaviors: &HashMap<String, UserBehaviorProfile>,
    ) -> HashMap<String, f64> {
        let mut feature_vector = HashMap::new();

        // 从文档ID提取基础特征
        let basic_features = self.extract_features_from_doc_id(doc_id);
        for feature in basic_features {
            *feature_vector.entry(feature).or_insert(0.0) += 1.0;
        }

        // 基于与此文档交互的用户的兴趣分析文档特征
        let mut interacting_users_interests: Vec<String> = Vec::new();
        for profile in user_behaviors.values() {
            if profile.document_interactions.contains_key(doc_id) {
                interacting_users_interests.extend(profile.interest_categories.clone());
            }
        }

        // 统计用户兴趣分布作为文档特征
        let mut interest_counts = HashMap::new();
        for interest in interacting_users_interests {
            *interest_counts.entry(interest).or_insert(0) += 1;
        }

        // 转换为特征权重
        for (interest, count) in interest_counts {
            *feature_vector.entry(interest).or_insert(0.0) += count as f64 * 0.3;
        }

        // 标准化特征向量
        let total_weight: f64 = feature_vector.values().sum();
        if total_weight > 0.0 {
            for value in feature_vector.values_mut() {
                *value /= total_weight;
            }
        }

        feature_vector
    }

    /// 从文档ID提取特征（简化实现）
    fn extract_features_from_doc_id(&self, doc_id: &str) -> Vec<String> {
        doc_id
            .split(['_', '-', '.', ' '])
            .filter(|token| token.len() > 2)
            .map(|token| token.to_lowercase())
            .collect()
    }

    /// 计算内容相似度（余弦相似度）
    fn calculate_content_similarity(
        &self,
        user_vector: &HashMap<String, f64>,
        doc_vector: &HashMap<String, f64>,
    ) -> f64 {
        if user_vector.is_empty() || doc_vector.is_empty() {
            return 0.0;
        }

        let mut dot_product = 0.0;
        let mut user_magnitude_sq = 0.0;
        let mut doc_magnitude_sq = 0.0;

        // 计算所有特征的点积和幅度
        let all_features: std::collections::HashSet<String> = user_vector
            .keys()
            .chain(doc_vector.keys())
            .cloned()
            .collect();

        for feature in all_features {
            let user_weight = user_vector.get(&feature).unwrap_or(&0.0);
            let doc_weight = doc_vector.get(&feature).unwrap_or(&0.0);

            dot_product += user_weight * doc_weight;
            user_magnitude_sq += user_weight * user_weight;
            doc_magnitude_sq += doc_weight * doc_weight;
        }

        let magnitude_product = (user_magnitude_sq * doc_magnitude_sq).sqrt();

        if magnitude_product > 0.0 {
            dot_product / magnitude_product
        } else {
            0.0
        }
    }

    /// 应用内容过滤策略
    fn apply_content_filtering_strategies(
        &self,
        base_score: f64,
        user_profile: &UserBehaviorProfile,
        doc_id: &str,
    ) -> f64 {
        let mut adjusted_score = base_score;

        // 1. 新鲜度偏好调整
        if user_profile.search_behavior.prefers_recent_content {
            // 如果文档ID包含时间相关信息，给予加权
            if doc_id.contains("2024") || doc_id.contains("recent") || doc_id.contains("new") {
                adjusted_score *= 1.2;
            }
        }

        // 2. 复杂度偏好调整
        let complexity_indicators = [
            "advanced",
            "detailed",
            "comprehensive",
            "intro",
            "basic",
            "simple",
        ];
        let is_complex = complexity_indicators.iter()
            .take(3) // advanced, detailed, comprehensive
            .any(|&indicator| doc_id.to_lowercase().contains(indicator));
        let is_simple = complexity_indicators.iter()
            .skip(3) // intro, basic, simple
            .any(|&indicator| doc_id.to_lowercase().contains(indicator));

        match user_profile
            .search_behavior
            .preferred_complexity_level
            .as_str()
        {
            "advanced" => {
                if is_complex {
                    adjusted_score *= 1.3;
                }
                if is_simple {
                    adjusted_score *= 0.7;
                }
            }
            "beginner" => {
                if is_simple {
                    adjusted_score *= 1.3;
                }
                if is_complex {
                    adjusted_score *= 0.7;
                }
            }
            _ => {} // 中级用户不调整
        }

        // 3. 多样性惩罚（避免推荐过于相似的内容）
        // 简化实现：基于特征重复度
        adjusted_score
    }
}

impl HybridRecommender {
    pub async fn get_recommendations(
        &self,
        user_profile: &UserBehaviorProfile,
        collaborative_filter: &CollaborativeFilteringRecommender,
        content_based_filter: &ContentBasedRecommender,
        user_behavior_analyzer: &UserBehaviorAnalyzer,
        config: &PersonalizationConfig,
        context: Option<QueryContext>,
    ) -> Result<Vec<Document>> {
        // Step 1: 获取不同推荐策略的结果
        let cf_recommendations = collaborative_filter
            .get_recommendations(user_profile, user_behavior_analyzer, config)
            .await?;

        let cb_recommendations = content_based_filter
            .get_recommendations(user_profile, user_behavior_analyzer, config)
            .await?;

        // Step 2: 构建文档分数映射
        let mut doc_scores: HashMap<String, HybridScore> = HashMap::new();

        // 添加协同过滤分数
        for (index, doc) in cf_recommendations.iter().enumerate() {
            let cf_score = 1.0 - (index as f64 / cf_recommendations.len() as f64); // 归一化排序分数
            doc_scores.insert(
                doc.id.clone(),
                HybridScore {
                    collaborative_score: Some(cf_score),
                    content_score: None,
                    popularity_score: 0.0,
                    diversity_penalty: 0.0,
                    context_boost: 0.0,
                },
            );
        }

        // 添加基于内容的分数
        for (index, doc) in cb_recommendations.iter().enumerate() {
            let cb_score = 1.0 - (index as f64 / cb_recommendations.len() as f64);
            let hybrid_score = doc_scores.entry(doc.id.clone()).or_insert(HybridScore {
                collaborative_score: None,
                content_score: None,
                popularity_score: 0.0,
                diversity_penalty: 0.0,
                context_boost: 0.0,
            });
            hybrid_score.content_score = Some(cb_score);
        }

        // Step 3: 计算流行度分数
        self.add_popularity_scores(&mut doc_scores, user_behavior_analyzer)
            .await;

        // Step 4: 应用上下文相关性提升
        if let Some(query_context) = &context {
            self.apply_context_boost(&mut doc_scores, query_context, user_profile);
        }

        // Step 5: 计算多样性惩罚
        self.calculate_diversity_penalties(&mut doc_scores, user_profile);

        // Step 6: 计算最终混合分数
        let mut final_recommendations: Vec<(String, f64)> = doc_scores
            .into_iter()
            .map(|(doc_id, scores)| {
                let final_score = self.calculate_hybrid_score(&scores, user_profile, config);
                (doc_id, final_score)
            })
            .filter(|(_, score)| *score > 0.1) // 过滤低分文档
            .collect();

        // Step 7: 排序并限制数量
        final_recommendations.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        final_recommendations.truncate(config.recommendation_count as usize);

        // Step 8: 转换为文档对象
        let documents = final_recommendations
            .into_iter()
            .enumerate()
            .map(|(index, (doc_id, score))| Document {
                id: doc_id.clone(),
                title: format!("Hybrid Recommendation #{}: {}", index + 1, doc_id),
                content: format!(
                    "Document recommended by hybrid algorithm (score: {:.3})",
                    score
                ),
                knowledge_base_id: context
                    .as_ref()
                    .and_then(|ctx| ctx.knowledge_base_id.clone())
                    .unwrap_or_else(|| "default".to_string()),
                summary: None,
                file_type: "text".to_string(),
                file_size: 1024,
                file_path: None,
                original_filename: Some(format!("hybrid_{}.txt", doc_id)),
                mime_type: "text/plain".to_string(),
                language: Some("en".to_string()),
                status: DocumentStatus::Indexed,
                uploaded_by: "system".to_string(),
                indexed_at: Some(current_millis()),
                created_at: current_millis(),
                updated_at: current_millis(),
                metadata: DocumentMetadata {
                    author: None,
                    subject: None,
                    creator: None,
                    producer: None,
                    keywords: Vec::new(),
                    source_url: None,
                    page_count: None,
                    word_count: Some(50),
                    character_count: Some(300),
                    creation_date: None,
                    modification_date: None,
                    content_type: "text/plain".to_string(),
                    content_encoding: None,
                    content_language: None,
                    custom_fields: serde_json::Value::Null,
                },
                processing_metadata: DocumentProcessingMetadata {
                    extraction_method: "hybrid".to_string(),
                    extraction_quality: 1.0,
                    processing_time_ms: 150,
                    parsing_errors: Vec::new(),
                    parsing_warnings: Vec::new(),
                    file_checksum: format!("hybrid_hash_{}", doc_id),
                    structured_content: None,
                    processing_attempts: 1,
                    last_processing_attempt: Some(current_millis()),
                },
            })
            .collect();

        Ok(documents)
    }

    /// 添加流行度分数
    async fn add_popularity_scores(
        &self,
        doc_scores: &mut HashMap<String, HybridScore>,
        user_behavior_analyzer: &UserBehaviorAnalyzer,
    ) {
        let user_behaviors = user_behavior_analyzer.user_behaviors.read().await;
        let mut doc_interaction_counts: HashMap<String, usize> = HashMap::new();

        // 统计每个文档的总交互次数
        for profile in user_behaviors.values() {
            for doc_id in profile.document_interactions.keys() {
                *doc_interaction_counts.entry(doc_id.clone()).or_insert(0) += 1;
            }
        }

        // 计算归一化的流行度分数
        let max_interactions = doc_interaction_counts.values().max().unwrap_or(&1).clone() as f64;

        for (doc_id, hybrid_score) in doc_scores.iter_mut() {
            let interaction_count = doc_interaction_counts.get(doc_id).unwrap_or(&0).clone() as f64;
            hybrid_score.popularity_score = interaction_count / max_interactions;
        }
    }

    /// 应用上下文相关性提升
    fn apply_context_boost(
        &self,
        doc_scores: &mut HashMap<String, HybridScore>,
        context: &QueryContext,
        user_profile: &UserBehaviorProfile,
    ) {
        let query_keywords: Vec<String> = context
            .query_text
            .split_whitespace()
            .filter(|word| word.len() > 2)
            .map(|word| word.to_lowercase())
            .collect();

        for (doc_id, hybrid_score) in doc_scores.iter_mut() {
            // 计算文档ID与查询的相关性
            let relevance = self.calculate_query_document_relevance(doc_id, &query_keywords);

            // 考虑查询意图
            let intent_boost = match context.query_intent {
                QueryIntent::Information => 1.1,
                QueryIntent::HowTo => 0.9,
                QueryIntent::Definition => 1.0,
                _ => 1.0,
            };

            hybrid_score.context_boost = relevance * intent_boost;
        }
    }

    /// 计算查询-文档相关性
    fn calculate_query_document_relevance(&self, doc_id: &str, query_keywords: &[String]) -> f64 {
        if query_keywords.is_empty() {
            return 0.0;
        }

        let doc_tokens: Vec<String> = doc_id
            .split(['_', '-', '.', ' '])
            .map(|token| token.to_lowercase())
            .collect();

        let mut matches = 0;
        for keyword in query_keywords {
            if doc_tokens
                .iter()
                .any(|token| token.contains(keyword) || keyword.contains(token))
            {
                matches += 1;
            }
        }

        matches as f64 / query_keywords.len() as f64
    }

    /// 计算多样性惩罚
    fn calculate_diversity_penalties(
        &self,
        doc_scores: &mut HashMap<String, HybridScore>,
        user_profile: &UserBehaviorProfile,
    ) {
        let doc_ids: Vec<String> = doc_scores.keys().cloned().collect();

        for doc_id in &doc_ids {
            let mut diversity_penalty = 0.0;

            // 检查与用户已交互文档的相似性
            for interacted_doc in user_profile.document_interactions.keys() {
                let similarity = self.calculate_document_similarity(doc_id, interacted_doc);
                if similarity > 0.7 {
                    diversity_penalty += similarity * 0.3; // 相似度越高，惩罚越大
                }
            }

            // 检查与其他推荐文档的相似性（避免推荐过于相似的文档）
            for other_doc in &doc_ids {
                if doc_id != other_doc {
                    let similarity = self.calculate_document_similarity(doc_id, other_doc);
                    if similarity > 0.8 {
                        diversity_penalty += similarity * 0.1;
                    }
                }
            }

            if let Some(hybrid_score) = doc_scores.get_mut(doc_id) {
                hybrid_score.diversity_penalty = diversity_penalty.min(0.5); // 限制最大惩罚
            }
        }
    }

    /// 计算文档相似性（简化实现）
    fn calculate_document_similarity(&self, doc_id1: &str, doc_id2: &str) -> f64 {
        let tokens1: std::collections::HashSet<String> = doc_id1
            .split(['_', '-', '.', ' '])
            .filter(|token| token.len() > 2)
            .map(|token| token.to_lowercase())
            .collect();

        let tokens2: std::collections::HashSet<String> = doc_id2
            .split(['_', '-', '.', ' '])
            .filter(|token| token.len() > 2)
            .map(|token| token.to_lowercase())
            .collect();

        if tokens1.is_empty() && tokens2.is_empty() {
            return 1.0;
        }

        let intersection = tokens1.intersection(&tokens2).count() as f64;
        let union = tokens1.union(&tokens2).count() as f64;

        intersection / union
    }

    /// 计算最终混合分数
    fn calculate_hybrid_score(
        &self,
        scores: &HybridScore,
        user_profile: &UserBehaviorProfile,
        config: &PersonalizationConfig,
    ) -> f64 {
        // 动态权重分配（基于用户行为数据的可用性）
        let cf_weight = if scores.collaborative_score.is_some() {
            if user_profile.document_interactions.len()
                >= config.min_interactions_threshold as usize
            {
                0.4 // 有足够交互数据时，协同过滤权重较高
            } else {
                0.2 // 交互数据不足时，降低协同过滤权重
            }
        } else {
            0.0
        };

        let cb_weight = if scores.content_score.is_some() {
            if user_profile.interest_categories.is_empty() {
                0.3 // 兴趣类别不足时，内容推荐权重较低
            } else {
                0.4 // 有明确兴趣时，内容推荐权重较高
            }
        } else {
            0.0
        };

        // 确保权重归一化
        let total_primary_weight = cf_weight + cb_weight;
        let (normalized_cf_weight, normalized_cb_weight) = if total_primary_weight > 0.0 {
            (
                cf_weight / total_primary_weight * 0.7,
                cb_weight / total_primary_weight * 0.7,
            )
        } else {
            (0.0, 0.0)
        };

        let popularity_weight = 0.15;
        let context_weight = 0.10;
        let diversity_weight = 0.05;

        // 计算加权分数
        let mut final_score = 0.0;

        if let Some(cf_score) = scores.collaborative_score {
            final_score += cf_score * normalized_cf_weight;
        }

        if let Some(cb_score) = scores.content_score {
            final_score += cb_score * normalized_cb_weight;
        }

        final_score += scores.popularity_score * popularity_weight;
        final_score += scores.context_boost * context_weight;
        final_score -= scores.diversity_penalty * diversity_weight;

        // 应用个性化强度调节
        let personalization_factor = config.personalization_strength;
        let base_score = scores.popularity_score * 0.5 + 0.5; // 基础分数

        final_score =
            base_score * (1.0 - personalization_factor) + final_score * personalization_factor;

        final_score.max(0.0).min(1.0) // 确保分数在[0,1]范围内
    }
}

/// 混合推荐分数结构
#[derive(Debug, Clone)]
struct HybridScore {
    collaborative_score: Option<f64>,
    content_score: Option<f64>,
    popularity_score: f64,
    diversity_penalty: f64,
    context_boost: f64,
}

/// 用户反馈
#[derive(Debug, Clone)]
pub enum UserFeedback {
    /// 搜索结果相关性反馈
    SearchResultRelevance {
        query: String,
        document_id: String,
        relevance_score: f64, // 0.0-1.0
    },
    /// 文档评分
    DocumentRating {
        document_id: String,
        rating: f32, // 1.0-5.0
    },
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_personalization_config_default() {
        let config = PersonalizationConfig::default();
        assert_eq!(config.behavior_retention_days, 90);
        assert_eq!(config.min_interactions_threshold, 5);
        assert_eq!(config.personalization_strength, 0.7);
    }

    #[test]
    fn test_behavior_weights_default() {
        let weights = BehaviorWeights::default();
        assert_eq!(weights.search_weight, 1.0);
        assert_eq!(weights.click_weight, 2.0);
        assert_eq!(weights.rating_weight, 6.0);
    }

    #[test]
    fn test_cold_start_strategy_equality() {
        assert_eq!(
            ColdStartStrategy::PopularityBased,
            ColdStartStrategy::PopularityBased
        );
        assert_ne!(
            ColdStartStrategy::PopularityBased,
            ColdStartStrategy::Random
        );
    }

    #[test]
    fn test_time_preferences_default() {
        let prefs = TimePreferences::default();
        assert_eq!(prefs.preferred_hours, vec![9, 10, 11, 14, 15, 16]);
        assert_eq!(prefs.weekday_preference, 0.7);
    }
}
