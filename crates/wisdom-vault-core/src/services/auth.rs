use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use validator::Validate;
use wisdom_vault_common::time::current_millis;
use wisdom_vault_database::repositories::{MongoUserRepository, UserRepository};

use crate::password;

#[derive(Debug, thiserror::Error)]
pub enum Error {
    /// 用户名或密码错误
    #[error("Username or password is incorrect")]
    UsernameOrPasswordIncorrect,

    #[error("User account is disabled")]
    AccountDisabled,

    #[error("{0}")]
    Unknown(anyhow::Error),
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(length(min = 1))]
    pub principal: String,

    #[validate(length(min = 1))]
    pub password: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LoginResponse {
    pub id: String,
    pub username: String,
    pub email: String,
    pub full_name: Option<String>,
}

#[derive(Clone)]
pub struct AuthService {
    user_repo: Arc<MongoUserRepository>,
}

impl AuthService {
    pub fn new(user_repo: Arc<MongoUserRepository>) -> Self {
        Self { user_repo }
    }

    pub async fn authenticate(&self, request: LoginRequest) -> Result<LoginResponse, Error> {
        // Find user by username or email
        let user = self
            .user_repo
            .find_by_username_or_email(&request.principal)
            .await
            .map_err(|e| Error::Unknown(e))?
            .ok_or(Error::UsernameOrPasswordIncorrect)?;

        // Check if user is active
        if !user.is_active {
            return Err(Error::AccountDisabled);
        }

        // Verify password
        if !password::verify_password(&request.password, &user.password_hash)
            .map_err(Error::Unknown)?
        {
            return Err(Error::UsernameOrPasswordIncorrect);
        }

        // Update last login timestamp
        let mut updated_user = user.clone();
        updated_user.last_login_at = Some(current_millis());
        let _ = self
            .user_repo
            .update(&updated_user)
            .await
            .map_err(Error::Unknown)?;

        Ok(LoginResponse {
            id: user.id.to_string(),
            username: user.username,
            email: user.email,
            full_name: user.full_name,
        })
    }
}
