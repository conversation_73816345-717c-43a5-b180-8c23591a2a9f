use anyhow::{anyhow, Result};
use reqwest::multipart::{Form, Part};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::PathBuf};
use wisdom_vault_common::db::next_id;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DocumentMetadata {
    pub document_id: String,
    pub original_filename: String,
    pub file_path: PathBuf,
    pub file_size: u64,
    pub mime_type: String,
    pub content_type: String,
    pub title: Option<String>,
    pub author: Option<String>,
    pub subject: Option<String>,
    pub creator: Option<String>,
    pub producer: Option<String>,
    pub creation_date: Option<u64>,
    pub modification_date: Option<u64>,
    pub language: Option<String>,
    pub page_count: Option<i32>,
    pub word_count: Option<i32>,
    pub character_count: Option<i32>,
    pub keywords: Vec<String>,
    pub custom_properties: HashMap<String, String>,
    pub extracted_at: i64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ExtractedContent {
    pub document_id: String,
    pub plain_text: String,
    pub structured_content: Option<String>, // JSON representation of structured content
    pub content_hash: String,
    pub content_length: usize,
    pub extraction_method: String,
    pub extraction_quality: f32, // 0.0 to 1.0 quality score
    pub extracted_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedDocument {
    pub metadata: DocumentMetadata,
    pub content: ExtractedContent,
    pub parsing_errors: Vec<String>,
    pub parsing_warnings: Vec<String>,
    pub processing_time_ms: u64,
}

#[derive(Debug, Clone)]
pub struct TikaConfig {
    pub server_url: String,
    pub timeout_seconds: u64,
    pub max_file_size: u64,
    pub supported_mime_types: Vec<String>,
    pub extract_embedded_files: bool,
    pub preserve_metadata: bool,
    pub max_retries: u32,
}

impl TikaConfig {
    pub fn from_app_config_tika(
        server_url: &str,
        timeout_seconds: u64,
        max_retries: u32,
        supported_mime_types: &[String],
    ) -> Self {
        Self {
            server_url: server_url.to_string(),
            timeout_seconds,
            max_file_size: 100 * 1024 * 1024, // 100MB - keep default
            supported_mime_types: supported_mime_types.to_vec(),
            extract_embedded_files: false,
            preserve_metadata: true,
            max_retries,
        }
    }
}

impl Default for TikaConfig {
    fn default() -> Self {
        Self {
            server_url: "http://localhost:9998".to_string(),
            timeout_seconds: 300,             // 5 minutes
            max_file_size: 100 * 1024 * 1024, // 100MB
            supported_mime_types: vec![
                "application/pdf".to_string(),
                "application/msword".to_string(),
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    .to_string(),
                "application/vnd.ms-excel".to_string(),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".to_string(),
                "application/vnd.ms-powerpoint".to_string(),
                "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                    .to_string(),
                "text/plain".to_string(),
                "text/html".to_string(),
                "text/markdown".to_string(),
                "application/rtf".to_string(),
                "application/vnd.oasis.opendocument.text".to_string(),
            ],
            extract_embedded_files: false,
            preserve_metadata: true,
            max_retries: 3,
        }
    }
}

pub struct DocumentParserService {
    config: TikaConfig,
    http_client: reqwest::Client,
}

impl DocumentParserService {
    pub fn new(config: TikaConfig) -> Result<Self> {
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(config.timeout_seconds))
            .build()
            .map_err(|e| anyhow!("Failed to create HTTP client: {}", e))?;

        Ok(Self {
            config,
            http_client,
        })
    }

    pub async fn parse_document(
        &self,
        file_data: Vec<u8>,
        filename: &str,
        mime_type: &str,
    ) -> Result<ParsedDocument> {
        let start_time = std::time::Instant::now();
        let document_id = next_id();

        // Validate file size
        if file_data.len() as u64 > self.config.max_file_size {
            return Err(anyhow!(
                "File size exceeds maximum allowed size for parsing"
            ));
        }

        // Validate MIME type
        if !self
            .config
            .supported_mime_types
            .contains(&mime_type.to_string())
        {
            return Err(anyhow!("Unsupported MIME type for parsing: {}", mime_type));
        }

        // Check if Tika server is available
        self.health_check().await?;

        // Extract metadata and content concurrently
        let (metadata_result, content_result) = tokio::join!(
            self.extract_metadata(&file_data, filename, mime_type, &document_id),
            self.extract_content(&file_data, filename, mime_type, &document_id)
        );

        let mut parsing_errors = Vec::new();
        let parsing_warnings = Vec::new();

        let current_millis = wisdom_vault_common::time::current_millis();

        let metadata = match metadata_result {
            Ok(meta) => meta,
            Err(e) => {
                parsing_errors.push(format!("Metadata extraction failed: {e}"));
                // Create minimal metadata
                DocumentMetadata {
                    document_id: document_id.clone(),
                    original_filename: filename.to_string(),
                    file_path: PathBuf::from(filename),
                    file_size: file_data.len() as u64,
                    mime_type: mime_type.to_string(),
                    content_type: mime_type.to_string(),
                    title: None,
                    author: None,
                    subject: None,
                    creator: None,
                    producer: None,
                    creation_date: None,
                    modification_date: None,
                    language: None,
                    page_count: None,
                    word_count: None,
                    character_count: None,
                    keywords: Vec::new(),
                    custom_properties: HashMap::new(),
                    extracted_at: current_millis,
                }
            }
        };

        let content = match content_result {
            Ok(content) => content,
            Err(e) => {
                parsing_errors.push(format!("Content extraction failed: {e}"));
                // Create minimal content
                ExtractedContent {
                    document_id,
                    plain_text: String::new(),
                    structured_content: None,
                    content_hash: self.calculate_content_hash(""),
                    content_length: 0,
                    extraction_method: "failed".to_string(),
                    extraction_quality: 0.0,
                    extracted_at: current_millis,
                }
            }
        };

        let processing_time = start_time.elapsed().as_millis() as u64;

        Ok(ParsedDocument {
            metadata,
            content,
            parsing_errors,
            parsing_warnings,
            processing_time_ms: processing_time,
        })
    }

    async fn health_check(&self) -> Result<()> {
        let url = format!("{}/tika", self.config.server_url);

        match self.http_client.get(&url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    Ok(())
                } else {
                    Err(anyhow!(
                        "Tika server returned error status: {}",
                        response.status()
                    ))
                }
            }
            Err(e) => Err(anyhow!(
                "Failed to connect to Tika server: {}. Please ensure Tika server is running at {}",
                e,
                self.config.server_url
            )),
        }
    }

    async fn extract_metadata(
        &self,
        file_data: &[u8],
        filename: &str,
        mime_type: &str,
        document_id: &str,
    ) -> Result<DocumentMetadata> {
        let url = format!("{}/meta", self.config.server_url);

        let part = Part::bytes(file_data.to_vec())
            .file_name(filename.to_string())
            .mime_str(mime_type)?;

        let form = Form::new().part("file", part);

        let response = self.http_client.put(&url).multipart(form).send().await?;

        if !response.status().is_success() {
            return Err(anyhow!(
                "Metadata extraction failed with status: {}",
                response.status()
            ));
        }

        let metadata_json: serde_json::Value = response.json().await?;

        // Parse Tika metadata response
        let title = metadata_json
            .get("title")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let author = metadata_json
            .get("author")
            .or_else(|| metadata_json.get("creator"))
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let subject = metadata_json
            .get("subject")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let creator = metadata_json
            .get("Application-Name")
            .or_else(|| metadata_json.get("creator"))
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let producer = metadata_json
            .get("producer")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        // Parse dates
        let creation_date = metadata_json
            .get("Creation-Date")
            .or_else(|| metadata_json.get("created"))
            .and_then(|v| v.as_u64());

        let modification_date = metadata_json
            .get("Last-Modified")
            .or_else(|| metadata_json.get("modified"))
            .and_then(|v| v.as_u64());

        let language = metadata_json
            .get("language")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        // Parse numeric fields
        let page_count = metadata_json
            .get("xmpTPg:NPages")
            .or_else(|| metadata_json.get("Page-Count"))
            .and_then(|v| v.as_i64())
            .map(|n| n as i32);

        let word_count = metadata_json
            .get("meta:word-count")
            .and_then(|v| v.as_i64())
            .map(|n| n as i32);

        let character_count = metadata_json
            .get("meta:character-count")
            .and_then(|v| v.as_i64())
            .map(|n| n as i32);

        // Extract keywords
        let keywords = metadata_json
            .get("Keywords")
            .or_else(|| metadata_json.get("keywords"))
            .and_then(|v| v.as_str())
            .map(|s| s.split(',').map(|k| k.trim().to_string()).collect())
            .unwrap_or_default();

        // Extract custom properties
        let mut custom_properties = HashMap::new();
        if let Some(obj) = metadata_json.as_object() {
            for (key, value) in obj {
                if let Some(str_value) = value.as_str() {
                    if !matches!(
                        key.as_str(),
                        "title"
                            | "author"
                            | "creator"
                            | "subject"
                            | "producer"
                            | "Creation-Date"
                            | "created"
                            | "Last-Modified"
                            | "modified"
                            | "language"
                            | "xmpTPg:NPages"
                            | "Page-Count"
                            | "meta:word-count"
                            | "meta:character-count"
                            | "Keywords"
                            | "keywords"
                    ) {
                        custom_properties.insert(key.clone(), str_value.to_string());
                    }
                }
            }
        }

        let current_millis = wisdom_vault_common::time::current_millis();

        Ok(DocumentMetadata {
            document_id: document_id.to_owned(),
            original_filename: filename.to_string(),
            file_path: PathBuf::from(filename),
            file_size: file_data.len() as u64,
            mime_type: mime_type.to_string(),
            content_type: mime_type.to_string(),
            title,
            author,
            subject,
            creator,
            producer,
            creation_date,
            modification_date,
            language,
            page_count,
            word_count,
            character_count,
            keywords,
            custom_properties,
            extracted_at: current_millis,
        })
    }

    async fn extract_content(
        &self,
        file_data: &[u8],
        filename: &str,
        mime_type: &str,
        document_id: &str,
    ) -> Result<ExtractedContent> {
        let url = format!("{}/tika", self.config.server_url);

        let part = Part::bytes(file_data.to_vec())
            .file_name(filename.to_string())
            .mime_str(mime_type)?;

        let form = Form::new().part("file", part);

        let response = self
            .http_client
            .put(&url)
            .header("Accept", "text/plain")
            .multipart(form)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow!(
                "Content extraction failed with status: {}",
                response.status()
            ));
        }

        let plain_text = response.text().await?;

        // Calculate quality score based on content characteristics
        let extraction_quality = self.calculate_extraction_quality(&plain_text, file_data.len());

        // Also extract structured content (HTML/XML)
        let structured_content = (self
            .extract_structured_content(file_data, filename, mime_type)
            .await)
            .ok();

        let current_millis = wisdom_vault_common::time::current_millis();

        Ok(ExtractedContent {
            document_id: document_id.to_owned(),
            plain_text: plain_text.clone(),
            structured_content,
            content_hash: self.calculate_content_hash(&plain_text),
            content_length: plain_text.len(),
            extraction_method: "apache-tika".to_string(),
            extraction_quality,
            extracted_at: current_millis,
        })
    }

    async fn extract_structured_content(
        &self,
        file_data: &[u8],
        filename: &str,
        mime_type: &str,
    ) -> Result<String> {
        let url = format!("{}/tika", self.config.server_url);

        let part = Part::bytes(file_data.to_vec())
            .file_name(filename.to_string())
            .mime_str(mime_type)?;

        let form = Form::new().part("file", part);

        let response = self
            .http_client
            .put(&url)
            .header("Accept", "text/html")
            .multipart(form)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow!(
                "Structured content extraction failed with status: {}",
                response.status()
            ));
        }

        Ok(response.text().await?)
    }

    fn calculate_extraction_quality(&self, content: &str, file_size: usize) -> f32 {
        let content_length = content.len();

        // Base quality on content-to-file-size ratio
        let ratio = content_length as f32 / file_size as f32;

        // Quality indicators
        let has_meaningful_content = content_length > 100;
        let reasonable_ratio = ratio > 0.01 && ratio < 10.0; // Not too sparse, not too verbose
        let contains_words = content.split_whitespace().count() > 10;
        let not_mostly_special_chars = {
            let alphanumeric_count = content.chars().filter(|c| c.is_alphanumeric()).count();
            alphanumeric_count as f32 / content_length as f32 > 0.5
        };

        let mut quality: f32 = 0.0;

        if has_meaningful_content {
            quality += 0.3;
        }
        if reasonable_ratio {
            quality += 0.3;
        }
        if contains_words {
            quality += 0.2;
        }
        if not_mostly_special_chars {
            quality += 0.2;
        }

        // Normalize to 0.0-1.0 range
        quality.clamp(0.0, 1.0)
    }

    fn calculate_content_hash(&self, content: &str) -> String {
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{Hash, Hasher},
        };

        let mut hasher = DefaultHasher::new();
        content.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    pub async fn get_supported_mime_types(&self) -> Result<Vec<String>> {
        let url = format!("{}/mime-types", self.config.server_url);

        match self.http_client.get(&url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    let mime_types: Vec<String> = response.json().await?;
                    Ok(mime_types)
                } else {
                    // Return configured mime types as fallback
                    Ok(self.config.supported_mime_types.clone())
                }
            }
            Err(_) => {
                // Return configured mime types as fallback
                Ok(self.config.supported_mime_types.clone())
            }
        }
    }

    pub async fn detect_mime_type(&self, file_data: &[u8]) -> Result<String> {
        let url = format!("{}/detect/stream", self.config.server_url);

        let response = self
            .http_client
            .put(&url)
            .body(file_data.to_vec())
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow!(
                "MIME type detection failed with status: {}",
                response.status()
            ));
        }

        Ok(response.text().await?)
    }

    pub fn is_supported_mime_type(&self, mime_type: &str) -> bool {
        self.config
            .supported_mime_types
            .contains(&mime_type.to_string())
    }

    pub fn get_config(&self) -> &TikaConfig {
        &self.config
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // Note: These tests require a running Tika server
    // Run: docker run -p 9998:9998 apache/tika:latest

    #[tokio::test]
    #[ignore = "requires running Tika server"]
    async fn test_parse_simple_text() {
        let config = TikaConfig::default();
        let parser = DocumentParserService::new(config).unwrap();

        let content = b"Hello, World! This is a test document.";
        let result = parser
            .parse_document(content.to_vec(), "test.txt", "text/plain")
            .await;

        assert!(result.is_ok());
        let parsed = result.unwrap();
        assert!(!parsed.content.plain_text.is_empty());
        assert!(parsed.content.plain_text.contains("Hello, World!"));
    }

    #[tokio::test]
    #[ignore = "requires running Tika server"]
    async fn test_health_check() {
        let config = TikaConfig::default();
        let parser = DocumentParserService::new(config).unwrap();

        let result = parser.health_check().await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_quality_calculation() {
        let config = TikaConfig::default();
        let parser = DocumentParserService::new(config).unwrap();

        // High quality content
        let good_content =
            "This is a well-formed document with meaningful content that should score well.";
        let quality = parser.calculate_extraction_quality(good_content, 1000);
        assert!(quality > 0.5);

        // Low quality content
        let bad_content = "###@@!";
        let quality = parser.calculate_extraction_quality(bad_content, 1000);
        assert!(quality < 0.5);
    }
}
