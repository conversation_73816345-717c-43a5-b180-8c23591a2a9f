use anyhow::{Result, anyhow};
use argon2::{
    Argon2, PasswordHash, PasswordVerifier,
    password_hash::{PasswordHasher, Salt},
};
use validator::ValidateEmail;

/// Hash a password using argon2
pub fn hash_password(salt: &str, password: &str) -> Result<String> {
    // Validate password strength
    validate_password_strength(password)?;

    let argon2 = Argon2::default();
    argon2
        .hash_password(
            password.as_bytes(),
            Salt::from_b64(salt).map_err(|e| anyhow!("Invalid salt: {}", e))?,
        )
        .map(|hash| hash.to_string())
        .map_err(|e| anyhow!("Failed to hash password: {}", e))
}

/// Verify a password against its hash
pub fn verify_password(password: &str, hash: &str) -> Result<bool> {
    let parsed_hash =
        PasswordHash::new(hash).map_err(|e| anyhow!("Failed to verify password: {}", e))?;
    Argon2::default()
        .verify_password(password.as_bytes(), &parsed_hash)
        .map(|_| true)
        .map_err(|e| anyhow!("Failed to verify password: {}", e))
}

/// Validate password strength
fn validate_password_strength(password: &str) -> Result<()> {
    if password.len() < 8 {
        return Err(anyhow!("Password must be at least 8 characters long"));
    }

    if password.len() > 128 {
        return Err(anyhow!("Password must not exceed 128 characters"));
    }

    let has_uppercase = password.chars().any(|c| c.is_uppercase());
    let has_lowercase = password.chars().any(|c| c.is_lowercase());
    let has_digit = password.chars().any(|c| c.is_numeric());
    let has_special = password
        .chars()
        .any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));

    let strength_count = [has_uppercase, has_lowercase, has_digit, has_special]
        .iter()
        .filter(|&&x| x)
        .count();

    if strength_count < 3 {
        return Err(anyhow!(
            "Password must contain at least 3 of the following: uppercase letter, lowercase letter, digit, special character"
        ));
    }

    // Check for common weak passwords
    let weak_passwords = [
        "password",
        "123456",
        "123456789",
        "qwerty",
        "abc123",
        "password123",
        "admin",
        "letmein",
        "welcome",
        "monkey",
    ];

    if weak_passwords.contains(&password.to_lowercase().as_str()) {
        return Err(anyhow!("Password is too common and easily guessable"));
    }

    Ok(())
}

/// Validate email format
pub fn validate_email_format(email: &str) -> Result<()> {
    if !email.validate_email() {
        return Err(anyhow!("Invalid email format"));
    }
    Ok(())
}

/// Validate username format
pub fn validate_username(username: &str) -> Result<()> {
    if username.len() < 3 {
        return Err(anyhow!("Username must be at least 3 characters long"));
    }

    if username.len() > 50 {
        return Err(anyhow!("Username must not exceed 50 characters"));
    }

    // Username should contain only alphanumeric characters and underscores
    if !username.chars().all(|c| c.is_alphanumeric() || c == '_') {
        return Err(anyhow!(
            "Username can only contain letters, numbers, and underscores"
        ));
    }

    // Username should not start with a number
    if username.chars().next().unwrap().is_numeric() {
        return Err(anyhow!("Username cannot start with a number"));
    }

    Ok(())
}

/// Generate a random password
pub fn generate_random_password(length: usize) -> String {
    use rand::Rng;

    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789\
                                !@#$%^&*()_+-=";

    let mut rng = rand::rng();

    (0..length)
        .map(|_| {
            let idx = rng.random_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_hashing_and_verification() {
        let slat = "Slat123!";
        let password = "TestPassword123!";
        let hash = hash_password(slat, password).unwrap();

        println!("Hash: {hash}");

        assert!(verify_password(password, &hash).unwrap());
        assert!(!verify_password("wrongpassword", &hash).unwrap());
    }

    #[test]
    fn test_password_strength_validation() {
        // Valid password
        assert!(validate_password_strength("StrongPass123!").is_ok());

        // Too short
        assert!(validate_password_strength("weak").is_err());

        // Only has 2 character types (lowercase + digits)
        assert!(validate_password_strength("lowercase123").is_err());

        // Common password
        assert!(validate_password_strength("password").is_err());
    }

    #[test]
    fn test_email_validation() {
        assert!(validate_email_format("<EMAIL>").is_ok());
        assert!(validate_email_format("invalid-email").is_err());
    }

    #[test]
    fn test_username_validation() {
        assert!(validate_username("valid_user123").is_ok());
        assert!(validate_username("123invalid").is_err()); // starts with number
        assert!(validate_username("ab").is_err()); // too short
        assert!(validate_username("invalid-user").is_err()); // contains dash
    }

    #[test]
    fn test_random_password_generation() {
        let password = generate_random_password(12);
        assert_eq!(password.len(), 12);
        assert!(validate_password_strength(&password).is_ok());
    }
}
