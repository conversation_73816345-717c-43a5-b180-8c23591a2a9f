use anyhow::Result;
use chrono::Utc;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::PathBuf, sync::Arc};
use tokio::sync::RwLock;
use wisdom_vault_common::{db::next_id, time::current_millis};

use wisdom_vault_database::models::{
    EmbeddingModel, EmbeddingModelConfig, EmbeddingModelType, InferenceConfig,
    ModelPerformanceMetrics, NormalizationMethod, PaddingStrategy, PreprocessingConfig,
    TruncationStrategy,
};

/// 模型加载状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ModelLoadState {
    NotLoaded,
    Loading,
    Loaded,
    Failed(String),
}

/// 已加载的模型实例
#[derive(Debug)]
pub struct LoadedModel {
    pub model: EmbeddingModel,
    pub state: ModelLoadState,
    pub load_time: chrono::DateTime<chrono::Utc>,
    pub memory_usage_mb: f64,
    pub last_used: chrono::DateTime<chrono::Utc>,
    pub usage_count: u64,
}

/// 模型管理器配置
#[derive(Debug, Clone)]
pub struct ModelManagerConfig {
    /// 模型存储目录
    pub models_directory: PathBuf,
    /// 缓存目录
    pub cache_directory: PathBuf,
    /// 最大加载的模型数量
    pub max_loaded_models: usize,
    /// 模型内存限制(MB)
    pub memory_limit_mb: usize,
    /// 模型超时时间(秒)
    pub model_timeout_seconds: u64,
    /// 是否启用模型预热
    pub enable_warmup: bool,
    /// HuggingFace Hub配置
    pub hf_hub_config: HfHubConfig,
}

/// HuggingFace Hub配置
#[derive(Debug, Clone)]
pub struct HfHubConfig {
    pub token: Option<String>,
    pub cache_dir: Option<PathBuf>,
    pub offline: bool,
}

impl Default for ModelManagerConfig {
    fn default() -> Self {
        Self {
            models_directory: PathBuf::from("./models"),
            cache_directory: PathBuf::from("./cache/models"),
            max_loaded_models: 3,
            memory_limit_mb: 4096,
            model_timeout_seconds: 300,
            enable_warmup: true,
            hf_hub_config: HfHubConfig {
                token: None,
                cache_dir: None,
                offline: false,
            },
        }
    }
}

/// 嵌入模型管理器
pub struct EmbeddingModelManager {
    /// 配置
    config: ModelManagerConfig,
    /// 已注册的模型
    registered_models: Arc<RwLock<HashMap<String, EmbeddingModel>>>,
    /// 已加载的模型
    loaded_models: Arc<RwLock<HashMap<String, LoadedModel>>>,
    /// 默认模型ID
    default_model_id: Arc<RwLock<Option<String>>>,
    /// 模型使用统计
    usage_stats: Arc<RwLock<HashMap<String, ModelUsageStats>>>,
}

/// 模型使用统计
#[derive(Debug, Default, Clone)]
pub struct ModelUsageStats {
    pub total_requests: u64,
    pub total_tokens_processed: u64,
    pub total_processing_time_ms: u64,
    pub avg_processing_time_ms: f64,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
    pub error_count: u64,
}

impl EmbeddingModelManager {
    /// 创建新的模型管理器
    pub fn new(config: ModelManagerConfig) -> Result<Self> {
        // 确保目录存在
        std::fs::create_dir_all(&config.models_directory)?;
        std::fs::create_dir_all(&config.cache_directory)?;

        Ok(Self {
            config,
            registered_models: Arc::new(RwLock::new(HashMap::new())),
            loaded_models: Arc::new(RwLock::new(HashMap::new())),
            default_model_id: Arc::new(RwLock::new(None)),
            usage_stats: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// 注册嵌入模型
    pub async fn register_model(&self, model: EmbeddingModel) -> Result<()> {
        tracing::info!("注册嵌入模型: {} ({})", model.display_name, model.name);

        // 验证模型配置
        self.validate_model_config(&model)?;

        let model_id = model.id.clone();
        let is_default = model.is_default;

        // 添加到注册列表
        self.registered_models
            .write()
            .await
            .insert(model_id.clone(), model);

        // 设置默认模型
        if is_default {
            *self.default_model_id.write().await = Some(model_id.clone());
        }

        // 初始化使用统计
        self.usage_stats
            .write()
            .await
            .insert(model_id.clone(), ModelUsageStats::default());

        tracing::info!("模型注册成功: {}", model_id);
        Ok(())
    }

    /// 加载模型到内存
    pub async fn load_model(&self, model_id: &str) -> Result<()> {
        tracing::info!("开始加载模型: {}", model_id);

        // 检查是否已加载
        {
            let loaded = self.loaded_models.read().await;
            if let Some(loaded_model) = loaded.get(model_id) {
                match loaded_model.state {
                    ModelLoadState::Loaded => {
                        tracing::info!("模型已加载: {}", model_id);
                        return Ok(());
                    }
                    ModelLoadState::Loading => {
                        return Err(anyhow::anyhow!("模型正在加载中: {}", model_id));
                    }
                    _ => {}
                }
            }
        }

        // 获取模型配置
        let model = self.get_registered_model(model_id).await?;

        // 设置加载状态
        {
            let loaded_model = LoadedModel {
                model: model.clone(),
                state: ModelLoadState::Loading,
                load_time: Utc::now(),
                memory_usage_mb: 0.0,
                last_used: Utc::now(),
                usage_count: 0,
            };
            self.loaded_models
                .write()
                .await
                .insert(model_id.to_owned(), loaded_model);
        }

        // 检查内存限制
        self.check_memory_limits().await?;

        // 执行实际加载
        match self.perform_model_loading(&model).await {
            Ok(memory_usage) => {
                // 更新加载状态
                let mut loaded = self.loaded_models.write().await;
                if let Some(loaded_model) = loaded.get_mut(model_id) {
                    loaded_model.state = ModelLoadState::Loaded;
                    loaded_model.memory_usage_mb = memory_usage;
                }
                tracing::info!(
                    "模型加载成功: {}, 内存使用: {:.2}MB",
                    model_id,
                    memory_usage
                );
            }
            Err(e) => {
                // 更新失败状态
                let mut loaded = self.loaded_models.write().await;
                if let Some(loaded_model) = loaded.get_mut(model_id) {
                    loaded_model.state = ModelLoadState::Failed(e.to_string());
                }
                return Err(e);
            }
        }

        // 预热模型
        if self.config.enable_warmup {
            self.warmup_model(model_id).await?;
        }

        Ok(())
    }

    /// 卸载模型
    pub async fn unload_model(&self, model_id: &str) -> Result<()> {
        tracing::info!("卸载模型: {}", model_id);

        let mut loaded = self.loaded_models.write().await;
        if loaded.remove(model_id).is_some() {
            // 执行实际卸载逻辑
            self.perform_model_unloading(model_id).await?;
            tracing::info!("模型卸载成功: {}", model_id);
        }

        Ok(())
    }

    /// 获取模型
    pub async fn get_model(&self, model_id: &str) -> Result<EmbeddingModel> {
        // 确保模型已加载
        self.ensure_model_loaded(model_id).await?;

        // 更新使用统计
        self.update_usage_stats(model_id).await;

        // 返回模型
        self.get_registered_model(model_id).await
    }

    /// 获取默认模型
    pub async fn get_default_model(&self) -> Result<EmbeddingModel> {
        let guard = self.default_model_id.read().await;
        let default_id = guard
            .as_deref()
            .ok_or_else(|| anyhow::anyhow!("未设置默认模型"))?;

        self.get_model(default_id).await
    }

    /// 设置默认模型
    pub async fn set_default_model(&self, model_id: String) -> Result<()> {
        // 验证模型存在
        self.get_registered_model(&model_id).await?;

        // 更新默认模型
        *self.default_model_id.write().await = Some(model_id.clone());

        // 更新数据库中的标记
        self.update_default_model_flag(&model_id).await?;

        tracing::info!("设置默认模型: {}", model_id);
        Ok(())
    }

    /// 列出所有已注册的模型
    pub async fn list_registered_models(&self) -> Vec<EmbeddingModel> {
        self.registered_models
            .read()
            .await
            .values()
            .cloned()
            .collect()
    }

    /// 列出已加载的模型
    pub async fn list_loaded_models(&self) -> Vec<(EmbeddingModel, ModelLoadState)> {
        let loaded = self.loaded_models.read().await;
        loaded
            .values()
            .map(|lm| (lm.model.clone(), lm.state.clone()))
            .collect()
    }

    /// 获取模型使用统计
    pub async fn get_usage_stats(&self, model_id: &str) -> Option<ModelUsageStats> {
        self.usage_stats.read().await.get(model_id).cloned()
    }

    /// 预加载推荐的中文模型
    pub async fn setup_default_chinese_models(&self) -> Result<()> {
        tracing::info!("设置默认中文模型");

        // paraphrase-multilingual-MiniLM-L12-v2 模型配置
        let multilingual_model = EmbeddingModel {
            id: next_id(),
            name: "paraphrase-multilingual-MiniLM-L12-v2".to_string(),
            display_name: "多语言MiniLM模型".to_string(),
            model_type: EmbeddingModelType::SentenceTransformers,
            model_path: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2".to_string(),
            tokenizer_path: None,
            dimension: 384,
            max_sequence_length: 128,
            normalization: NormalizationMethod::L2,
            language_support: vec![
                "zh".to_string(),
                "en".to_string(),
                "multilingual".to_string(),
            ],
            model_config: self.create_default_model_config(),
            performance_metrics: self.create_default_performance_metrics(),
            is_active: true,
            is_default: true,
            version: "1.0.0".to_string(),
            created_at: current_millis(),
            updated_at: current_millis(),
        };

        self.register_model(multilingual_model).await?;

        // m3e-base 中文模型配置
        let m3e_model = EmbeddingModel {
            id: next_id(),
            name: "m3e-base".to_string(),
            display_name: "M3E中文嵌入模型".to_string(),
            model_type: EmbeddingModelType::SentenceTransformers,
            model_path: "moka-ai/m3e-base".to_string(),
            tokenizer_path: None,
            dimension: 768,
            max_sequence_length: 512,
            normalization: NormalizationMethod::L2,
            language_support: vec!["zh".to_string()],
            model_config: self.create_default_model_config(),
            performance_metrics: self.create_default_performance_metrics(),
            is_active: true,
            is_default: false,
            version: "1.0.0".to_string(),
            created_at: current_millis(),
            updated_at: current_millis(),
        };

        self.register_model(m3e_model).await?;

        tracing::info!("默认中文模型设置完成");
        Ok(())
    }

    /// 从HuggingFace Hub下载模型
    pub async fn download_model_from_hub(&self, model_name: &str) -> Result<PathBuf> {
        tracing::info!("从HuggingFace Hub下载模型: {}", model_name);

        // 这里是下载逻辑的占位符
        // 实际实现需要使用 hf-hub crate
        let model_path = self.config.models_directory.join(model_name);
        std::fs::create_dir_all(&model_path)?;

        // 模拟下载过程
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;

        tracing::info!("模型下载完成: {}", model_name);
        Ok(model_path)
    }

    /// 验证模型配置
    fn validate_model_config(&self, model: &EmbeddingModel) -> Result<()> {
        if model.name.is_empty() {
            return Err(anyhow::anyhow!("模型名称不能为空"));
        }

        if model.dimension <= 0 {
            return Err(anyhow::anyhow!("模型维度必须大于0"));
        }

        if model.max_sequence_length <= 0 {
            return Err(anyhow::anyhow!("最大序列长度必须大于0"));
        }

        Ok(())
    }

    /// 获取已注册的模型
    async fn get_registered_model(&self, model_id: &str) -> Result<EmbeddingModel> {
        self.registered_models
            .read()
            .await
            .get(model_id)
            .cloned()
            .ok_or_else(|| anyhow::anyhow!("模型未注册: {}", model_id))
    }

    /// 确保模型已加载
    async fn ensure_model_loaded(&self, model_id: &str) -> Result<()> {
        let loaded = self.loaded_models.read().await;
        match loaded.get(model_id) {
            Some(loaded_model) => match loaded_model.state {
                ModelLoadState::Loaded => Ok(()),
                ModelLoadState::Loading => Err(anyhow::anyhow!("模型正在加载中")),
                ModelLoadState::Failed(ref error) => {
                    Err(anyhow::anyhow!("模型加载失败: {}", error))
                }
                ModelLoadState::NotLoaded => {
                    drop(loaded);
                    self.load_model(model_id).await
                }
            },
            None => {
                drop(loaded);
                self.load_model(model_id).await
            }
        }
    }

    /// 执行实际的模型加载
    async fn perform_model_loading(&self, model: &EmbeddingModel) -> Result<f64> {
        tracing::info!("执行模型加载: {}", model.name);

        // 这里是模型加载的占位符实现
        // 实际需要使用 candle 加载 sentence-transformers 模型

        // 模拟加载时间
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

        // 模拟内存使用
        let memory_usage = match model.model_type {
            EmbeddingModelType::SentenceTransformers => {
                // 根据模型大小估算内存使用
                match model.dimension {
                    384 => 200.0,  // MiniLM类模型
                    768 => 500.0,  // Base类模型
                    1024 => 800.0, // Large类模型
                    _ => 300.0,
                }
            }
            _ => 300.0,
        };

        Ok(memory_usage)
    }

    /// 执行模型卸载
    async fn perform_model_unloading(&self, model_id: &str) -> Result<()> {
        tracing::info!("执行模型卸载: {}", model_id);

        // 这里是模型卸载的占位符实现
        // 实际需要释放模型占用的内存

        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        Ok(())
    }

    /// 预热模型
    async fn warmup_model(&self, model_id: &str) -> Result<()> {
        tracing::info!("预热模型: {}", model_id);

        // 使用示例文本进行推理预热
        let warmup_texts = vec!["这是一个测试文本", "This is a test text", "模型预热中"];

        for _text in warmup_texts {
            // 这里应该调用实际的推理方法
            // 当前只是模拟
            tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        }

        tracing::info!("模型预热完成: {}", model_id);
        Ok(())
    }

    /// 检查内存限制
    async fn check_memory_limits(&self) -> Result<()> {
        let loaded = self.loaded_models.read().await;
        let total_memory: f64 = loaded.values().map(|lm| lm.memory_usage_mb).sum();

        if total_memory > self.config.memory_limit_mb as f64 {
            // 需要卸载一些模型
            drop(loaded);
            self.evict_least_used_models().await?;
        }

        Ok(())
    }

    /// 驱逐最少使用的模型
    async fn evict_least_used_models(&self) -> Result<()> {
        tracing::info!("开始驱逐最少使用的模型");

        let mut loaded = self.loaded_models.write().await;

        // 按使用时间排序
        let mut models_by_usage: Vec<_> = loaded
            .iter()
            .map(|(id, model)| (id.clone(), model.last_used))
            .collect();

        models_by_usage.sort_by(|a, b| a.1.cmp(&b.1));

        // 移除最旧的模型，直到内存使用降到限制以下
        let mut current_memory: f64 = loaded.values().map(|lm| lm.memory_usage_mb).sum();

        for (model_id, _) in models_by_usage {
            if current_memory <= self.config.memory_limit_mb as f64 * 0.8 {
                break;
            }

            if let Some(removed_model) = loaded.remove(&model_id) {
                current_memory -= removed_model.memory_usage_mb;
                tracing::info!("驱逐模型: {}", model_id);
            }
        }

        Ok(())
    }

    /// 更新使用统计
    async fn update_usage_stats(&self, model_id: &str) {
        let mut stats = self.usage_stats.write().await;
        if let Some(stat) = stats.get_mut(model_id) {
            stat.total_requests += 1;
            stat.last_used = Some(Utc::now());
        }

        // 更新已加载模型的最后使用时间
        let mut loaded = self.loaded_models.write().await;
        if let Some(loaded_model) = loaded.get_mut(model_id) {
            loaded_model.last_used = Utc::now();
            loaded_model.usage_count += 1;
        }
    }

    /// 更新默认模型标记
    async fn update_default_model_flag(&self, model_id: &str) -> Result<()> {
        // 重置所有模型的默认标记
        let mut models = self.registered_models.write().await;
        for model in models.values_mut() {
            model.is_default = model.id == model_id;
        }
        Ok(())
    }

    /// 创建默认模型配置
    fn create_default_model_config(&self) -> EmbeddingModelConfig {
        EmbeddingModelConfig {
            batch_size: 32,
            device: "cpu".to_string(),
            precision: "fp32".to_string(),
            use_cache: true,
            cache_size_mb: 512,
            preprocessing_config: PreprocessingConfig {
                lowercase: true,
                remove_punctuation: false,
                remove_stopwords: false,
                stem_words: false,
                max_length: 512,
                truncation_strategy: TruncationStrategy::Tail,
                padding_strategy: PaddingStrategy::MaxLength,
            },
            inference_config: InferenceConfig {
                temperature: 1.0,
                top_k: None,
                top_p: None,
                repetition_penalty: 1.0,
                attention_dropout: 0.1,
                hidden_dropout: 0.1,
            },
        }
    }

    /// 创建默认性能指标
    fn create_default_performance_metrics(&self) -> ModelPerformanceMetrics {
        ModelPerformanceMetrics {
            avg_processing_time_ms: 0.0,
            throughput_tokens_per_sec: 0.0,
            memory_usage_mb: 0.0,
            accuracy_score: None,
            f1_score: None,
            benchmark_results: serde_json::Value::Null,
            last_evaluated_at: current_millis(),
        }
    }
}
