use anyhow::Result;
use chrono::Utc;
use std::{collections::HashMap, sync::Arc};
use wisdom_vault_common::{db::next_id, time::current_millis};
use wisdom_vault_database::{
    models::{Document, DocumentRelation, DocumentRelationType, RelationMetadata},
    repositories::{DocumentRepository, DocumentRelationRepository},
};

/// 文档关系服务
/// 提供文档间关系管理、关系发现和关系图构建功能
pub struct DocumentRelationService {
    document_repo: Arc<dyn DocumentRepository + Send + Sync>,
    relation_repo: Arc<dyn DocumentRelationRepository + Send + Sync>,
}

impl DocumentRelationService {
    pub fn new(
        document_repo: Arc<dyn DocumentRepository + Send + Sync>,
        relation_repo: Arc<dyn DocumentRelationRepository + Send + Sync>,
    ) -> Self {
        Self { 
            document_repo,
            relation_repo,
        }
    }

    /// 创建文档关系
    #[allow(clippy::too_many_arguments)]
    pub async fn create_relation(
        &self,
        source_document_id: String,
        target_document_id: String,
        relation_type: DocumentRelationType,
        strength: f64,
        confidence: f64,
        description: Option<String>,
        created_by: Option<String>,
    ) -> Result<DocumentRelation> {
        // 验证文档存在
        let source_doc = self
            .document_repo
            .find_by_id(&source_document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Source document not found"))?;

        let target_doc = self
            .document_repo
            .find_by_id(&target_document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Target document not found"))?;

        // 检查是否为同一文档
        if source_document_id == target_document_id {
            return Err(anyhow::anyhow!("Cannot create relation with itself"));
        }

        // 检查文档是否属于同一知识库
        if source_doc.knowledge_base_id != target_doc.knowledge_base_id {
            return Err(anyhow::anyhow!(
                "Documents must be in the same knowledge base"
            ));
        }

        // 创建关系元数据
        let metadata = RelationMetadata {
            extraction_method: "manual".to_string(),
            evidence_snippets: Vec::new(),
            semantic_similarity: None,
            manual_verified: created_by.is_some(),
        };

        tracing::info!(
            "Created relation {:?} between documents {} and {}",
            relation_type,
            source_document_id,
            target_document_id
        );
        // 创建关系
        let relation = DocumentRelation {
            id: next_id(),
            source_document_id,
            target_document_id,
            relation_type,
            strength: strength.clamp(0.0, 1.0),
            confidence: confidence.clamp(0.0, 1.0),
            description,
            metadata,
            created_by,
            created_at: current_millis(),
        };

        // 保存关系到数据库
        self.relation_repo.create(&relation).await
    }

    /// 删除文档关系
    pub async fn delete_relation(&self, relation_id: &str) -> Result<bool> {
        self.relation_repo.delete(relation_id).await
    }

    /// 获取文档的所有出向关系
    pub async fn get_outbound_relations(
        &self,
        document_id: &str,
    ) -> Result<Vec<DocumentRelation>> {
        self.relation_repo.find_outbound_relations(document_id).await
    }

    /// 获取文档的所有入向关系
    pub async fn get_inbound_relations(&self, document_id: &str) -> Result<Vec<DocumentRelation>> {
        self.relation_repo.find_inbound_relations(document_id).await
    }

    /// 获取文档的所有关系（入向和出向）
    pub async fn get_all_relations(&self, document_id: &str) -> Result<Vec<DocumentRelation>> {
        let mut outbound = self.get_outbound_relations(document_id).await?;
        let inbound = self.get_inbound_relations(document_id).await?;

        outbound.extend(inbound);
        Ok(outbound)
    }

    /// 基于内容相似性自动发现文档关系
    pub async fn discover_similar_relations(
        &self,
        document_id: &str,
        similarity_threshold: f64,
        max_relations: usize,
    ) -> Result<Vec<DocumentRelation>> {
        let document = self
            .document_repo
            .find_by_id(document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Document not found"))?;

        // 获取同知识库的所有文档
        let all_documents = self
            .document_repo
            .find_by_knowledge_base(&document.knowledge_base_id, None, None)
            .await?;

        let mut similar_relations = Vec::new();

        for other_doc in all_documents {
            if other_doc.id == document_id {
                continue;
            }

            // 计算内容相似性
            let similarity = self.calculate_content_similarity(&document, &other_doc);

            if similarity >= similarity_threshold {
                let relation = self
                    .create_similarity_relation(
                        document_id.to_owned(),
                        other_doc.id.clone(),
                        similarity,
                        similarity,
                    )
                    .await?;

                similar_relations.push(relation);
            }
        }

        // 按相似性排序并限制数量
        similar_relations.sort_by(|a, b| {
            b.strength
                .partial_cmp(&a.strength)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        similar_relations.truncate(max_relations);

        Ok(similar_relations)
    }

    /// 基于引用模式发现文档关系
    pub async fn discover_reference_relations(
        &self,
        document_id: &str,
    ) -> Result<Vec<DocumentRelation>> {
        let document = self
            .document_repo
            .find_by_id(document_id)
            .await?
            .ok_or_else(|| anyhow::anyhow!("Document not found"))?;

        let all_documents = self
            .document_repo
            .find_by_knowledge_base(&document.knowledge_base_id, None, None)
            .await?;

        let mut reference_relations = Vec::new();

        // 简单的引用检测：查找标题在内容中的出现
        for other_doc in all_documents {
            if other_doc.id == document_id {
                continue;
            }

            // 检查当前文档是否引用了其他文档
            if self.contains_reference(&document.content, &other_doc.title) {
                let relation = DocumentRelation {
                    id: next_id(),
                    source_document_id: document_id.to_owned(),
                    target_document_id: other_doc.id.clone(),
                    relation_type: DocumentRelationType::References,
                    strength: 0.8,
                    confidence: 0.7,
                    description: Some(format!("References document '{}'", other_doc.title)),
                    metadata: RelationMetadata {
                        extraction_method: "reference_detection".to_string(),
                        evidence_snippets: self
                            .extract_reference_snippets(&document.content, &other_doc.title),
                        semantic_similarity: None,
                        manual_verified: false,
                    },
                    created_by: None,
                    created_at: current_millis(),
                };
                reference_relations.push(relation);
            }

            // 检查其他文档是否引用了当前文档
            if self.contains_reference(&other_doc.content, &document.title) {
                let relation = DocumentRelation {
                    id: next_id(),
                    source_document_id: other_doc.id,
                    target_document_id: document_id.to_owned(),
                    relation_type: DocumentRelationType::References,
                    strength: 0.8,
                    confidence: 0.7,
                    description: Some(format!("Referenced by document '{}'", other_doc.title)),
                    metadata: RelationMetadata {
                        extraction_method: "reference_detection".to_string(),
                        evidence_snippets: self
                            .extract_reference_snippets(&other_doc.content, &document.title),
                        semantic_similarity: None,
                        manual_verified: false,
                    },
                    created_by: None,
                    created_at: current_millis(),
                };
                reference_relations.push(relation);
            }
        }

        Ok(reference_relations)
    }

    /// 构建文档关系图
    pub async fn build_relation_graph(&self, kb_id: String) -> Result<DocumentRelationGraph> {
        let documents = self
            .document_repo
            .find_by_knowledge_base(&kb_id, None, None)
            .await?;

        let mut nodes = Vec::new();
        let mut edges = Vec::new();

        // 创建节点
        for doc in &documents {
            nodes.push(RelationGraphNode {
                id: doc.id.clone(),
                title: doc.title.clone(),
                document_type: doc.file_type.clone(),
                created_at: doc.created_at,
                inbound_count: 0,  // 将在后面计算
                outbound_count: 0, // 将在后面计算
            });
        }

        // 获取所有关系并创建边
        for doc in &documents {
            let relations = self.get_outbound_relations(&doc.id).await?;
            for relation in relations {
                edges.push(RelationGraphEdge {
                    id: relation.id,
                    source_id: relation.source_document_id,
                    target_id: relation.target_document_id,
                    relation_type: relation.relation_type.clone(),
                    strength: relation.strength,
                    confidence: relation.confidence,
                });
            }
        }

        // 计算入度和出度
        let mut inbound_counts: HashMap<String, usize> = HashMap::new();
        let mut outbound_counts: HashMap<String, usize> = HashMap::new();

        for edge in &edges {
            *outbound_counts.entry(edge.source_id.clone()).or_insert(0) += 1;
            *inbound_counts.entry(edge.target_id.clone()).or_insert(0) += 1;
        }

        // 更新节点的度数信息
        for node in &mut nodes {
            node.inbound_count = inbound_counts.get(&node.id).copied().unwrap_or(0);
            node.outbound_count = outbound_counts.get(&node.id).copied().unwrap_or(0);
        }

        let total_relations = edges.len();

        Ok(DocumentRelationGraph {
            knowledge_base_id: kb_id,
            nodes,
            edges,
            total_documents: documents.len(),
            total_relations,
            created_at: Utc::now(),
        })
    }

    /// 获取关系统计信息
    pub async fn get_relation_statistics(&self, kb_id: &str) -> Result<RelationStatistics> {
        let graph = self.build_relation_graph(kb_id.to_owned()).await?;

        let mut relation_type_counts: HashMap<String, usize> = HashMap::new();
        for edge in &graph.edges {
            let type_name = format!("{:?}", edge.relation_type);
            *relation_type_counts.entry(type_name).or_insert(0) += 1;
        }

        let most_connected_document = graph
            .nodes
            .iter()
            .max_by_key(|node| node.inbound_count + node.outbound_count)
            .cloned();

        let average_connections = if graph.total_documents > 0 {
            graph.total_relations as f64 / graph.total_documents as f64
        } else {
            0.0
        };

        Ok(RelationStatistics {
            total_documents: graph.total_documents,
            total_relations: graph.total_relations,
            relation_type_counts,
            most_connected_document,
            average_connections,
            graph_density: self.calculate_graph_density(&graph),
        })
    }

    /// 计算内容相似性
    fn calculate_content_similarity(&self, doc1: &Document, doc2: &Document) -> f64 {
        // 简单的相似性计算：基于共同关键词
        let keywords1: Vec<String> = doc1
            .metadata
            .keywords
            .iter()
            .map(|k| k.to_lowercase())
            .collect();
        let keywords2: Vec<String> = doc2
            .metadata
            .keywords
            .iter()
            .map(|k| k.to_lowercase())
            .collect();

        if keywords1.is_empty() || keywords2.is_empty() {
            return 0.0;
        }

        let common_keywords: usize = keywords1.iter().filter(|k| keywords2.contains(k)).count();

        let total_keywords = keywords1.len() + keywords2.len() - common_keywords;

        if total_keywords == 0 {
            0.0
        } else {
            common_keywords as f64 / total_keywords as f64
        }
    }

    /// 创建相似性关系
    async fn create_similarity_relation(
        &self,
        source_id: String,
        target_id: String,
        similarity: f64,
        confidence: f64,
    ) -> Result<DocumentRelation> {
        Ok(DocumentRelation {
            id: next_id(),
            source_document_id: source_id,
            target_document_id: target_id,
            relation_type: DocumentRelationType::Similar,
            strength: similarity,
            confidence,
            description: Some(format!("Content similarity: {:.2}", similarity)),
            metadata: RelationMetadata {
                extraction_method: "content_similarity".to_string(),
                evidence_snippets: Vec::new(),
                semantic_similarity: Some(similarity),
                manual_verified: false,
            },
            created_by: None,
            created_at: current_millis(),
        })
    }

    /// 检查内容中是否包含引用
    fn contains_reference(&self, content: &str, title: &str) -> bool {
        let content_lower = content.to_lowercase();
        let title_lower = title.to_lowercase();

        // 简单的引用检测
        content_lower.contains(&title_lower)
            || content_lower.contains(&format!("\"{title_lower}\""))
            || content_lower.contains(&format!("《{title_lower}》"))
    }

    /// 提取引用片段
    fn extract_reference_snippets(&self, content: &str, title: &str) -> Vec<String> {
        let mut snippets = Vec::new();
        let title_lower = title.to_lowercase();

        // 按行分割内容
        for line in content.lines() {
            if line.to_lowercase().contains(&title_lower) {
                snippets.push(line.trim().to_string());
                if snippets.len() >= 3 {
                    // 最多提取3个片段
                    break;
                }
            }
        }

        snippets
    }

    /// 计算图密度
    fn calculate_graph_density(&self, graph: &DocumentRelationGraph) -> f64 {
        if graph.total_documents <= 1 {
            return 0.0;
        }

        let max_possible_edges = graph.total_documents * (graph.total_documents - 1);
        if max_possible_edges == 0 {
            0.0
        } else {
            graph.total_relations as f64 / max_possible_edges as f64
        }
    }
}

/// 文档关系图
#[derive(Debug, Clone)]
pub struct DocumentRelationGraph {
    pub knowledge_base_id: String,
    pub nodes: Vec<RelationGraphNode>,
    pub edges: Vec<RelationGraphEdge>,
    pub total_documents: usize,
    pub total_relations: usize,
    pub created_at: chrono::DateTime<Utc>,
}

/// 关系图节点
#[derive(Debug, Clone)]
pub struct RelationGraphNode {
    pub id: String,
    pub title: String,
    pub document_type: String,
    pub created_at: i64,
    pub inbound_count: usize,
    pub outbound_count: usize,
}

/// 关系图边
#[derive(Debug, Clone)]
pub struct RelationGraphEdge {
    pub id: String,
    pub source_id: String,
    pub target_id: String,
    pub relation_type: DocumentRelationType,
    pub strength: f64,
    pub confidence: f64,
}

/// 关系统计信息
#[derive(Debug, Clone)]
pub struct RelationStatistics {
    pub total_documents: usize,
    pub total_relations: usize,
    pub relation_type_counts: HashMap<String, usize>,
    pub most_connected_document: Option<RelationGraphNode>,
    pub average_connections: f64,
    pub graph_density: f64,
}

/// 关系发现配置
#[derive(Debug, Clone)]
pub struct RelationDiscoveryConfig {
    pub similarity_threshold: f64,
    pub max_similar_relations: usize,
    pub enable_reference_detection: bool,
    pub enable_content_similarity: bool,
    pub enable_keyword_matching: bool,
    pub reference_confidence: f64,
}

impl Default for RelationDiscoveryConfig {
    fn default() -> Self {
        Self {
            similarity_threshold: 0.3,
            max_similar_relations: 10,
            enable_reference_detection: true,
            enable_content_similarity: true,
            enable_keyword_matching: true,
            reference_confidence: 0.7,
        }
    }
}

impl DocumentRelationService {
    /// 使用配置进行关系发现
    pub async fn discover_relations_with_config(
        &self,
        document_id: &str,
        config: &RelationDiscoveryConfig,
    ) -> Result<Vec<DocumentRelation>> {
        let mut all_relations = Vec::new();

        // 基于相似性的关系发现
        if config.enable_content_similarity {
            let similar_relations = self
                .discover_similar_relations(
                    document_id,
                    config.similarity_threshold,
                    config.max_similar_relations,
                )
                .await?;
            all_relations.extend(similar_relations);
        }

        // 基于引用的关系发现
        if config.enable_reference_detection {
            let reference_relations = self.discover_reference_relations(document_id).await?;
            all_relations.extend(reference_relations);
        }

        // 去重（基于源文档和目标文档的组合）
        let mut seen = std::collections::HashSet::new();
        all_relations.retain(|relation| {
            let key = (
                relation.source_document_id.clone(),
                relation.target_document_id.clone(),
            );
            seen.insert(key)
        });

        Ok(all_relations)
    }

    /// 批量发现关系
    pub async fn batch_discover_relations(
        &self,
        document_ids: Vec<String>,
        config: &RelationDiscoveryConfig,
    ) -> Result<Vec<DocumentRelation>> {
        let mut all_relations = Vec::new();

        for document_id in document_ids {
            match self
                .discover_relations_with_config(&document_id, config)
                .await
            {
                Ok(mut relations) => all_relations.append(&mut relations),
                Err(e) => {
                    tracing::warn!(
                        "Failed to discover relations for document {}: {}",
                        document_id,
                        e
                    );
                    continue;
                }
            }
        }

        Ok(all_relations)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_contains_reference() {
        let document_repo = Arc::new(MockDocumentRepository);
        let relation_repo = Arc::new(MockDocumentRelationRepository);
        let service = DocumentRelationService::new(document_repo, relation_repo);

        assert!(service.contains_reference("这个文档引用了《用户手册》中的内容", "用户手册"));
        assert!(service.contains_reference("参考\"技术文档\"了解详情", "技术文档"));
        assert!(!service.contains_reference("这是一个普通文档", "用户手册"));
    }

    #[test]
    fn test_extract_reference_snippets() {
        let document_repo = Arc::new(MockDocumentRepository);
        let relation_repo = Arc::new(MockDocumentRelationRepository);
        let service = DocumentRelationService::new(document_repo, relation_repo);

        let content = "第一行\n这里引用了用户手册的内容\n第三行\n另一处提到了用户手册";
        let snippets = service.extract_reference_snippets(content, "用户手册");

        assert_eq!(snippets.len(), 2);
        assert!(snippets[0].contains("引用了用户手册"));
        assert!(snippets[1].contains("提到了用户手册"));
    }

    #[test]
    fn test_calculate_graph_density() {
        let document_repo = Arc::new(MockDocumentRepository);
        let relation_repo = Arc::new(MockDocumentRelationRepository);
        let service = DocumentRelationService::new(document_repo, relation_repo);

        let graph = DocumentRelationGraph {
            knowledge_base_id: next_id(),
            nodes: vec![],
            edges: vec![],
            total_documents: 4,
            total_relations: 6,
            created_at: Utc::now(),
        };

        let density = service.calculate_graph_density(&graph);
        let expected = 6.0 / (4.0 * 3.0); // 6 edges out of 12 possible
        assert!((density - expected).abs() < 0.001);
    }

    // Mock implementation for testing
    struct MockDocumentRepository;
    struct MockDocumentRelationRepository;

    #[async_trait::async_trait]
    impl DocumentRepository for MockDocumentRepository {
        async fn create(&self, _document: &Document) -> Result<Document> {
            unimplemented!()
        }
        async fn find_by_id(&self, _id: &str) -> Result<Option<Document>> {
            unimplemented!()
        }
        async fn find_by_knowledge_base(
            &self,
            _kb_id: &str,
            _limit: Option<u32>,
            _offset: Option<u32>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn update(&self, _document: &Document) -> Result<Document> {
            unimplemented!()
        }
        async fn delete(&self, _id: &str) -> Result<bool> {
            unimplemented!()
        }
        async fn search(&self, _query: &str, _kb_id: Option<&str>, _limit: Option<u32>, _offset: Option<u32>) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_status(
            &self,
            _status: wisdom_vault_database::models::DocumentStatus,
            _limit: Option<u32>,
            _offset: Option<u32>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_author(
            &self,
            _author: &str,
            _kb_id: Option<&str>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_by_file_type(
            &self,
            _file_type: &str,
            _kb_id: Option<&str>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn list(
            &self,
            _limit: Option<u32>,
            _offset: Option<u32>,
        ) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn count(&self) -> Result<i64> {
            unimplemented!()
        }
        async fn count_by_status(&self, _status: &wisdom_vault_database::models::DocumentStatus) -> Result<i64> {
            unimplemented!()
        }
        async fn update_status(
            &self,
            _id: &str,
            _status: wisdom_vault_database::models::DocumentStatus,
        ) -> Result<bool> {
            unimplemented!()
        }
        async fn update_processing_metadata(
            &self,
            _id: &str,
            _metadata: &wisdom_vault_database::models::DocumentProcessingMetadata,
        ) -> Result<bool> {
            unimplemented!()
        }
        async fn find_by_checksum(&self, _checksum: &str) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn batch_update_status(
            &self,
            _ids: Vec<String>,
            _status: wisdom_vault_database::models::DocumentStatus,
        ) -> Result<u64> {
            unimplemented!()
        }
        async fn find_failed_documents(&self, _kb_id: Option<&str>) -> Result<Vec<Document>> {
            unimplemented!()
        }
        async fn find_processing_documents(&self, _kb_id: Option<&str>) -> Result<Vec<Document>> {
            unimplemented!()
        }
    }

    #[async_trait::async_trait]
    impl DocumentRelationRepository for MockDocumentRelationRepository {
        async fn create(&self, _relation: &DocumentRelation) -> Result<DocumentRelation> {
            unimplemented!()
        }
        async fn find_by_id(&self, _id: &str) -> Result<Option<DocumentRelation>> {
            unimplemented!()
        }
        async fn delete(&self, _id: &str) -> Result<bool> {
            unimplemented!()
        }
        async fn find_outbound_relations(&self, _document_id: &str) -> Result<Vec<DocumentRelation>> {
            unimplemented!()
        }
        async fn find_inbound_relations(&self, _document_id: &str) -> Result<Vec<DocumentRelation>> {
            unimplemented!()
        }
        async fn find_by_type(
            &self,
            _relation_type: &DocumentRelationType,
            _limit: Option<u32>,
        ) -> Result<Vec<DocumentRelation>> {
            unimplemented!()
        }
        async fn find_by_strength_range(
            &self,
            _min_strength: f64,
            _max_strength: f64,
            _limit: Option<u32>,
        ) -> Result<Vec<DocumentRelation>> {
            unimplemented!()
        }
        async fn find_by_documents(
            &self,
            _source_id: &str,
            _target_id: &str,
        ) -> Result<Option<DocumentRelation>> {
            unimplemented!()
        }
        async fn count_by_document(&self, _document_id: &str) -> Result<i64> {
            unimplemented!()
        }
        async fn delete_by_document(&self, _document_id: &str) -> Result<u64> {
            unimplemented!()
        }
        async fn batch_create(&self, _relations: Vec<DocumentRelation>) -> Result<Vec<DocumentRelation>> {
            unimplemented!()
        }
    }
}
