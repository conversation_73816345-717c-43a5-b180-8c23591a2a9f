use anyhow::{Result, anyhow};
use wisdom_vault_common::{db::next_id, time::current_millis};
use wisdom_vault_database::{
    models::{Role, User, UserRole},
    repositories::{
        MongoRoleRepository, MongoUserRepository, MongoUserRoleRepository, RoleRepository,
        UserRepository, UserRoleRepository,
    },
};

use crate::password;

pub struct UserManagementService {
    user_repo: MongoUserRepository,
    role_repo: MongoRoleRepository,
    user_role_repo: MongoUserRoleRepository,
}

impl UserManagementService {
    pub fn new(
        user_repo: MongoUserRepository,
        role_repo: MongoRoleRepository,
        user_role_repo: MongoUserRoleRepository,
    ) -> Self {
        Self {
            user_repo,
            role_repo,
            user_role_repo,
        }
    }

    pub async fn create_user(
        &self,
        username: String,
        email: String,
        password: Option<String>,
        full_name: Option<String>,
        phone: Option<String>,
        organization_id: Option<String>,
        is_active: Option<bool>,
        created_by: String,
    ) -> Result<(User, Option<String>)> {
        // 检查用户名和邮箱是否已存在
        if let Some(_) = self.user_repo.find_by_username(&username).await? {
            return Err(anyhow!("Username already exists"));
        }

        if let Some(_) = self.user_repo.find_by_email(&email).await? {
            return Err(anyhow!("Email already exists"));
        }

        let id = next_id();

        // 处理密码
        let (password_hash, generated_password) = if let Some(password) = password {
            (password::hash_password(&id, &password)?, None)
        } else {
            let generated = password::generate_random_password(12);
            let hash = password::hash_password(&id, &generated)?;
            (hash, Some(generated))
        };

        // 创建用户
        let user = User {
            id,
            username,
            email,
            password_hash,
            full_name,
            avatar_id: None,
            phone,
            organization_id,
            is_active: is_active.unwrap_or(true),
            last_login_at: None,
            created_at: current_millis(),
            updated_at: current_millis(),
        };

        let created_user = self.user_repo.create(&user).await?;

        // 分配默认角色 (user)
        if let Some(default_role) = self.role_repo.find_by_name("user").await? {
            let _ = self
                .user_role_repo
                .assign_role(&created_user.id, &default_role.id, &created_by)
                .await;
        }

        Ok((created_user, generated_password))
    }

    pub async fn get_user_by_id(&self, id: &str) -> Result<Option<User>> {
        self.user_repo.find_by_id(id).await
    }

    pub async fn list_users(
        &self,
        query: Option<String>,
        organization_id: Option<&str>,
        is_active: Option<bool>,
        page: Option<u32>,
        limit: Option<u32>,
    ) -> Result<(Vec<User>, u64)> {
        let offset = page.map(|p| (p.saturating_sub(1)) * limit.unwrap_or(20));

        self.user_repo
            .search(query.as_deref(), organization_id, is_active, limit, offset)
            .await
    }

    pub async fn update_user(
        &self,
        id: &str,
        username: Option<String>,
        email: Option<String>,
        full_name: Option<String>,
        phone: Option<String>,
        organization_id: Option<String>,
        avatar_id: Option<String>,
    ) -> Result<User> {
        let mut user = self
            .user_repo
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("User not found"))?;

        // 检查用户名和邮箱唯一性
        if let Some(new_username) = &username {
            if new_username != &user.username {
                if let Some(_) = self.user_repo.find_by_username(new_username).await? {
                    return Err(anyhow!("Username already exists"));
                }
            }
        }

        if let Some(new_email) = &email {
            if new_email != &user.email {
                if let Some(_) = self.user_repo.find_by_email(new_email).await? {
                    return Err(anyhow!("Email already exists"));
                }
            }
        }

        // 更新字段
        if let Some(username) = username {
            user.username = username;
        }
        if let Some(email) = email {
            user.email = email;
        }
        if let Some(full_name) = full_name {
            user.full_name = Some(full_name);
        }
        if let Some(phone) = phone {
            user.phone = Some(phone);
        }
        if let Some(organization_id) = organization_id {
            user.organization_id = Some(organization_id);
        }
        if let Some(avatar_id) = avatar_id {
            user.avatar_id = Some(avatar_id);
        }

        user.updated_at = current_millis();
        self.user_repo.update(&user).await
    }

    pub async fn set_user_status(&self, id: &str, is_active: bool) -> Result<bool> {
        self.user_repo.set_active_status(id, is_active).await
    }

    pub async fn delete_user(&self, id: &str) -> Result<bool> {
        // 删除用户前先删除角色关联
        let user_roles = self.user_role_repo.find_by_user(id).await?;
        for user_role in user_roles {
            let _ = self
                .user_role_repo
                .remove_role(id, &user_role.role_id)
                .await;
        }

        self.user_repo.delete(id).await
    }

    pub async fn assign_role_to_user(
        &self,
        user_id: &str,
        role_id: &str,
        assigned_by: &str,
    ) -> Result<UserRole> {
        // 检查用户和角色是否存在
        if self.user_repo.find_by_id(user_id).await?.is_none() {
            return Err(anyhow!("User not found"));
        }

        if self.role_repo.find_by_id(role_id).await?.is_none() {
            return Err(anyhow!("Role not found"));
        }

        self.user_role_repo
            .assign_role(user_id, role_id, assigned_by)
            .await
    }

    pub async fn remove_role_from_user(&self, user_id: &str, role_id: &str) -> Result<bool> {
        self.user_role_repo.remove_role(user_id, role_id).await
    }

    pub async fn get_user_roles(&self, user_id: &str) -> Result<Vec<Role>> {
        let user_roles = self.user_role_repo.find_by_user(user_id).await?;
        let mut roles = Vec::new();

        for user_role in user_roles {
            if let Some(role) = self.role_repo.find_by_id(&user_role.role_id).await? {
                roles.push(role);
            }
        }

        Ok(roles)
    }

    pub async fn batch_update_user_status(
        &self,
        user_ids: Vec<String>,
        is_active: bool,
    ) -> Result<(i32, i32, Vec<String>)> {
        let mut success_count = 0;
        let mut failed_count = 0;
        let mut failed_users = Vec::new();

        for user_id in user_ids {
            match self.user_repo.set_active_status(&user_id, is_active).await {
                Ok(true) => success_count += 1,
                Ok(false) | Err(_) => {
                    failed_count += 1;
                    failed_users.push(user_id.to_string());
                }
            }
        }

        Ok((success_count, failed_count, failed_users))
    }

    pub async fn batch_delete_users(
        &self,
        user_ids: Vec<String>,
    ) -> Result<(i32, i32, Vec<String>)> {
        let mut success_count = 0;
        let mut failed_count = 0;
        let mut failed_users = Vec::new();

        for user_id in user_ids {
            match self.delete_user(&user_id).await {
                Ok(true) => success_count += 1,
                Ok(false) | Err(_) => {
                    failed_count += 1;
                    failed_users.push(user_id.to_string());
                }
            }
        }

        Ok((success_count, failed_count, failed_users))
    }
}
