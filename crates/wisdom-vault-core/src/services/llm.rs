use anyhow::{anyhow, Result};
use async_trait::async_trait;
use openai_api_rs::v1::{
    api::OpenAIClient,
    chat_completion::{self, ChatCompletionRequest},
};
use serde::{Deserialize, Serialize};
use std::{sync::Arc, time::Duration};
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// LLM 服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    pub api_key: Option<String>,
    pub base_url: Option<String>,
    pub default_model: String,
    pub max_tokens: u32,
    pub temperature: f32,
    pub timeout_seconds: u64,
    pub max_retries: u32,
}

impl LLMConfig {
    pub fn from_app_config_ai(
        api_key: Option<String>,
        base_url: Option<String>,
        default_model: &str,
        max_tokens: u32,
        temperature: f32,
        timeout_seconds: u64,
        max_retries: u32,
    ) -> Self {
        Self {
            api_key,
            base_url,
            default_model: default_model.to_string(),
            max_tokens,
            temperature,
            timeout_seconds,
            max_retries,
        }
    }
}

impl Default for LLMConfig {
    fn default() -> Self {
        Self {
            api_key: None,
            base_url: Some("https://api.openai.com/v1".to_string()),
            default_model: "gpt-4o-mini".to_string(),
            max_tokens: 4000,
            temperature: 0.7,
            timeout_seconds: 30,
            max_retries: 3,
        }
    }
}

/// 聊天消息角色
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageRole {
    System,
    User,
    Assistant,
}

impl From<MessageRole> for chat_completion::MessageRole {
    fn from(role: MessageRole) -> Self {
        match role {
            MessageRole::System => chat_completion::MessageRole::system,
            MessageRole::User => chat_completion::MessageRole::user,
            MessageRole::Assistant => chat_completion::MessageRole::assistant,
        }
    }
}

/// 聊天消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub role: MessageRole,
    pub content: String,
}

impl From<ChatMessage> for chat_completion::ChatCompletionMessage {
    fn from(msg: ChatMessage) -> Self {
        chat_completion::ChatCompletionMessage {
            role: msg.role.into(),
            content: chat_completion::Content::Text(msg.content),
            name: None,
            tool_calls: None,
            tool_call_id: None,
        }
    }
}

/// LLM 响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMResponse {
    pub content: String,
    pub model: String,
    pub usage: Option<TokenUsage>,
    pub finish_reason: Option<String>,
}

/// Token 使用情况
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// LLM 服务trait
#[async_trait]
pub trait LLMService: Send + Sync {
    /// 发送聊天请求
    async fn chat_completion(
        &self,
        messages: Vec<ChatMessage>,
        model: Option<String>,
        max_tokens: Option<u32>,
        temperature: Option<f32>,
    ) -> Result<LLMResponse>;

    /// 流式聊天完成 (暂时返回完整响应，后续可扩展为流式)
    async fn stream_chat_completion(
        &self,
        messages: Vec<ChatMessage>,
        model: Option<String>,
        max_tokens: Option<u32>,
        temperature: Option<f32>,
    ) -> Result<LLMResponse>;

    /// 获取支持的模型列表
    fn supported_models(&self) -> Vec<String>;

    /// 健康检查
    async fn health_check(&self) -> Result<bool>;
}

/// OpenAI LLM 服务实现
pub struct OpenAILLMService {
    client: Arc<RwLock<OpenAIClient>>,
    config: LLMConfig,
}

impl OpenAILLMService {
    /// 创建新的 OpenAI LLM 服务
    pub fn new(config: LLMConfig) -> Result<Self> {
        let api_key = config
            .api_key
            .clone()
            .ok_or_else(|| anyhow::anyhow!("OpenAI API key is required"))?;

        let mut client_builder = OpenAIClient::builder().with_api_key(api_key);

        if let Some(base_url) = &config.base_url {
            client_builder = client_builder.with_endpoint(base_url);
        }

        let client = client_builder
            .build()
            .map_err(|e| anyhow!("Failed to build OpenAI client: {}", e))?;

        Ok(Self {
            client: Arc::new(RwLock::new(client)),
            config,
        })
    }

    /// 内部方法：执行带重试的请求
    async fn execute_with_retry<F, T>(&self, operation: F) -> Result<T>
    where
        F: Fn() -> futures::future::BoxFuture<'static, Result<T>> + Send + Sync,
        T: Send + 'static,
    {
        let mut last_error = None;

        for attempt in 1..=self.config.max_retries {
            match tokio::time::timeout(
                Duration::from_secs(self.config.timeout_seconds),
                operation(),
            )
            .await
            {
                Ok(Ok(result)) => return Ok(result),
                Ok(Err(e)) => {
                    last_error = Some(e);
                    if attempt < self.config.max_retries {
                        let delay = Duration::from_millis(1000 * attempt as u64);
                        warn!(
                            "LLM request failed (attempt {}), retrying after {:?}: {}",
                            attempt,
                            delay,
                            last_error.as_ref().unwrap()
                        );
                        tokio::time::sleep(delay).await;
                    }
                }
                Err(_) => {
                    last_error = Some(anyhow::anyhow!("Request timeout"));
                    if attempt < self.config.max_retries {
                        warn!("LLM request timeout (attempt {}), retrying", attempt);
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| anyhow::anyhow!("All retry attempts failed")))
    }
}

#[async_trait]
impl LLMService for OpenAILLMService {
    async fn chat_completion(
        &self,
        messages: Vec<ChatMessage>,
        model: Option<String>,
        max_tokens: Option<u32>,
        temperature: Option<f32>,
    ) -> Result<LLMResponse> {
        let model = model.unwrap_or_else(|| self.config.default_model.clone());
        let max_tokens = max_tokens.unwrap_or(self.config.max_tokens);
        let temperature = temperature.unwrap_or(self.config.temperature);

        let openai_messages: Vec<chat_completion::ChatCompletionMessage> =
            messages.into_iter().map(|msg| msg.into()).collect();

        let mut request = ChatCompletionRequest::new(model.clone(), openai_messages);
        request.max_tokens = Some(max_tokens.into());
        request.temperature = Some(temperature.into());

        let client = self.client.clone();
        let response = self
            .execute_with_retry(|| {
                let client = client.clone();
                let request = request.clone();
                Box::pin(async move {
                    let mut client = client.write().await;
                    client
                        .chat_completion(request)
                        .await
                        .map_err(|e| anyhow::anyhow!(e))
                })
            })
            .await?;

        let content = response
            .choices
            .first()
            .and_then(|choice| choice.message.content.as_ref())
            .ok_or_else(|| anyhow::anyhow!("No content in response"))?
            .clone();

        let finish_reason = response
            .choices
            .first()
            .and_then(|choice| choice.finish_reason.as_ref().map(|r| format!("{r:?}")));

        Ok(LLMResponse {
            content,
            model,
            usage: Some(TokenUsage {
                prompt_tokens: response.usage.prompt_tokens as u32,
                completion_tokens: response.usage.completion_tokens as u32,
                total_tokens: response.usage.total_tokens as u32,
            }),
            finish_reason,
        })
    }

    async fn stream_chat_completion(
        &self,
        messages: Vec<ChatMessage>,
        model: Option<String>,
        max_tokens: Option<u32>,
        temperature: Option<f32>,
    ) -> Result<LLMResponse> {
        // 当前版本先使用普通完成，后续可扩展为真正的流式
        self.chat_completion(messages, model, max_tokens, temperature)
            .await
    }

    fn supported_models(&self) -> Vec<String> {
        vec![
            "gpt-4o".to_string(),
            "gpt-4o-mini".to_string(),
            "gpt-4-turbo".to_string(),
            "gpt-3.5-turbo".to_string(),
        ]
    }

    async fn health_check(&self) -> Result<bool> {
        let test_messages = vec![ChatMessage {
            role: MessageRole::User,
            content: "Hello".to_string(),
        }];

        match self
            .chat_completion(
                test_messages,
                Some("gpt-4o-mini".to_string()),
                Some(10),
                Some(0.1),
            )
            .await
        {
            Ok(_) => {
                info!("OpenAI LLM service health check passed");
                Ok(true)
            }
            Err(e) => {
                error!("OpenAI LLM service health check failed: {}", e);
                Ok(false)
            }
        }
    }
}

/// LLM 服务管理器
pub struct LLMServiceManager {
    primary_service: Arc<dyn LLMService>,
    fallback_services: Vec<Arc<dyn LLMService>>,
}

impl LLMServiceManager {
    /// 创建新的 LLM 服务管理器
    pub fn new(primary_service: Arc<dyn LLMService>) -> Self {
        Self {
            primary_service,
            fallback_services: Vec::new(),
        }
    }

    /// 添加备用服务
    pub fn add_fallback_service(&mut self, service: Arc<dyn LLMService>) {
        self.fallback_services.push(service);
    }

    /// 获取可用的服务 (带故障转移)
    async fn get_available_service(&self) -> Result<Arc<dyn LLMService>> {
        // 首先尝试主服务
        if self.primary_service.health_check().await.unwrap_or(false) {
            return Ok(self.primary_service.clone());
        }

        // 尝试备用服务
        for service in &self.fallback_services {
            if service.health_check().await.unwrap_or(false) {
                warn!("Primary LLM service unavailable, using fallback service");
                return Ok(service.clone());
            }
        }

        Err(anyhow::anyhow!("No available LLM services"))
    }
}

#[async_trait]
impl LLMService for LLMServiceManager {
    async fn chat_completion(
        &self,
        messages: Vec<ChatMessage>,
        model: Option<String>,
        max_tokens: Option<u32>,
        temperature: Option<f32>,
    ) -> Result<LLMResponse> {
        let service = self.get_available_service().await?;
        service
            .chat_completion(messages, model, max_tokens, temperature)
            .await
    }

    async fn stream_chat_completion(
        &self,
        messages: Vec<ChatMessage>,
        model: Option<String>,
        max_tokens: Option<u32>,
        temperature: Option<f32>,
    ) -> Result<LLMResponse> {
        let service = self.get_available_service().await?;
        service
            .stream_chat_completion(messages, model, max_tokens, temperature)
            .await
    }

    fn supported_models(&self) -> Vec<String> {
        self.primary_service.supported_models()
    }

    async fn health_check(&self) -> Result<bool> {
        self.get_available_service().await.map(|_| true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_llm_config_default() {
        let config = LLMConfig::default();
        assert_eq!(config.default_model, "gpt-4o-mini");
        assert_eq!(config.max_tokens, 4000);
        assert_eq!(config.temperature, 0.7);
    }

    #[tokio::test]
    async fn test_chat_message_conversion() {
        let message = ChatMessage {
            role: MessageRole::User,
            content: "Hello".to_string(),
        };

        let openai_message: chat_completion::ChatCompletionMessage = message.into();
        assert!(matches!(
            openai_message.role,
            chat_completion::MessageRole::user
        ));
    }

    // 集成测试需要真实的 API key，在 CI 中可能会跳过
    #[tokio::test]
    async fn test_openai_service_creation() {
        let config = LLMConfig {
            api_key: Some("test-key".to_string()),
            ..Default::default()
        };

        let result = OpenAILLMService::new(config);
        assert!(result.is_ok());
    }
}
