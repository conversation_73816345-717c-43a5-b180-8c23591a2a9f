use anyhow::Result;
use chrono::Duration;
use std::sync::Arc;
use wisdom_vault_common::{db::next_id, time::current_millis};
use wisdom_vault_database::{
    models::{ProcessingTask, TaskContext, TaskPriority, TaskStatus, TaskType},
    repositories::{ProcessingTaskRepository, TaskStatistics},
};

/// 处理任务服务
/// 负责任务的创建、调度、状态管理和监控
pub struct ProcessingTaskService {
    repository: Arc<dyn ProcessingTaskRepository + Send + Sync>,
}

impl ProcessingTaskService {
    pub fn new(repository: Arc<dyn ProcessingTaskRepository + Send + Sync>) -> Self {
        Self { repository }
    }

    /// 创建新的处理任务
    pub async fn create_task(
        &self,
        task_type: TaskType,
        resource_id: String,
        priority: TaskPriority,
        context: TaskContext,
    ) -> Result<ProcessingTask> {
        let max_retries = self.get_max_retries_for_task_type(&task_type);

        let task = ProcessingTask {
            id: next_id(),
            task_type,
            status: TaskStatus::Queued,
            resource_id,
            priority,
            progress: 0.0,
            retry_count: 0,
            max_retries,
            error_message: None,
            error_details: None,
            context: Some(context),
            created_at: current_millis(),
            started_at: current_millis(),
            completed_at: None,
            next_retry_at: None,
        };

        self.repository.create(&task).await
    }

    /// 获取下一个待处理任务
    pub async fn get_next_task(&self) -> Result<Option<ProcessingTask>> {
        let tasks = self.repository.find_pending_tasks(Some(1)).await?;
        Ok(tasks.into_iter().next())
    }

    /// 开始处理任务
    pub async fn start_task(&self, task_id: &str) -> Result<bool> {
        if let Some(mut task) = self.repository.find_by_id(task_id).await? {
            if task.status == TaskStatus::Queued {
                task.status = TaskStatus::Running;
                task.started_at = current_millis();
                self.repository.update(&task).await?;
                return Ok(true);
            }
        }
        Ok(false)
    }

    /// 更新任务进度
    pub async fn update_progress(&self, task_id: &str, progress: f64) -> Result<bool> {
        let progress = progress.clamp(0.0, 1.0);
        self.repository.update_progress(task_id, progress).await
    }

    /// 完成任务
    pub async fn complete_task(&self, task_id: &str) -> Result<bool> {
        if let Some(mut task) = self.repository.find_by_id(task_id).await? {
            task.status = TaskStatus::Completed;
            task.progress = 1.0;
            task.completed_at = Some(current_millis());
            self.repository.update(&task).await?;
            return Ok(true);
        }
        Ok(false)
    }

    /// 标记任务失败
    pub async fn fail_task(
        &self,
        task_id: &str,
        error_message: &str,
        error_details: Option<serde_json::Value>,
    ) -> Result<bool> {
        if let Some(mut task) = self.repository.find_by_id(task_id).await? {
            task.status = TaskStatus::Failed;
            task.error_message = Some(error_message.to_string());
            task.error_details = error_details.map(|v| v.to_string());
            task.completed_at = Some(current_millis());

            // 如果可以重试，设置下次重试时间
            if task.retry_count < task.max_retries {
                let retry_delay = self.calculate_retry_delay(task.retry_count);
                task.next_retry_at = Some(current_millis() + retry_delay.num_milliseconds());
            }

            self.repository.update(&task).await?;
            return Ok(true);
        }
        Ok(false)
    }

    /// 重试失败的任务
    pub async fn retry_task(&self, task_id: &str) -> Result<bool> {
        if let Some(mut task) = self.repository.find_by_id(task_id).await? {
            if task.status == TaskStatus::Failed && task.retry_count < task.max_retries {
                task.status = TaskStatus::Queued;
                task.retry_count += 1;
                task.progress = 0.0;
                task.error_message = None;
                task.error_details = None;
                task.started_at = current_millis(); // 重新设置开始时间
                task.completed_at = None;
                task.next_retry_at = None;

                self.repository.update(&task).await?;
                return Ok(true);
            }
        }
        Ok(false)
    }

    /// 取消任务
    pub async fn cancel_task(&self, task_id: &str) -> Result<bool> {
        if let Some(mut task) = self.repository.find_by_id(task_id).await? {
            if matches!(task.status, TaskStatus::Queued | TaskStatus::Running) {
                task.status = TaskStatus::Cancelled;
                task.completed_at = Some(current_millis());
                self.repository.update(&task).await?;
                return Ok(true);
            }
        }
        Ok(false)
    }

    /// 获取任务详情
    pub async fn get_task(&self, task_id: &str) -> Result<Option<ProcessingTask>> {
        self.repository.find_by_id(task_id).await
    }

    /// 根据资源ID获取任务列表
    pub async fn get_tasks_by_resource(&self, resource_id: &str) -> Result<Vec<ProcessingTask>> {
        self.repository.find_by_resource(resource_id).await
    }

    /// 获取指定状态的任务
    pub async fn get_tasks_by_status(&self, status: TaskStatus) -> Result<Vec<ProcessingTask>> {
        self.repository.list_by_status(&status, None, None).await
    }

    /// 获取排队中的任务
    pub async fn get_queued_tasks(&self, limit: Option<u32>) -> Result<Vec<ProcessingTask>> {
        self.repository
            .find_pending_tasks(Some(limit.unwrap_or(100)))
            .await
    }

    /// 获取可重试的任务
    pub async fn get_retry_ready_tasks(&self) -> Result<Vec<ProcessingTask>> {
        self.repository.find_retry_tasks().await
    }

    /// 批量处理重试任务
    pub async fn process_retry_tasks(&self) -> Result<u64> {
        let retry_tasks = self.get_retry_ready_tasks().await?;
        let mut processed_count = 0;

        for task in retry_tasks {
            if self.retry_task(&task.id).await? {
                processed_count += 1;
            }
        }

        Ok(processed_count)
    }

    /// 获取任务统计信息
    pub async fn get_statistics(&self) -> Result<TaskStatistics> {
        self.repository.get_statistics().await
    }

    /// 清理已完成的旧任务
    pub async fn cleanup_old_tasks(&self, days_old: i64) -> Result<u64> {
        let cutoff_time = current_millis() - Duration::days(days_old).num_milliseconds();
        self.repository.cleanup_completed_tasks(cutoff_time).await
    }

    /// 获取失败的任务
    pub async fn get_failed_tasks(&self, limit: Option<u32>) -> Result<Vec<ProcessingTask>> {
        // 使用list_by_status获取失败的任务
        self.repository
            .list_by_status(&TaskStatus::Failed, limit, None)
            .await
    }

    /// 根据任务类型获取最大重试次数
    fn get_max_retries_for_task_type(&self, task_type: &TaskType) -> u32 {
        match task_type {
            TaskType::DocumentParsing => 3,
            TaskType::MetadataExtraction => 2,
            TaskType::DocumentIndexing => 3,
            TaskType::VectorGeneration => 2,
            TaskType::KnowledgeGraphExtraction => 1,
            TaskType::DocumentClassification => 2,
        }
    }

    /// 计算重试延迟（指数退避）
    fn calculate_retry_delay(&self, retry_count: u32) -> Duration {
        let base_delay_minutes = 2_i64;
        let delay_minutes = base_delay_minutes * (2_i64.pow(retry_count.min(5)));
        Duration::minutes(delay_minutes.min(60)) // 最大延迟1小时
    }

    /// 检查任务是否可以重试
    pub async fn can_retry_task(&self, task_id: &str) -> Result<bool> {
        if let Some(task) = self.repository.find_by_id(task_id).await? {
            return Ok(task.status == TaskStatus::Failed
                && task.retry_count < task.max_retries
                && (task.next_retry_at.is_none()
                    || task.next_retry_at.unwrap() <= current_millis()));
        }
        Ok(false)
    }

    /// 强制重新排队任务（忽略重试限制）
    pub async fn force_requeue_task(&self, task_id: &str) -> Result<bool> {
        if let Some(mut task) = self.repository.find_by_id(task_id).await? {
            if matches!(task.status, TaskStatus::Failed | TaskStatus::Cancelled) {
                task.status = TaskStatus::Queued;
                task.progress = 0.0;
                task.error_message = None;
                task.error_details = None;
                task.started_at = current_millis(); // 重新设置开始时间
                task.completed_at = None;
                task.next_retry_at = None;

                self.repository.update(&task).await?;
                return Ok(true);
            }
        }
        Ok(false)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use std::collections::HashMap;

    // Mock repository for testing
    struct MockProcessingTaskRepository {
        tasks: std::sync::Mutex<HashMap<String, ProcessingTask>>,
    }

    impl MockProcessingTaskRepository {
        fn new() -> Self {
            Self {
                tasks: std::sync::Mutex::new(HashMap::new()),
            }
        }
    }

    #[async_trait]
    impl ProcessingTaskRepository for MockProcessingTaskRepository {
        async fn create(&self, task: &ProcessingTask) -> Result<ProcessingTask> {
            let mut tasks = self.tasks.lock().unwrap();
            tasks.insert(task.id.clone(), task.clone());
            Ok(task.clone())
        }

        async fn find_by_id(&self, id: &str) -> Result<Option<ProcessingTask>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.get(id).cloned())
        }

        async fn update(&self, task: &ProcessingTask) -> Result<ProcessingTask> {
            let mut tasks = self.tasks.lock().unwrap();
            tasks.insert(task.id.clone(), task.clone());
            Ok(task.clone())
        }

        // 其他方法的简单实现...
        async fn delete(&self, _id: &str) -> Result<bool> {
            Ok(true)
        }
        async fn find_by_status(&self, _status: TaskStatus) -> Result<Vec<ProcessingTask>> {
            Ok(vec![])
        }
        async fn find_by_resource_id(&self, _resource_id: &str) -> Result<Vec<ProcessingTask>> {
            Ok(vec![])
        }
        async fn find_queued_tasks(&self, _limit: Option<u32>) -> Result<Vec<ProcessingTask>> {
            Ok(vec![])
        }
        async fn find_next_task(&self) -> Result<Option<ProcessingTask>> {
            Ok(None)
        }
        async fn update_status(&self, _id: &str, _status: TaskStatus) -> Result<bool> {
            Ok(true)
        }
        async fn update_progress(&self, _id: &str, _progress: f64) -> Result<bool> {
            Ok(true)
        }
        async fn update_error(
            &self,
            _id: &str,
            _error_message: String,
            _error_details: Option<serde_json::Value>,
        ) -> Result<bool> {
            Ok(true)
        }
        async fn increment_retry_count(&self, _id: &str) -> Result<bool> {
            Ok(true)
        }
        async fn set_next_retry_time(&self, _id: &str, _next_retry_at: u64) -> Result<bool> {
            Ok(true)
        }
        async fn batch_update_status(&self, _ids: Vec<String>, _status: TaskStatus) -> Result<u64> {
            Ok(0)
        }
        async fn cleanup_completed_tasks(&self, _older_than: u64) -> Result<u64> {
            Ok(0)
        }
        async fn count_by_status(&self, _status: TaskStatus) -> Result<i64> {
            Ok(0)
        }
        async fn get_task_statistics(&self) -> Result<TaskStatistics> {
            Ok(TaskStatistics {
                total_tasks: 0,
                queued_tasks: 0,
                running_tasks: 0,
                completed_tasks: 0,
                failed_tasks: 0,
                cancelled_tasks: 0,
                retrying_tasks: 0,
            })
        }
        async fn find_failed_tasks(
            &self,
            _max_retries_exceeded: bool,
        ) -> Result<Vec<ProcessingTask>> {
            Ok(vec![])
        }
        async fn find_retry_ready_tasks(&self) -> Result<Vec<ProcessingTask>> {
            Ok(vec![])
        }
    }

    #[tokio::test]
    async fn test_create_task() {
        let repo = Arc::new(MockProcessingTaskRepository::new());
        let service = ProcessingTaskService::new(repo);

        let context = TaskContext {
            file_path: Some("test.pdf".to_string()),
            original_filename: Some("test.pdf".to_string()),
            mime_type: Some("application/pdf".to_string()),
            file_size: Some(1024),
            knowledge_base_id: Some(next_id()),
            user_id: Some(next_id()),
            additional_params: serde_json::json!({}),
        };

        let task = service
            .create_task(
                TaskType::DocumentParsing,
                next_id(),
                TaskPriority::Normal,
                context,
            )
            .await
            .unwrap();

        assert_eq!(task.status, TaskStatus::Queued);
        assert_eq!(task.progress, 0.0);
        assert_eq!(task.retry_count, 0);
    }

    #[tokio::test]
    async fn test_retry_delay_calculation() {
        let repo = Arc::new(MockProcessingTaskRepository::new());
        let service = ProcessingTaskService::new(repo);

        // Test exponential backoff
        assert_eq!(service.calculate_retry_delay(0), Duration::minutes(2));
        assert_eq!(service.calculate_retry_delay(1), Duration::minutes(4));
        assert_eq!(service.calculate_retry_delay(2), Duration::minutes(8));
        assert_eq!(service.calculate_retry_delay(3), Duration::minutes(16));
    }
}
