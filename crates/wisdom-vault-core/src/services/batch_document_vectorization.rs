use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, VecDeque},
    sync::Arc,
};
use tokio::sync::{mpsc, RwLock, Semaphore};
use wisdom_vault_common::{db::next_id, time::current_millis};

use wisdom_vault_database::{
    models::TaskPriority,
    repositories::{DocumentChunkRepository, DocumentRepository},
};

use crate::services::{QualityReport, VectorizationService, VectorQualityAssessment};

/// 批量处理策略
#[derive(Debug, Clone)]
pub enum BatchProcessingStrategy {
    /// 并发处理 - 同时处理多个文档
    Concurrent { max_parallel: usize },
    /// 顺序处理 - 逐个处理文档
    Sequential,
    /// 优先级队列 - 根据优先级处理
    PriorityQueue,
    /// 负载均衡 - 根据文档大小分配
    LoadBalanced { max_tokens_per_batch: usize },
}

/// 批量向量化配置
#[derive(Debug, <PERSON><PERSON>)]
pub struct BatchVectorizationConfig {
    /// 处理策略
    pub strategy: BatchProcessingStrategy,
    /// 默认批次大小
    pub default_batch_size: usize,
    /// 最大重试次数
    pub max_retries: u32,
    /// 超时时间(秒)
    pub timeout_seconds: u64,
    /// 质量阈值
    pub quality_threshold: f64,
    /// 是否启用自动恢复
    pub enable_auto_recovery: bool,
    /// 进度报告间隔(处理的文档数)
    pub progress_report_interval: usize,
    /// 是否启用增量处理
    pub enable_incremental: bool,
    /// 是否强制重新计算
    pub force_recompute: bool,
}

impl Default for BatchVectorizationConfig {
    fn default() -> Self {
        Self {
            strategy: BatchProcessingStrategy::Concurrent { max_parallel: 4 },
            default_batch_size: 32,
            max_retries: 3,
            timeout_seconds: 300,
            quality_threshold: 0.8,
            enable_auto_recovery: true,
            progress_report_interval: 10,
            enable_incremental: true,
            force_recompute: false,
        }
    }
}

/// 批处理进度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchProgress {
    pub batch_id: String,
    pub total_documents: usize,
    pub processed_documents: usize,
    pub successful_documents: usize,
    pub failed_documents: usize,
    pub current_stage: BatchStage,
    pub started_at: i64,
    pub estimated_completion: Option<i64>,
    pub processing_rate: f64, // 文档/秒
    pub quality_report: Option<QualityReport>,
}

/// 批处理阶段
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BatchStage {
    Initializing,
    LoadingDocuments,
    Vectorizing,
    QualityAssessment,
    Finalizing,
    Completed,
    Failed(String),
    Cancelled,
}

/// 批处理结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchResult {
    pub batch_id: String,
    pub total_processed: usize,
    pub successful: usize,
    pub failed: usize,
    pub skipped: usize,
    pub processing_time_ms: i64,
    pub overall_quality_score: f64,
    pub failed_document_ids: Vec<String>,
    pub quality_report: QualityReport,
}

/// 批量文档向量化系统
pub struct BatchDocumentVectorization {
    /// 向量化服务
    vectorization_service: Arc<VectorizationService>,
    /// 质量评估服务
    quality_assessment_service: Arc<VectorQualityAssessment>,
    /// 文档仓库
    document_repository: Arc<dyn DocumentRepository + Send + Sync>,
    /// 分块仓库
    chunk_repository: Arc<dyn DocumentChunkRepository + Send + Sync>,
    /// 配置
    config: BatchVectorizationConfig,
    /// 活跃批次
    active_batches: Arc<RwLock<HashMap<String, BatchProgress>>>,
    /// 任务队列
    task_queue: Arc<RwLock<VecDeque<BatchTask>>>,
    /// 并发控制
    semaphore: Arc<Semaphore>,
    /// 进度通知通道
    progress_sender: Arc<RwLock<Option<mpsc::UnboundedSender<BatchProgress>>>>,
}

/// 批处理任务
#[derive(Debug, Clone)]
struct BatchTask {
    pub batch_id: String,
    pub document_ids: Vec<String>,
    pub model_id: String,
    pub priority: TaskPriority,
    pub force_recompute: bool,
}

impl BatchDocumentVectorization {
    /// 创建新的批量文档向量化系统
    pub fn new(
        vectorization_service: Arc<VectorizationService>,
        quality_assessment_service: Arc<VectorQualityAssessment>,
        document_repository: Arc<dyn DocumentRepository + Send + Sync>,
        chunk_repository: Arc<dyn DocumentChunkRepository + Send + Sync>,
        config: BatchVectorizationConfig,
    ) -> Self {
        let max_parallel = match config.strategy {
            BatchProcessingStrategy::Concurrent { max_parallel } => max_parallel,
            _ => 1,
        };

        Self {
            vectorization_service,
            quality_assessment_service,
            document_repository,
            chunk_repository,
            config,
            active_batches: Arc::new(RwLock::new(HashMap::new())),
            task_queue: Arc::new(RwLock::new(VecDeque::new())),
            semaphore: Arc::new(Semaphore::new(max_parallel)),
            progress_sender: Arc::new(RwLock::new(None)),
        }
    }

    /// 设置进度通知回调
    pub async fn set_progress_callback(&self, sender: mpsc::UnboundedSender<BatchProgress>) {
        *self.progress_sender.write().await = Some(sender);
    }

    /// 提交批量向量化任务
    pub async fn submit_batch_task(
        &self,
        document_ids: Vec<String>,
        model_id: String,
        priority: TaskPriority,
        force_recompute: bool,
    ) -> Result<String> {
        let batch_id = next_id();

        tracing::info!(
            "提交批量向量化任务: batch_id={}, 文档数量={}, model_id={}",
            batch_id,
            document_ids.len(),
            model_id
        );

        // 创建批处理任务
        let task = BatchTask {
            batch_id: batch_id.clone(),
            document_ids: document_ids.clone(),
            model_id,
            priority,
            force_recompute,
        };

        // 初始化进度信息
        let progress = BatchProgress {
            batch_id: batch_id.clone(),
            total_documents: document_ids.len(),
            processed_documents: 0,
            successful_documents: 0,
            failed_documents: 0,
            current_stage: BatchStage::Initializing,
            started_at: current_millis(),
            estimated_completion: None,
            processing_rate: 0.0,
            quality_report: None,
        };

        // 添加到活跃批次
        self.active_batches
            .write()
            .await
            .insert(batch_id.clone(), progress.clone());

        // 根据处理策略添加到队列
        match self.config.strategy {
            BatchProcessingStrategy::PriorityQueue => {
                self.add_task_with_priority(task).await;
            }
            _ => {
                self.task_queue.write().await.push_back(task);
            }
        }

        // 异步开始处理
        let processor = self.clone_for_async();
        tokio::spawn({
            let batch_id = batch_id.clone();
            async move {
                if let Err(e) = processor.process_batch_task(&batch_id).await {
                    tracing::error!("批处理任务失败: batch_id={}, error={}", batch_id, e);
                    processor.mark_batch_failed(&batch_id, &e.to_string()).await;
                }
            }
        });

        Ok(batch_id)
    }

    /// 获取批处理进度
    pub async fn get_batch_progress(&self, batch_id: &str) -> Option<BatchProgress> {
        self.active_batches.read().await.get(batch_id).cloned()
    }

    /// 取消批处理任务
    pub async fn cancel_batch(&self, batch_id: &str) -> Result<()> {
        let mut batches = self.active_batches.write().await;
        if let Some(progress) = batches.get_mut(batch_id) {
            progress.current_stage = BatchStage::Cancelled;
            tracing::info!("批处理任务已取消: {}", batch_id);
        }
        Ok(())
    }

    /// 处理批次任务
    async fn process_batch_task(&self, batch_id: &str) -> Result<BatchResult> {
        let _permit = self.semaphore.acquire().await?;

        tracing::info!("开始处理批次任务: {}", batch_id);
        let start_time = std::time::Instant::now();

        // 获取任务信息
        let task = self
            .get_task_from_queue(batch_id)
            .await
            .ok_or_else(|| anyhow::anyhow!("Task not found: {}", batch_id))?;

        // 更新进度: 加载文档
        self.update_progress(batch_id, |p| {
            p.current_stage = BatchStage::LoadingDocuments;
        })
        .await;

        // 根据策略处理文档
        let result = match self.config.strategy {
            BatchProcessingStrategy::Concurrent { max_parallel } => {
                self.process_concurrent(&task, max_parallel).await?
            }
            BatchProcessingStrategy::Sequential => self.process_sequential(&task).await?,
            BatchProcessingStrategy::PriorityQueue => self.process_with_priority(&task).await?,
            BatchProcessingStrategy::LoadBalanced {
                max_tokens_per_batch,
            } => {
                self.process_load_balanced(&task, max_tokens_per_batch)
                    .await?
            }
        };

        let processing_time = start_time.elapsed().as_millis() as i64;

        // 处理文档，收集结果和质量信息
        let mut successful_docs = Vec::new();
        let mut failed_docs = Vec::new();
        let mut skipped_docs = Vec::new();
        let mut all_vectors = Vec::new();
        
        for (i, result_opt) in result.iter().enumerate() {
            let document_id = &task.document_ids[i];
            
            match result_opt {
                Ok(_) => {
                    // 检查文档是否应该被跳过
                    if self.should_skip_document(document_id).await {
                        skipped_docs.push(document_id.clone());
                    } else {
                        successful_docs.push(document_id.clone());
                        // 收集文档的向量用于质量评估
                        // TODO: 这里需要从向量化服务获取实际的向量数据
                        // 目前简化处理，跳过向量收集
                        tracing::debug!("收集文档 {} 的向量用于质量评估", document_id);
                    }
                }
                Err(_) => {
                    failed_docs.push(document_id.clone());
                }
            }
        }

        // 计算整体质量分数和生成质量报告
        let (overall_quality_score, quality_report) = if !all_vectors.is_empty() {
            let quality_report = self.quality_assessment_service
                .generate_quality_report(&all_vectors);
            
            let overall_score = quality_report.avg_quality_score;
            (overall_score, quality_report)
        } else {
            (0.0, QualityReport::default())
        };

        // 生成最终结果
        let batch_result = BatchResult {
            batch_id: batch_id.to_owned(),
            total_processed: result.len(),
            successful: successful_docs.len(),
            failed: failed_docs.len(),
            skipped: skipped_docs.len(),
            processing_time_ms: processing_time,
            overall_quality_score,
            failed_document_ids: failed_docs,
            quality_report,
        };

        // 更新最终进度
        self.update_progress(batch_id, |p| {
            p.current_stage = BatchStage::Completed;
            p.processed_documents = batch_result.total_processed;
            p.successful_documents = batch_result.successful;
            p.failed_documents = batch_result.failed;
        })
        .await;

        tracing::info!(
            "批次处理完成: batch_id={}, 成功={}, 失败={}, 耗时={}ms",
            batch_id,
            batch_result.successful,
            batch_result.failed,
            processing_time
        );

        Ok(batch_result)
    }

    /// 检查文档是否应该被跳过
    async fn should_skip_document(&self, document_id: &str) -> bool {
        // 跳过逻辑：
        // 1. 检查文档是否已经有高质量的向量
        // 2. 检查文档是否在短时间内已经处理过
        // 3. 检查文档内容是否过短或为空
        
        if let Ok(Some(document)) = self.document_repository.find_by_id(document_id).await {
            // 如果不强制重新计算且文档已有向量化结果
            if !self.config.force_recompute {
                // 简化检查：通过向量化服务来判断是否已有向量化结果
                // TODO: 添加更精确的向量化状态检查
                tracing::debug!("检查文档 {} 的向量化状态", document_id);
                // 暂时跳过已有向量检查，因为缺少相应的repository
                // 未来可以添加 DocumentEmbeddingRepository 来检查向量化状态
            }
            
            // 检查文档内容长度
            if document.content.trim().len() < 10 {
                tracing::debug!("Skipping document {} - content too short", document_id);
                return true;
            }
        }
        
        false
    }

    /// 并发处理
    async fn process_concurrent(
        &self,
        task: &BatchTask,
        max_parallel: usize,
    ) -> Result<Vec<Result<(), anyhow::Error>>> {
        self.update_progress(&task.batch_id, |p| {
            p.current_stage = BatchStage::Vectorizing;
        })
        .await;

        let semaphore = Arc::new(Semaphore::new(max_parallel));
        let mut handles = Vec::new();

        for document_id in task.document_ids.iter() {
            let sem = semaphore.clone();
            let vectorization_service = self.vectorization_service.clone();
            let document_id = document_id.clone();
            let model_id = task.model_id.clone();
            let force_recompute = task.force_recompute;
            let batch_id = task.batch_id.clone();

            // 克隆 self 用于进度更新
            let batch_processor = self.clone_for_async();

            let handle = tokio::spawn(async move {
                let _permit = sem
                    .acquire()
                    .await
                    .map_err(|e| anyhow::anyhow!("Semaphore error: {}", e))?;

                let result = vectorization_service
                    .vectorize_document(document_id, Some(model_id), force_recompute)
                    .await;

                // 更新进度
                batch_processor
                    .update_progress(&batch_id, |p| {
                        p.processed_documents += 1;
                        if result.is_ok() {
                            p.successful_documents += 1;
                        } else {
                            p.failed_documents += 1;
                        }

                        // 计算处理速度和预估完成时间
                        let elapsed = current_millis() - p.started_at;
                        if elapsed / 1000 > 0 {
                            p.processing_rate =
                                p.processed_documents as f64 / ((elapsed / 1000) as f64);
                            if p.processing_rate > 0.0 {
                                let remaining_docs = p.total_documents - p.processed_documents;
                                let eta_seconds = remaining_docs as f64 / p.processing_rate;
                                p.estimated_completion =
                                    Some(current_millis() + (eta_seconds as i64 * 1000));
                            }
                        }
                    })
                    .await;

                result.map(|_| ())
            });

            handles.push(handle);
        }

        // 等待所有任务完成
        let mut results = Vec::new();
        for handle in handles {
            match handle.await {
                Ok(result) => results.push(result),
                Err(e) => results.push(Err(anyhow::anyhow!("Task join error: {}", e))),
            }
        }

        Ok(results)
    }

    /// 顺序处理
    async fn process_sequential(&self, task: &BatchTask) -> Result<Vec<Result<(), anyhow::Error>>> {
        self.update_progress(&task.batch_id, |p| {
            p.current_stage = BatchStage::Vectorizing;
        })
        .await;

        let mut results = Vec::new();

        for (index, document_id) in task.document_ids.iter().enumerate() {
            let result = self
                .vectorization_service
                .vectorize_document(
                    document_id.clone(),
                    Some(task.model_id.clone()),
                    task.force_recompute,
                )
                .await;

            // 更新进度
            self.update_progress(&task.batch_id, |p| {
                p.processed_documents = index + 1;
                if result.is_ok() {
                    p.successful_documents += 1;
                } else {
                    p.failed_documents += 1;
                }
            })
            .await;

            results.push(result.map(|_| ()));

            // 如果启用了增量处理，在处理间隔报告进度
            if (index + 1) % self.config.progress_report_interval == 0 {
                self.send_progress_notification(&task.batch_id).await;
            }
        }

        Ok(results)
    }

    /// 优先级处理
    async fn process_with_priority(
        &self,
        task: &BatchTask,
    ) -> Result<Vec<Result<(), anyhow::Error>>> {
        // 按文档创建时间或大小排序（这里简化为顺序处理）
        self.process_sequential(task).await
    }

    /// 负载均衡处理
    async fn process_load_balanced(
        &self,
        task: &BatchTask,
        max_tokens_per_batch: usize,
    ) -> Result<Vec<Result<(), anyhow::Error>>> {
        tracing::info!(
            "开始负载均衡处理: batch_id={}, max_tokens_per_batch={}",
            task.batch_id,
            max_tokens_per_batch
        );

        // 获取所有文档及其大小信息
        let mut documents_with_size = Vec::new();
        for document_id in &task.document_ids {
            if let Ok(Some(document)) = self.document_repository.find_by_id(document_id).await {
                let estimated_tokens = self.estimate_token_count(&document.content);
                documents_with_size.push((document_id.clone(), document, estimated_tokens));
            }
        }

        // 按文档大小排序（大的文档优先处理）
        documents_with_size.sort_by(|a, b| b.2.cmp(&a.2));

        // 创建动态分组，每组不超过max_tokens_per_batch
        let mut batches = Vec::new();
        let mut current_batch = Vec::new();
        let mut current_batch_tokens = 0;

        for (doc_id, document, tokens) in documents_with_size {
            // 如果单个文档超过批次限制，单独处理
            if tokens > max_tokens_per_batch {
                if !current_batch.is_empty() {
                    batches.push(std::mem::take(&mut current_batch));
                    current_batch_tokens = 0;
                }
                batches.push(vec![(doc_id, document, tokens)]);
                continue;
            }

            // 如果添加当前文档会超过限制，先提交当前批次
            if current_batch_tokens + tokens > max_tokens_per_batch && !current_batch.is_empty() {
                batches.push(std::mem::take(&mut current_batch));
                current_batch_tokens = 0;
            }

            current_batch.push((doc_id, document, tokens));
            current_batch_tokens += tokens;
        }

        // 提交最后一个批次
        if !current_batch.is_empty() {
            batches.push(current_batch);
        }

        tracing::info!(
            "负载均衡分组完成: {} 个批次，平均每批 {:.1} 个文档",
            batches.len(),
            task.document_ids.len() as f64 / batches.len() as f64
        );

        // 并行处理各批次
        let mut all_results = Vec::new();
        let batch_count = batches.len();

        for (batch_index, batch_group) in batches.into_iter().enumerate() {
            tracing::debug!(
                "处理负载均衡批次 {}/{}: {} 个文档，总Token约 {}",
                batch_index + 1,
                batch_count,
                batch_group.len(),
                batch_group.iter().map(|(_, _, tokens)| tokens).sum::<usize>()
            );

            // 并发处理当前批次中的文档
            let semaphore = Arc::new(Semaphore::new(4)); // 每批次内部并发度
            let mut batch_tasks = Vec::new();

            for (doc_id, _document, _) in batch_group {
                let permit = semaphore.clone().acquire_owned().await?;
                let vectorization_service = self.vectorization_service.clone();
                let model_id = task.model_id.clone();
                let force_recompute = task.force_recompute;

                let task_future = async move {
                    let _permit = permit;
                    tracing::debug!("开始处理文档: {}", doc_id);
                    
                    let result = vectorization_service
                        .vectorize_document(doc_id.clone(), Some(model_id), force_recompute)
                        .await;

                    if let Err(ref e) = result {
                        tracing::error!("文档向量化失败: {} - {}", doc_id, e);
                    } else {
                        tracing::debug!("文档向量化完成: {}", doc_id);
                    }

                    result.map(|_| ())
                };

                batch_tasks.push(task_future);
            }

            // 等待当前批次完成
            let batch_results = futures::future::join_all(batch_tasks).await;
            all_results.extend(batch_results);

            // 更新进度
            self.update_progress(&task.batch_id, |p| {
                p.processed_documents = all_results.len();
                p.successful_documents = all_results.iter().filter(|r| r.is_ok()).count();
                p.failed_documents = all_results.iter().filter(|r| r.is_err()).count();
            })
            .await;
        }

        Ok(all_results)
    }

    /// 估算文本的Token数量
    fn estimate_token_count(&self, text: &str) -> usize {
        // 简单估算：按平均每个Token约4个字符计算
        // 实际实现可能需要更精确的tokenizer
        (text.len() + 3) / 4
    }

    /// 更新进度
    async fn update_progress<F>(&self, batch_id: &str, updater: F)
    where
        F: FnOnce(&mut BatchProgress),
    {
        let mut batches = self.active_batches.write().await;
        if let Some(progress) = batches.get_mut(batch_id) {
            updater(progress);

            // 发送进度通知
            if let Some(sender) = self.progress_sender.read().await.as_ref() {
                let _ = sender.send(progress.clone());
            }
        }
    }

    /// 发送进度通知
    async fn send_progress_notification(&self, batch_id: &str) {
        if let Some(progress) = self.active_batches.read().await.get(batch_id) {
            if let Some(sender) = self.progress_sender.read().await.as_ref() {
                let _ = sender.send(progress.clone());
            }
        }
    }

    /// 标记批次失败
    async fn mark_batch_failed(&self, batch_id: &str, error: &str) {
        self.update_progress(batch_id, |p| {
            p.current_stage = BatchStage::Failed(error.to_string());
        })
        .await;
    }

    /// 根据优先级添加任务
    async fn add_task_with_priority(&self, task: BatchTask) {
        let mut queue = self.task_queue.write().await;

        // 找到合适的位置插入（按优先级排序）
        let insert_pos = queue
            .iter()
            .position(|t| t.priority < task.priority)
            .unwrap_or(queue.len());
        queue.insert(insert_pos, task);
    }

    /// 从队列获取任务
    async fn get_task_from_queue(&self, batch_id: &str) -> Option<BatchTask> {
        let mut queue = self.task_queue.write().await;
        if let Some(pos) = queue.iter().position(|t| t.batch_id == batch_id) {
            queue.remove(pos)
        } else {
            None
        }
    }

    /// 获取所有活跃批次
    pub async fn get_active_batches(&self) -> Vec<BatchProgress> {
        self.active_batches.read().await.values().cloned().collect()
    }

    /// 清理已完成的批次
    pub async fn cleanup_completed_batches(&self, older_than_hours: i64) {
        let cutoff =
            current_millis() - chrono::Duration::hours(older_than_hours).num_milliseconds();
        let mut batches = self.active_batches.write().await;

        batches.retain(|_, progress| {
            !matches!(
                progress.current_stage,
                BatchStage::Completed | BatchStage::Failed(_) | BatchStage::Cancelled
            ) || progress.started_at > cutoff
        });
    }

    /// 克隆用于异步操作
    fn clone_for_async(&self) -> Self {
        Self {
            vectorization_service: self.vectorization_service.clone(),
            quality_assessment_service: self.quality_assessment_service.clone(),
            document_repository: self.document_repository.clone(),
            chunk_repository: self.chunk_repository.clone(),
            config: self.config.clone(),
            active_batches: self.active_batches.clone(),
            task_queue: self.task_queue.clone(),
            semaphore: self.semaphore.clone(),
            progress_sender: self.progress_sender.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_batch_config_creation() {
        let config = BatchVectorizationConfig::default();
        assert_eq!(config.default_batch_size, 32);
        assert_eq!(config.max_retries, 3);
    }

    #[test]
    fn test_batch_progress_initialization() {
        let batch_id = next_id();
        let progress = BatchProgress {
            batch_id,
            total_documents: 100,
            processed_documents: 0,
            successful_documents: 0,
            failed_documents: 0,
            current_stage: BatchStage::Initializing,
            started_at: current_millis(),
            estimated_completion: None,
            processing_rate: 0.0,
            quality_report: None,
        };

        assert_eq!(progress.total_documents, 100);
        assert_eq!(progress.current_stage, BatchStage::Initializing);
    }
}
