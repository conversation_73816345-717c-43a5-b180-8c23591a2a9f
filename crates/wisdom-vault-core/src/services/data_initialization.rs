use anyhow::{Result, anyhow};
use std::{collections::HashMap, sync::Arc};
use tracing::{info, warn};
use wisdom_vault_common::time::current_millis;
use wisdom_vault_database::{
    models::{Permission, PermissionAction, Role, RolePermission, User},
    repositories::{
        MongoPermissionRepository, MongoRolePermissionRepository, MongoRoleRepository,
        MongoUserRepository, MongoUserRoleRepository, PermissionRepository,
        RolePermissionRepository, RoleRepository, UserRepository, UserRoleRepository,
    },
};

use crate::services::password;

/// 数据初始化配置
#[derive(Debug, Clone)]
pub struct InitializationConfig {
    /// 管理员用户名
    pub admin_username: String,
    /// 管理员邮箱
    pub admin_email: String,
    /// 管理员密码
    pub admin_password: String,
    /// 管理员全名
    pub admin_full_name: String,
    /// 是否强制重新初始化
    pub force_reinit: bool,
}

impl Default for InitializationConfig {
    fn default() -> Self {
        Self {
            admin_username: "admin".to_string(),
            admin_email: "<EMAIL>".to_string(),
            admin_password: "admin123".to_string(),
            admin_full_name: "系统管理员".to_string(),
            force_reinit: false,
        }
    }
}

/// 数据初始化服务
pub struct DataInitializationService {
    user_repo: Arc<MongoUserRepository>,
    role_repo: Arc<MongoRoleRepository>,
    permission_repo: Arc<MongoPermissionRepository>,
    user_role_repo: Arc<MongoUserRoleRepository>,
    role_permission_repo: Arc<MongoRolePermissionRepository>,
}

impl DataInitializationService {
    pub fn new(
        user_repo: Arc<MongoUserRepository>,
        role_repo: Arc<MongoRoleRepository>,
        permission_repo: Arc<MongoPermissionRepository>,
        user_role_repo: Arc<MongoUserRoleRepository>,
        role_permission_repo: Arc<MongoRolePermissionRepository>,
    ) -> Self {
        Self {
            user_repo,
            role_repo,
            permission_repo,
            user_role_repo,
            role_permission_repo,
        }
    }

    /// 执行完整的系统初始化
    pub async fn initialize_system(&self, config: InitializationConfig) -> Result<()> {
        info!("开始系统数据初始化...");

        // 检查是否需要初始化
        if !config.force_reinit && self.check_initialization_status().await? {
            info!("系统已初始化，跳过数据初始化");
            return Ok(());
        }

        // 1. 创建系统权限
        let permissions = self.create_system_permissions().await?;
        info!("创建了 {} 个系统权限", permissions.len());

        // 2. 创建系统角色
        let roles = self.create_system_roles().await?;
        info!("创建了 {} 个系统角色", roles.len());

        // 3. 分配角色权限
        self.assign_role_permissions(&roles, &permissions).await?;
        info!("完成角色权限分配");

        // 4. 创建超级管理员
        let admin_user = self.create_admin_user(&config).await?;
        info!("创建超级管理员用户: {}", admin_user.username);

        // 5. 分配管理员角色
        if let Some(admin_role) = roles.get("admin") {
            self.user_role_repo
                .assign_role(&admin_user.id, &admin_role.id, "system")
                .await?;
            info!("为超级管理员分配 admin 角色");
        }

        info!("系统数据初始化完成!");
        Ok(())
    }

    /// 检查系统是否已经初始化
    async fn check_initialization_status(&self) -> Result<bool> {
        // 检查是否存在用户
        let count = self.user_repo.count().await?;

        Ok(count > 0)
    }

    /// 创建系统基础权限
    async fn create_system_permissions(&self) -> Result<HashMap<String, Permission>> {
        let mut permissions = HashMap::new();
        let current_time = current_millis();

        let permission_definitions = vec![
            // 用户管理权限
            ("user.create", "user", PermissionAction::Create, "创建用户"),
            ("user.read", "user", PermissionAction::Read, "查看用户"),
            ("user.update", "user", PermissionAction::Update, "更新用户"),
            ("user.delete", "user", PermissionAction::Delete, "删除用户"),
            ("user.manage", "user", PermissionAction::Manage, "管理用户"),
            // 角色权限管理
            ("role.create", "role", PermissionAction::Create, "创建角色"),
            ("role.read", "role", PermissionAction::Read, "查看角色"),
            ("role.update", "role", PermissionAction::Update, "更新角色"),
            ("role.delete", "role", PermissionAction::Delete, "删除角色"),
            ("role.manage", "role", PermissionAction::Manage, "管理角色"),
            // 知识库权限
            (
                "knowledge_base.create",
                "knowledge_base",
                PermissionAction::Create,
                "创建知识库",
            ),
            (
                "knowledge_base.read",
                "knowledge_base",
                PermissionAction::Read,
                "查看知识库",
            ),
            (
                "knowledge_base.update",
                "knowledge_base",
                PermissionAction::Update,
                "更新知识库",
            ),
            (
                "knowledge_base.delete",
                "knowledge_base",
                PermissionAction::Delete,
                "删除知识库",
            ),
            (
                "knowledge_base.manage",
                "knowledge_base",
                PermissionAction::Manage,
                "管理知识库",
            ),
            // 文档权限
            (
                "document.create",
                "document",
                PermissionAction::Create,
                "创建文档",
            ),
            (
                "document.read",
                "document",
                PermissionAction::Read,
                "查看文档",
            ),
            (
                "document.update",
                "document",
                PermissionAction::Update,
                "更新文档",
            ),
            (
                "document.delete",
                "document",
                PermissionAction::Delete,
                "删除文档",
            ),
            (
                "document.manage",
                "document",
                PermissionAction::Manage,
                "管理文档",
            ),
            // 系统管理权限
            (
                "system.manage",
                "system",
                PermissionAction::Manage,
                "系统管理",
            ),
            (
                "system.config",
                "system",
                PermissionAction::Update,
                "系统配置",
            ),
        ];

        for (name, resource, action, description) in permission_definitions {
            // 检查权限是否已存在
            if let Some(existing) = self.permission_repo.find_by_name(name).await? {
                permissions.insert(name.to_string(), existing);
                continue;
            }

            let permission = Permission {
                id: wisdom_vault_common::db::next_id(),
                name: name.to_string(),
                resource: resource.to_string(),
                action,
                description: Some(description.to_string()),
                created_at: current_time,
            };

            let created_permission = self.permission_repo.create(&permission).await?;
            permissions.insert(name.to_string(), created_permission);
        }

        Ok(permissions)
    }

    /// 创建系统基础角色
    async fn create_system_roles(&self) -> Result<HashMap<String, Role>> {
        let mut roles = HashMap::new();
        let current_time = current_millis();

        let role_definitions = vec![
            ("admin", "超级管理员", "拥有所有系统权限的超级管理员", true),
            ("user", "普通用户", "具有基本操作权限的普通用户", true),
            ("viewer", "只读用户", "仅能查看内容的只读用户", true),
        ];

        for (name, display_name, description, is_system) in role_definitions {
            // 检查角色是否已存在
            if let Some(existing) = self.role_repo.find_by_name(name).await? {
                roles.insert(name.to_string(), existing);
                continue;
            }

            let role = Role {
                id: wisdom_vault_common::db::next_id(),
                name: name.to_string(),
                display_name: display_name.to_string(),
                description: Some(description.to_string()),
                is_system,
                created_at: current_time,
                updated_at: current_time,
            };

            let created_role = self.role_repo.create(&role).await?;
            roles.insert(name.to_string(), created_role);
        }

        Ok(roles)
    }

    /// 分配角色权限
    async fn assign_role_permissions(
        &self,
        roles: &HashMap<String, Role>,
        permissions: &HashMap<String, Permission>,
    ) -> Result<()> {
        let current_time = current_millis();

        // 超级管理员拥有所有权限
        if let Some(admin_role) = roles.get("admin") {
            for permission in permissions.values() {
                // 检查权限是否已分配
                if self
                    .role_permission_repo
                    .find_by_role_and_permission(&admin_role.id, &permission.id)
                    .await?
                    .is_some()
                {
                    continue;
                }

                let role_permission = RolePermission {
                    id: wisdom_vault_common::db::next_id(),
                    role_id: admin_role.id.clone(),
                    permission_id: permission.id.clone(),
                    granted_by: "system".to_string(),
                    granted_at: current_time,
                };

                self.role_permission_repo.create(&role_permission).await?;
            }
        }

        // 普通用户权限
        if let Some(user_role) = roles.get("user") {
            let user_permissions = vec![
                "knowledge_base.read",
                "document.create",
                "document.read",
                "document.update",
            ];

            for perm_name in user_permissions {
                if let Some(permission) = permissions.get(perm_name) {
                    // 检查权限是否已分配
                    if self
                        .role_permission_repo
                        .find_by_role_and_permission(&user_role.id, &permission.id)
                        .await?
                        .is_some()
                    {
                        continue;
                    }

                    let role_permission = RolePermission {
                        id: wisdom_vault_common::db::next_id(),
                        role_id: user_role.id.clone(),
                        permission_id: permission.id.clone(),
                        granted_by: "system".to_string(),
                        granted_at: current_time,
                    };

                    self.role_permission_repo.create(&role_permission).await?;
                }
            }
        }

        // 只读用户权限
        if let Some(viewer_role) = roles.get("viewer") {
            let viewer_permissions = vec!["knowledge_base.read", "document.read"];

            for perm_name in viewer_permissions {
                if let Some(permission) = permissions.get(perm_name) {
                    // 检查权限是否已分配
                    if self
                        .role_permission_repo
                        .find_by_role_and_permission(&viewer_role.id, &permission.id)
                        .await?
                        .is_some()
                    {
                        continue;
                    }

                    let role_permission = RolePermission {
                        id: wisdom_vault_common::db::next_id(),
                        role_id: viewer_role.id.clone(),
                        permission_id: permission.id.clone(),
                        granted_by: "system".to_string(),
                        granted_at: current_time,
                    };

                    self.role_permission_repo.create(&role_permission).await?;
                }
            }
        }

        Ok(())
    }

    /// 创建超级管理员用户
    async fn create_admin_user(&self, config: &InitializationConfig) -> Result<User> {
        // 检查管理员用户是否已存在
        if let Some(existing) = self
            .user_repo
            .find_by_username(&config.admin_username)
            .await?
        {
            if config.force_reinit {
                warn!("强制重新初始化，将更新现有管理员用户");
            } else {
                info!("管理员用户已存在，跳过创建");
                return Ok(existing);
            }
        }

        let user_id = wisdom_vault_common::db::next_id();
        let password_hash = password::hash_password(&user_id, &config.admin_password)?;
        let current_time = current_millis();

        let admin_user = User {
            id: user_id,
            username: config.admin_username.clone(),
            email: config.admin_email.clone(),
            password_hash,
            full_name: Some(config.admin_full_name.clone()),
            avatar_id: None,
            phone: None,
            organization_id: None,
            is_active: true,
            last_login_at: None,
            created_at: current_time,
            updated_at: current_time,
        };

        // 如果是强制重新初始化且用户已存在，则更新用户
        if config.force_reinit {
            if let Ok(Some(_)) = self
                .user_repo
                .find_by_username(&config.admin_username)
                .await
            {
                return self.user_repo.update(&admin_user).await;
            }
        }

        // 检查邮箱是否已被使用
        if let Some(_) = self.user_repo.find_by_email(&config.admin_email).await? {
            if !config.force_reinit {
                return Err(anyhow!("管理员邮箱已被使用"));
            }
        }

        self.user_repo.create(&admin_user).await
    }

    /// 获取初始化状态报告
    pub async fn get_initialization_status(&self) -> Result<InitializationStatus> {
        let admin_role = self.role_repo.find_by_name("admin").await?;
        let user_role = self.role_repo.find_by_name("user").await?;
        let admin_user = self.user_repo.find_by_username("admin").await?;

        let total_permissions = self.permission_repo.count().await?;
        let total_roles = self.role_repo.count().await?;
        let total_users = self.user_repo.count().await?;

        Ok(InitializationStatus {
            is_initialized: admin_role.is_some() && admin_user.is_some(),
            admin_role_exists: admin_role.is_some(),
            user_role_exists: user_role.is_some(),
            admin_user_exists: admin_user.is_some(),
            total_permissions,
            total_roles,
            total_users,
        })
    }
}

/// 初始化状态
#[derive(Debug, Clone)]
pub struct InitializationStatus {
    pub is_initialized: bool,
    pub admin_role_exists: bool,
    pub user_role_exists: bool,
    pub admin_user_exists: bool,
    pub total_permissions: u64,
    pub total_roles: u64,
    pub total_users: u64,
}
