use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::future::Future;
use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::services::HybridSearchResults;
use crate::vector_search::HybridSearchRequest;

/// 混合搜索缓存系统
pub struct HybridSearchCache {
    /// L1缓存：内存缓存，快速访问
    l1_cache: Arc<RwLock<InMemoryCache>>,
    /// L2缓存：Redis缓存，持久化存储
    l2_cache: Arc<RwLock<RedisCache>>,
    /// 缓存配置
    config: CacheConfig,
    /// 缓存性能统计
    performance_stats: Arc<RwLock<CachePerformanceStats>>,
    /// 缓存失效管理器
    invalidation_manager: Arc<CacheInvalidationManager>,
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// L1缓存最大条目数
    pub l1_max_entries: usize,
    /// L1缓存TTL（秒）
    pub l1_ttl_seconds: u64,
    /// L2缓存TTL（秒）
    pub l2_ttl_seconds: u64,
    /// 预热缓存大小
    pub preload_cache_size: usize,
    /// 启用缓存预测
    pub enable_predictive_caching: bool,
    /// 启用缓存压缩
    pub enable_compression: bool,
    /// 最大缓存键长度
    pub max_cache_key_length: usize,
    /// LRU淘汰策略阈值
    pub lru_eviction_threshold: f64,
    /// 缓存统计采样率
    pub stats_sampling_rate: f64,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            l1_max_entries: 1000,
            l1_ttl_seconds: 300,  // 5分钟
            l2_ttl_seconds: 3600, // 1小时
            preload_cache_size: 100,
            enable_predictive_caching: true,
            enable_compression: true,
            max_cache_key_length: 256,
            lru_eviction_threshold: 0.8,
            stats_sampling_rate: 0.1,
        }
    }
}

/// 缓存条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry {
    /// 缓存键
    pub key: String,
    /// 缓存值
    pub value: CachedSearchResults,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 访问时间
    pub accessed_at: DateTime<Utc>,
    /// 访问次数
    pub access_count: u64,
    /// TTL（秒）
    pub ttl_seconds: u64,
    /// 缓存层级
    pub cache_level: CacheLevel,
    /// 压缩状态
    pub is_compressed: bool,
}

/// 缓存层级
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CacheLevel {
    /// L1内存缓存
    L1Memory,
    /// L2Redis缓存
    L2Redis,
    /// 预测缓存
    Predictive,
}

/// 缓存的搜索结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CachedSearchResults {
    /// 搜索结果
    pub results: HybridSearchResults,
    /// 缓存元数据
    pub cache_metadata: CacheMetadata,
}

/// 缓存元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheMetadata {
    /// 搜索查询哈希
    pub query_hash: String,
    /// 结果质量评分
    pub quality_score: f64,
    /// 缓存权重（用于淘汰策略）
    pub cache_weight: f64,
    /// 用户ID（用于个性化缓存）
    pub user_id: Option<String>,
    /// 知识库ID
    pub knowledge_base_id: Option<String>,
    /// 搜索上下文标识
    pub context_fingerprint: String,
}

/// L1内存缓存
pub struct InMemoryCache {
    /// 缓存存储
    cache_store: HashMap<String, CacheEntry>,
    /// LRU访问顺序
    access_order: Vec<String>,
    /// 缓存统计
    stats: L1CacheStats,
}

/// L1缓存统计
#[derive(Debug, Clone, Default)]
pub struct L1CacheStats {
    /// 缓存命中次数
    pub hits: u64,
    /// 缓存未命中次数
    pub misses: u64,
    /// 缓存淘汰次数
    pub evictions: u64,
    /// 平均访问时间（微秒）
    pub avg_access_time_us: f64,
}

/// L2Redis缓存
pub struct RedisCache {
    /// Redis连接
    connection: Arc<RwLock<redis::aio::MultiplexedConnection>>,
    /// 缓存统计
    stats: L2CacheStats,
    /// 压缩器
    compressor: CacheCompressor,
}

/// L2缓存统计
#[derive(Debug, Clone, Default)]
pub struct L2CacheStats {
    /// 缓存命中次数
    pub hits: u64,
    /// 缓存未命中次数
    pub misses: u64,
    /// 序列化时间（微秒）
    pub avg_serialization_time_us: f64,
    /// 压缩比率
    pub compression_ratio: f64,
}

/// 缓存压缩器
pub struct CacheCompressor {
    /// 启用压缩
    pub enabled: bool,
    /// 压缩阈值（字节）
    pub compression_threshold: usize,
}

impl Default for CacheCompressor {
    fn default() -> Self {
        Self {
            enabled: true,
            compression_threshold: 1024, // 1KB
        }
    }
}

/// 缓存失效管理器
pub struct CacheInvalidationManager {
    /// 失效规则
    invalidation_rules: Vec<InvalidationRule>,
    /// 失效事件监听器
    event_listeners: Vec<Box<dyn CacheInvalidationListener>>,
    /// 定时失效任务
    scheduled_tasks: HashMap<String, ScheduledInvalidationTask>,
}

/// 失效规则
#[derive(Debug, Clone)]
pub struct InvalidationRule {
    /// 规则ID
    pub rule_id: String,
    /// 规则类型
    pub rule_type: InvalidationRuleType,
    /// 匹配模式
    pub pattern: String,
    /// 失效策略
    pub strategy: InvalidationStrategy,
}

/// 失效规则类型
#[derive(Debug, Clone, PartialEq)]
pub enum InvalidationRuleType {
    /// 基于时间
    TimeBased,
    /// 基于文档更新
    DocumentUpdate,
    /// 基于知识库变更
    KnowledgeBaseChange,
    /// 基于用户行为
    UserBehavior,
    /// 基于系统事件
    SystemEvent,
}

/// 失效策略
#[derive(Debug, Clone, PartialEq)]
pub enum InvalidationStrategy {
    /// 立即失效
    Immediate,
    /// 延迟失效
    Delayed { delay_seconds: u64 },
    /// 批量失效
    Batch { batch_size: usize },
    /// 渐进式失效
    Progressive { rate_limit: f64 },
}

/// 缓存失效监听器
pub trait CacheInvalidationListener: Send + Sync {
    fn on_cache_invalidated(
        &self,
        keys: &[String],
        reason: &str,
    ) -> Pin<Box<dyn Future<Output = Result<()>> + Send + '_>>;
}

/// 定时失效任务
#[derive(Debug, Clone)]
pub struct ScheduledInvalidationTask {
    /// 任务ID
    pub task_id: String,
    /// 执行时间
    pub execute_at: DateTime<Utc>,
    /// 失效键模式
    pub key_pattern: String,
    /// 失效原因
    pub reason: String,
}

/// 缓存性能统计
#[derive(Debug, Clone, Default)]
pub struct CachePerformanceStats {
    /// 总请求数
    pub total_requests: u64,
    /// L1缓存命中数
    pub l1_hits: u64,
    /// L2缓存命中数
    pub l2_hits: u64,
    /// 缓存未命中数
    pub cache_misses: u64,
    /// 平均响应时间（微秒）
    pub avg_response_time_us: f64,
    /// 缓存命中率
    pub hit_rate: f64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: u64,
    /// 网络IO统计
    pub network_io_stats: NetworkIOStats,
    /// 压缩统计
    pub compression_stats: CompressionStats,
}

/// 网络IO统计
#[derive(Debug, Clone, Default)]
pub struct NetworkIOStats {
    /// 发送字节数
    pub bytes_sent: u64,
    /// 接收字节数
    pub bytes_received: u64,
    /// 网络延迟（毫秒）
    pub avg_latency_ms: f64,
}

/// 压缩统计
#[derive(Debug, Clone, Default)]
pub struct CompressionStats {
    /// 压缩前大小
    pub uncompressed_size: u64,
    /// 压缩后大小
    pub compressed_size: u64,
    /// 压缩时间（微秒）
    pub compression_time_us: f64,
    /// 解压缩时间（微秒）
    pub decompression_time_us: f64,
}

impl HybridSearchCache {
    /// 创建新的混合搜索缓存
    pub fn new(config: CacheConfig, redis_connection: redis::aio::MultiplexedConnection) -> Self {
        let l1_cache = Arc::new(RwLock::new(InMemoryCache::new()));
        let l2_cache = Arc::new(RwLock::new(RedisCache::new(redis_connection)));
        let performance_stats = Arc::new(RwLock::new(CachePerformanceStats::default()));
        let invalidation_manager = Arc::new(CacheInvalidationManager::new());

        Self {
            l1_cache,
            l2_cache,
            config,
            performance_stats,
            invalidation_manager,
        }
    }

    /// 获取缓存结果
    pub async fn get(&self, request: &HybridSearchRequest) -> Result<Option<HybridSearchResults>> {
        let start_time = std::time::Instant::now();
        let cache_key = self.generate_cache_key(request).await?;

        // 更新统计
        {
            let mut stats = self.performance_stats.write().await;
            stats.total_requests += 1;
        }

        // 首先尝试L1缓存
        if let Some(cached_result) = self.get_from_l1(&cache_key).await? {
            self.update_hit_stats(
                CacheLevel::L1Memory,
                start_time.elapsed().as_micros() as f64,
            )
            .await;
            return Ok(Some(cached_result.results));
        }

        // 然后尝试L2缓存
        if let Some(cached_result) = self.get_from_l2(&cache_key).await? {
            // 将结果写入L1缓存
            self.put_to_l1(&cache_key, &cached_result).await?;
            self.update_hit_stats(CacheLevel::L2Redis, start_time.elapsed().as_micros() as f64)
                .await;
            return Ok(Some(cached_result.results));
        }

        // 缓存未命中
        self.update_miss_stats(start_time.elapsed().as_micros() as f64)
            .await;
        Ok(None)
    }

    /// 存储搜索结果到缓存
    pub async fn put(
        &self,
        request: &HybridSearchRequest,
        results: &HybridSearchResults,
    ) -> Result<()> {
        let cache_key = self.generate_cache_key(request).await?;
        let cached_results = self.create_cached_results(request, results).await;

        // 同时写入L1和L2缓存
        let l1_future = self.put_to_l1(&cache_key, &cached_results);
        let l2_future = self.put_to_l2(&cache_key, &cached_results);

        tokio::try_join!(l1_future, l2_future)?;

        // 如果启用了预测缓存，进行预测性缓存
        if self.config.enable_predictive_caching {
            self.trigger_predictive_caching(request).await?;
        }

        Ok(())
    }

    /// 失效指定键的缓存
    pub async fn invalidate(&self, keys: &[String]) -> Result<()> {
        // 从L1缓存中移除
        {
            let mut l1_cache = self.l1_cache.write().await;
            for key in keys {
                l1_cache.remove(key);
            }
        }

        // 从L2缓存中移除
        {
            let mut l2_cache = self.l2_cache.write().await;
            l2_cache.delete_multiple(keys).await?;
        }

        // 通知失效监听器
        for listener in &self.invalidation_manager.event_listeners {
            listener
                .on_cache_invalidated(keys, "Manual invalidation")
                .await?;
        }

        Ok(())
    }

    /// 基于模式失效缓存
    pub async fn invalidate_pattern(&self, pattern: &str) -> Result<()> {
        // 获取匹配的键
        let matching_keys = self.get_keys_matching_pattern(pattern).await?;

        if !matching_keys.is_empty() {
            self.invalidate(&matching_keys).await?;
        }

        Ok(())
    }

    /// 清空所有缓存
    pub async fn clear_all(&self) -> Result<()> {
        // 清空L1缓存
        {
            let mut l1_cache = self.l1_cache.write().await;
            l1_cache.clear();
        }

        // 清空L2缓存
        {
            let mut l2_cache = self.l2_cache.write().await;
            l2_cache.flush_all().await?;
        }

        tracing::info!("所有混合搜索缓存已清空");
        Ok(())
    }

    /// 预热缓存
    pub async fn warm_up(
        &self,
        popular_queries: &[(HybridSearchRequest, HybridSearchResults)],
    ) -> Result<()> {
        let warm_up_count = popular_queries.len().min(self.config.preload_cache_size);

        for (request, results) in popular_queries.iter().take(warm_up_count) {
            if let Err(e) = self.put(request, results).await {
                tracing::warn!("缓存预热失败 query={}: {}", request.query_text, e);
            }
        }

        tracing::info!("缓存预热完成，预热了 {} 个查询", warm_up_count);
        Ok(())
    }

    /// 获取缓存统计信息
    pub async fn get_cache_stats(&self) -> CachePerformanceStats {
        let stats = self.performance_stats.read().await;
        let mut result = stats.clone();

        // 计算命中率
        let total_hits = result.l1_hits + result.l2_hits;
        result.hit_rate = if result.total_requests > 0 {
            total_hits as f64 / result.total_requests as f64
        } else {
            0.0
        };

        result
    }

    /// 生成缓存键
    async fn generate_cache_key(&self, request: &HybridSearchRequest) -> Result<String> {
        use sha2::{Digest, Sha256};

        let key_components = serde_json::json!({
            "query_text": request.query_text,
            "query_vector": request.query_vector,
            "model_id": request.model_id,
            "knowledge_base_id": request.knowledge_base_id,
            "vector_weight": request.vector_weight,
            "text_weight": request.text_weight,
            "similarity_threshold": request.similarity_threshold,
            "limit": request.limit,
            // "fusion_strategy": request.fusion_strategy,
        });

        let hash = Sha256::digest(key_components.to_string());
        let cache_key = format!("hybrid_search:{}", hex::encode(hash));

        // 限制键长度
        if cache_key.len() > self.config.max_cache_key_length {
            let truncated = &cache_key[..self.config.max_cache_key_length];
            Ok(truncated.to_string())
        } else {
            Ok(cache_key)
        }
    }

    /// 从L1缓存获取
    async fn get_from_l1(&self, key: &str) -> Result<Option<CachedSearchResults>> {
        let mut l1_cache = self.l1_cache.write().await;
        if let Some(entry) = l1_cache.get(key) {
            if !entry.is_expired() {
                let value = entry.value.clone();
                l1_cache.update_access(key);
                return Ok(Some(value));
            } else {
                l1_cache.remove(key);
            }
        }
        Ok(None)
    }

    /// 从L2缓存获取
    async fn get_from_l2(&self, key: &str) -> Result<Option<CachedSearchResults>> {
        let mut l2_cache = self.l2_cache.write().await;
        l2_cache.get(key).await
    }

    /// 存储到L1缓存
    async fn put_to_l1(&self, key: &str, cached_results: &CachedSearchResults) -> Result<()> {
        let mut l1_cache = self.l1_cache.write().await;
        l1_cache
            .put(key, cached_results, self.config.l1_ttl_seconds)
            .await
    }

    /// 存储到L2缓存
    async fn put_to_l2(&self, key: &str, cached_results: &CachedSearchResults) -> Result<()> {
        let mut l2_cache = self.l2_cache.write().await;
        l2_cache
            .put(key, cached_results, self.config.l2_ttl_seconds)
            .await
    }

    /// 创建缓存结果
    async fn create_cached_results(
        &self,
        request: &HybridSearchRequest,
        results: &HybridSearchResults,
    ) -> CachedSearchResults {
        let query_hash = self.calculate_query_hash(&request.query_text);
        let quality_score = self.calculate_result_quality(results);
        let cache_weight = self.calculate_cache_weight(results, quality_score);
        let context_fingerprint = self.generate_context_fingerprint(request);

        CachedSearchResults {
            results: results.clone(),
            cache_metadata: CacheMetadata {
                query_hash,
                quality_score,
                cache_weight,
                user_id: None, // TODO: 从请求中获取用户ID
                knowledge_base_id: request.knowledge_base_id.clone(),
                context_fingerprint,
            },
        }
    }

    /// 触发预测性缓存
    async fn trigger_predictive_caching(&self, request: &HybridSearchRequest) -> Result<()> {
        // 基于当前查询预测可能的相关查询
        let predicted_queries = self.predict_related_queries(request).await?;

        // 异步预加载预测的查询（这里简化实现）
        for predicted_query in predicted_queries {
            // 在实际实现中，这里会触发后台任务来执行这些查询并缓存结果
            tracing::debug!("预测查询: {}", predicted_query);
        }

        Ok(())
    }

    /// 预测相关查询
    async fn predict_related_queries(&self, request: &HybridSearchRequest) -> Result<Vec<String>> {
        // 简化实现：基于查询词生成变体
        let query_words: Vec<&str> = request.query_text.split_whitespace().collect();
        let mut predicted_queries = Vec::new();

        // 生成部分查询
        if query_words.len() > 1 {
            for i in 0..query_words.len() {
                let partial_query = query_words[..i + 1].join(" ");
                predicted_queries.push(partial_query);
            }
        }

        // 生成同义词查询（简化实现）
        // 在实际实现中，这里会使用同义词词典或语言模型

        Ok(predicted_queries)
    }

    /// 获取匹配模式的键
    async fn get_keys_matching_pattern(&self, pattern: &str) -> Result<Vec<String>> {
        let mut matching_keys = Vec::new();

        // 从L1缓存获取匹配的键
        {
            let l1_cache = self.l1_cache.read().await;
            for key in l1_cache.get_all_keys() {
                if self.key_matches_pattern(key, pattern) {
                    matching_keys.push(key.clone());
                }
            }
        }

        // 从L2缓存获取匹配的键
        {
            let l2_cache = self.l2_cache.read().await;
            let l2_keys = l2_cache.get_keys_matching_pattern(pattern).await?;
            matching_keys.extend(l2_keys);
        }

        Ok(matching_keys)
    }

    /// 检查键是否匹配模式
    fn key_matches_pattern(&self, key: &str, pattern: &str) -> bool {
        // 简化的模式匹配，支持通配符
        if pattern.contains('*') {
            let parts: Vec<&str> = pattern.split('*').collect();
            if parts.is_empty() {
                return true;
            }

            let mut key_remaining = key;
            for (i, part) in parts.iter().enumerate() {
                if part.is_empty() {
                    continue;
                }

                if i == 0 && !key_remaining.starts_with(part) {
                    return false;
                }

                if let Some(pos) = key_remaining.find(part) {
                    key_remaining = &key_remaining[pos + part.len()..];
                } else {
                    return false;
                }
            }

            true
        } else {
            key == pattern
        }
    }

    /// 计算查询哈希
    fn calculate_query_hash(&self, query: &str) -> String {
        use sha2::{Digest, Sha256};
        let hash = Sha256::digest(query);
        hex::encode(hash)[..16].to_string() // 取前16个字符
    }

    /// 计算结果质量
    fn calculate_result_quality(&self, results: &HybridSearchResults) -> f64 {
        if results.results.is_empty() {
            return 0.0;
        }

        // 基于结果的平均得分和数量计算质量
        let avg_score = results.results.iter().map(|r| r.final_score).sum::<f64>()
            / results.results.len() as f64;

        let result_count_factor = (results.results.len() as f64 / 20.0).min(1.0);

        avg_score * 0.7 + result_count_factor * 0.3
    }

    /// 计算缓存权重
    fn calculate_cache_weight(&self, results: &HybridSearchResults, quality_score: f64) -> f64 {
        // 基于质量得分、搜索时间等因素计算缓存权重
        let time_factor = (1000.0 - results.search_time_ms.min(1000) as f64) / 1000.0;
        quality_score * 0.6 + time_factor * 0.4
    }

    /// 生成上下文指纹
    fn generate_context_fingerprint(&self, request: &HybridSearchRequest) -> String {
        use sha2::{Digest, Sha256};

        let context_data = format!(
            "kb:{:?}_model:{:?}_fusion:{:?}",
            request.knowledge_base_id,
            request.model_id,
            "default_fusion_strategy" // 使用默认字符串，因为request结构体中没有此字段
        );

        let hash = Sha256::digest(context_data);
        hex::encode(hash)[..12].to_string()
    }

    /// 更新命中统计
    async fn update_hit_stats(&self, cache_level: CacheLevel, response_time_us: f64) {
        let mut stats = self.performance_stats.write().await;

        match cache_level {
            CacheLevel::L1Memory => stats.l1_hits += 1,
            CacheLevel::L2Redis => stats.l2_hits += 1,
            _ => {}
        }

        // 更新平均响应时间
        let total_requests = stats.total_requests as f64;
        stats.avg_response_time_us = (stats.avg_response_time_us * (total_requests - 1.0)
            + response_time_us)
            / total_requests;
    }

    /// 更新未命中统计
    async fn update_miss_stats(&self, response_time_us: f64) {
        let mut stats = self.performance_stats.write().await;
        stats.cache_misses += 1;

        // 更新平均响应时间
        let total_requests = stats.total_requests as f64;
        stats.avg_response_time_us = (stats.avg_response_time_us * (total_requests - 1.0)
            + response_time_us)
            / total_requests;
    }
}

impl InMemoryCache {
    pub fn new() -> Self {
        Self {
            cache_store: HashMap::new(),
            access_order: Vec::new(),
            stats: L1CacheStats::default(),
        }
    }

    pub fn get(&mut self, key: &str) -> Option<&CacheEntry> {
        self.cache_store.get(key)
    }

    pub async fn put(
        &mut self,
        key: &str,
        cached_results: &CachedSearchResults,
        ttl_seconds: u64,
    ) -> Result<()> {
        let now = Utc::now();
        let entry = CacheEntry {
            key: key.to_string(),
            value: cached_results.clone(),
            created_at: now,
            accessed_at: now,
            access_count: 1,
            ttl_seconds,
            cache_level: CacheLevel::L1Memory,
            is_compressed: false,
        };

        self.cache_store.insert(key.to_string(), entry);
        self.update_access_order(key);

        Ok(())
    }

    pub fn remove(&mut self, key: &str) {
        self.cache_store.remove(key);
        self.access_order.retain(|k| k != key);
    }

    pub fn clear(&mut self) {
        self.cache_store.clear();
        self.access_order.clear();
        self.stats = L1CacheStats::default();
    }

    pub fn update_access(&mut self, key: &str) {
        if let Some(entry) = self.cache_store.get_mut(key) {
            entry.accessed_at = Utc::now();
            entry.access_count += 1;
        }
        self.update_access_order(key);
    }

    pub fn get_all_keys(&self) -> Vec<&String> {
        self.cache_store.keys().collect()
    }

    fn update_access_order(&mut self, key: &str) {
        self.access_order.retain(|k| k != key);
        self.access_order.push(key.to_string());
    }
}

impl Default for InMemoryCache {
    fn default() -> Self {
        Self::new()
    }
}

impl CacheEntry {
    pub fn is_expired(&self) -> bool {
        let now = Utc::now();
        let elapsed = now.signed_duration_since(self.created_at);
        elapsed.num_seconds() as u64 > self.ttl_seconds
    }
}

impl RedisCache {
    pub fn new(connection: redis::aio::MultiplexedConnection) -> Self {
        Self {
            connection: Arc::new(RwLock::new(connection)),
            stats: L2CacheStats::default(),
            compressor: CacheCompressor::default(),
        }
    }

    pub async fn get(&mut self, key: &str) -> Result<Option<CachedSearchResults>> {
        let start_time = std::time::Instant::now();
        let mut conn = self.connection.write().await;
        let result: Option<String> = redis::cmd("GET").arg(key).query_async(&mut *conn).await?;
        drop(conn);

        if let Some(serialized_data) = result {
            let data = if self.compressor.enabled {
                self.decompress_data(&serialized_data)?
            } else {
                serialized_data
            };

            let cached_results: CachedSearchResults = serde_json::from_str(&data)?;
            self.stats.hits += 1;

            let elapsed_us = start_time.elapsed().as_micros() as f64;
            self.update_serialization_time(elapsed_us);

            Ok(Some(cached_results))
        } else {
            self.stats.misses += 1;
            Ok(None)
        }
    }

    pub async fn put(
        &mut self,
        key: &str,
        cached_results: &CachedSearchResults,
        ttl_seconds: u64,
    ) -> Result<()> {
        let start_time = std::time::Instant::now();
        let serialized_data = serde_json::to_string(cached_results)?;

        let final_data = if self.compressor.enabled
            && serialized_data.len() > self.compressor.compression_threshold
        {
            let compressed = self.compress_data(&serialized_data)?;
            self.update_compression_stats(serialized_data.len(), compressed.len());
            compressed
        } else {
            serialized_data
        };

        let mut conn = self.connection.write().await;
        redis::cmd("SETEX")
            .arg(key)
            .arg(ttl_seconds)
            .arg(&final_data)
            .query_async::<()>(&mut *conn)
            .await?;
        drop(conn);

        let elapsed_us = start_time.elapsed().as_micros() as f64;
        self.update_serialization_time(elapsed_us);

        Ok(())
    }

    pub async fn delete_multiple(&mut self, keys: &[String]) -> Result<()> {
        if keys.is_empty() {
            return Ok(());
        }

        let mut conn = self.connection.write().await;
        let mut cmd = redis::cmd("DEL");
        for key in keys {
            cmd.arg(key);
        }
        cmd.query_async::<()>(&mut *conn).await?;

        Ok(())
    }

    pub async fn flush_all(&mut self) -> Result<()> {
        let mut conn = self.connection.write().await;
        redis::cmd("FLUSHDB").query_async::<()>(&mut *conn).await?;
        Ok(())
    }

    pub async fn get_keys_matching_pattern(&self, pattern: &str) -> Result<Vec<String>> {
        let mut conn = self.connection.write().await;
        let keys: Vec<String> = redis::cmd("KEYS")
            .arg(pattern)
            .query_async(&mut *conn)
            .await?;
        Ok(keys)
    }

    fn compress_data(&self, data: &str) -> Result<String> {
        // 简化的压缩实现，实际中可以使用更好的压缩算法
        use base64::{engine::general_purpose, Engine};
        Ok(general_purpose::STANDARD.encode(data.as_bytes()))
    }

    fn decompress_data(&self, compressed_data: &str) -> Result<String> {
        use base64::{engine::general_purpose, Engine};
        let decoded = general_purpose::STANDARD.decode(compressed_data)?;
        Ok(String::from_utf8(decoded)?)
    }

    fn update_serialization_time(&mut self, elapsed_us: f64) {
        let current_count = self.stats.hits + self.stats.misses;
        if current_count > 0 {
            self.stats.avg_serialization_time_us =
                (self.stats.avg_serialization_time_us * (current_count - 1) as f64 + elapsed_us)
                    / current_count as f64;
        }
    }

    fn update_compression_stats(&mut self, original_size: usize, compressed_size: usize) {
        let ratio = compressed_size as f64 / original_size as f64;
        if self.stats.compression_ratio == 0.0 {
            self.stats.compression_ratio = ratio;
        } else {
            self.stats.compression_ratio = (self.stats.compression_ratio + ratio) / 2.0;
        }
    }
}

impl CacheInvalidationManager {
    pub fn new() -> Self {
        Self {
            invalidation_rules: Vec::new(),
            event_listeners: Vec::new(),
            scheduled_tasks: HashMap::new(),
        }
    }

    pub fn add_rule(&mut self, rule: InvalidationRule) {
        self.invalidation_rules.push(rule);
    }

    pub fn add_listener(&mut self, listener: Box<dyn CacheInvalidationListener>) {
        self.event_listeners.push(listener);
    }

    pub async fn schedule_invalidation(&mut self, task: ScheduledInvalidationTask) {
        self.scheduled_tasks.insert(task.task_id.clone(), task);
    }

    pub async fn process_scheduled_tasks(&mut self) -> Result<()> {
        let now = Utc::now();
        let mut completed_tasks = Vec::new();

        for (task_id, task) in &self.scheduled_tasks {
            if now >= task.execute_at {
                // 执行失效任务
                tracing::info!("执行定时缓存失效任务: {} - {}", task_id, task.reason);
                completed_tasks.push(task_id.clone());
            }
        }

        // 移除已完成的任务
        for task_id in completed_tasks {
            self.scheduled_tasks.remove(&task_id);
        }

        Ok(())
    }
}

impl Default for CacheInvalidationManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use crate::ResultFusionStrategy;

    use super::*;

    #[test]
    fn test_cache_config_default() {
        let config = CacheConfig::default();
        assert_eq!(config.l1_max_entries, 1000);
        assert_eq!(config.l1_ttl_seconds, 300);
        assert_eq!(config.l2_ttl_seconds, 3600);
    }

    #[test]
    fn test_cache_entry_expiration() {
        let now = Utc::now();
        let cached_results = CachedSearchResults {
            results: HybridSearchResults {
                results: Vec::new(),
                total_count: 0,
                search_time_ms: 0,
                fusion_strategy: ResultFusionStrategy::WeightedLinearCombination,
                keyword_stats: None,
                vector_stats: None,
                fusion_stats: crate::services::FusionStats {
                    keyword_results_count: 0,
                    vector_results_count: 0,
                    overlap_results_count: 0,
                    fusion_time_ms: 0,
                    applied_weights: crate::services::FusionWeights {
                        keyword_weight: 0.5,
                        vector_weight: 0.5,
                        time_weight: 0.1,
                        quality_weight: 0.1,
                    },
                },
                metadata: HashMap::new(),
            },
            cache_metadata: CacheMetadata {
                query_hash: "test_hash".to_string(),
                quality_score: 0.8,
                cache_weight: 0.7,
                user_id: None,
                knowledge_base_id: None,
                context_fingerprint: "test_context".to_string(),
            },
        };

        let entry = CacheEntry {
            key: "test_key".to_string(),
            value: cached_results,
            created_at: now - chrono::Duration::seconds(600), // 10分钟前
            accessed_at: now,
            access_count: 1,
            ttl_seconds: 300, // 5分钟TTL
            cache_level: CacheLevel::L1Memory,
            is_compressed: false,
        };

        assert!(entry.is_expired());
    }

    #[test]
    fn test_invalidation_rule_types() {
        let rule = InvalidationRule {
            rule_id: "test_rule".to_string(),
            rule_type: InvalidationRuleType::TimeBased,
            pattern: "hybrid_search:*".to_string(),
            strategy: InvalidationStrategy::Immediate,
        };

        assert_eq!(rule.rule_type, InvalidationRuleType::TimeBased);
        assert_eq!(rule.strategy, InvalidationStrategy::Immediate);
    }
}
