use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use wisdom_vault_common::{
    db::next_id,
    time::{current_millis, to_datetime},
};

use crate::services::{
    result_fusion_algorithms::ResultFusionStrategy, KeywordSearchRequest, KeywordSearchResults,
    KeywordSearchService, VectorSearchRequest, VectorSearchResults, VectorSearchService,
};
use wisdom_vault_database::models::{Document, DocumentStatus};

/// 混合搜索请求
#[derive(Debug, Clone)]
pub struct HybridSearchRequest {
    /// 查询文本
    pub query_text: String,
    /// 知识库ID过滤
    pub knowledge_base_id: Option<String>,
    /// 结果数量限制
    pub limit: Option<u32>,
    /// 关键词搜索权重
    pub keyword_weight: Option<f64>,
    /// 向量搜索权重
    pub vector_weight: Option<f64>,
    /// 相似度阈值
    pub similarity_threshold: Option<f64>,
    /// 其他参数
    pub model_id: Option<String>,
    pub query_vector: Option<Vec<f32>>,
    pub text_weight: Option<f64>,
}

impl Default for HybridSearchRequest {
    fn default() -> Self {
        Self {
            query_text: String::new(),
            knowledge_base_id: None,
            limit: Some(10),
            keyword_weight: Some(0.5),
            vector_weight: Some(0.5),
            similarity_threshold: Some(0.7),
            model_id: None,
            query_vector: None,
            text_weight: Some(0.5),
        }
    }
}

/// 混合搜索配置
#[derive(Debug, Clone)]
pub struct HybridSearchConfig {
    /// 默认关键词搜索权重
    pub default_keyword_weight: f64,
    /// 默认向量搜索权重
    pub default_vector_weight: f64,
    /// 默认相似度阈值
    pub default_similarity_threshold: f64,
    /// 默认结果数量限制
    pub default_limit: u32,
    /// 最大结果数量限制
    pub max_limit: u32,
    /// 启用结果缓存
    pub enable_result_cache: bool,
    /// 缓存TTL（秒）
    pub cache_ttl_seconds: u64,
    /// 启用个性化推荐
    pub enable_personalization: bool,
    /// 最大并发搜索数
    pub max_concurrent_searches: usize,
    /// 结果融合策略
    pub default_fusion_strategy: ResultFusionStrategy,
}

impl Default for HybridSearchConfig {
    fn default() -> Self {
        Self {
            default_keyword_weight: 0.4,
            default_vector_weight: 0.6,
            default_similarity_threshold: 0.7,
            default_limit: 20,
            max_limit: 100,
            enable_result_cache: true,
            cache_ttl_seconds: 300, // 5分钟
            enable_personalization: true,
            max_concurrent_searches: 5,
            default_fusion_strategy: ResultFusionStrategy::WeightedLinearCombination,
        }
    }
}

/// 混合搜索结果项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HybridSearchResult {
    /// 文档信息
    pub document: Document,
    /// 融合后的综合得分
    pub final_score: f64,
    /// 关键词搜索得分
    pub keyword_score: Option<f64>,
    /// 向量搜索得分
    pub vector_score: Option<f64>,
    /// 搜索类型标识
    pub search_sources: Vec<SearchSource>,
    /// 匹配的关键词（来自关键词搜索）
    pub matched_terms: Vec<String>,
    /// 高亮片段
    pub highlights: Vec<String>,
    /// 相似度计算方式
    pub similarity_method: String,
    /// 使用的模型信息
    pub model_name: Option<String>,
    /// 排序因子详情
    pub ranking_factors: RankingFactors,
    /// 额外元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 搜索来源
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SearchSource {
    /// 关键词搜索
    Keyword,
    /// 向量搜索
    Vector,
    /// 混合搜索
    Hybrid,
}

/// 排序因子详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RankingFactors {
    /// 文本相关性得分
    pub text_relevance: f64,
    /// 语义相似度得分
    pub semantic_similarity: f64,
    /// 时间衰减因子
    pub time_decay: f64,
    /// 文档质量得分
    pub document_quality: f64,
    /// 用户偏好权重
    pub user_preference: f64,
    /// 流行度得分
    pub popularity_score: f64,
}

impl Default for RankingFactors {
    fn default() -> Self {
        Self {
            text_relevance: 0.0,
            semantic_similarity: 0.0,
            time_decay: 1.0,
            document_quality: 0.5,
            user_preference: 0.5,
            popularity_score: 0.0,
        }
    }
}

/// 混合搜索结果集
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HybridSearchResults {
    /// 搜索结果列表
    pub results: Vec<HybridSearchResult>,
    /// 总结果数量
    pub total_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 使用的融合策略
    pub fusion_strategy: ResultFusionStrategy,
    /// 关键词搜索统计
    pub keyword_stats: Option<KeywordSearchStats>,
    /// 向量搜索统计
    pub vector_stats: Option<VectorSearchStats>,
    /// 融合过程统计
    pub fusion_stats: FusionStats,
    /// 额外的搜索元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 关键词搜索统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeywordSearchStats {
    /// 结果数量
    pub result_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 匹配的查询词数量
    pub matched_terms_count: usize,
    /// 平均BM25得分
    pub avg_bm25_score: f64,
}

/// 向量搜索统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorSearchStats {
    /// 结果数量
    pub result_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 使用的模型名称
    pub model_name: String,
    /// 平均相似度得分
    pub avg_similarity_score: f64,
    /// 查询向量维度
    pub query_vector_dimension: usize,
}

/// 融合过程统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FusionStats {
    /// 关键词结果数量
    pub keyword_results_count: u64,
    /// 向量结果数量
    pub vector_results_count: u64,
    /// 重叠结果数量
    pub overlap_results_count: u64,
    /// 融合用时（毫秒）
    pub fusion_time_ms: u64,
    /// 应用的权重
    pub applied_weights: FusionWeights,
}

/// 融合权重
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FusionWeights {
    /// 关键词权重
    pub keyword_weight: f64,
    /// 向量权重
    pub vector_weight: f64,
    /// 时间权重
    pub time_weight: f64,
    /// 质量权重
    pub quality_weight: f64,
}

/// 混合搜索服务
pub struct HybridSearchService {
    /// 关键词搜索服务
    keyword_search_service: Arc<KeywordSearchService>,
    /// 向量搜索服务
    vector_search_service: Arc<VectorSearchService>,
    /// 服务配置
    config: HybridSearchConfig,
    /// 搜索性能统计
    performance_stats: Arc<RwLock<HybridSearchPerformanceStats>>,
    /// 结果缓存
    result_cache: Arc<RwLock<HashMap<String, (HybridSearchResults, std::time::Instant)>>>,
}

/// 混合搜索性能统计
#[derive(Debug, Clone, Default)]
pub struct HybridSearchPerformanceStats {
    /// 总搜索次数
    pub total_searches: u64,
    /// 成功搜索次数
    pub successful_searches: u64,
    /// 失败搜索次数
    pub failed_searches: u64,
    /// 平均搜索时间（毫秒）
    pub avg_search_time_ms: f64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 缓存未命中次数
    pub cache_misses: u64,
    /// 各融合策略使用次数
    pub fusion_strategy_usage: HashMap<String, u64>,
    /// 平均结果数量
    pub avg_result_count: f64,
}

impl HybridSearchService {
    /// 创建新的混合搜索服务
    pub fn new(
        keyword_search_service: Arc<KeywordSearchService>,
        vector_search_service: Arc<VectorSearchService>,
        config: HybridSearchConfig,
    ) -> Self {
        Self {
            keyword_search_service,
            vector_search_service,
            config,
            performance_stats: Arc::new(RwLock::new(HybridSearchPerformanceStats::default())),
            result_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 使用默认配置创建服务
    pub fn new_with_defaults(
        keyword_search_service: Arc<KeywordSearchService>,
        vector_search_service: Arc<VectorSearchService>,
    ) -> Self {
        Self::new(
            keyword_search_service,
            vector_search_service,
            HybridSearchConfig::default(),
        )
    }

    /// 执行混合搜索
    pub async fn hybrid_search(&self, request: HybridSearchRequest) -> Result<HybridSearchResults> {
        let start_time = std::time::Instant::now();
        let mut stats = self.performance_stats.write().await;
        stats.total_searches += 1;
        drop(stats);

        // 检查缓存
        if self.config.enable_result_cache {
            let cache_key = self.generate_cache_key(&request)?;
            if let Some(cached_result) = self.get_cached_result(&cache_key).await {
                self.update_cache_hit_stats().await;
                return Ok(cached_result);
            }
            self.update_cache_miss_stats().await;
        }

        // 并发执行关键词搜索和向量搜索
        let (keyword_results, vector_results) = self.execute_parallel_searches(&request).await?;

        // 融合搜索结果
        let fusion_start = std::time::Instant::now();
        let hybrid_results = self
            .fuse_search_results(
                keyword_results,
                vector_results,
                &request,
                start_time.elapsed().as_millis() as u64,
            )
            .await?;
        let fusion_time = fusion_start.elapsed().as_millis() as u64;

        // 更新融合统计信息
        let mut final_results = hybrid_results;
        final_results.fusion_stats.fusion_time_ms = fusion_time;
        final_results.search_time_ms = start_time.elapsed().as_millis() as u64;

        // 缓存结果
        if self.config.enable_result_cache {
            let cache_key = self.generate_cache_key(&request)?;
            self.cache_result(cache_key, final_results.clone()).await;
        }

        // 更新性能统计
        self.update_search_stats(
            start_time.elapsed().as_millis() as u64,
            final_results.results.len() as u64,
            true,
        )
        .await;

        Ok(final_results)
    }

    /// 并发执行关键词和向量搜索
    async fn execute_parallel_searches(
        &self,
        request: &HybridSearchRequest,
    ) -> Result<(Option<KeywordSearchResults>, Option<VectorSearchResults>)> {
        let keyword_future = self.execute_keyword_search(request);
        let vector_future = self.execute_vector_search(request);

        // 使用tokio::join!并发执行
        let (keyword_result, vector_result) = tokio::join!(keyword_future, vector_future);

        Ok((keyword_result.ok(), vector_result.ok()))
    }

    /// 执行关键词搜索
    async fn execute_keyword_search(
        &self,
        request: &HybridSearchRequest,
    ) -> Result<KeywordSearchResults> {
        let keyword_request = KeywordSearchRequest {
            query: request.query_text.clone(),
            knowledge_base_id: request.knowledge_base_id.clone(),
            limit: request.limit,
            offset: Some(0),
            min_score: None, // 使用默认最小得分
            status_filter: Some(vec![DocumentStatus::Indexed]),
            enable_query_expansion: Some(true),
        };

        self.keyword_search_service.search(keyword_request).await
    }

    /// 执行向量搜索
    async fn execute_vector_search(
        &self,
        request: &HybridSearchRequest,
    ) -> Result<VectorSearchResults> {
        let vector_request = VectorSearchRequest {
            query_vector: request.query_vector.clone(),
            query_text: Some(request.query_text.clone()),
            model_id: request.model_id.clone(),
            similarity_threshold: request.similarity_threshold,
            limit: request.limit,
            knowledge_base_id: request.knowledge_base_id.clone(),
            model_name_filter: None,
            embedding_type_filter: None,
        };

        self.vector_search_service
            .vector_similarity_search(vector_request)
            .await
    }

    /// 融合搜索结果
    async fn fuse_search_results(
        &self,
        keyword_results: Option<KeywordSearchResults>,
        vector_results: Option<VectorSearchResults>,
        request: &HybridSearchRequest,
        total_search_time: u64,
    ) -> Result<HybridSearchResults> {
        // 确定融合策略
        let fusion_strategy = ResultFusionStrategy::WeightedLinearCombination; // 默认策略

        // 收集所有文档结果
        let mut document_scores: HashMap<String, HybridSearchResult> = HashMap::new();

        // 处理关键词搜索结果
        let keyword_stats = if let Some(ref kw_results) = keyword_results {
            self.process_keyword_results(&mut document_scores, kw_results, request)
                .await;
            Some(KeywordSearchStats {
                result_count: kw_results.results.len() as u64,
                search_time_ms: kw_results.search_time_ms,
                matched_terms_count: kw_results.query_stats.processed_terms.len(),
                avg_bm25_score: kw_results.results.iter().map(|r| r.score).sum::<f64>()
                    / kw_results.results.len().max(1) as f64,
            })
        } else {
            None
        };

        // 处理向量搜索结果
        let vector_stats = if let Some(ref vec_results) = vector_results {
            self.process_vector_results(&mut document_scores, vec_results, request)
                .await;
            Some(VectorSearchStats {
                result_count: vec_results.results.len() as u64,
                search_time_ms: vec_results.search_time_ms,
                model_name: vec_results
                    .results
                    .first()
                    .map(|r| r.embedding.model_name.clone())
                    .unwrap_or_else(|| "unknown".to_string()),
                avg_similarity_score: vec_results.results.iter().map(|r| r.score).sum::<f64>()
                    / vec_results.results.len().max(1) as f64,
                query_vector_dimension: vec_results
                    .results
                    .first()
                    .map(|r| r.embedding.embedding.len())
                    .unwrap_or(0),
            })
        } else {
            None
        };

        // 应用融合策略
        let mut fused_results: Vec<HybridSearchResult> = document_scores.into_values().collect();
        self.apply_fusion_strategy(&mut fused_results, &fusion_strategy, request)
            .await?;

        // 应用最终排序和限制
        fused_results.sort_by(|a, b| {
            b.final_score
                .partial_cmp(&a.final_score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        let limit = request.limit.unwrap_or(self.config.default_limit) as usize;
        fused_results.truncate(limit);

        // 计算重叠统计
        let keyword_count = keyword_results
            .as_ref()
            .map(|r| r.results.len() as u64)
            .unwrap_or(0);
        let vector_count = vector_results
            .as_ref()
            .map(|r| r.results.len() as u64)
            .unwrap_or(0);
        let overlap_count = self.calculate_overlap_count(&keyword_results, &vector_results);

        // 构建融合统计
        let fusion_stats = FusionStats {
            keyword_results_count: keyword_count,
            vector_results_count: vector_count,
            overlap_results_count: overlap_count,
            fusion_time_ms: 0, // 将在调用者处设置
            applied_weights: FusionWeights {
                keyword_weight: request
                    .text_weight
                    .unwrap_or(self.config.default_keyword_weight),
                vector_weight: request
                    .vector_weight
                    .unwrap_or(self.config.default_vector_weight),
                time_weight: 0.1,
                quality_weight: 0.1,
            },
        };

        Ok(HybridSearchResults {
            results: fused_results,
            total_count: (keyword_count + vector_count - overlap_count),
            search_time_ms: total_search_time,
            fusion_strategy,
            keyword_stats,
            vector_stats,
            fusion_stats,
            metadata: HashMap::new(),
        })
    }

    /// 处理关键词搜索结果
    async fn process_keyword_results(
        &self,
        document_scores: &mut HashMap<String, HybridSearchResult>,
        keyword_results: &KeywordSearchResults,
        _request: &HybridSearchRequest,
    ) {
        for kw_result in &keyword_results.results {
            let doc_id = kw_result.document.id.clone();

            let hybrid_result = HybridSearchResult {
                document: kw_result.document.clone(),
                final_score: 0.0, // 将在融合时计算
                keyword_score: Some(kw_result.score),
                vector_score: None,
                search_sources: vec![SearchSource::Keyword],
                matched_terms: kw_result.matched_terms.clone(),
                highlights: kw_result.highlights.clone(),
                similarity_method: "bm25".to_string(),
                model_name: None,
                ranking_factors: RankingFactors {
                    text_relevance: kw_result.score,
                    semantic_similarity: 0.0,
                    time_decay: self.calculate_time_decay(&kw_result.document),
                    document_quality: self.calculate_document_quality(&kw_result.document),
                    user_preference: self.calculate_user_preference(&kw_result.document, _request).await,
                    popularity_score: self.calculate_popularity_score(&kw_result.document).await,
                },
                metadata: HashMap::new(),
            };

            document_scores.insert(doc_id, hybrid_result);
        }
    }

    /// 处理向量搜索结果
    async fn process_vector_results(
        &self,
        document_scores: &mut HashMap<String, HybridSearchResult>,
        vector_results: &VectorSearchResults,
        request: &HybridSearchRequest,
    ) {
        for vec_result in &vector_results.results {
            let doc_id = vec_result.embedding.chunk_id.clone(); // 使用chunk_id作为文档标识

            if let Some(existing_result) = document_scores.get_mut(&doc_id) {
                // 文档已存在，更新向量搜索信息
                existing_result.vector_score = Some(vec_result.score);
                existing_result.search_sources.push(SearchSource::Vector);
                existing_result.similarity_method = "cosine".to_string();
                existing_result.model_name = Some(vec_result.embedding.model_name.clone());
                existing_result.ranking_factors.semantic_similarity = vec_result.score;
            } else {
                // 新文档，创建混合结果
                // 注意：这里需要从chunk_id获取完整的文档信息
                // 简化实现，使用虚拟文档信息
                let virtual_document = Document {
                    id: doc_id.clone(),
                    knowledge_base_id: request.knowledge_base_id.clone().unwrap_or_else(next_id),
                    title: format!("向量搜索结果 {doc_id}"),
                    summary: None,
                    content: String::new(), // 需要从chunk中获取
                    file_type: "unknown".to_string(),
                    file_size: 0,
                    file_path: None,
                    original_filename: None,
                    mime_type: "text/plain".to_string(),
                    language: None,
                    metadata: wisdom_vault_database::models::DocumentMetadata {
                        author: None,
                        subject: None,
                        creator: None,
                        producer: None,
                        keywords: Vec::new(),
                        source_url: None,
                        page_count: None,
                        word_count: None,
                        character_count: None,
                        creation_date: None,
                        modification_date: None,
                        content_type: "text/plain".to_string(),
                        content_encoding: None,
                        content_language: None,
                        custom_fields: serde_json::Value::Object(serde_json::Map::new()),
                    },
                    processing_metadata:
                        wisdom_vault_database::models::DocumentProcessingMetadata {
                            extraction_method: "hybrid_search".to_string(),
                            extraction_quality: 1.0,
                            processing_time_ms: 0,
                            parsing_errors: Vec::new(),
                            parsing_warnings: Vec::new(),
                            file_checksum: String::new(),
                            structured_content: None,
                            processing_attempts: 1,
                            last_processing_attempt: Some(current_millis()),
                        },
                    status: DocumentStatus::Indexed,
                    uploaded_by: next_id(), // 虚拟用户ID
                    indexed_at: Some(current_millis()),
                    created_at: current_millis(),
                    updated_at: current_millis(),
                };

                let hybrid_result = HybridSearchResult {
                    document: virtual_document,
                    final_score: 0.0, // 将在融合时计算
                    keyword_score: None,
                    vector_score: Some(vec_result.score),
                    search_sources: vec![SearchSource::Vector],
                    matched_terms: Vec::new(),
                    highlights: Vec::new(),
                    similarity_method: "cosine".to_string(),
                    model_name: Some(vec_result.embedding.model_name.clone()),
                    ranking_factors: RankingFactors {
                        text_relevance: 0.0,
                        semantic_similarity: vec_result.score,
                        time_decay: self.calculate_time_decay_from_timestamp(vec_result.embedding.created_at as u64),
                        document_quality: 0.5, // 向量搜索结果的文档质量评估需要更多上下文
                        user_preference: self.calculate_user_preference_from_vector(vec_result, request).await,
                        popularity_score: self.calculate_popularity_score_from_vector(vec_result).await,
                    },
                    metadata: vec_result.metadata.clone(),
                };

                document_scores.insert(doc_id, hybrid_result);
            }
        }
    }

    /// 应用融合策略
    async fn apply_fusion_strategy(
        &self,
        results: &mut [HybridSearchResult],
        strategy: &ResultFusionStrategy,
        request: &HybridSearchRequest,
    ) -> Result<()> {
        let keyword_weight = request
            .text_weight
            .unwrap_or(self.config.default_keyword_weight);
        let vector_weight = request
            .vector_weight
            .unwrap_or(self.config.default_vector_weight);

        match strategy {
            ResultFusionStrategy::WeightedLinearCombination => {
                self.apply_weighted_linear_combination(results, keyword_weight, vector_weight)
                    .await;
            }
            ResultFusionStrategy::ReciprocalRankFusion => {
                self.apply_reciprocal_rank_fusion(results).await;
            }
            ResultFusionStrategy::NormalizedScoreFusion => {
                self.apply_normalized_score_fusion(results, keyword_weight, vector_weight)
                    .await;
            }
            ResultFusionStrategy::HybridReranking => {
                self.apply_hybrid_reranking(results, keyword_weight, vector_weight)
                    .await;
            }
            ResultFusionStrategy::DynamicWeightAdjustment => {
                self.apply_dynamic_weight_adjustment(results, request).await;
            }
        }

        Ok(())
    }

    /// 应用加权线性组合
    async fn apply_weighted_linear_combination(
        &self,
        results: &mut [HybridSearchResult],
        keyword_weight: f64,
        vector_weight: f64,
    ) {
        for result in results.iter_mut() {
            let keyword_score = result.keyword_score.unwrap_or(0.0);
            let vector_score = result.vector_score.unwrap_or(0.0);

            result.final_score = keyword_score * keyword_weight + vector_score * vector_weight;

            // 添加其他排序因子
            result.final_score *= result.ranking_factors.time_decay;
            result.final_score *= 1.0 + result.ranking_factors.document_quality * 0.1;
            result.final_score *= 1.0 + result.ranking_factors.user_preference * 0.05;
        }
    }

    /// 应用排序融合（RRF）
    async fn apply_reciprocal_rank_fusion(&self, results: &mut [HybridSearchResult]) {
        // 先按关键词得分排序并分配排名
        let mut keyword_ranks: HashMap<String, f64> = HashMap::new();
        let mut keyword_sorted: Vec<_> = results.iter().collect();
        keyword_sorted.sort_by(|a, b| {
            b.keyword_score
                .unwrap_or(0.0)
                .partial_cmp(&a.keyword_score.unwrap_or(0.0))
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        for (rank, result) in keyword_sorted.iter().enumerate() {
            if result.keyword_score.is_some() {
                keyword_ranks.insert(result.document.id.clone(), 1.0 / (60.0 + rank as f64 + 1.0));
            }
        }

        // 再按向量得分排序并分配排名
        let mut vector_ranks: HashMap<String, f64> = HashMap::new();
        let mut vector_sorted: Vec<_> = results.iter().collect();
        vector_sorted.sort_by(|a, b| {
            b.vector_score
                .unwrap_or(0.0)
                .partial_cmp(&a.vector_score.unwrap_or(0.0))
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        for (rank, result) in vector_sorted.iter().enumerate() {
            if result.vector_score.is_some() {
                vector_ranks.insert(result.document.id.clone(), 1.0 / (60.0 + rank as f64 + 1.0));
            }
        }

        // 计算RRF得分
        for result in results.iter_mut() {
            let keyword_rrf = keyword_ranks.get(&result.document.id).unwrap_or(&0.0);
            let vector_rrf = vector_ranks.get(&result.document.id).unwrap_or(&0.0);
            result.final_score = keyword_rrf + vector_rrf;
        }
    }

    /// 应用分数归一化融合
    async fn apply_normalized_score_fusion(
        &self,
        results: &mut [HybridSearchResult],
        keyword_weight: f64,
        vector_weight: f64,
    ) {
        // 收集所有得分用于归一化
        let keyword_scores: Vec<f64> = results.iter().filter_map(|r| r.keyword_score).collect();
        let vector_scores: Vec<f64> = results.iter().filter_map(|r| r.vector_score).collect();

        // 计算归一化参数
        let keyword_max = keyword_scores.iter().fold(0.0f64, |a, &b| a.max(b));
        let keyword_min = keyword_scores.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let vector_max = vector_scores.iter().fold(0.0f64, |a, &b| a.max(b));
        let vector_min = vector_scores.iter().fold(f64::INFINITY, |a, &b| a.min(b));

        // 应用归一化融合
        for result in results.iter_mut() {
            let normalized_keyword = if keyword_max > keyword_min {
                result
                    .keyword_score
                    .map(|score| (score - keyword_min) / (keyword_max - keyword_min))
                    .unwrap_or(0.0)
            } else {
                result.keyword_score.unwrap_or(0.0)
            };

            let normalized_vector = if vector_max > vector_min {
                result
                    .vector_score
                    .map(|score| (score - vector_min) / (vector_max - vector_min))
                    .unwrap_or(0.0)
            } else {
                result.vector_score.unwrap_or(0.0)
            };

            result.final_score =
                normalized_keyword * keyword_weight + normalized_vector * vector_weight;
        }
    }

    /// 应用混合重排序
    async fn apply_hybrid_reranking(
        &self,
        results: &mut [HybridSearchResult],
        keyword_weight: f64,
        vector_weight: f64,
    ) {
        // 先进行基础加权组合
        self.apply_weighted_linear_combination(results, keyword_weight, vector_weight)
            .await;

        // 计算多样性惩罚
        let mut diversity_penalties = Vec::new();
        for i in 0..results.len() {
            let diversity_penalty = self.calculate_diversity_penalty(&results[i], results).await;
            diversity_penalties.push(diversity_penalty);
        }

        // 应用重排序算法
        for (i, result) in results.iter_mut().enumerate() {
            // 多样性惩罚：如果结果过于相似，降低得分
            result.final_score *= 1.0 - diversity_penalties[i];

            // 查询意图匹配：根据查询意图调整得分
            let intent_boost = self.calculate_intent_matching_boost(result).await;
            result.final_score *= 1.0 + intent_boost;

            // 新鲜度加权：较新的文档得分略高
            let freshness_boost = self.calculate_freshness_boost(&result.document);
            result.final_score *= 1.0 + freshness_boost;
        }
    }

    /// 应用动态权重调整
    async fn apply_dynamic_weight_adjustment(
        &self,
        results: &mut [HybridSearchResult],
        request: &HybridSearchRequest,
    ) {
        // 根据查询特征动态调整权重
        let query_length = request.query_text.len();
        let query_complexity = self.calculate_query_complexity(&request.query_text);

        // 短查询偏向关键词搜索，长查询偏向语义搜索
        let dynamic_keyword_weight = if query_length < 20 {
            0.7 // 短查询更依赖关键词匹配
        } else if query_length > 100 {
            0.3 // 长查询更依赖语义理解
        } else {
            0.5 // 中等长度查询平衡
        };

        let dynamic_vector_weight = 1.0 - dynamic_keyword_weight;

        // 根据查询复杂度进一步调整
        let complexity_adjustment = (query_complexity - 0.5) * 0.2;
        let final_keyword_weight = dynamic_keyword_weight - complexity_adjustment;
        let final_vector_weight = dynamic_vector_weight + complexity_adjustment;

        // 应用动态权重
        self.apply_weighted_linear_combination(results, final_keyword_weight, final_vector_weight)
            .await;
    }

    /// 计算时间衰减因子
    fn calculate_time_decay(&self, document: &Document) -> f64 {
        let now = chrono::Utc::now();
        let doc_created_at = to_datetime(document.created_at);
        let doc_age = now.signed_duration_since(doc_created_at);
        let days_old = doc_age.num_days() as f64;

        // 使用指数衰减，半衰期为180天
        (-days_old / 180.0).exp()
    }

    /// 计算文档质量得分
    fn calculate_document_quality(&self, document: &Document) -> f64 {
        let mut quality_score: f64 = 0.5; // 基础得分

        // 基于文档长度
        let content_length = document.content.len() as f64;
        if content_length > 100.0 && content_length < 10000.0 {
            quality_score += 0.2; // 适中长度文档质量较高
        }

        // 基于标题质量
        if !document.title.is_empty() && document.title.len() > 5 {
            quality_score += 0.1;
        }

        // 基于是否有摘要
        if document.summary.is_some() {
            quality_score += 0.1;
        }

        // 基于文件类型
        match document.file_type.as_str() {
            "pdf" | "docx" | "doc" => quality_score += 0.1,
            "txt" | "md" => quality_score += 0.05,
            _ => {}
        }

        quality_score.clamp(0.0_f64, 1.0_f64)
    }

    /// 计算多样性惩罚
    async fn calculate_diversity_penalty(
        &self,
        target: &HybridSearchResult,
        all_results: &[HybridSearchResult],
    ) -> f64 {
        let mut max_similarity: f64 = 0.0;

        for other in all_results {
            if other.document.id != target.document.id {
                let title_similarity =
                    self.calculate_text_similarity(&target.document.title, &other.document.title);
                max_similarity = max_similarity.max(title_similarity);
            }
        }

        // 如果与其他结果过于相似，应用惩罚
        if max_similarity > 0.8 {
            0.3 // 30%的惩罚
        } else if max_similarity > 0.6 {
            0.1 // 10%的惩罚
        } else {
            0.0 // 无惩罚
        }
    }

    /// 计算意图匹配提升
    async fn calculate_intent_matching_boost(&self, result: &HybridSearchResult) -> f64 {
        // 简化实现：根据匹配的关键词数量提升
        let matched_terms_count = result.matched_terms.len() as f64;
        if matched_terms_count > 3.0 {
            0.2 // 20%提升
        } else if matched_terms_count > 1.0 {
            0.1 // 10%提升
        } else {
            0.0 // 无提升
        }
    }

    /// 计算新鲜度提升
    fn calculate_freshness_boost(&self, document: &Document) -> f64 {
        let now = chrono::Utc::now();
        let doc_age = now.signed_duration_since(to_datetime(document.created_at));
        let days_old = doc_age.num_days() as f64;

        if days_old < 7.0 {
            0.1 // 一周内的文档10%提升
        } else if days_old < 30.0 {
            0.05 // 一个月内的文档5%提升
        } else {
            0.0 // 无提升
        }
    }

    /// 计算查询复杂度
    fn calculate_query_complexity(&self, query: &str) -> f64 {
        let word_count = query.split_whitespace().count() as f64;
        let char_count = query.len() as f64;
        let unique_words = query
            .split_whitespace()
            .collect::<std::collections::HashSet<_>>()
            .len() as f64;

        // 综合考虑词数、字符数和词汇多样性
        let complexity = (word_count / 20.0).min(1.0) * 0.4
            + (char_count / 200.0).min(1.0) * 0.3
            + (unique_words / word_count.max(1.0)) * 0.3;

        complexity.clamp(0.0_f64, 1.0_f64)
    }

    /// 计算文本相似度
    fn calculate_text_similarity(&self, text1: &str, text2: &str) -> f64 {
        // 简化的Jaccard相似度计算
        let words1: std::collections::HashSet<&str> = text1.split_whitespace().collect();
        let words2: std::collections::HashSet<&str> = text2.split_whitespace().collect();

        let intersection = words1.intersection(&words2).count() as f64;
        let union = words1.union(&words2).count() as f64;

        if union > 0.0 {
            intersection / union
        } else {
            0.0
        }
    }

    /// 计算重叠结果数量
    fn calculate_overlap_count(
        &self,
        keyword_results: &Option<KeywordSearchResults>,
        vector_results: &Option<VectorSearchResults>,
    ) -> u64 {
        if let (Some(kw_results), Some(vec_results)) = (keyword_results, vector_results) {
            let keyword_ids: std::collections::HashSet<String> = kw_results
                .results
                .iter()
                .map(|r| r.document.id.clone())
                .collect();
            let vector_ids: std::collections::HashSet<String> = vec_results
                .results
                .iter()
                .map(|r| r.embedding.chunk_id.clone())
                .collect();

            keyword_ids.intersection(&vector_ids).count() as u64
        } else {
            0
        }
    }

    /// 生成缓存键
    fn generate_cache_key(&self, request: &HybridSearchRequest) -> Result<String> {
        use sha2::{Digest, Sha256};

        let key_data = serde_json::json!({
            "query_text": request.query_text,
            "query_vector": request.query_vector,
            "model_id": request.model_id,
            "knowledge_base_id": request.knowledge_base_id,
            "vector_weight": request.vector_weight,
            "text_weight": request.text_weight,
            "similarity_threshold": request.similarity_threshold,
            "limit": request.limit,
            "fusion_strategy": "default", // 使用默认字符串，因为request结构体中没有此字段
        });

        let hash = Sha256::digest(key_data.to_string());
        Ok(format!("hybrid_search:{}", hex::encode(hash)))
    }

    /// 获取缓存结果
    async fn get_cached_result(&self, cache_key: &str) -> Option<HybridSearchResults> {
        let cache = self.result_cache.read().await;
        if let Some((result, timestamp)) = cache.get(cache_key) {
            if timestamp.elapsed().as_secs() <= self.config.cache_ttl_seconds {
                return Some(result.clone());
            }
        }
        None
    }

    /// 缓存结果
    async fn cache_result(&self, cache_key: String, result: HybridSearchResults) {
        let mut cache = self.result_cache.write().await;

        // 限制缓存大小
        if cache.len() >= 1000 {
            // 移除最旧的条目
            if let Some(oldest_key) = cache
                .iter()
                .min_by_key(|(_, (_, timestamp))| timestamp)
                .map(|(k, _)| k.clone())
            {
                cache.remove(&oldest_key);
            }
        }

        cache.insert(cache_key, (result, std::time::Instant::now()));
    }

    /// 更新搜索统计
    async fn update_search_stats(&self, search_time_ms: u64, result_count: u64, success: bool) {
        let mut stats = self.performance_stats.write().await;

        if success {
            stats.successful_searches += 1;
        } else {
            stats.failed_searches += 1;
        }

        // 更新平均搜索时间
        let total_searches = stats.successful_searches + stats.failed_searches;
        stats.avg_search_time_ms = (stats.avg_search_time_ms * (total_searches - 1) as f64
            + search_time_ms as f64)
            / total_searches as f64;

        // 更新平均结果数量
        stats.avg_result_count = (stats.avg_result_count * (stats.successful_searches - 1) as f64
            + result_count as f64)
            / stats.successful_searches as f64;
    }

    /// 更新缓存命中统计
    async fn update_cache_hit_stats(&self) {
        let mut stats = self.performance_stats.write().await;
        stats.cache_hits += 1;
    }

    /// 更新缓存未命中统计
    async fn update_cache_miss_stats(&self) {
        let mut stats = self.performance_stats.write().await;
        stats.cache_misses += 1;
    }

    /// 获取性能统计
    pub async fn get_performance_stats(&self) -> HybridSearchPerformanceStats {
        self.performance_stats.read().await.clone()
    }

    /// 清理缓存
    pub async fn clear_cache(&self) {
        let mut cache = self.result_cache.write().await;
        cache.clear();
        tracing::info!("混合搜索缓存已清理");
    }

    /// 配置更新
    pub async fn update_config(&mut self, new_config: HybridSearchConfig) {
        self.config = new_config;
        tracing::info!("混合搜索配置已更新");
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &HybridSearchConfig {
        &self.config
    }

    /// 计算用户偏好得分
    async fn calculate_user_preference(&self, document: &Document, _request: &HybridSearchRequest) -> f64 {
        // 简化实现：基于文档的文件类型、创建时间和内容特征
        let mut preference_score: f64 = 0.5; // 基础分数
        
        // 基于文件类型偏好（可以从用户历史行为中学习）
        match document.file_type.as_str() {
            "pdf" | "docx" => preference_score += 0.2, // 假设用户偏好文档类型
            "txt" | "md" => preference_score += 0.1,   // 中等偏好文本文件
            "ppt" | "pptx" => preference_score += 0.15, // 偏好演示文件
            _ => {} // 其他类型保持基础分数
        }
        
        // 基于文档长度偏好（用户可能偏好适中长度的文档）
        let content_length = document.content.len();
        if content_length > 500 && content_length < 5000 {
            preference_score += 0.1; // 适中长度加分
        } else if content_length > 10000 {
            preference_score -= 0.1; // 过长文档可能降低偏好
        }
        
        // 基于语言偏好（简化实现）
        if let Some(ref language) = document.language {
            if language == "zh" || language == "en" {
                preference_score += 0.1; // 主要语言加分
            }
        }
        
        // 基于关键词匹配用户兴趣（简化实现，实际应该从用户画像中获取）
        let user_interest_keywords = vec!["技术", "AI", "机器学习", "深度学习", "算法"];
        let content_lower = document.content.to_lowercase();
        let title_lower = document.title.to_lowercase();
        
        for keyword in &user_interest_keywords {
            if content_lower.contains(&keyword.to_lowercase()) || title_lower.contains(&keyword.to_lowercase()) {
                preference_score += 0.05; // 每个匹配的兴趣关键词加分
            }
        }
        
        preference_score.clamp(0.0, 1.0)
    }

    /// 计算文档流行度得分
    async fn calculate_popularity_score(&self, document: &Document) -> f64 {
        let mut popularity_score = 0.0;
        
        // 基于文档年龄的流行度衰减
        let time_decay = self.calculate_time_decay(document);
        popularity_score += time_decay * 0.3; // 较新的文档流行度更高
        
        // 基于文档质量的流行度提升
        let quality_score = self.calculate_document_quality(document);
        popularity_score += quality_score * 0.4; // 高质量文档更流行
        
        // 基于文档大小的流行度评估（中等大小的文档通常更受欢迎）
        let size_score = self.calculate_size_popularity_factor(document);
        popularity_score += size_score * 0.2;
        
        // 基于文件类型的流行度（某些类型可能更受欢迎）
        let type_popularity = self.calculate_type_popularity_factor(&document.file_type);
        popularity_score += type_popularity * 0.1;
        
        popularity_score.clamp(0.0_f64, 1.0_f64)
    }

    /// 从向量搜索结果计算用户偏好
    async fn calculate_user_preference_from_vector(&self, _vec_result: &crate::services::VectorSearchResult, _request: &HybridSearchRequest) -> f64 {
        // 简化实现：向量搜索结果的用户偏好计算
        // 实际应该基于向量相似度和用户历史偏好向量
        0.6 // 向量搜索通常表明较强的语义相关性，给予较高的偏好分数
    }

    /// 从向量搜索结果计算流行度
    async fn calculate_popularity_score_from_vector(&self, _vec_result: &crate::services::VectorSearchResult) -> f64 {
        // 简化实现：基于向量搜索结果计算流行度
        // 实际应该考虑向量空间中的文档密度、检索频率等
        0.4 // 给予中等流行度分数
    }

    /// 从时间戳计算时间衰减
    fn calculate_time_decay_from_timestamp(&self, timestamp: u64) -> f64 {
        let now = chrono::Utc::now();
        let doc_created_at = to_datetime(timestamp as i64);
        let doc_age = now.signed_duration_since(doc_created_at);
        let days_old = doc_age.num_days() as f64;

        // 使用指数衰减，半衰期为180天
        (-days_old / 180.0).exp()
    }

    /// 计算文档大小对流行度的影响因子
    fn calculate_size_popularity_factor(&self, document: &Document) -> f64 {
        let content_length = document.content.len() as f64;
        
        if content_length < 100.0 {
            0.2 // 内容太少，流行度较低
        } else if content_length > 100.0 && content_length < 1000.0 {
            0.8 // 适中内容，流行度较高
        } else if content_length < 5000.0 {
            0.6 // 较长内容，流行度中等
        } else {
            0.3 // 内容过长，流行度较低
        }
    }

    /// 计算文件类型对流行度的影响因子
    fn calculate_type_popularity_factor(&self, file_type: &str) -> f64 {
        match file_type.to_lowercase().as_str() {
            "pdf" => 0.8,     // PDF文档通常很受欢迎
            "docx" | "doc" => 0.7, // Word文档也很受欢迎
            "ppt" | "pptx" => 0.6, // 演示文稿中等受欢迎
            "txt" | "md" => 0.5,   // 纯文本文件中等
            "xlsx" | "xls" => 0.4, // 表格文件较少使用
            "jpg" | "png" | "gif" => 0.3, // 图片文件受欢迎程度较低
            _ => 0.2, // 其他文件类型流行度较低
        }
    }

    /// 高级用户偏好计算（基于用户历史行为）
    pub async fn calculate_advanced_user_preference(
        &self,
        document: &Document,
        user_id: Option<&str>,
        user_search_history: &[String],
        user_document_interactions: &[(String, f64)], // (document_id, interaction_score)
    ) -> f64 {
        let mut preference_score: f64 = 0.5;

        // 基于搜索历史计算偏好
        if !user_search_history.is_empty() {
            let history_match_score = self.calculate_search_history_match(document, user_search_history);
            preference_score += history_match_score * 0.3;
        }

        // 基于文档交互历史计算偏好
        if !user_document_interactions.is_empty() {
            let interaction_score = self.calculate_interaction_preference(document, user_document_interactions);
            preference_score += interaction_score * 0.4;
        }

        // 基于用户画像调整偏好
        if let Some(_uid) = user_id {
            // 这里可以根据用户ID获取用户画像并调整偏好
            // 简化实现：假设从用户画像中获取偏好调整因子
            let user_profile_adjustment = 0.1; // 从用户画像数据库获取
            preference_score += user_profile_adjustment;
        }

        preference_score.clamp(0.0, 1.0)
    }

    /// 计算搜索历史匹配度
    fn calculate_search_history_match(&self, document: &Document, search_history: &[String]) -> f64 {
        if search_history.is_empty() {
            return 0.0;
        }

        let mut total_match_score = 0.0;
        let document_text = format!("{} {}", document.title, document.content).to_lowercase();

        for query in search_history {
            let query_lower = query.to_lowercase();
            let query_words: Vec<&str> = query_lower.split_whitespace().collect();
            
            let mut query_match_score = 0.0;
            for word in query_words {
                if document_text.contains(word) {
                    query_match_score += 1.0;
                }
            }
            
            total_match_score += query_match_score / query.split_whitespace().count() as f64;
        }

        (total_match_score / search_history.len() as f64).min(1.0)
    }

    /// 计算交互偏好得分
    fn calculate_interaction_preference(&self, document: &Document, interactions: &[(String, f64)]) -> f64 {
        // 寻找与当前文档相似的历史交互文档
        let mut preference_score = 0.0;
        let mut similarity_count = 0;

        for (interacted_doc_id, interaction_score) in interactions {
            // 简化实现：基于文档ID相似性（实际应该基于文档内容相似性）
            if interacted_doc_id.len() == document.id.len() {
                // 假设ID长度相同表示相似类型的文档
                preference_score += interaction_score * 0.5;
                similarity_count += 1;
            }
            
            // 基于文档标题相似性
            let title_similarity = self.calculate_text_similarity(&document.title, &format!("doc_{}", interacted_doc_id));
            if title_similarity > 0.3 {
                preference_score += interaction_score * title_similarity;
                similarity_count += 1;
            }
        }

        if similarity_count > 0 {
            preference_score / similarity_count as f64
        } else {
            0.0
        }
    }

    /// 动态流行度计算（基于实时统计）
    pub async fn calculate_dynamic_popularity(
        &self,
        document: &Document,
        recent_view_count: i64,
        recent_download_count: i64,
        recent_share_count: i64,
        global_avg_views: f64,
    ) -> f64 {
        let mut popularity_score = 0.0;

        // 基于最近浏览量
        let view_popularity = if global_avg_views > 0.0 {
            (recent_view_count as f64 / global_avg_views).min(2.0) * 0.4
        } else {
            (recent_view_count as f64 / 10.0).min(1.0) * 0.4
        };
        popularity_score += view_popularity;

        // 基于下载量
        let download_popularity = (recent_download_count as f64 / 5.0).min(1.0) * 0.3;
        popularity_score += download_popularity;

        // 基于分享量（分享通常表明更高的价值）
        let share_popularity = (recent_share_count as f64 / 2.0).min(1.0) * 0.3;
        popularity_score += share_popularity;

        // 时间衰减因子
        let time_decay = self.calculate_time_decay(document);
        popularity_score *= time_decay;

        popularity_score.clamp(0.0_f64, 1.0_f64)
    }

    /// 个性化时间衰减计算
    pub fn calculate_personalized_time_decay(&self, document: &Document, user_time_preference: f64) -> f64 {
        let base_decay = self.calculate_time_decay(document);
        
        // user_time_preference: 0.0 = 偏好旧文档, 1.0 = 偏好新文档
        if user_time_preference < 0.5 {
            // 用户偏好旧文档，减少时间衰减的影响
            let decay_reduction = (0.5 - user_time_preference) * 2.0; // 0.0 到 1.0
            base_decay + (1.0 - base_decay) * decay_reduction * 0.5
        } else {
            // 用户偏好新文档，增强时间衰减的影响
            let decay_enhancement = (user_time_preference - 0.5) * 2.0; // 0.0 到 1.0
            base_decay * (1.0 + decay_enhancement * 0.5)
        }.clamp(0.0_f64, 1.0_f64)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hybrid_search_config_default() {
        let config = HybridSearchConfig::default();
        assert_eq!(config.default_keyword_weight, 0.4);
        assert_eq!(config.default_vector_weight, 0.6);
        assert_eq!(config.default_limit, 20);
    }

    #[test]
    fn test_ranking_factors_default() {
        let factors = RankingFactors::default();
        assert_eq!(factors.time_decay, 1.0);
        assert_eq!(factors.document_quality, 0.5);
        assert_eq!(factors.user_preference, 0.5);
    }

    #[test]
    fn test_fusion_strategy_serialization() {
        let strategy = ResultFusionStrategy::WeightedLinearCombination;
        let serialized = serde_json::to_string(&strategy).unwrap();
        let deserialized: ResultFusionStrategy = serde_json::from_str(&serialized).unwrap();
        assert_eq!(strategy, deserialized);
    }
}
