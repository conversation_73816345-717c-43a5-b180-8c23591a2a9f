use anyhow::Result;
use chrono::Duration;
use std::{
    cmp::Ordering,
    collections::{<PERSON>aryH<PERSON>p, HashMap, VecDeque},
    sync::Arc,
};
use tokio::{
    sync::{mpsc, Notify, RwLock, Semaphore},
    time::{interval, Instant},
};
use wisdom_vault_common::time::{current_millis, now, to_datetime};

use wisdom_vault_database::models::{
    EmbeddingTask, EmbeddingTaskStatus, EmbeddingTaskType, TaskPriority,
};

use crate::services::{
    BatchDocumentVectorization, EmbeddingModelManager, IncrementalVectorUpdate,
    VectorizationService,
};

/// 任务调度器配置
#[derive(Debug, Clone)]
pub struct TaskSchedulerConfig {
    /// 最大并发任务数
    pub max_concurrent_tasks: usize,
    /// 任务心跳间隔(秒)
    pub heartbeat_interval_seconds: u64,
    /// 任务超时时间(秒)
    pub task_timeout_seconds: u64,
    /// 重试延迟策略
    pub retry_delay_strategy: RetryDelayStrategy,
    /// 队列清理间隔(秒)
    pub queue_cleanup_interval_seconds: u64,
    /// 最大队列大小
    pub max_queue_size: usize,
    /// 是否启用负载均衡
    pub enable_load_balancing: bool,
    /// 任务优先级权重
    pub priority_weights: HashMap<TaskPriority, f64>,
}

impl Default for TaskSchedulerConfig {
    fn default() -> Self {
        let mut priority_weights = HashMap::new();
        priority_weights.insert(TaskPriority::Low, 1.0);
        priority_weights.insert(TaskPriority::Normal, 2.0);
        priority_weights.insert(TaskPriority::High, 4.0);
        priority_weights.insert(TaskPriority::Critical, 8.0);

        Self {
            max_concurrent_tasks: 4,
            heartbeat_interval_seconds: 30,
            task_timeout_seconds: 300, // 5分钟
            retry_delay_strategy: RetryDelayStrategy::Exponential {
                base_delay_ms: 1000,
                max_delay_ms: 60000,
            },
            queue_cleanup_interval_seconds: 300, // 5分钟
            max_queue_size: 1000,
            enable_load_balancing: true,
            priority_weights,
        }
    }
}

/// 重试延迟策略
#[derive(Debug, Clone)]
pub enum RetryDelayStrategy {
    /// 固定延迟
    Fixed { delay_ms: u64 },
    /// 指数退避
    Exponential {
        base_delay_ms: u64,
        max_delay_ms: u64,
    },
    /// 线性增长
    Linear {
        initial_delay_ms: u64,
        increment_ms: u64,
    },
}

/// 调度策略
#[derive(Debug, Clone)]
pub enum SchedulingStrategy {
    /// 先进先出
    FIFO,
    /// 优先级队列
    Priority,
    /// 最短作业优先
    ShortestJobFirst,
    /// 轮询调度
    RoundRobin,
    /// 负载均衡
    LoadBalanced,
}

/// 任务执行器类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum TaskExecutorType {
    Vectorization,
    IncrementalUpdate,
    BatchProcessing,
    QualityAssessment,
}

/// 任务执行上下文
#[derive(Debug, Clone)]
pub struct TaskExecutionContext {
    pub task_id: String,
    pub executor_type: TaskExecutorType,
    pub started_at: i64,
    pub estimated_completion: Option<i64>,
    pub progress: f64,
    pub resource_usage: ResourceUsage,
}

/// 资源使用情况
#[derive(Debug, Clone, Default)]
pub struct ResourceUsage {
    pub memory_mb: f64,
    pub cpu_percentage: f64,
    pub gpu_utilization: f64,
    pub network_io_mbps: f64,
}

/// 调度统计信息
#[derive(Debug, Default, Clone)]
pub struct SchedulingStatistics {
    pub total_tasks_scheduled: u64,
    pub total_tasks_completed: u64,
    pub total_tasks_failed: u64,
    pub total_tasks_timeout: u64,
    pub avg_execution_time_ms: f64,
    pub avg_wait_time_ms: f64,
    pub current_queue_size: usize,
    pub active_tasks_count: usize,
    pub resource_utilization: ResourceUsage,
}

/// 可调度的任务包装器
#[derive(Debug, Clone)]
struct SchedulableTask {
    pub task: EmbeddingTask,
    pub scheduled_at: i64,
    pub estimated_duration_ms: Option<i64>,
    pub resource_requirements: ResourceUsage,
    pub retry_delay_until: Option<i64>,
}

impl PartialEq for SchedulableTask {
    fn eq(&self, other: &Self) -> bool {
        self.task.id == other.task.id
    }
}

impl Eq for SchedulableTask {}

impl PartialOrd for SchedulableTask {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for SchedulableTask {
    fn cmp(&self, other: &Self) -> Ordering {
        // 优先级越高越优先，然后按创建时间排序
        other
            .task
            .priority
            .cmp(&self.task.priority)
            .then_with(|| self.task.created_at.cmp(&other.task.created_at))
    }
}

/// 向量化任务调度器
pub struct VectorizationTaskScheduler {
    /// 配置
    config: TaskSchedulerConfig,
    /// 任务队列 (按不同策略组织)
    task_queues: Arc<RwLock<HashMap<TaskExecutorType, BinaryHeap<SchedulableTask>>>>,
    /// 活跃任务追踪
    active_tasks: Arc<RwLock<HashMap<String, TaskExecutionContext>>>,
    /// 已完成任务历史
    completed_tasks: Arc<RwLock<VecDeque<EmbeddingTask>>>,
    /// 并发控制
    semaphore: Arc<Semaphore>,
    /// 统计信息
    statistics: Arc<RwLock<SchedulingStatistics>>,
    /// 服务依赖
    vectorization_service: Arc<VectorizationService>,
    incremental_update: Arc<IncrementalVectorUpdate>,
    batch_vectorization: Arc<BatchDocumentVectorization>,
    model_manager: Arc<EmbeddingModelManager>,
    /// 调度器控制
    scheduler_running: Arc<RwLock<bool>>,
    stop_signal: Arc<Notify>,
    /// 任务完成通知
    task_completion_tx: mpsc::UnboundedSender<TaskCompletionEvent>,
    task_completion_rx: Arc<RwLock<Option<mpsc::UnboundedReceiver<TaskCompletionEvent>>>>,
}

/// 任务完成事件
#[derive(Debug, Clone)]
pub struct TaskCompletionEvent {
    pub task_id: String,
    pub status: EmbeddingTaskStatus,
    pub execution_time_ms: i64,
    pub error_message: Option<String>,
    pub resource_usage: ResourceUsage,
}

impl VectorizationTaskScheduler {
    /// 创建新的任务调度器
    pub fn new(
        config: TaskSchedulerConfig,
        vectorization_service: Arc<VectorizationService>,
        incremental_update: Arc<IncrementalVectorUpdate>,
        batch_vectorization: Arc<BatchDocumentVectorization>,
        model_manager: Arc<EmbeddingModelManager>,
    ) -> Self {
        let (tx, rx) = mpsc::unbounded_channel();

        Self {
            config: config.clone(),
            task_queues: Arc::new(RwLock::new(HashMap::new())),
            active_tasks: Arc::new(RwLock::new(HashMap::new())),
            completed_tasks: Arc::new(RwLock::new(VecDeque::new())),
            semaphore: Arc::new(Semaphore::new(config.max_concurrent_tasks)),
            statistics: Arc::new(RwLock::new(SchedulingStatistics::default())),
            vectorization_service,
            incremental_update,
            batch_vectorization,
            model_manager,
            scheduler_running: Arc::new(RwLock::new(false)),
            stop_signal: Arc::new(Notify::new()),
            task_completion_tx: tx,
            task_completion_rx: Arc::new(RwLock::new(Some(rx))),
        }
    }

    /// 启动任务调度器
    pub async fn start(&self) -> Result<()> {
        {
            let mut running = self.scheduler_running.write().await;
            if *running {
                return Err(anyhow::anyhow!("Scheduler is already running"));
            }
            *running = true;
        }

        tracing::info!("启动向量化任务调度器");

        // 启动主调度循环
        let scheduler = Arc::new(self.clone_for_async());
        tokio::spawn(async move {
            scheduler.scheduler_main_loop().await;
        });

        // 启动心跳检查
        let heartbeat_scheduler = Arc::new(self.clone_for_async());
        tokio::spawn(async move {
            heartbeat_scheduler.heartbeat_loop().await;
        });

        // 启动队列清理
        let cleanup_scheduler = Arc::new(self.clone_for_async());
        tokio::spawn(async move {
            cleanup_scheduler.cleanup_loop().await;
        });

        // 启动任务完成事件处理
        let event_scheduler = Arc::new(self.clone_for_async());
        tokio::spawn(async move {
            event_scheduler.handle_completion_events().await;
        });

        Ok(())
    }

    /// 停止任务调度器
    pub async fn stop(&self) -> Result<()> {
        {
            let mut running = self.scheduler_running.write().await;
            *running = false;
        }

        // 发送停止信号
        self.stop_signal.notify_waiters();

        // 等待活跃任务完成
        let start_time = Instant::now();
        let timeout = tokio::time::Duration::from_secs(30);

        while start_time.elapsed() < timeout {
            let active_count = self.active_tasks.read().await.len();
            if active_count == 0 {
                break;
            }
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }

        tracing::info!("向量化任务调度器已停止");
        Ok(())
    }

    /// 提交任务到调度器
    pub async fn submit_task(&self, task: EmbeddingTask) -> Result<()> {
        // 检查队列大小限制
        let total_queued = self.get_total_queued_tasks().await;
        if total_queued >= self.config.max_queue_size {
            return Err(anyhow::anyhow!(
                "任务队列已满，当前任务数: {}",
                total_queued
            ));
        }

        // 确定任务执行器类型
        let executor_type = self.determine_executor_type(&task);

        // 创建可调度任务
        let schedulable_task = SchedulableTask {
            task: task.clone(),
            scheduled_at: current_millis(),
            estimated_duration_ms: self.estimate_task_duration(&task).await,
            resource_requirements: self.estimate_resource_requirements(&task).await,
            retry_delay_until: None,
        };

        // 添加到相应队列
        {
            let mut queues = self.task_queues.write().await;
            let queue = queues.entry(executor_type).or_insert_with(BinaryHeap::new);
            queue.push(schedulable_task);
        }

        // 更新统计信息
        {
            let mut stats = self.statistics.write().await;
            stats.total_tasks_scheduled += 1;
            stats.current_queue_size = self.get_total_queued_tasks().await;
        }

        tracing::info!(
            "任务已提交到调度器: {} (类型: {:?})",
            task.id,
            executor_type
        );
        Ok(())
    }

    /// 主调度循环
    async fn scheduler_main_loop(&self) {
        let mut interval = interval(tokio::time::Duration::from_millis(100));

        loop {
            tokio::select! {
                _ = interval.tick() => {
                    if !*self.scheduler_running.read().await {
                        break;
                    }

                    if let Err(e) = self.process_pending_tasks().await {
                        tracing::error!("处理待处理任务时出错: {}", e);
                    }
                }
                _ = self.stop_signal.notified() => {
                    tracing::info!("收到停止信号，退出调度循环");
                    break;
                }
            }
        }
    }

    /// 处理待处理任务
    async fn process_pending_tasks(&self) -> Result<()> {
        // 获取可用的许可证数量
        let available_permits = self.semaphore.available_permits();
        if available_permits == 0 {
            return Ok(()); // 没有可用的执行槽位
        }

        // 根据负载均衡策略选择任务
        let tasks_to_execute = if self.config.enable_load_balancing {
            self.select_balanced_tasks(available_permits).await
        } else {
            self.select_priority_tasks(available_permits).await
        };

        // 执行选中的任务
        for task in tasks_to_execute {
            if let Ok(_permit) = self.semaphore.try_acquire() {
                let executor = Arc::new(self.clone_for_async());
                let completion_tx = self.task_completion_tx.clone();

                tokio::spawn(async move {
                    let result = executor.execute_task(task.clone()).await;
                    let completion_event = match result {
                        Ok(usage) => TaskCompletionEvent {
                            task_id: task.task.id,
                            status: EmbeddingTaskStatus::Completed,
                            execution_time_ms: 0, // 将在execute_task中计算
                            error_message: None,
                            resource_usage: usage,
                        },
                        Err(e) => TaskCompletionEvent {
                            task_id: task.task.id,
                            status: EmbeddingTaskStatus::Failed,
                            execution_time_ms: 0,
                            error_message: Some(e.to_string()),
                            resource_usage: ResourceUsage::default(),
                        },
                    };

                    let _ = completion_tx.send(completion_event);
                    // _permit会在这里自动释放
                });
            }
        }

        Ok(())
    }

    /// 执行单个任务
    async fn execute_task(&self, schedulable_task: SchedulableTask) -> Result<ResourceUsage> {
        let task = &schedulable_task.task;
        let start_time = Instant::now();

        tracing::info!("开始执行任务: {} (类型: {:?})", task.id, task.task_type);

        // 记录任务开始执行
        let context = TaskExecutionContext {
            task_id: task.id.clone(),
            executor_type: self.determine_executor_type(task),
            started_at: current_millis(),
            estimated_completion: schedulable_task
                .estimated_duration_ms
                .map(|ms| current_millis() + Duration::milliseconds(ms).num_milliseconds()),
            progress: 0.0,
            resource_usage: ResourceUsage::default(),
        };

        self.active_tasks
            .write()
            .await
            .insert(task.id.clone(), context);

        // 根据任务类型执行
        let result = match task.task_type {
            EmbeddingTaskType::Document => self
                .vectorization_service
                .vectorize_document(task.resource_id.clone(), Some(task.model_id.clone()), false)
                .await
                .map(|_| ResourceUsage::default()),
            EmbeddingTaskType::Chunk => self
                .vectorization_service
                .vectorize_chunk(&task.resource_id, Some(&task.model_id), false)
                .await
                .map(|_| ResourceUsage::default()),
            EmbeddingTaskType::Batch => {
                // 批量处理逻辑
                Ok(ResourceUsage::default())
            }
            EmbeddingTaskType::Incremental => {
                // 增量更新逻辑
                Ok(ResourceUsage::default())
            }
            EmbeddingTaskType::Recompute => self
                .vectorization_service
                .vectorize_document(task.resource_id.clone(), Some(task.model_id.clone()), true)
                .await
                .map(|_| ResourceUsage::default()),
        };

        // 移除活跃任务记录
        self.active_tasks.write().await.remove(&task.id);

        let execution_time = start_time.elapsed();
        tracing::info!(
            "任务执行完成: {} (耗时: {}ms)",
            task.id,
            execution_time.as_millis()
        );

        result
    }

    /// 心跳循环 - 检查超时任务
    async fn heartbeat_loop(&self) {
        let mut interval = interval(tokio::time::Duration::from_secs(
            self.config.heartbeat_interval_seconds,
        ));

        loop {
            tokio::select! {
                _ = interval.tick() => {
                    if !*self.scheduler_running.read().await {
                        break;
                    }

                    self.check_timeout_tasks().await;
                }
                _ = self.stop_signal.notified() => {
                    break;
                }
            }
        }
    }

    /// 检查超时任务
    async fn check_timeout_tasks(&self) {
        let timeout_duration = Duration::seconds(self.config.task_timeout_seconds as i64);
        let now = now();
        let mut timeout_tasks = Vec::new();

        // 查找超时任务
        {
            let active_tasks = self.active_tasks.read().await;
            for (task_id, context) in active_tasks.iter() {
                if now.signed_duration_since(to_datetime(context.started_at)) > timeout_duration {
                    timeout_tasks.push(task_id.clone());
                }
            }
        }

        // 处理超时任务
        for task_id in timeout_tasks {
            tracing::warn!("检测到超时任务: {}", task_id);

            // 移除活跃任务
            self.active_tasks.write().await.remove(&task_id);

            // 更新统计信息
            self.statistics.write().await.total_tasks_timeout += 1;

            // 发送超时事件
            let completion_event = TaskCompletionEvent {
                task_id,
                status: EmbeddingTaskStatus::Failed,
                execution_time_ms: self.config.task_timeout_seconds as i64 * 1000,
                error_message: Some("Task timeout".to_string()),
                resource_usage: ResourceUsage::default(),
            };

            let _ = self.task_completion_tx.send(completion_event);
        }
    }

    /// 队列清理循环
    async fn cleanup_loop(&self) {
        let mut interval = interval(tokio::time::Duration::from_secs(
            self.config.queue_cleanup_interval_seconds,
        ));

        loop {
            tokio::select! {
                _ = interval.tick() => {
                    if !*self.scheduler_running.read().await {
                        break;
                    }

                    self.cleanup_completed_tasks().await;
                }
                _ = self.stop_signal.notified() => {
                    break;
                }
            }
        }
    }

    /// 清理已完成任务历史
    async fn cleanup_completed_tasks(&self) {
        let cutoff = current_millis() - Duration::hours(24).num_milliseconds(); // 保留24小时的历史
        let mut completed = self.completed_tasks.write().await;

        while let Some(task) = completed.front() {
            if let Some(completed_at) = task.completed_at {
                if completed_at < cutoff {
                    completed.pop_front();
                } else {
                    break;
                }
            } else {
                break;
            }
        }
    }

    /// 处理任务完成事件
    async fn handle_completion_events(&self) {
        let mut rx = self.task_completion_rx.write().await.take().unwrap();

        while let Some(event) = rx.recv().await {
            if !*self.scheduler_running.read().await {
                break;
            }

            self.process_completion_event(event).await;
        }
    }

    /// 处理单个完成事件
    async fn process_completion_event(&self, event: TaskCompletionEvent) {
        // 更新统计信息
        {
            let mut stats = self.statistics.write().await;
            match event.status {
                EmbeddingTaskStatus::Completed => {
                    stats.total_tasks_completed += 1;
                }
                EmbeddingTaskStatus::Failed => {
                    stats.total_tasks_failed += 1;
                }
                _ => {}
            }

            stats.active_tasks_count = self.active_tasks.read().await.len();
            stats.current_queue_size = self.get_total_queued_tasks().await;
        }

        tracing::debug!("处理任务完成事件: {} -> {:?}", event.task_id, event.status);
    }

    /// 选择优先级任务
    async fn select_priority_tasks(&self, max_tasks: usize) -> Vec<SchedulableTask> {
        let mut selected = Vec::new();
        let mut queues = self.task_queues.write().await;

        // 按执行器类型的优先级顺序处理
        let executor_order = [
            TaskExecutorType::Vectorization,
            TaskExecutorType::IncrementalUpdate,
            TaskExecutorType::BatchProcessing,
            TaskExecutorType::QualityAssessment,
        ];

        for executor_type in &executor_order {
            if selected.len() >= max_tasks {
                break;
            }

            if let Some(queue) = queues.get_mut(executor_type) {
                while selected.len() < max_tasks && !queue.is_empty() {
                    if let Some(task) = queue.pop() {
                        // 检查重试延迟
                        if let Some(retry_until) = task.retry_delay_until {
                            if current_millis() < retry_until {
                                queue.push(task); // 放回队列
                                break;
                            }
                        }
                        selected.push(task);
                    }
                }
            }
        }

        selected
    }

    /// 选择负载均衡任务
    async fn select_balanced_tasks(&self, max_tasks: usize) -> Vec<SchedulableTask> {
        // 简化的负载均衡逻辑，这里可以根据资源使用情况进行更复杂的选择
        self.select_priority_tasks(max_tasks).await
    }

    /// 确定任务执行器类型
    fn determine_executor_type(&self, task: &EmbeddingTask) -> TaskExecutorType {
        match task.task_type {
            EmbeddingTaskType::Document
            | EmbeddingTaskType::Chunk
            | EmbeddingTaskType::Recompute => TaskExecutorType::Vectorization,
            EmbeddingTaskType::Incremental => TaskExecutorType::IncrementalUpdate,
            EmbeddingTaskType::Batch => TaskExecutorType::BatchProcessing,
        }
    }

    /// 估算任务执行时间
    async fn estimate_task_duration(&self, task: &EmbeddingTask) -> Option<i64> {
        // 基于历史数据和任务类型的简单估算
        match task.task_type {
            EmbeddingTaskType::Document => Some(30000),    // 30秒
            EmbeddingTaskType::Chunk => Some(5000),        // 5秒
            EmbeddingTaskType::Batch => Some(120000),      // 2分钟
            EmbeddingTaskType::Incremental => Some(15000), // 15秒
            EmbeddingTaskType::Recompute => Some(45000),   // 45秒
        }
    }

    /// 估算资源需求
    async fn estimate_resource_requirements(&self, task: &EmbeddingTask) -> ResourceUsage {
        match task.task_type {
            EmbeddingTaskType::Document => ResourceUsage {
                memory_mb: 512.0,
                cpu_percentage: 50.0,
                gpu_utilization: 30.0,
                network_io_mbps: 1.0,
            },
            EmbeddingTaskType::Chunk => ResourceUsage {
                memory_mb: 128.0,
                cpu_percentage: 25.0,
                gpu_utilization: 15.0,
                network_io_mbps: 0.5,
            },
            _ => ResourceUsage::default(),
        }
    }

    /// 获取总排队任务数
    async fn get_total_queued_tasks(&self) -> usize {
        let queues = self.task_queues.read().await;
        queues.values().map(|q| q.len()).sum()
    }

    /// 获取调度统计信息
    pub async fn get_statistics(&self) -> SchedulingStatistics {
        let mut stats = self.statistics.read().await.clone();
        stats.active_tasks_count = self.active_tasks.read().await.len();
        stats.current_queue_size = self.get_total_queued_tasks().await;
        stats
    }

    /// 获取活跃任务列表
    pub async fn get_active_tasks(&self) -> Vec<TaskExecutionContext> {
        self.active_tasks.read().await.values().cloned().collect()
    }

    /// 克隆用于异步操作
    fn clone_for_async(&self) -> Self {
        Self {
            config: self.config.clone(),
            task_queues: self.task_queues.clone(),
            active_tasks: self.active_tasks.clone(),
            completed_tasks: self.completed_tasks.clone(),
            semaphore: self.semaphore.clone(),
            statistics: self.statistics.clone(),
            vectorization_service: self.vectorization_service.clone(),
            incremental_update: self.incremental_update.clone(),
            batch_vectorization: self.batch_vectorization.clone(),
            model_manager: self.model_manager.clone(),
            scheduler_running: self.scheduler_running.clone(),
            stop_signal: self.stop_signal.clone(),
            task_completion_tx: self.task_completion_tx.clone(),
            task_completion_rx: self.task_completion_rx.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use wisdom_vault_common::db::next_id;
    use wisdom_vault_database::EmbeddingTaskMetadata;

    use super::*;

    #[test]
    fn test_scheduler_config_default() {
        let config = TaskSchedulerConfig::default();
        assert_eq!(config.max_concurrent_tasks, 4);
        assert_eq!(config.heartbeat_interval_seconds, 30);
        assert!(config.enable_load_balancing);
    }

    #[test]
    fn test_schedulable_task_ordering() {
        let task1 = SchedulableTask {
            task: EmbeddingTask {
                id: next_id(),
                task_type: EmbeddingTaskType::Document,
                resource_id: next_id(),
                model_id: next_id(),
                status: EmbeddingTaskStatus::Pending,
                priority: TaskPriority::High,
                progress: 0.0,
                batch_id: None,
                retry_count: 0,
                max_retries: 3,
                error_message: None,
                processing_metadata: EmbeddingTaskMetadata {
                    text_length: None,
                    token_count: None,
                    preprocessing_steps: Vec::new(),
                    model_config: serde_json::Value::Null,
                    quality_score: None,
                    processing_time_ms: None,
                    memory_usage_mb: None,
                    gpu_used: false,
                },
                created_at: current_millis(),
                started_at: None,
                completed_at: None,
                updated_at: current_millis(),
            },
            scheduled_at: current_millis(),
            estimated_duration_ms: Some(30000),
            resource_requirements: ResourceUsage::default(),
            retry_delay_until: None,
        };

        let task2 = SchedulableTask {
            task: EmbeddingTask {
                priority: TaskPriority::Normal,
                ..task1.task.clone()
            },
            ..task1.clone()
        };

        // 高优先级应该排在前面
        assert!(task1 < task2);
    }

    #[test]
    fn test_retry_delay_strategy() {
        let strategy = RetryDelayStrategy::Exponential {
            base_delay_ms: 1000,
            max_delay_ms: 60000,
        };
        match strategy {
            RetryDelayStrategy::Exponential {
                base_delay_ms,
                max_delay_ms,
            } => {
                assert_eq!(base_delay_ms, 1000);
                assert_eq!(max_delay_ms, 60000);
            }
            _ => panic!("Wrong retry strategy type"),
        }
    }
}
