use anyhow::Result;
use chrono::Utc;
use rand::Rng;
use serde::Serialize;
use std::{collections::HashMap, sync::Arc};
use tokio::sync::{Mute<PERSON>, RwLock};
use wisdom_vault_common::{
    db::next_id,
    time::{current_millis, to_datetime},
};

use super::keyword_search::{KeywordSearchRequest, KeywordSearchService, QueryStatistics};

/// 搜索性能监控配置
#[derive(Debug, Clone)]
pub struct SearchPerformanceConfig {
    /// 监控统计的时间窗口（秒）
    pub monitoring_window_seconds: u64,
    /// 慢查询阈值（毫秒）
    pub slow_query_threshold_ms: u64,
    /// 保留的历史记录数量
    pub max_history_records: usize,
    /// 监控采样率（0.0-1.0）
    pub sampling_rate: f64,
    /// 是否启用详细日志
    pub enable_detailed_logging: bool,
}

impl Default for SearchPerformanceConfig {
    fn default() -> Self {
        Self {
            monitoring_window_seconds: 300, // 5分钟
            slow_query_threshold_ms: 2000,  // 2秒
            max_history_records: 1000,
            sampling_rate: 1.0, // 100% 采样
            enable_detailed_logging: true,
        }
    }
}

/// 搜索查询记录
#[derive(Debug, Clone, Serialize)]
pub struct SearchQueryRecord {
    /// 查询ID
    pub query_id: String,
    /// 查询文本
    pub query_text: String,
    /// 知识库ID
    pub knowledge_base_id: Option<String>,
    /// 开始时间
    pub start_time: i64,
    /// 响应时间（毫秒）
    pub response_time_ms: u64,
    /// 结果数量
    pub result_count: u64,
    /// 是否为慢查询
    pub is_slow_query: bool,
    /// 查询统计信息
    pub query_stats: Option<QueryStatistics>,
    /// 用户信息（可选）
    pub user_id: Option<String>,
    /// 请求IP（可选）
    pub client_ip: Option<String>,
}

/// 搜索性能指标
#[derive(Debug, Clone, Serialize)]
pub struct SearchPerformanceMetrics {
    /// 时间窗口
    pub time_window: String,
    /// 总查询数
    pub total_queries: u64,
    /// 成功查询数
    pub successful_queries: u64,
    /// 失败查询数
    pub failed_queries: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 最大响应时间（毫秒）
    pub max_response_time_ms: u64,
    /// 最小响应时间（毫秒）
    pub min_response_time_ms: u64,
    /// 95%响应时间（毫秒）
    pub p95_response_time_ms: f64,
    /// 99%响应时间（毫秒）
    pub p99_response_time_ms: f64,
    /// 慢查询数量
    pub slow_queries_count: u64,
    /// 慢查询率
    pub slow_query_rate: f64,
    /// 平均结果数量
    pub avg_result_count: f64,
    /// 查询频率（每秒）
    pub queries_per_second: f64,
    /// 最后更新时间
    pub last_updated: i64,
}

/// 热门查询统计
#[derive(Debug, Clone, Serialize)]
pub struct PopularQuery {
    /// 查询文本
    pub query_text: String,
    /// 查询次数
    pub query_count: u64,
    /// 平均响应时间
    pub avg_response_time_ms: f64,
    /// 平均结果数量
    pub avg_result_count: f64,
    /// 最后查询时间
    pub last_queried: i64,
}

/// 搜索性能异常
#[derive(Debug, Clone, Serialize)]
pub struct SearchPerformanceAnomaly {
    /// 异常ID
    pub anomaly_id: String,
    /// 异常类型
    pub anomaly_type: AnomalyType,
    /// 异常描述
    pub description: String,
    /// 严重程度
    pub severity: AnomalySeverity,
    /// 检测时间
    pub detected_at: i64,
    /// 相关查询记录
    pub related_queries: Vec<String>,
    /// 建议措施
    pub recommendations: Vec<String>,
}

/// 异常类型
#[derive(Debug, Clone, Serialize, PartialEq, Eq)]
pub enum AnomalyType {
    /// 响应时间异常
    SlowResponse,
    /// 高错误率
    HighErrorRate,
    /// 查询频率异常
    UnusualQueryFrequency,
    /// 内存使用异常
    MemoryPressure,
    /// 索引质量下降
    IndexQualityDegradation,
}

/// 异常严重程度
#[derive(Debug, Clone, Serialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum AnomalySeverity {
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 严重
    Critical,
    /// 紧急
    Emergency,
}

/// 性能调优建议
#[derive(Debug, Clone, Serialize)]
pub struct PerformanceTuningRecommendation {
    /// 建议ID
    pub recommendation_id: String,
    /// 建议类型
    pub recommendation_type: RecommendationType,
    /// 建议标题
    pub title: String,
    /// 建议描述
    pub description: String,
    /// 预期效果
    pub expected_impact: String,
    /// 实施难度
    pub implementation_difficulty: DifficultyLevel,
    /// 优先级
    pub priority: RecommendationPriority,
    /// 相关指标
    pub related_metrics: Vec<String>,
    /// 生成时间
    pub generated_at: i64,
}

/// 建议类型
#[derive(Debug, Clone, Serialize)]
pub enum RecommendationType {
    /// 索引优化
    IndexOptimization,
    /// 查询优化
    QueryOptimization,
    /// 缓存策略
    CacheStrategy,
    /// 资源配置
    ResourceConfiguration,
    /// 架构调整
    ArchitectureAdjustment,
}

/// 实施难度
#[derive(Debug, Clone, Serialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum DifficultyLevel {
    /// 简单
    Easy,
    /// 中等
    Medium,
    /// 困难
    Hard,
    /// 专家级
    Expert,
}

/// 建议优先级
#[derive(Debug, Clone, Serialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum RecommendationPriority {
    /// 低
    Low,
    /// 中
    Medium,
    /// 高
    High,
    /// 紧急
    Urgent,
}

/// 搜索性能监控和调优服务
pub struct SearchPerformanceMonitor {
    /// 关键词搜索服务
    search_service: Arc<KeywordSearchService>,
    /// 监控配置
    config: SearchPerformanceConfig,
    /// 查询记录历史
    query_history: Arc<RwLock<Vec<SearchQueryRecord>>>,
    /// 性能指标缓存
    metrics_cache: Arc<RwLock<Option<SearchPerformanceMetrics>>>,
    /// 热门查询统计
    popular_queries: Arc<RwLock<HashMap<String, PopularQuery>>>,
    /// 性能异常记录
    anomalies: Arc<RwLock<Vec<SearchPerformanceAnomaly>>>,
    /// 调优建议
    recommendations: Arc<RwLock<Vec<PerformanceTuningRecommendation>>>,
    /// 监控状态
    is_monitoring: Arc<RwLock<bool>>,
    /// 统计计算锁
    stats_calculation_lock: Arc<Mutex<()>>,
}

impl SearchPerformanceMonitor {
    /// 创建新的性能监控器
    pub fn new(search_service: Arc<KeywordSearchService>, config: SearchPerformanceConfig) -> Self {
        Self {
            search_service,
            config,
            query_history: Arc::new(RwLock::new(Vec::new())),
            metrics_cache: Arc::new(RwLock::new(None)),
            popular_queries: Arc::new(RwLock::new(HashMap::new())),
            anomalies: Arc::new(RwLock::new(Vec::new())),
            recommendations: Arc::new(RwLock::new(Vec::new())),
            is_monitoring: Arc::new(RwLock::new(false)),
            stats_calculation_lock: Arc::new(Mutex::new(())),
        }
    }

    /// 使用默认配置创建监控器
    pub fn new_with_defaults(search_service: Arc<KeywordSearchService>) -> Self {
        Self::new(search_service, SearchPerformanceConfig::default())
    }

    /// 启动性能监控
    pub async fn start_monitoring(&self) -> Result<()> {
        let mut is_monitoring = self.is_monitoring.write().await;
        if *is_monitoring {
            return Err(anyhow::anyhow!("性能监控已经在运行"));
        }
        *is_monitoring = true;

        tracing::info!("启动搜索性能监控");

        // 启动性能指标计算任务
        let metrics_calculator = self.clone_for_metrics_calculation();
        tokio::spawn(async move {
            metrics_calculator.run_metrics_calculation().await;
        });

        // 启动异常检测任务
        let anomaly_detector = self.clone_for_anomaly_detection();
        tokio::spawn(async move {
            anomaly_detector.run_anomaly_detection().await;
        });

        // 启动调优建议生成任务
        let recommendation_generator = self.clone_for_recommendation_generation();
        tokio::spawn(async move {
            recommendation_generator
                .run_recommendation_generation()
                .await;
        });

        Ok(())
    }

    /// 停止性能监控
    pub async fn stop_monitoring(&self) -> Result<()> {
        let mut is_monitoring = self.is_monitoring.write().await;
        *is_monitoring = false;
        tracing::info!("停止搜索性能监控");
        Ok(())
    }

    /// 记录搜索查询
    pub async fn record_search_query(
        &self,
        query_request: &KeywordSearchRequest,
        response_time_ms: u64,
        result_count: u64,
        query_stats: Option<QueryStatistics>,
        user_id: Option<String>,
        client_ip: Option<String>,
    ) -> Result<()> {
        // 采样控制
        let mut rng = rand::rng();
        if rng.random::<f64>() > self.config.sampling_rate {
            return Ok(());
        }

        let query_record = SearchQueryRecord {
            query_id: next_id(),
            query_text: query_request.query.clone(),
            knowledge_base_id: query_request.knowledge_base_id.clone(),
            start_time: current_millis(),
            response_time_ms,
            result_count,
            is_slow_query: response_time_ms > self.config.slow_query_threshold_ms,
            query_stats,
            user_id,
            client_ip,
        };

        // 记录查询历史
        let mut history = self.query_history.write().await;
        history.push(query_record.clone());

        // 保持历史记录数量限制
        if history.len() > self.config.max_history_records {
            history.remove(0);
        }

        // 更新热门查询统计
        self.update_popular_queries(&query_record).await;

        // 详细日志记录
        if self.config.enable_detailed_logging {
            if query_record.is_slow_query {
                tracing::warn!(
                    "慢查询检测: query='{}', response_time={}ms, results={}",
                    query_record.query_text,
                    query_record.response_time_ms,
                    query_record.result_count
                );
            } else {
                tracing::debug!(
                    "查询记录: query='{}', response_time={}ms, results={}",
                    query_record.query_text,
                    query_record.response_time_ms,
                    query_record.result_count
                );
            }
        }

        Ok(())
    }

    /// 获取性能指标
    pub async fn get_performance_metrics(&self) -> Result<SearchPerformanceMetrics> {
        // 尝试从缓存获取
        if let Some(cached_metrics) = self.metrics_cache.read().await.as_ref() {
            // 检查缓存是否还新鲜（60秒内）
            if (current_millis() - cached_metrics.last_updated) < 60 * 1000 {
                return Ok(cached_metrics.clone());
            }
        }

        // 重新计算指标
        self.calculate_performance_metrics().await
    }

    /// 获取热门查询
    pub async fn get_popular_queries(&self, limit: Option<usize>) -> Vec<PopularQuery> {
        let popular_queries = self.popular_queries.read().await;
        let mut queries: Vec<PopularQuery> = popular_queries.values().cloned().collect();

        // 按查询次数排序
        queries.sort_by(|a, b| b.query_count.cmp(&a.query_count));

        // 应用限制
        if let Some(limit) = limit {
            queries.truncate(limit);
        }

        queries
    }

    /// 获取性能异常
    pub async fn get_performance_anomalies(
        &self,
        limit: Option<usize>,
    ) -> Vec<SearchPerformanceAnomaly> {
        let mut anomalies = self.anomalies.read().await.clone();

        // 按检测时间倒序排序
        anomalies.sort_by(|a, b| b.detected_at.cmp(&a.detected_at));

        // 应用限制
        if let Some(limit) = limit {
            anomalies.truncate(limit);
        }

        anomalies
    }

    /// 获取调优建议
    pub async fn get_tuning_recommendations(
        &self,
        limit: Option<usize>,
    ) -> Vec<PerformanceTuningRecommendation> {
        let mut recommendations = self.recommendations.read().await.clone();

        // 按优先级和生成时间排序
        recommendations.sort_by(|a, b| {
            b.priority
                .cmp(&a.priority)
                .then_with(|| b.generated_at.cmp(&a.generated_at))
        });

        // 应用限制
        if let Some(limit) = limit {
            recommendations.truncate(limit);
        }

        recommendations
    }

    /// 清理历史数据
    pub async fn cleanup_historical_data(&self, days_to_keep: u32) -> Result<u64> {
        let cutoff_time = current_millis() - (days_to_keep * 24 * 60 * 60 * 1000) as i64;
        let mut cleaned_count = 0;

        // 清理查询历史
        let mut history = self.query_history.write().await;
        let initial_count = history.len();
        history.retain(|record| record.start_time > cutoff_time);
        cleaned_count += initial_count - history.len();

        // 清理异常记录
        let mut anomalies = self.anomalies.write().await;
        let initial_anomaly_count = anomalies.len();
        anomalies.retain(|anomaly| anomaly.detected_at > cutoff_time);
        cleaned_count += initial_anomaly_count - anomalies.len();

        tracing::info!("清理了 {} 条历史性能数据", cleaned_count);
        Ok(cleaned_count as u64)
    }

    /// 导出性能报告
    pub async fn export_performance_report(&self, format: ReportFormat) -> Result<String> {
        let metrics = self.get_performance_metrics().await?;
        let popular_queries = self.get_popular_queries(Some(10)).await;
        let anomalies = self.get_performance_anomalies(Some(20)).await;
        let recommendations = self.get_tuning_recommendations(Some(10)).await;

        match format {
            ReportFormat::Json => {
                let report = serde_json::json!({
                    "metrics": metrics,
                    "popular_queries": popular_queries,
                    "anomalies": anomalies,
                    "recommendations": recommendations,
                    "generated_at": Utc::now()
                });
                Ok(serde_json::to_string_pretty(&report)?)
            }
            ReportFormat::Markdown => {
                self.generate_markdown_report(
                    &metrics,
                    &popular_queries,
                    &anomalies,
                    &recommendations,
                )
                .await
            }
        }
    }

    /// 克隆用于指标计算
    fn clone_for_metrics_calculation(&self) -> SearchPerformanceMonitor {
        SearchPerformanceMonitor {
            search_service: Arc::clone(&self.search_service),
            config: self.config.clone(),
            query_history: Arc::clone(&self.query_history),
            metrics_cache: Arc::clone(&self.metrics_cache),
            popular_queries: Arc::clone(&self.popular_queries),
            anomalies: Arc::clone(&self.anomalies),
            recommendations: Arc::clone(&self.recommendations),
            is_monitoring: Arc::clone(&self.is_monitoring),
            stats_calculation_lock: Arc::clone(&self.stats_calculation_lock),
        }
    }

    /// 克隆用于异常检测
    fn clone_for_anomaly_detection(&self) -> SearchPerformanceMonitor {
        self.clone_for_metrics_calculation()
    }

    /// 克隆用于建议生成
    fn clone_for_recommendation_generation(&self) -> SearchPerformanceMonitor {
        self.clone_for_metrics_calculation()
    }

    /// 运行指标计算任务
    async fn run_metrics_calculation(self) {
        let mut interval = tokio::time::interval(
            tokio::time::Duration::from_secs(60), // 每分钟计算一次
        );

        while *self.is_monitoring.read().await {
            interval.tick().await;

            if let Err(e) = self.calculate_and_cache_metrics().await {
                tracing::error!("计算性能指标时出错: {}", e);
            }
        }
    }

    /// 运行异常检测任务
    async fn run_anomaly_detection(self) {
        let mut interval = tokio::time::interval(
            tokio::time::Duration::from_secs(300), // 每5分钟检测一次
        );

        while *self.is_monitoring.read().await {
            interval.tick().await;

            if let Err(e) = self.detect_anomalies().await {
                tracing::error!("检测性能异常时出错: {}", e);
            }
        }
    }

    /// 运行调优建议生成任务
    async fn run_recommendation_generation(self) {
        let mut interval = tokio::time::interval(
            tokio::time::Duration::from_secs(3600), // 每小时生成一次
        );

        while *self.is_monitoring.read().await {
            interval.tick().await;

            if let Err(e) = self.generate_tuning_recommendations().await {
                tracing::error!("生成调优建议时出错: {}", e);
            }
        }
    }

    /// 计算并缓存性能指标
    async fn calculate_and_cache_metrics(&self) -> Result<()> {
        let metrics = self.calculate_performance_metrics().await?;
        *self.metrics_cache.write().await = Some(metrics);
        Ok(())
    }

    /// 计算性能指标
    async fn calculate_performance_metrics(&self) -> Result<SearchPerformanceMetrics> {
        let _lock = self.stats_calculation_lock.lock().await;

        let history = self.query_history.read().await;
        let cutoff_time = current_millis() - (self.config.monitoring_window_seconds * 1000) as i64;

        // 过滤时间窗口内的查询
        let recent_queries: Vec<&SearchQueryRecord> = history
            .iter()
            .filter(|record| record.start_time > cutoff_time)
            .collect();

        if recent_queries.is_empty() {
            return Ok(SearchPerformanceMetrics {
                time_window: format!("{}s", self.config.monitoring_window_seconds),
                total_queries: 0,
                successful_queries: 0,
                failed_queries: 0,
                avg_response_time_ms: 0.0,
                max_response_time_ms: 0,
                min_response_time_ms: 0,
                p95_response_time_ms: 0.0,
                p99_response_time_ms: 0.0,
                slow_queries_count: 0,
                slow_query_rate: 0.0,
                avg_result_count: 0.0,
                queries_per_second: 0.0,
                last_updated: current_millis(),
            });
        }

        // 计算基本统计
        let total_queries = recent_queries.len() as u64;
        let slow_queries_count = recent_queries
            .iter()
            .filter(|record| record.is_slow_query)
            .count() as u64;

        let response_times: Vec<u64> = recent_queries
            .iter()
            .map(|record| record.response_time_ms)
            .collect();

        let result_counts: Vec<u64> = recent_queries
            .iter()
            .map(|record| record.result_count)
            .collect();

        // 计算响应时间统计
        let avg_response_time_ms =
            response_times.iter().sum::<u64>() as f64 / response_times.len() as f64;
        let max_response_time_ms = *response_times.iter().max().unwrap_or(&0);
        let min_response_time_ms = *response_times.iter().min().unwrap_or(&0);

        // 计算百分位数
        let mut sorted_times = response_times.clone();
        sorted_times.sort();
        let p95_index = ((sorted_times.len() as f64) * 0.95) as usize;
        let p99_index = ((sorted_times.len() as f64) * 0.99) as usize;
        let p95_response_time_ms = sorted_times.get(p95_index).unwrap_or(&0).clone() as f64;
        let p99_response_time_ms = sorted_times.get(p99_index).unwrap_or(&0).clone() as f64;

        // 计算其他指标
        let avg_result_count =
            result_counts.iter().sum::<u64>() as f64 / result_counts.len() as f64;
        let slow_query_rate = slow_queries_count as f64 / total_queries as f64;
        let queries_per_second =
            total_queries as f64 / self.config.monitoring_window_seconds as f64;

        Ok(SearchPerformanceMetrics {
            time_window: format!("{}s", self.config.monitoring_window_seconds),
            total_queries,
            successful_queries: total_queries, // 假设所有记录的查询都是成功的
            failed_queries: 0,
            avg_response_time_ms,
            max_response_time_ms,
            min_response_time_ms,
            p95_response_time_ms,
            p99_response_time_ms,
            slow_queries_count,
            slow_query_rate,
            avg_result_count,
            queries_per_second,
            last_updated: current_millis(),
        })
    }

    /// 更新热门查询统计
    async fn update_popular_queries(&self, query_record: &SearchQueryRecord) {
        let mut popular_queries = self.popular_queries.write().await;

        let query = popular_queries
            .entry(query_record.query_text.clone())
            .or_insert_with(|| PopularQuery {
                query_text: query_record.query_text.clone(),
                query_count: 0,
                avg_response_time_ms: 0.0,
                avg_result_count: 0.0,
                last_queried: query_record.start_time,
            });

        // 更新统计信息
        query.query_count += 1;
        query.avg_response_time_ms = (query.avg_response_time_ms * (query.query_count - 1) as f64
            + query_record.response_time_ms as f64)
            / query.query_count as f64;
        query.avg_result_count = (query.avg_result_count * (query.query_count - 1) as f64
            + query_record.result_count as f64)
            / query.query_count as f64;
        query.last_queried = query_record.start_time;
    }

    /// 检测性能异常
    async fn detect_anomalies(&self) -> Result<()> {
        let metrics = self.get_performance_metrics().await?;
        let mut new_anomalies = Vec::new();

        // 检测慢查询异常
        if metrics.slow_query_rate > 0.1 {
            // 10%的慢查询率
            new_anomalies.push(SearchPerformanceAnomaly {
                anomaly_id: next_id(),
                anomaly_type: AnomalyType::SlowResponse,
                description: format!("慢查询率过高: {:.1}%", metrics.slow_query_rate * 100.0),
                severity: if metrics.slow_query_rate > 0.2 {
                    AnomalySeverity::Critical
                } else {
                    AnomalySeverity::Warning
                },
                detected_at: current_millis(),
                related_queries: Vec::new(),
                recommendations: vec![
                    "检查索引质量".to_string(),
                    "优化查询复杂度".to_string(),
                    "考虑增加缓存".to_string(),
                ],
            });
        }

        // 检测响应时间异常
        if metrics.p95_response_time_ms > 5000.0 {
            // P95超过5秒
            new_anomalies.push(SearchPerformanceAnomaly {
                anomaly_id: next_id(),
                anomaly_type: AnomalyType::SlowResponse,
                description: format!("P95响应时间过长: {:.0}ms", metrics.p95_response_time_ms),
                severity: AnomalySeverity::Warning,
                detected_at: current_millis(),
                related_queries: Vec::new(),
                recommendations: vec!["优化搜索算法".to_string(), "增加硬件资源".to_string()],
            });
        }

        // 添加新异常到记录中
        if !new_anomalies.is_empty() {
            let mut anomalies = self.anomalies.write().await;
            anomalies.extend(new_anomalies);

            // 保持异常记录数量限制
            if anomalies.len() > 100 {
                anomalies.sort_by(|a, b| b.detected_at.cmp(&a.detected_at));
                anomalies.truncate(100);
            }
        }

        Ok(())
    }

    /// 生成调优建议
    async fn generate_tuning_recommendations(&self) -> Result<()> {
        let metrics = self.get_performance_metrics().await?;
        let mut new_recommendations = Vec::new();

        // 基于指标生成建议
        if metrics.avg_response_time_ms > 1000.0 {
            new_recommendations.push(PerformanceTuningRecommendation {
                recommendation_id: next_id(),
                recommendation_type: RecommendationType::IndexOptimization,
                title: "优化搜索索引".to_string(),
                description: "平均响应时间较长，建议重建或优化搜索索引".to_string(),
                expected_impact: "预期可减少30-50%的查询时间".to_string(),
                implementation_difficulty: DifficultyLevel::Medium,
                priority: RecommendationPriority::High,
                related_metrics: vec!["avg_response_time_ms".to_string()],
                generated_at: current_millis(),
            });
        }

        if metrics.queries_per_second > 10.0 {
            new_recommendations.push(PerformanceTuningRecommendation {
                recommendation_id: next_id(),
                recommendation_type: RecommendationType::CacheStrategy,
                title: "增加查询缓存".to_string(),
                description: "查询频率较高，建议实施查询结果缓存策略".to_string(),
                expected_impact: "预期可减少80%的重复查询时间".to_string(),
                implementation_difficulty: DifficultyLevel::Easy,
                priority: RecommendationPriority::Medium,
                related_metrics: vec!["queries_per_second".to_string()],
                generated_at: current_millis(),
            });
        }

        // 添加新建议
        if !new_recommendations.is_empty() {
            let mut recommendations = self.recommendations.write().await;
            recommendations.extend(new_recommendations);

            // 保持建议数量限制
            if recommendations.len() > 50 {
                recommendations.sort_by(|a, b| {
                    b.priority
                        .cmp(&a.priority)
                        .then_with(|| b.generated_at.cmp(&a.generated_at))
                });
                recommendations.truncate(50);
            }
        }

        Ok(())
    }

    /// 生成Markdown格式报告
    async fn generate_markdown_report(
        &self,
        metrics: &SearchPerformanceMetrics,
        popular_queries: &[PopularQuery],
        anomalies: &[SearchPerformanceAnomaly],
        recommendations: &[PerformanceTuningRecommendation],
    ) -> Result<String> {
        let mut report = String::new();

        report.push_str("# 搜索性能报告\n\n");
        report.push_str(&format!(
            "**生成时间**: {}\n\n",
            Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
        ));

        // 性能指标部分
        report.push_str("## 性能指标\n\n");
        report.push_str(&format!("- **总查询数**: {}\n", metrics.total_queries));
        report.push_str(&format!(
            "- **平均响应时间**: {:.2}ms\n",
            metrics.avg_response_time_ms
        ));
        report.push_str(&format!(
            "- **P95响应时间**: {:.2}ms\n",
            metrics.p95_response_time_ms
        ));
        report.push_str(&format!(
            "- **慢查询率**: {:.1}%\n",
            metrics.slow_query_rate * 100.0
        ));
        report.push_str(&format!(
            "- **查询频率**: {:.2} QPS\n\n",
            metrics.queries_per_second
        ));

        // 热门查询部分
        if !popular_queries.is_empty() {
            report.push_str("## 热门查询\n\n");
            for (i, query) in popular_queries.iter().enumerate().take(5) {
                report.push_str(&format!(
                    "{}. \"{}\" ({}次, 平均{:.2}ms)\n",
                    i + 1,
                    query.query_text,
                    query.query_count,
                    query.avg_response_time_ms
                ));
            }
            report.push_str("\n");
        }

        // 性能异常部分
        if !anomalies.is_empty() {
            report.push_str("## 性能异常\n\n");
            for anomaly in anomalies.iter().take(5) {
                report.push_str(&format!(
                    "- **{:?}**: {} ({})\n",
                    anomaly.severity,
                    anomaly.description,
                    to_datetime(anomaly.detected_at).format("%Y-%m-%d %H:%M")
                ));
            }
            report.push_str("\n");
        }

        // 调优建议部分
        if !recommendations.is_empty() {
            report.push_str("## 调优建议\n\n");
            for rec in recommendations.iter().take(5) {
                report.push_str(&format!(
                    "### {} (优先级: {:?})\n\n{}\n\n**预期效果**: {}\n\n",
                    rec.title, rec.priority, rec.description, rec.expected_impact
                ));
            }
        }

        Ok(report)
    }
}

/// 报告格式
#[derive(Debug, Clone)]
pub enum ReportFormat {
    /// JSON格式
    Json,
    /// Markdown格式
    Markdown,
}
