use anyhow::Result;
use chrono::Duration;
use std::sync::Arc;
use tokio::time::{interval, sleep};
use tracing::{error, info, warn};
use wisdom_vault_common::time::current_millis;

use crate::services::{DocumentProcessingPipeline, ProcessingTaskService};
use wisdom_vault_database::models::{ProcessingTask, TaskStatus, TaskType};

/// 后台任务处理器
/// 负责自动处理排队的任务和重试失败的任务
pub struct TaskProcessor {
    processing_task_service: Arc<ProcessingTaskService>,
    document_processing_pipeline: Arc<DocumentProcessingPipeline>,
    max_concurrent_tasks: usize,
    retry_check_interval_seconds: u64,
    processing_timeout_minutes: u64,
    is_running: Arc<tokio::sync::RwLock<bool>>,
}

impl TaskProcessor {
    pub fn new(
        processing_task_service: Arc<ProcessingTaskService>,
        document_processing_pipeline: Arc<DocumentProcessingPipeline>,
    ) -> Self {
        Self {
            processing_task_service,
            document_processing_pipeline,
            max_concurrent_tasks: 5,          // 可配置
            retry_check_interval_seconds: 60, // 每分钟检查一次重试任务
            processing_timeout_minutes: 30,   // 任务超时时间
            is_running: Arc::new(tokio::sync::RwLock::new(false)),
        }
    }

    /// 启动任务处理器
    pub async fn start(&self) -> Result<()> {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            warn!("Task processor is already running");
            return Ok(());
        }
        *is_running = true;
        drop(is_running);

        info!(
            "Starting task processor with max {} concurrent tasks",
            self.max_concurrent_tasks
        );

        // 启动重试任务检查器
        let retry_checker = self.start_retry_checker();

        // 启动超时任务清理器
        let timeout_cleaner = self.start_timeout_cleaner();

        // 启动主任务处理循环
        let task_processor = self.start_task_processing_loop();

        // 等待所有任务完成或遇到错误
        tokio::select! {
            result = retry_checker => {
                error!("Retry checker stopped: {:?}", result);
            }
            result = timeout_cleaner => {
                error!("Timeout cleaner stopped: {:?}", result);
            }
            result = task_processor => {
                error!("Task processor stopped: {:?}", result);
            }
        }

        let mut is_running = self.is_running.write().await;
        *is_running = false;

        Ok(())
    }

    /// 停止任务处理器
    pub async fn stop(&self) {
        let mut is_running = self.is_running.write().await;
        *is_running = false;
        info!("Task processor stop signal sent");
    }

    /// 检查是否正在运行
    pub async fn is_running(&self) -> bool {
        *self.is_running.read().await
    }

    /// 启动重试任务检查器
    async fn start_retry_checker(&self) -> Result<()> {
        let mut interval = interval(tokio::time::Duration::from_secs(
            self.retry_check_interval_seconds,
        ));

        loop {
            interval.tick().await;

            if !self.is_running().await {
                break;
            }

            match self.processing_task_service.process_retry_tasks().await {
                Ok(count) => {
                    if count > 0 {
                        info!("Processed {} retry tasks", count);
                    }
                }
                Err(e) => {
                    error!("Failed to process retry tasks: {}", e);
                }
            }
        }

        Ok(())
    }

    /// 启动超时任务清理器
    async fn start_timeout_cleaner(&self) -> Result<()> {
        let mut interval = interval(tokio::time::Duration::from_secs(300)); // 每5分钟检查一次

        loop {
            interval.tick().await;

            if !self.is_running().await {
                break;
            }

            match self.cleanup_timeout_tasks().await {
                Ok(count) => {
                    if count > 0 {
                        info!("Cleaned up {} timeout tasks", count);
                    }
                }
                Err(e) => {
                    error!("Failed to cleanup timeout tasks: {}", e);
                }
            }
        }

        Ok(())
    }

    /// 启动主任务处理循环
    async fn start_task_processing_loop(&self) -> Result<()> {
        let mut active_tasks: Vec<tokio::task::JoinHandle<()>> = Vec::new();

        loop {
            if !self.is_running().await {
                // 等待所有活跃任务完成
                for handle in active_tasks {
                    let _ = handle.await;
                }
                break;
            }

            // 清理已完成的任务
            active_tasks.retain(|handle| !handle.is_finished());

            // 如果还有空闲容量，获取新任务
            if active_tasks.len() < self.max_concurrent_tasks {
                match self.processing_task_service.get_next_task().await {
                    Ok(Some(task)) => {
                        let task_service = self.processing_task_service.clone();
                        let pipeline = self.document_processing_pipeline.clone();

                        let handle = tokio::spawn(async move {
                            Self::process_single_task(task_service, pipeline, task).await;
                        });

                        active_tasks.push(handle);
                    }
                    Ok(None) => {
                        // 没有待处理任务，短暂休眠
                        sleep(tokio::time::Duration::from_secs(5)).await;
                    }
                    Err(e) => {
                        error!("Failed to get next task: {}", e);
                        sleep(tokio::time::Duration::from_secs(10)).await;
                    }
                }
            } else {
                // 任务队列已满，等待一些任务完成
                sleep(tokio::time::Duration::from_secs(1)).await;
            }
        }

        Ok(())
    }

    /// 处理单个任务
    async fn process_single_task(
        task_service: Arc<ProcessingTaskService>,
        pipeline: Arc<DocumentProcessingPipeline>,
        task: ProcessingTask,
    ) {
        info!(
            "Starting to process task {} of type {:?}",
            task.id, task.task_type
        );

        // 标记任务开始
        if let Err(e) = task_service.start_task(&task.id).await {
            error!("Failed to start task {}: {}", task.id, e);
            return;
        }

        // 根据任务类型执行相应的处理
        let result = match task.task_type {
            TaskType::DocumentParsing => {
                Self::process_document_parsing(&task_service, &pipeline, &task).await
            }
            TaskType::MetadataExtraction => {
                Self::process_metadata_extraction(&task_service, &pipeline, &task).await
            }
            TaskType::DocumentIndexing => {
                Self::process_document_indexing(&task_service, &pipeline, &task).await
            }
            TaskType::VectorGeneration => {
                Self::process_vector_generation(&task_service, &pipeline, &task).await
            }
            TaskType::KnowledgeGraphExtraction => {
                Self::process_knowledge_graph_extraction(&task_service, &pipeline, &task).await
            }
            TaskType::DocumentClassification => {
                Self::process_document_classification(&task_service, &pipeline, &task).await
            }
        };

        match result {
            Ok(_) => {
                if let Err(e) = task_service.complete_task(&task.id).await {
                    error!("Failed to mark task {} as completed: {}", task.id, e);
                } else {
                    info!(
                        "Successfully completed task {} of type {:?}",
                        task.id, task.task_type
                    );
                }
            }
            Err(e) => {
                error!("Task {} failed: {}", task.id, e);
                if let Err(fail_err) = task_service
                    .fail_task(
                        &task.id,
                        &e.to_string(),
                        Some(serde_json::json!({"error": e.to_string()})),
                    )
                    .await
                {
                    error!("Failed to mark task {} as failed: {}", task.id, fail_err);
                }
            }
        }
    }

    /// 处理文档解析任务
    async fn process_document_parsing(
        task_service: &ProcessingTaskService,
        _pipeline: &DocumentProcessingPipeline,
        task: &ProcessingTask,
    ) -> Result<()> {
        info!("Processing document parsing for task {}", task.id);

        // 更新进度
        task_service.update_progress(&task.id, 0.5).await?;

        // 模拟文档解析处理
        // 在实际实现中，这里会调用文档解析服务
        sleep(tokio::time::Duration::from_secs(2)).await;

        // 完成进度
        task_service.update_progress(&task.id, 1.0).await?;

        Ok(())
    }

    /// 处理元数据提取任务
    async fn process_metadata_extraction(
        task_service: &ProcessingTaskService,
        _pipeline: &DocumentProcessingPipeline,
        task: &ProcessingTask,
    ) -> Result<()> {
        info!("Processing metadata extraction for task {}", task.id);

        task_service.update_progress(&task.id, 0.3).await?;
        sleep(tokio::time::Duration::from_secs(1)).await;

        task_service.update_progress(&task.id, 0.8).await?;
        sleep(tokio::time::Duration::from_secs(1)).await;

        task_service.update_progress(&task.id, 1.0).await?;

        Ok(())
    }

    /// 处理文档索引任务
    async fn process_document_indexing(
        task_service: &ProcessingTaskService,
        _pipeline: &DocumentProcessingPipeline,
        task: &ProcessingTask,
    ) -> Result<()> {
        info!("Processing document indexing for task {}", task.id);

        task_service.update_progress(&task.id, 0.4).await?;
        sleep(tokio::time::Duration::from_secs(3)).await;

        task_service.update_progress(&task.id, 1.0).await?;

        Ok(())
    }

    /// 处理向量生成任务
    async fn process_vector_generation(
        task_service: &ProcessingTaskService,
        _pipeline: &DocumentProcessingPipeline,
        task: &ProcessingTask,
    ) -> Result<()> {
        info!("Processing vector generation for task {}", task.id);

        task_service.update_progress(&task.id, 0.6).await?;
        sleep(tokio::time::Duration::from_secs(2)).await;

        task_service.update_progress(&task.id, 1.0).await?;

        Ok(())
    }

    /// 处理知识图谱提取任务
    async fn process_knowledge_graph_extraction(
        task_service: &ProcessingTaskService,
        _pipeline: &DocumentProcessingPipeline,
        task: &ProcessingTask,
    ) -> Result<()> {
        info!("Processing knowledge graph extraction for task {}", task.id);

        task_service.update_progress(&task.id, 0.7).await?;
        sleep(tokio::time::Duration::from_secs(4)).await;

        task_service.update_progress(&task.id, 1.0).await?;

        Ok(())
    }

    /// 处理文档分类任务
    async fn process_document_classification(
        task_service: &ProcessingTaskService,
        _pipeline: &DocumentProcessingPipeline,
        task: &ProcessingTask,
    ) -> Result<()> {
        info!("Processing document classification for task {}", task.id);

        task_service.update_progress(&task.id, 0.5).await?;
        sleep(tokio::time::Duration::from_secs(1)).await;

        task_service.update_progress(&task.id, 1.0).await?;

        Ok(())
    }

    /// 清理超时任务
    async fn cleanup_timeout_tasks(&self) -> Result<u64> {
        let timeout_threshold = current_millis()
            - Duration::minutes(self.processing_timeout_minutes as i64).num_milliseconds();
        let running_tasks = self
            .processing_task_service
            .get_tasks_by_status(TaskStatus::Running)
            .await?;

        let mut timeout_count = 0;
        for task in running_tasks {
            if task.started_at < timeout_threshold {
                warn!(
                    "Task {} has timed out (started at {})",
                    task.id, task.started_at
                );

                if let Err(e) = self.processing_task_service.fail_task(
                    &task.id,
                    "Task timed out",
                    Some(serde_json::json!({"timeout_threshold": timeout_threshold, "started_at": task.started_at}))
                ).await {
                    error!("Failed to mark timeout task {} as failed: {}", task.id, e);
                } else {
                    timeout_count += 1;
                }
            }
        }

        Ok(timeout_count)
    }

    /// 获取处理器状态
    pub async fn get_status(&self) -> ProcessorStatus {
        let is_running = self.is_running().await;
        let stats = self
            .processing_task_service
            .get_statistics()
            .await
            .unwrap_or_default();

        ProcessorStatus {
            is_running,
            max_concurrent_tasks: self.max_concurrent_tasks,
            retry_check_interval_seconds: self.retry_check_interval_seconds,
            processing_timeout_minutes: self.processing_timeout_minutes,
            task_statistics: stats,
        }
    }
}

/// 处理器状态信息
#[derive(Debug, serde::Serialize)]
pub struct ProcessorStatus {
    pub is_running: bool,
    pub max_concurrent_tasks: usize,
    pub retry_check_interval_seconds: u64,
    pub processing_timeout_minutes: u64,
    pub task_statistics: wisdom_vault_database::repositories::TaskStatistics,
}

#[cfg(test)]
mod tests {

    // 这里可以添加测试，但需要mock相关服务
    #[tokio::test]
    async fn test_processor_creation() {
        // 由于需要mock复杂的服务依赖，这里只测试基本创建
        // 在实际项目中，可以使用dependency injection框架来简化测试
    }
}
