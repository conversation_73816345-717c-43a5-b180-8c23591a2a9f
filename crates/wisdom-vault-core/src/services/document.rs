use anyhow::Result;
use std::sync::Arc;
use wisdom_vault_common::time::current_millis;
use wisdom_vault_database::{
    models::{Document, DocumentProcessingMetadata, DocumentStatus},
    repositories::{DocumentCategoryRepository, DocumentRepository, DocumentTagRepository},
};

/// 文档管理服务
/// 提供文档的业务逻辑操作，包括CRUD、搜索、状态管理等
pub struct DocumentService {
    repository: Arc<dyn DocumentRepository + Send + Sync>,
    document_tag_repository: Option<Arc<dyn DocumentTagRepository + Send + Sync>>,
    document_category_repository: Option<Arc<dyn DocumentCategoryRepository + Send + Sync>>,
}

impl DocumentService {
    pub fn new(repository: Arc<dyn DocumentRepository + Send + Sync>) -> Self {
        Self {
            repository,
            document_tag_repository: None,
            document_category_repository: None,
        }
    }

    /// 创建增强的文档服务，包含标签和分类功能
    pub fn with_enhanced_search(
        repository: Arc<dyn DocumentRepository + Send + Sync>,
        document_tag_repository: Arc<dyn DocumentTagRepository + Send + Sync>,
        document_category_repository: Arc<dyn DocumentCategoryRepository + Send + Sync>,
    ) -> Self {
        Self {
            repository,
            document_tag_repository: Some(document_tag_repository),
            document_category_repository: Some(document_category_repository),
        }
    }

    /// 创建新文档
    pub async fn create_document(&self, mut document: Document) -> Result<Document> {
        // 确保创建时间
        document.created_at = current_millis();
        document.updated_at = current_millis();

        // 验证必要字段
        if document.title.trim().is_empty() {
            return Err(anyhow::anyhow!("Document title cannot be empty"));
        }

        if document.content.trim().is_empty() {
            return Err(anyhow::anyhow!("Document content cannot be empty"));
        }

        self.repository.create(&document).await
    }

    /// 根据ID获取文档
    pub async fn get_document(&self, id: &str) -> Result<Option<Document>> {
        self.repository.find_by_id(id).await
    }

    /// 根据知识库获取文档列表
    pub async fn get_documents_by_knowledge_base(&self, kb_id: &str) -> Result<Vec<Document>> {
        self.repository
            .find_by_knowledge_base(kb_id, None, None)
            .await
    }

    /// 更新文档
    pub async fn update_document(&self, mut document: Document) -> Result<Document> {
        // 更新时间戳
        document.updated_at = current_millis();

        // 验证必要字段
        if document.title.trim().is_empty() {
            return Err(anyhow::anyhow!("Document title cannot be empty"));
        }

        self.repository.update(&document).await
    }

    /// 软删除文档
    pub async fn delete_document(&self, id: &str) -> Result<bool> {
        self.repository.delete(id).await
    }

    /// 搜索文档
    pub async fn search_documents(
        &self,
        query: &str,
        kb_id: Option<&str>,
    ) -> Result<Vec<Document>> {
        if query.trim().is_empty() {
            return Ok(Vec::new());
        }

        self.repository.search(query, kb_id, None, None).await
    }

    /// 高级搜索文档
    pub async fn advanced_search(
        &self,
        search_params: AdvancedSearchParams,
    ) -> Result<AdvancedSearchResult> {
        let mut documents = Vec::new();
        let mut filters_applied = Vec::new();

        // 基础文本搜索
        if let Some(ref query) = search_params.query {
            if !query.trim().is_empty() {
                documents = self
                    .repository
                    .search(
                        query,
                        search_params.knowledge_base_id.as_deref(),
                        None,
                        None,
                    )
                    .await?;
                filters_applied.push("text_search".to_string());
            }
        }

        // 如果没有基础查询，从知识库获取所有文档
        if documents.is_empty() && search_params.has_filters() {
            documents = match search_params.knowledge_base_id {
                Some(ref kb_id) => {
                    self.repository
                        .find_by_knowledge_base(kb_id, None, None)
                        .await?
                }
                None => {
                    // 如果没有指定知识库，需要分页获取所有文档
                    self.repository.list(Some(1000), Some(0)).await?
                }
            };
        }

        // 按标签过滤
        if let Some(ref tag_ids) = search_params.tag_ids {
            if !tag_ids.is_empty() {
                documents = self
                    .filter_by_tags(documents, tag_ids.as_slice(), search_params.match_all_tags)
                    .await?;
                filters_applied.push("tags".to_string());
            }
        }

        // 按分类过滤
        if let Some(ref category_ids) = search_params.category_ids {
            if !category_ids.is_empty() {
                documents = self
                    .filter_by_categories(
                        documents,
                        category_ids,
                        search_params.match_all_categories,
                    )
                    .await?;
                filters_applied.push("categories".to_string());
            }
        }

        // 按文件类型过滤
        if let Some(ref file_types) = search_params.file_types {
            if !file_types.is_empty() {
                documents = self.filter_by_file_types(documents, file_types);
                filters_applied.push("file_types".to_string());
            }
        }

        // 按作者过滤
        if let Some(ref authors) = search_params.authors {
            if !authors.is_empty() {
                documents = self.filter_by_authors(documents, authors);
                filters_applied.push("authors".to_string());
            }
        }

        // 按状态过滤
        if let Some(ref statuses) = search_params.statuses {
            if !statuses.is_empty() {
                documents = self.filter_by_statuses(documents, statuses);
                filters_applied.push("statuses".to_string());
            }
        }

        // 按日期范围过滤
        if let Some(ref date_range) = search_params.date_range {
            documents = self.filter_by_date_range(documents, date_range);
            filters_applied.push("date_range".to_string());
        }

        // 排序
        self.sort_documents(
            &mut documents,
            &search_params.sort_by,
            &search_params.sort_order,
        );

        // 分页
        let total_count = documents.len();
        let offset = search_params.offset.unwrap_or(0) as usize;
        let limit = search_params.limit.unwrap_or(50) as usize;

        let paginated_documents = if offset < documents.len() {
            let end = std::cmp::min(offset + limit, documents.len());
            documents[offset..end].to_vec()
        } else {
            Vec::new()
        };

        Ok(AdvancedSearchResult {
            documents: paginated_documents,
            total_count: total_count as i64,
            filters_applied,
            search_params,
        })
    }

    /// 按标签过滤文档
    async fn filter_by_tags(
        &self,
        documents: Vec<Document>,
        tag_ids: &[String],
        match_all: bool,
    ) -> Result<Vec<Document>> {
        if let Some(ref doc_tag_repo) = self.document_tag_repository {
            let mut filtered_documents = Vec::new();

            for document in documents {
                let document_tags = doc_tag_repo.find_by_document(&document.id).await?;
                let document_tag_ids: Vec<&str> =
                    document_tags.iter().map(|t| t.id.as_str()).collect();

                let matches = if match_all {
                    tag_ids
                        .iter()
                        .all(|tag_id| document_tag_ids.contains(&tag_id.as_str()))
                } else {
                    tag_ids
                        .iter()
                        .any(|tag_id| document_tag_ids.contains(&tag_id.as_str()))
                };

                if matches {
                    filtered_documents.push(document);
                }
            }

            Ok(filtered_documents)
        } else {
            Ok(documents) // 如果没有标签仓储，返回原始文档
        }
    }

    /// 按分类过滤文档
    async fn filter_by_categories(
        &self,
        documents: Vec<Document>,
        category_ids: &[String],
        match_all: bool,
    ) -> Result<Vec<Document>> {
        if let Some(ref doc_category_repo) = self.document_category_repository {
            let mut filtered_documents = Vec::new();

            for document in documents {
                let document_categories = doc_category_repo.find_by_document(&document.id).await?;
                let document_category_ids: Vec<&str> =
                    document_categories.iter().map(|c| c.id.as_str()).collect();

                let matches = if match_all {
                    category_ids
                        .iter()
                        .all(|category_id| document_category_ids.contains(&category_id.as_str()))
                } else {
                    category_ids
                        .iter()
                        .any(|category_id| document_category_ids.contains(&category_id.as_str()))
                };

                if matches {
                    filtered_documents.push(document);
                }
            }

            Ok(filtered_documents)
        } else {
            Ok(documents) // 如果没有分类仓储，返回原始文档
        }
    }

    /// 按文件类型过滤文档
    fn filter_by_file_types(
        &self,
        documents: Vec<Document>,
        file_types: &[String],
    ) -> Vec<Document> {
        documents
            .into_iter()
            .filter(|doc| {
                file_types
                    .iter()
                    .any(|ft| doc.file_type.to_lowercase().contains(&ft.to_lowercase()))
            })
            .collect()
    }

    /// 按作者过滤文档
    fn filter_by_authors(&self, documents: Vec<Document>, authors: &[String]) -> Vec<Document> {
        documents
            .into_iter()
            .filter(|doc| {
                authors.iter().any(|author| {
                    doc.metadata
                        .author
                        .as_ref()
                        .map(|a| a.to_lowercase().contains(&author.to_lowercase()))
                        .unwrap_or(false)
                })
            })
            .collect()
    }

    /// 按状态过滤文档
    fn filter_by_statuses(
        &self,
        documents: Vec<Document>,
        statuses: &[DocumentStatus],
    ) -> Vec<Document> {
        documents
            .into_iter()
            .filter(|doc| {
                statuses.iter().any(|status| {
                    std::mem::discriminant(&doc.status) == std::mem::discriminant(status)
                })
            })
            .collect()
    }

    /// 按日期范围过滤文档
    fn filter_by_date_range(
        &self,
        documents: Vec<Document>,
        date_range: &DateRangeFilter,
    ) -> Vec<Document> {
        documents
            .into_iter()
            .filter(|doc| {
                let doc_date = match date_range.date_field.as_str() {
                    "created_at" => doc.created_at,
                    "updated_at" => doc.updated_at,
                    _ => doc.created_at,
                };

                let after_start = date_range.start.is_none_or(|start| doc_date >= start);
                let before_end = date_range.end.is_none_or(|end| doc_date <= end);

                after_start && before_end
            })
            .collect()
    }

    /// 排序文档
    fn sort_documents(
        &self,
        documents: &mut [Document],
        sort_by: &SearchSortBy,
        sort_order: &SearchSortOrder,
    ) {
        documents.sort_by(|a, b| {
            let comparison = match sort_by {
                SearchSortBy::Relevance => std::cmp::Ordering::Equal, // 保持原顺序
                SearchSortBy::CreatedAt => a.created_at.cmp(&b.created_at),
                SearchSortBy::UpdatedAt => a.updated_at.cmp(&b.updated_at),
                SearchSortBy::Title => a.title.cmp(&b.title),
                SearchSortBy::FileSize => a.file_size.cmp(&b.file_size),
                SearchSortBy::Author => a
                    .metadata
                    .author
                    .as_deref()
                    .unwrap_or("")
                    .cmp(b.metadata.author.as_deref().unwrap_or("")),
            };

            match sort_order {
                SearchSortOrder::Asc => comparison,
                SearchSortOrder::Desc => comparison.reverse(),
            }
        });
    }

    /// 根据状态获取文档
    pub async fn get_documents_by_status(
        &self,
        status: DocumentStatus,
        _kb_id: Option<&str>,
    ) -> Result<Vec<Document>> {
        self.repository.find_by_status(&status, None, None).await
    }

    /// 根据作者获取文档
    pub async fn get_documents_by_author(
        &self,
        author: &str,
        kb_id: Option<&str>,
    ) -> Result<Vec<Document>> {
        if author.trim().is_empty() {
            return Ok(Vec::new());
        }

        self.repository.search(author, kb_id, None, None).await
    }

    /// 根据文件类型获取文档
    pub async fn get_documents_by_file_type(
        &self,
        file_type: &str,
        kb_id: Option<&str>,
    ) -> Result<Vec<Document>> {
        if file_type.trim().is_empty() {
            return Ok(Vec::new());
        }

        self.repository.search(file_type, kb_id, None, None).await
    }

    /// 分页获取文档列表
    pub async fn list_documents_with_pagination(
        &self,
        _kb_id: Option<&str>,
        limit: u32,
        offset: u32,
    ) -> Result<Vec<Document>> {
        let limit = if limit == 0 || limit > 100 { 50 } else { limit };
        self.repository.list(Some(limit), Some(offset)).await
    }

    /// 获取文档总数
    pub async fn count_documents(&self, _kb_id: Option<&str>) -> Result<i64> {
        self.repository.count().await
    }

    /// 根据状态统计文档数量
    pub async fn count_documents_by_status(
        &self,
        status: DocumentStatus,
        _kb_id: Option<&str>,
    ) -> Result<i64> {
        self.repository.count_by_status(&status).await
    }

    /// 更新文档状态
    pub async fn update_document_status(&self, id: &str, status: DocumentStatus) -> Result<bool> {
        self.repository.update_status(id, status).await
    }

    /// 更新文档处理元数据
    pub async fn update_processing_metadata(
        &self,
        id: &str,
        metadata: &DocumentProcessingMetadata,
    ) -> Result<bool> {
        self.repository
            .update_processing_metadata(id, metadata)
            .await
    }

    /// 根据校验和查找文档（重复检测）
    pub async fn find_documents_by_checksum(&self, checksum: &str) -> Result<Vec<Document>> {
        if checksum.trim().is_empty() {
            return Ok(Vec::new());
        }

        self.repository.find_by_checksum(checksum).await
    }

    /// 批量更新文档状态
    pub async fn batch_update_status(
        &self,
        ids: Vec<String>,
        status: DocumentStatus,
    ) -> Result<u64> {
        if ids.is_empty() {
            return Ok(0);
        }

        self.repository.batch_update_status(ids, status).await
    }

    /// 获取失败的文档
    pub async fn get_failed_documents(&self, kb_id: Option<&str>) -> Result<Vec<Document>> {
        self.repository.find_failed_documents(kb_id).await
    }

    /// 获取正在处理的文档
    pub async fn get_processing_documents(&self, kb_id: Option<&str>) -> Result<Vec<Document>> {
        self.repository.find_processing_documents(kb_id).await
    }

    /// 验证用户是否有权限访问文档
    pub async fn validate_document_access(
        &self,
        document_id: &str,
        user_id: &str,
        _kb_id: &str,
    ) -> Result<bool> {
        // 简单的权限检查，实际实现中需要根据具体的权限模型
        let document = self.get_document(document_id).await?;
        match document {
            Some(doc) => {
                // 检查文档是否属于指定的知识库
                Ok(doc.uploaded_by == user_id || doc.knowledge_base_id == _kb_id)
            }
            None => Ok(false),
        }
    }

    /// 获取文档统计信息
    pub async fn get_document_statistics(&self, kb_id: Option<&str>) -> Result<DocumentStatistics> {
        let total_count = self.count_documents(kb_id).await?;
        let indexed_count = self
            .count_documents_by_status(DocumentStatus::Indexed, kb_id)
            .await?;
        let processing_count = self
            .count_documents_by_status(DocumentStatus::Processing, kb_id)
            .await?;
        let failed_count = self
            .count_documents_by_status(DocumentStatus::Failed, kb_id)
            .await?;
        let archived_count = self
            .count_documents_by_status(DocumentStatus::Archived, kb_id)
            .await?;

        Ok(DocumentStatistics {
            total_count,
            indexed_count,
            processing_count,
            failed_count,
            archived_count,
        })
    }
}

/// 高级搜索参数
#[derive(Debug, Clone)]
pub struct AdvancedSearchParams {
    pub query: Option<String>,
    pub knowledge_base_id: Option<String>,
    pub tag_ids: Option<Vec<String>>,
    pub category_ids: Option<Vec<String>>,
    pub file_types: Option<Vec<String>>,
    pub authors: Option<Vec<String>>,
    pub statuses: Option<Vec<DocumentStatus>>,
    pub date_range: Option<DateRangeFilter>,
    pub match_all_tags: bool,
    pub match_all_categories: bool,
    pub sort_by: SearchSortBy,
    pub sort_order: SearchSortOrder,
    pub limit: Option<u32>,
    pub offset: Option<u32>,
}

impl AdvancedSearchParams {
    /// 检查是否有任何过滤条件
    pub fn has_filters(&self) -> bool {
        self.tag_ids.as_ref().is_some_and(|t| !t.is_empty())
            || self.category_ids.as_ref().is_some_and(|c| !c.is_empty())
            || self.file_types.as_ref().is_some_and(|f| !f.is_empty())
            || self.authors.as_ref().is_some_and(|a| !a.is_empty())
            || self.statuses.as_ref().is_some_and(|s| !s.is_empty())
            || self.date_range.is_some()
    }
}

impl Default for AdvancedSearchParams {
    fn default() -> Self {
        Self {
            query: None,
            knowledge_base_id: None,
            tag_ids: None,
            category_ids: None,
            file_types: None,
            authors: None,
            statuses: None,
            date_range: None,
            match_all_tags: false,
            match_all_categories: false,
            sort_by: SearchSortBy::Relevance,
            sort_order: SearchSortOrder::Desc,
            limit: Some(50),
            offset: Some(0),
        }
    }
}

/// 高级搜索结果
#[derive(Debug, Clone)]
pub struct AdvancedSearchResult {
    pub documents: Vec<Document>,
    pub total_count: i64,
    pub filters_applied: Vec<String>,
    pub search_params: AdvancedSearchParams,
}

impl AdvancedSearchResult {
    pub fn has_more(&self) -> bool {
        let offset = self.search_params.offset.unwrap_or(0) as i64;
        let limit = self.search_params.limit.unwrap_or(50) as i64;
        offset + limit < self.total_count
    }

    pub fn current_page(&self) -> i64 {
        let offset = self.search_params.offset.unwrap_or(0) as i64;
        let limit = self.search_params.limit.unwrap_or(50) as i64;
        if limit == 0 {
            1
        } else {
            (offset / limit) + 1
        }
    }

    pub fn total_pages(&self) -> i64 {
        let limit = self.search_params.limit.unwrap_or(50) as i64;
        if limit == 0 {
            1
        } else {
            (self.total_count + limit - 1) / limit
        }
    }
}

/// 日期范围过滤器
#[derive(Debug, Clone)]
pub struct DateRangeFilter {
    pub start: Option<i64>,
    pub end: Option<i64>,
    pub date_field: String, // "created_at" 或 "updated_at"
}

/// 搜索排序字段
#[derive(Debug, Clone)]
pub enum SearchSortBy {
    Relevance,
    CreatedAt,
    UpdatedAt,
    Title,
    FileSize,
    Author,
}

/// 搜索排序顺序
#[derive(Debug, Clone)]
pub enum SearchSortOrder {
    Asc,
    Desc,
}

/// 文档统计信息
#[derive(Debug, Clone)]
pub struct DocumentStatistics {
    pub total_count: i64,
    pub indexed_count: i64,
    pub processing_count: i64,
    pub failed_count: i64,
    pub archived_count: i64,
}

impl DocumentStatistics {
    pub fn ready_count(&self) -> i64 {
        self.total_count - self.processing_count - self.failed_count - self.archived_count
    }

    pub fn success_rate(&self) -> f64 {
        if self.total_count == 0 {
            0.0
        } else {
            (self.indexed_count as f64) / (self.total_count as f64) * 100.0
        }
    }
}
