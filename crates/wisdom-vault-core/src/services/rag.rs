use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::services::{
    ChatMessage, HybridSearchService, LLMService, MessageRole,
    hybrid_search::HybridSearchRequest,
};
use wisdom_vault_database::models::{Document, DocumentChunk};

/// RAG 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RAGConfig {
    /// 默认检索文档数量
    pub default_retrieval_count: u32,
    /// 最大检索文档数量
    pub max_retrieval_count: u32,
    /// 上下文窗口大小 (token数)
    pub context_window_size: u32,
    /// 最大上下文长度 (字符数)
    pub max_context_length: usize,
    /// 文档相关性阈值
    pub relevance_threshold: f64,
    /// 启用查询重写
    pub enable_query_rewriting: bool,
    /// 启用上下文压缩
    pub enable_context_compression: bool,
    /// 答案最小置信度
    pub min_confidence_threshold: f64,
}

impl Default for RAGConfig {
    fn default() -> Self {
        Self {
            default_retrieval_count: 5,
            max_retrieval_count: 20,
            context_window_size: 8000,
            max_context_length: 16000,
            relevance_threshold: 0.7,
            enable_query_rewriting: true,
            enable_context_compression: true,
            min_confidence_threshold: 0.6,
        }
    }
}

/// RAG 请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RAGRequest {
    /// 用户问题
    pub question: String,
    /// 知识库 ID
    pub knowledge_base_id: String,
    /// 对话上下文 (可选)
    pub conversation_context: Option<ConversationContext>,
    /// 检索参数 (可选)
    pub retrieval_params: Option<RetrievalParams>,
    /// LLM 参数 (可选)
    pub llm_params: Option<LLMParams>,
}

/// 对话上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationContext {
    /// 对话 ID
    pub conversation_id: String,
    /// 历史消息 (最近N条)
    pub history: Vec<ChatMessage>,
    /// 用户偏好
    pub user_preferences: Option<HashMap<String, String>>,
}

/// 检索参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetrievalParams {
    /// 检索数量
    pub count: Option<u32>,
    /// 相关性阈值
    pub relevance_threshold: Option<f64>,
    /// 关键词权重
    pub keyword_weight: Option<f64>,
    /// 向量权重
    pub vector_weight: Option<f64>,
}

/// LLM 参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMParams {
    /// 模型名称
    pub model: Option<String>,
    /// 最大 token 数
    pub max_tokens: Option<u32>,
    /// 温度参数
    pub temperature: Option<f32>,
}

/// RAG 响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RAGResponse {
    /// 生成的答案
    pub answer: String,
    /// 答案置信度 (0.0-1.0)
    pub confidence: f64,
    /// 引用的文档来源
    pub sources: Vec<DocumentSource>,
    /// 使用的模型
    pub model: String,
    /// Token 使用情况
    pub token_usage: Option<TokenUsageInfo>,
    /// 检索统计
    pub retrieval_stats: RetrievalStats,
    /// 响应时间 (毫秒)
    pub response_time_ms: u64,
}

/// 文档来源
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentSource {
    /// 文档 ID
    pub document_id: String,
    /// 文档标题
    pub title: String,
    /// 相关片段
    pub content_snippet: String,
    /// 相关性分数
    pub relevance_score: f64,
    /// 文档来源类型
    pub source_type: String,
}

/// Token 使用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenUsageInfo {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// 检索统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetrievalStats {
    /// 检索到的文档数量
    pub retrieved_count: u32,
    /// 过滤后的文档数量
    pub filtered_count: u32,
    /// 检索时间 (毫秒)
    pub retrieval_time_ms: u64,
    /// 平均相关性分数
    pub avg_relevance_score: f64,
}

/// RAG 处理上下文
#[derive(Debug)]
struct RAGProcessingContext {
    pub processed_question: String,
    pub retrieved_documents: Vec<(Document, f64)>,
    pub retrieved_chunks: Vec<(DocumentChunk, f64)>,
    pub context_text: String,
    pub sources: Vec<DocumentSource>,
}

/// RAG 服务
pub struct RAGService {
    llm_service: Arc<dyn LLMService>,
    search_service: Arc<HybridSearchService>,
    config: RAGConfig,
    query_cache: Arc<RwLock<HashMap<String, RAGResponse>>>,
}

impl RAGService {
    /// 创建新的 RAG 服务
    pub fn new(
        llm_service: Arc<dyn LLMService>,
        search_service: Arc<HybridSearchService>,
        config: RAGConfig,
    ) -> Self {
        Self {
            llm_service,
            search_service,
            config,
            query_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 处理 RAG 问答请求
    pub async fn answer_question(&self, request: RAGRequest) -> Result<RAGResponse> {
        let start_time = std::time::Instant::now();

        info!("Processing RAG request for question: {}", request.question);

        // 1. 问题预处理和理解
        let processed_question = self
            .preprocess_question(&request.question, &request.conversation_context)
            .await?;

        // 2. 检索相关文档
        let (retrieved_docs, retrieved_chunks, retrieval_time) = self
            .retrieve_relevant_documents(&processed_question, &request)
            .await?;

        // 3. 构建上下文
        let context = self
            .build_context(&retrieved_docs, &retrieved_chunks, &processed_question)
            .await?;

        // 4. 生成答案
        let llm_response = self
            .generate_answer(
                &processed_question,
                &context.context_text,
                &request.llm_params,
            )
            .await?;

        // 5. 后处理和质量评估
        let confidence = self
            .assess_answer_confidence(&llm_response.content, &context)
            .await?;

        // 6. 计算统计信息
        let retrieval_stats = RetrievalStats {
            retrieved_count: retrieved_docs.len() as u32,
            filtered_count: context.sources.len() as u32,
            retrieval_time_ms: retrieval_time,
            avg_relevance_score: if !retrieved_docs.is_empty() {
                retrieved_docs.iter().map(|(_, score)| score).sum::<f64>()
                    / retrieved_docs.len() as f64
            } else {
                0.0
            },
        };

        let response = RAGResponse {
            answer: llm_response.content,
            confidence,
            sources: context.sources,
            model: llm_response.model,
            token_usage: llm_response.usage.map(|u| TokenUsageInfo {
                prompt_tokens: u.prompt_tokens,
                completion_tokens: u.completion_tokens,
                total_tokens: u.total_tokens,
            }),
            retrieval_stats,
            response_time_ms: start_time.elapsed().as_millis() as u64,
        };

        info!(
            "RAG request completed in {}ms with confidence {:.2}",
            response.response_time_ms, response.confidence
        );

        Ok(response)
    }

    /// 问题预处理和理解
    async fn preprocess_question(
        &self,
        question: &str,
        context: &Option<ConversationContext>,
    ) -> Result<String> {
        if !self.config.enable_query_rewriting {
            return Ok(question.to_string());
        }

        // 如果有对话上下文，进行查询重写
        if let Some(ctx) = context {
            if !ctx.history.is_empty() {
                return self.rewrite_query_with_context(question, ctx).await;
            }
        }

        // 基础预处理：清理和标准化
        let processed = question
            .trim()
            .replace(['\n', '\r', '\t'], " ")
            .split_whitespace()
            .collect::<Vec<_>>()
            .join(" ");

        Ok(processed)
    }

    /// 基于对话上下文重写查询
    async fn rewrite_query_with_context(
        &self,
        question: &str,
        context: &ConversationContext,
    ) -> Result<String> {
        let history_text = context
            .history
            .iter()
            .take(3) // 只使用最近3轮对话
            .map(|msg| format!("{:?}: {}", msg.role, msg.content))
            .collect::<Vec<_>>()
            .join("\n");

        let rewrite_prompt = format!(
            "基于以下对话历史，将用户的最新问题重写为一个独立的、完整的问题：

对话历史：
{}

最新问题：{}

请将问题重写为一个独立的、完整的问题，保持原意但加入必要的上下文信息：",
            history_text, question
        );

        let messages = vec![ChatMessage {
            role: MessageRole::User,
            content: rewrite_prompt,
        }];

        match self
            .llm_service
            .chat_completion(
                messages,
                Some("gpt-4o-mini".to_string()),
                Some(100),
                Some(0.3),
            )
            .await
        {
            Ok(response) => Ok(response.content.trim().to_string()),
            Err(e) => {
                warn!("Query rewriting failed, using original question: {}", e);
                Ok(question.to_string())
            }
        }
    }

    /// 检索相关文档
    async fn retrieve_relevant_documents(
        &self,
        question: &str,
        request: &RAGRequest,
    ) -> Result<(Vec<(Document, f64)>, Vec<(DocumentChunk, f64)>, u64)> {
        let retrieval_start = std::time::Instant::now();

        let retrieval_count = request
            .retrieval_params
            .as_ref()
            .and_then(|p| p.count)
            .unwrap_or(self.config.default_retrieval_count)
            .min(self.config.max_retrieval_count);

        let hybrid_request = HybridSearchRequest {
            query_text: question.to_string(),
            knowledge_base_id: Some(request.knowledge_base_id.clone()),
            limit: Some(retrieval_count),
            keyword_weight: request
                .retrieval_params
                .as_ref()
                .and_then(|p| p.keyword_weight),
            vector_weight: request
                .retrieval_params
                .as_ref()
                .and_then(|p| p.vector_weight),
            similarity_threshold: request
                .retrieval_params
                .as_ref()
                .and_then(|p| p.relevance_threshold),
            ..Default::default()
        };

        let search_results = self.search_service.hybrid_search(hybrid_request).await?;

        let retrieval_time = retrieval_start.elapsed().as_millis() as u64;

        // 过滤低相关性文档
        let threshold = request
            .retrieval_params
            .as_ref()
            .and_then(|p| p.relevance_threshold)
            .unwrap_or(self.config.relevance_threshold);

        let filtered_docs: Vec<(Document, f64)> = search_results
            .results
            .into_iter()
            .filter(|result| result.final_score >= threshold)
            .map(|result| (result.document, result.final_score))
            .collect();

        // 对于文档块，我们暂时使用空的向量，因为混合搜索结果中没有单独的块
        let filtered_chunks: Vec<(DocumentChunk, f64)> = Vec::new();

        debug!(
            "Retrieved {} documents and {} chunks in {}ms",
            filtered_docs.len(),
            filtered_chunks.len(),
            retrieval_time
        );

        Ok((filtered_docs, filtered_chunks, retrieval_time))
    }

    /// 构建 RAG 上下文
    async fn build_context(
        &self,
        documents: &[(Document, f64)],
        chunks: &[(DocumentChunk, f64)],
        question: &str,
    ) -> Result<RAGProcessingContext> {
        let mut context_parts = Vec::new();
        let mut sources = Vec::new();
        let mut current_length = 0;

        // 首先添加最相关的文档片段
        let mut all_content: Vec<(String, f64, DocumentSource)> = Vec::new();

        // 添加文档内容
        for (doc, score) in documents {
            let content = &doc.content;
            let snippet = if content.len() > 500 {
                format!("{}...", &content[..500])
            } else {
                content.clone()
            };

            let source = DocumentSource {
                document_id: doc.id.to_string(),
                title: doc.title.clone(),
                content_snippet: snippet.clone(),
                relevance_score: *score,
                source_type: "document".to_string(),
            };

            all_content.push((content.clone(), *score, source));
        }

        // 添加文档块内容
        for (chunk, score) in chunks {
            let source = DocumentSource {
                document_id: chunk.document_id.to_string(),
                title: format!("Document Chunk {}", chunk.id),
                content_snippet: if chunk.content.len() > 200 {
                    format!("{}...", &chunk.content[..200])
                } else {
                    chunk.content.clone()
                },
                relevance_score: *score,
                source_type: "chunk".to_string(),
            };

            all_content.push((chunk.content.clone(), *score, source));
        }

        // 按相关性排序
        all_content.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        // 构建上下文，确保不超过长度限制
        for (content, _score, source) in all_content {
            let content_to_add = if self.config.enable_context_compression {
                self.compress_content(&content, question).await?
            } else {
                content
            };

            if current_length + content_to_add.len() > self.config.max_context_length {
                break;
            }

            context_parts.push(content_to_add);
            sources.push(source);
            current_length += context_parts.last().unwrap().len();
        }

        let context_text = if context_parts.is_empty() {
            "没有找到相关的文档内容。".to_string()
        } else {
            format!(
                "以下是相关的文档内容：\n\n{}",
                context_parts
                    .iter()
                    .enumerate()
                    .map(|(i, content)| format!("文档{}：\n{}\n", i + 1, content))
                    .collect::<Vec<_>>()
                    .join("\n")
            )
        };

        Ok(RAGProcessingContext {
            processed_question: question.to_string(),
            retrieved_documents: documents.to_vec(),
            retrieved_chunks: chunks.to_vec(),
            context_text,
            sources,
        })
    }

    /// 压缩内容 (简单实现)
    async fn compress_content(&self, content: &str, question: &str) -> Result<String> {
        // 简单的内容压缩：提取与问题最相关的段落
        let paragraphs: Vec<&str> = content
            .split('\n')
            .filter(|p| !p.trim().is_empty())
            .collect();

        if paragraphs.len() <= 3 {
            return Ok(content.to_string());
        }

        let question_lower = question.to_lowercase();
        let mut scored_paragraphs: Vec<(&str, usize)> = paragraphs
            .iter()
            .map(|p| {
                let p_lower = p.to_lowercase();
                let score = question_lower
                    .split_whitespace()
                    .filter(|word| p_lower.contains(word))
                    .count();
                (*p, score)
            })
            .collect();

        scored_paragraphs.sort_by(|a, b| b.1.cmp(&a.1));

        let selected = scored_paragraphs
            .into_iter()
            .take(3)
            .map(|(p, _)| p)
            .collect::<Vec<_>>()
            .join("\n");

        Ok(selected)
    }

    /// 生成答案
    async fn generate_answer(
        &self,
        question: &str,
        context: &str,
        llm_params: &Option<LLMParams>,
    ) -> Result<crate::services::LLMResponse> {
        let system_prompt = "你是一个智能知识库助手。请基于提供的文档内容回答用户的问题。\n\n规则：\n1. 只基于提供的文档内容回答问题\n2. 如果文档中没有相关信息，请明确说明\n3. 回答要准确、简洁、有条理\n4. 如果可能，请引用具体的文档内容\n5. 保持专业和友好的语调";

        let user_prompt = format!(
            "基于以下文档内容回答问题：\n\n{}\n\n问题：{}\n\n请提供准确的答案：",
            context, question
        );

        let messages = vec![
            ChatMessage {
                role: MessageRole::System,
                content: system_prompt.to_string(),
            },
            ChatMessage {
                role: MessageRole::User,
                content: user_prompt,
            },
        ];

        let model = llm_params.as_ref().and_then(|p| p.model.clone());
        let max_tokens = llm_params.as_ref().and_then(|p| p.max_tokens);
        let temperature = llm_params.as_ref().and_then(|p| p.temperature);

        self.llm_service
            .chat_completion(messages, model, max_tokens, temperature)
            .await
    }

    /// 评估答案置信度
    async fn assess_answer_confidence(
        &self,
        answer: &str,
        context: &RAGProcessingContext,
    ) -> Result<f64> {
        // 简单的置信度评估算法
        let mut confidence = 0.5; // 基础置信度

        // 基于检索文档数量调整
        if !context.retrieved_documents.is_empty() {
            confidence += 0.1 * (context.retrieved_documents.len() as f64).min(5.0) / 5.0;
        }

        // 基于平均相关性分数调整
        if !context.retrieved_documents.is_empty() {
            let avg_score = context
                .retrieved_documents
                .iter()
                .map(|(_, score)| score)
                .sum::<f64>()
                / context.retrieved_documents.len() as f64;
            confidence += 0.2 * avg_score;
        }

        // 基于答案长度和质量调整
        if answer.len() > 50 && !answer.contains("没有找到") && !answer.contains("不确定") {
            confidence += 0.1;
        }

        // 如果答案明确表示不确定或找不到信息
        if answer.contains("不确定")
            || answer.contains("没有相关信息")
            || answer.contains("无法确定")
        {
            confidence *= 0.5;
        }

        Ok(confidence.min(1.0).max(0.0))
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<bool> {
        // 检查 LLM 服务
        if !self.llm_service.health_check().await.unwrap_or(false) {
            return Ok(false);
        }

        // 检查搜索服务 (简单检查)
        // 这里可以添加更复杂的健康检查逻辑

        Ok(true)
    }

    /// 清理缓存
    pub async fn clear_cache(&self) {
        let mut cache = self.query_cache.write().await;
        cache.clear();
        info!("RAG service cache cleared");
    }
}

#[cfg(test)]
mod tests {
    use wisdom_vault_common::db::next_id;

    use super::*;

    #[test]
    fn test_rag_config_default() {
        let config = RAGConfig::default();
        assert_eq!(config.default_retrieval_count, 5);
        assert_eq!(config.max_retrieval_count, 20);
        assert_eq!(config.relevance_threshold, 0.7);
    }

    #[test]
    fn test_rag_request_serialization() {
        let request = RAGRequest {
            question: "What is AI?".to_string(),
            knowledge_base_id: next_id(),
            conversation_context: None,
            retrieval_params: None,
            llm_params: None,
        };

        let json = serde_json::to_string(&request).unwrap();
        let deserialized: RAGRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(request.question, deserialized.question);
    }
}
