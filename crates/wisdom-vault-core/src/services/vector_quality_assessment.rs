use anyhow::Result;
use serde::{Deserialize, Serialize};

use wisdom_vault_common::{db::next_id, time::current_millis};
use wisdom_vault_database::models::{
    DocumentEmbedding, NormalizationMethod, SimilarityDistribution, VectorQualityMetrics,
};

/// 向量质量评估配置
#[derive(Debug, Clone)]
pub struct VectorQualityConfig {
    /// 质量阈值
    pub quality_threshold: f64,
    /// 异常检测阈值
    pub anomaly_threshold: f64,
    /// 最小向量模长
    pub min_magnitude: f64,
    /// 最大稀疏率
    pub max_sparsity_ratio: f64,
    /// 相似度计算样本数
    pub similarity_sample_size: usize,
}

impl Default for VectorQualityConfig {
    fn default() -> Self {
        Self {
            quality_threshold: 0.8,
            anomaly_threshold: 0.95,
            min_magnitude: 0.1,
            max_sparsity_ratio: 0.9,
            similarity_sample_size: 100,
        }
    }
}

/// 向量质量评估系统
pub struct VectorQualityAssessment {
    config: VectorQualityConfig,
}

impl VectorQualityAssessment {
    /// 创建新的向量质量评估系统
    pub fn new(config: VectorQualityConfig) -> Self {
        Self { config }
    }

    /// 评估单个向量的质量
    pub async fn assess_single_vector(
        &self,
        embedding: &DocumentEmbedding,
    ) -> Result<VectorQualityMetrics> {
        let vector = &embedding.embedding;

        // 1. 基本统计指标
        let magnitude = self.calculate_magnitude(vector);
        let sparsity_ratio = self.calculate_sparsity_ratio(vector);
        let dimension_consistency = vector.len() == embedding.dimension as usize;

        // 2. 异常检测
        let anomaly_score = self.detect_anomaly(vector);

        // 3. 整体质量评分
        let quality_score =
            self.calculate_quality_score(magnitude, sparsity_ratio, anomaly_score.unwrap_or(0.0));

        // 4. 相似度分布 (单向量情况下使用默认值)
        let similarity_distribution = SimilarityDistribution {
            mean: 0.0,
            std_dev: 1.0,
            min: -1.0,
            max: 1.0,
            percentile_25: -0.5,
            percentile_50: 0.0,
            percentile_75: 0.5,
            percentile_95: 0.95,
        };

        Ok(VectorQualityMetrics {
            id: next_id(),
            embedding_id: embedding.id.clone(),
            dimension_consistency,
            magnitude,
            sparsity_ratio,
            norm_type: self.determine_normalization_type(vector),
            quality_score,
            anomaly_score,
            similarity_distribution,
            computed_at: current_millis(),
        })
    }

    /// 评估向量批次的质量
    pub async fn assess_vector_batch(
        &self,
        embeddings: &[DocumentEmbedding],
    ) -> Result<Vec<VectorQualityMetrics>> {
        if embeddings.is_empty() {
            return Ok(Vec::new());
        }

        // 1. 计算批次间的相似度分布
        let similarity_distribution = self
            .calculate_batch_similarity_distribution(embeddings)
            .await?;

        // 2. 为每个向量计算质量指标
        let mut quality_metrics = Vec::new();

        for embedding in embeddings {
            let vector = &embedding.embedding;

            // 基本指标
            let magnitude = self.calculate_magnitude(vector);
            let sparsity_ratio = self.calculate_sparsity_ratio(vector);
            let dimension_consistency = vector.len() == embedding.dimension as usize;

            // 异常检测 (基于批次上下文)
            let anomaly_score = self.detect_batch_anomaly(vector, embeddings);

            // 质量评分
            let quality_score = self.calculate_quality_score(
                magnitude,
                sparsity_ratio,
                anomaly_score.unwrap_or(0.0),
            );

            let metrics = VectorQualityMetrics {
                id: next_id(),
                embedding_id: embedding.id.clone(),
                dimension_consistency,
                magnitude,
                sparsity_ratio,
                norm_type: self.determine_normalization_type(vector),
                quality_score,
                anomaly_score,
                similarity_distribution: similarity_distribution.clone(),
                computed_at: current_millis(),
            };

            quality_metrics.push(metrics);
        }

        Ok(quality_metrics)
    }

    /// 计算向量模长
    fn calculate_magnitude(&self, vector: &[f32]) -> f64 {
        vector
            .iter()
            .map(|x| (*x as f64).powi(2))
            .sum::<f64>()
            .sqrt()
    }

    /// 计算稀疏率
    fn calculate_sparsity_ratio(&self, vector: &[f32]) -> f64 {
        let zero_count = vector.iter().filter(|&&x| x.abs() < 1e-6).count();
        zero_count as f64 / vector.len() as f64
    }

    /// 异常检测 (单向量)
    fn detect_anomaly(&self, vector: &[f32]) -> Option<f64> {
        // 检查异常值
        let magnitude = self.calculate_magnitude(vector);
        let sparsity = self.calculate_sparsity_ratio(vector);

        // 基于统计规则的异常检测
        let mut anomaly_factors = Vec::new();

        // 1. 模长异常
        if magnitude < self.config.min_magnitude {
            anomaly_factors.push(0.8); // 模长过小
        }
        if magnitude > 100.0 {
            anomaly_factors.push(0.9); // 模长过大
        }

        // 2. 稀疏度异常
        if sparsity > self.config.max_sparsity_ratio {
            anomaly_factors.push(0.7); // 过于稀疏
        }

        // 3. 值分布异常
        let max_val = vector.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));
        let min_val = vector.iter().fold(f32::INFINITY, |a, &b| a.min(b));
        if max_val - min_val > 100.0 {
            anomaly_factors.push(0.6); // 值域过大
        }

        if anomaly_factors.is_empty() {
            None
        } else {
            Some(anomaly_factors.iter().sum::<f64>() / anomaly_factors.len() as f64)
        }
    }

    /// 批次内异常检测
    fn detect_batch_anomaly(&self, vector: &[f32], batch: &[DocumentEmbedding]) -> Option<f64> {
        if batch.len() < 2 {
            return self.detect_anomaly(vector);
        }

        // 计算与批次中其他向量的平均相似度
        let mut similarities = Vec::new();

        for other_embedding in batch {
            if other_embedding.embedding.len() == vector.len() {
                let similarity =
                    self.calculate_cosine_similarity(vector, &other_embedding.embedding);
                similarities.push(similarity);
            }
        }

        if similarities.is_empty() {
            return self.detect_anomaly(vector);
        }

        // 计算相似度统计
        let mean_similarity: f64 = similarities.iter().sum::<f64>() / similarities.len() as f64;
        let variance: f64 = similarities
            .iter()
            .map(|s| (s - mean_similarity).powi(2))
            .sum::<f64>()
            / similarities.len() as f64;
        let std_dev = variance.sqrt();

        // 如果平均相似度过低，可能是异常向量
        if mean_similarity < 0.3 && std_dev > 0.2 {
            Some(0.8)
        } else if mean_similarity < 0.1 {
            Some(0.9)
        } else {
            self.detect_anomaly(vector)
        }
    }

    /// 计算质量评分
    fn calculate_quality_score(
        &self,
        magnitude: f64,
        sparsity_ratio: f64,
        anomaly_score: f64,
    ) -> f64 {
        let mut score = 1.0;

        // 1. 模长评分 (理想范围: 0.5-2.0)
        let magnitude_score = if magnitude >= 0.5 && magnitude <= 2.0 {
            1.0
        } else if magnitude >= 0.1 && magnitude <= 5.0 {
            0.8
        } else {
            0.4
        };

        // 2. 稀疏度评分 (稀疏度越低越好)
        let sparsity_score = (1.0 - sparsity_ratio).max(0.0);

        // 3. 异常评分 (异常分数越低越好)
        let anomaly_penalty = anomaly_score * 0.5;

        // 综合评分
        score = (magnitude_score * 0.4 + sparsity_score * 0.4 + (1.0 - anomaly_penalty) * 0.2)
            .max(0.0)
            .min(1.0);

        score
    }

    /// 确定标准化类型
    fn determine_normalization_type(&self, vector: &[f32]) -> NormalizationMethod {
        let magnitude = self.calculate_magnitude(vector);

        // 检查是否接近L2标准化 (模长接近1)
        if (magnitude - 1.0).abs() < 0.1 {
            return NormalizationMethod::L2;
        }

        // 检查是否接近L1标准化
        let l1_norm: f64 = vector.iter().map(|x| (*x as f64).abs()).sum();
        if (l1_norm - 1.0).abs() < 0.1 {
            return NormalizationMethod::L1;
        }

        // 检查是否为MinMax标准化 (值在0-1范围内)
        let min_val = vector.iter().fold(f32::INFINITY, |a, &b| a.min(b));
        let max_val = vector.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));
        if min_val >= 0.0 && max_val <= 1.0 && (max_val - min_val) > 0.8 {
            return NormalizationMethod::MinMax;
        }

        NormalizationMethod::None
    }

    /// 计算批次相似度分布
    async fn calculate_batch_similarity_distribution(
        &self,
        embeddings: &[DocumentEmbedding],
    ) -> Result<SimilarityDistribution> {
        if embeddings.len() < 2 {
            return Ok(SimilarityDistribution {
                mean: 0.0,
                std_dev: 1.0,
                min: -1.0,
                max: 1.0,
                percentile_25: -0.5,
                percentile_50: 0.0,
                percentile_75: 0.5,
                percentile_95: 0.95,
            });
        }

        let mut similarities = Vec::new();

        // 计算所有向量对的相似度 (采样以提高效率)
        let sample_size = self.config.similarity_sample_size.min(embeddings.len());
        let step = if embeddings.len() > sample_size {
            embeddings.len() / sample_size
        } else {
            1
        };

        for i in (0..embeddings.len()).step_by(step) {
            for j in (i + 1..embeddings.len()).step_by(step) {
                if embeddings[i].embedding.len() == embeddings[j].embedding.len() {
                    let similarity = self.calculate_cosine_similarity(
                        &embeddings[i].embedding,
                        &embeddings[j].embedding,
                    );
                    similarities.push(similarity);
                }
            }
        }

        if similarities.is_empty() {
            return Ok(SimilarityDistribution {
                mean: 0.0,
                std_dev: 1.0,
                min: -1.0,
                max: 1.0,
                percentile_25: -0.5,
                percentile_50: 0.0,
                percentile_75: 0.5,
                percentile_95: 0.95,
            });
        }

        // 计算统计指标
        similarities.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let mean = similarities.iter().sum::<f64>() / similarities.len() as f64;
        let variance = similarities.iter().map(|s| (s - mean).powi(2)).sum::<f64>()
            / similarities.len() as f64;
        let std_dev = variance.sqrt();

        let min = similarities[0];
        let max = similarities[similarities.len() - 1];
        let percentile_25 = similarities[similarities.len() / 4];
        let percentile_50 = similarities[similarities.len() / 2];
        let percentile_75 = similarities[similarities.len() * 3 / 4];
        let percentile_95 = similarities[similarities.len() * 95 / 100];

        Ok(SimilarityDistribution {
            mean,
            std_dev,
            min,
            max,
            percentile_25,
            percentile_50,
            percentile_75,
            percentile_95,
        })
    }

    /// 计算余弦相似度
    fn calculate_cosine_similarity(&self, vec1: &[f32], vec2: &[f32]) -> f64 {
        if vec1.len() != vec2.len() {
            return 0.0;
        }

        let dot_product: f64 = vec1
            .iter()
            .zip(vec2.iter())
            .map(|(a, b)| (*a as f64) * (*b as f64))
            .sum();

        let norm1: f64 = vec1.iter().map(|x| (*x as f64).powi(2)).sum::<f64>().sqrt();
        let norm2: f64 = vec2.iter().map(|x| (*x as f64).powi(2)).sum::<f64>().sqrt();

        if norm1 == 0.0 || norm2 == 0.0 {
            return 0.0;
        }

        dot_product / (norm1 * norm2)
    }

    /// 验证向量质量是否满足要求
    pub fn validate_quality(&self, metrics: &VectorQualityMetrics) -> bool {
        metrics.quality_score >= self.config.quality_threshold
            && metrics
                .anomaly_score
                .map_or(true, |score| score < self.config.anomaly_threshold)
            && metrics.dimension_consistency
            && metrics.magnitude >= self.config.min_magnitude
            && metrics.sparsity_ratio <= self.config.max_sparsity_ratio
    }

    /// 生成质量报告
    pub fn generate_quality_report(&self, metrics: &[VectorQualityMetrics]) -> QualityReport {
        if metrics.is_empty() {
            return QualityReport::default();
        }

        let total_count = metrics.len();
        let valid_count = metrics.iter().filter(|m| self.validate_quality(m)).count();
        let avg_quality_score =
            metrics.iter().map(|m| m.quality_score).sum::<f64>() / total_count as f64;
        let avg_magnitude = metrics.iter().map(|m| m.magnitude).sum::<f64>() / total_count as f64;
        let avg_sparsity =
            metrics.iter().map(|m| m.sparsity_ratio).sum::<f64>() / total_count as f64;

        let dimension_consistency_rate =
            metrics.iter().filter(|m| m.dimension_consistency).count() as f64 / total_count as f64;

        QualityReport {
            total_vectors: total_count,
            valid_vectors: valid_count,
            quality_pass_rate: valid_count as f64 / total_count as f64,
            avg_quality_score,
            avg_magnitude,
            avg_sparsity_ratio: avg_sparsity,
            dimension_consistency_rate,
            recommendations: self.generate_recommendations(metrics),
        }
    }

    /// 生成改进建议
    fn generate_recommendations(&self, metrics: &[VectorQualityMetrics]) -> Vec<String> {
        let mut recommendations = Vec::new();

        if metrics.is_empty() {
            return recommendations;
        }

        let avg_quality =
            metrics.iter().map(|m| m.quality_score).sum::<f64>() / metrics.len() as f64;
        let avg_sparsity =
            metrics.iter().map(|m| m.sparsity_ratio).sum::<f64>() / metrics.len() as f64;
        let anomaly_count = metrics.iter().filter(|m| m.anomaly_score.is_some()).count();

        if avg_quality < 0.7 {
            recommendations.push("总体向量质量偏低，建议检查文本预处理和模型配置".to_string());
        }

        if avg_sparsity > 0.8 {
            recommendations
                .push("向量稀疏度过高，建议调整模型参数或使用更高维度的模型".to_string());
        }

        if anomaly_count as f64 / metrics.len() as f64 > 0.1 {
            recommendations
                .push("检测到较多异常向量，建议检查输入文本质量和模型稳定性".to_string());
        }

        let inconsistent_count = metrics.iter().filter(|m| !m.dimension_consistency).count();
        if inconsistent_count > 0 {
            recommendations.push(format!(
                "发现{}个维度不一致的向量，请检查模型配置",
                inconsistent_count
            ));
        }

        if recommendations.is_empty() {
            recommendations.push("向量质量良好，无需特殊优化".to_string());
        }

        recommendations
    }
}

/// 质量评估报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityReport {
    pub total_vectors: usize,
    pub valid_vectors: usize,
    pub quality_pass_rate: f64,
    pub avg_quality_score: f64,
    pub avg_magnitude: f64,
    pub avg_sparsity_ratio: f64,
    pub dimension_consistency_rate: f64,
    pub recommendations: Vec<String>,
}

impl Default for QualityReport {
    fn default() -> Self {
        Self {
            total_vectors: 0,
            valid_vectors: 0,
            quality_pass_rate: 0.0,
            avg_quality_score: 0.0,
            avg_magnitude: 0.0,
            avg_sparsity_ratio: 0.0,
            dimension_consistency_rate: 0.0,
            recommendations: Vec::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    fn create_test_embedding(vector: Vec<f32>) -> DocumentEmbedding {
        DocumentEmbedding {
            id: next_id(),
            chunk_id: next_id(),
            embedding: vector,
            model_name: "test-model".to_string(),
            model_version: "1.0".to_string(),
            dimension: 3,
            embedding_type: wisdom_vault_database::models::EmbeddingType::Dense,
            processing_metadata: wisdom_vault_database::models::EmbeddingMetadata {
                processing_time_ms: 100,
                normalization_method: "L2".to_string(),
                chunk_preprocessing: vec!["tokenization".to_string()],
                similarity_threshold: Some(0.8),
            },
            created_at: current_millis(),
        }
    }

    #[tokio::test]
    async fn test_single_vector_assessment() {
        let config = VectorQualityConfig::default();
        let assessment = VectorQualityAssessment::new(config);

        let embedding = create_test_embedding(vec![0.6, 0.8, 0.0]); // L2 normalized
        let metrics = assessment.assess_single_vector(&embedding).await.unwrap();

        assert!(metrics.quality_score > 0.5);
        assert_eq!(metrics.norm_type, NormalizationMethod::L2);
        assert!(metrics.dimension_consistency);
    }

    #[tokio::test]
    async fn test_batch_assessment() {
        let config = VectorQualityConfig::default();
        let assessment = VectorQualityAssessment::new(config);

        let embeddings = vec![
            create_test_embedding(vec![0.6, 0.8, 0.0]),
            create_test_embedding(vec![0.8, 0.6, 0.0]),
            create_test_embedding(vec![0.0, 0.6, 0.8]),
        ];

        let metrics = assessment.assess_vector_batch(&embeddings).await.unwrap();
        assert_eq!(metrics.len(), 3);

        let report = assessment.generate_quality_report(&metrics);
        assert_eq!(report.total_vectors, 3);
        assert!(report.avg_quality_score > 0.0);
    }

    #[test]
    fn test_cosine_similarity() {
        let config = VectorQualityConfig::default();
        let assessment = VectorQualityAssessment::new(config);

        let vec1 = vec![1.0, 0.0, 0.0];
        let vec2 = vec![0.0, 1.0, 0.0];
        let vec3 = vec![1.0, 0.0, 0.0];

        assert_eq!(assessment.calculate_cosine_similarity(&vec1, &vec2), 0.0);
        assert_eq!(assessment.calculate_cosine_similarity(&vec1, &vec3), 1.0);
    }

    #[test]
    fn test_quality_validation() {
        let config = VectorQualityConfig::default();
        let assessment = VectorQualityAssessment::new(config);

        let good_metrics = VectorQualityMetrics {
            id: next_id(),
            embedding_id: next_id(),
            dimension_consistency: true,
            magnitude: 1.0,
            sparsity_ratio: 0.3,
            norm_type: NormalizationMethod::L2,
            quality_score: 0.9,
            anomaly_score: None,
            similarity_distribution: SimilarityDistribution {
                mean: 0.5,
                std_dev: 0.2,
                min: 0.0,
                max: 1.0,
                percentile_25: 0.3,
                percentile_50: 0.5,
                percentile_75: 0.7,
                percentile_95: 0.9,
            },
            computed_at: current_millis(),
        };

        assert!(assessment.validate_quality(&good_metrics));
    }
}
