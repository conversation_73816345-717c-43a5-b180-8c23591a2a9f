use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use wisdom_vault_common::time::to_datetime;

use crate::services::HybridSearchResult;

/// 结果融合策略
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ResultFusionStrategy {
    /// 加权线性组合
    WeightedLinearCombination,
    /// 倒数排名融合（RRF）
    ReciprocalRankFusion,
    /// 归一化分数融合
    NormalizedScoreFusion,
    /// 混合重排序
    HybridReranking,
    /// 动态权重调整
    DynamicWeightAdjustment,
}

/// 结果融合管理器
pub struct ResultFusionManager {
    /// 融合算法配置
    config: FusionAlgorithmConfig,
    /// 性能统计
    performance_stats: HashMap<String, FusionPerformanceStats>,
}

/// 融合算法配置
#[derive(Debug, Clone)]
pub struct FusionAlgorithmConfig {
    /// RRF K参数（用于排序融合）
    pub rrf_k_parameter: f64,
    /// 分数归一化方法
    pub score_normalization_method: ScoreNormalizationMethod,
    /// 多样性阈值
    pub diversity_threshold: f64,
    /// 时间衰减系数
    pub time_decay_factor: f64,
    /// 质量权重系数
    pub quality_weight_factor: f64,
    /// 流行度权重系数
    pub popularity_weight_factor: f64,
    /// 启用学习排序
    pub enable_learning_to_rank: bool,
}

impl Default for FusionAlgorithmConfig {
    fn default() -> Self {
        Self {
            rrf_k_parameter: 60.0,
            score_normalization_method: ScoreNormalizationMethod::MinMaxNormalization,
            diversity_threshold: 0.8,
            time_decay_factor: 0.1,
            quality_weight_factor: 0.15,
            popularity_weight_factor: 0.05,
            enable_learning_to_rank: true,
        }
    }
}

/// 分数归一化方法
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ScoreNormalizationMethod {
    /// Min-Max归一化
    MinMaxNormalization,
    /// Z-Score标准化
    ZScoreNormalization,
    /// Sigmoid归一化
    SigmoidNormalization,
    /// 分位数归一化
    QuantileNormalization,
}

/// 融合性能统计
#[derive(Debug, Clone, Default)]
pub struct FusionPerformanceStats {
    /// 使用次数
    pub usage_count: u64,
    /// 平均融合时间（微秒）
    pub avg_fusion_time_us: f64,
    /// 平均结果质量评分
    pub avg_result_quality: f64,
    /// 用户满意度评分
    pub user_satisfaction_score: f64,
}

/// 高级融合参数
#[derive(Debug, Clone)]
pub struct AdvancedFusionParams {
    /// 查询上下文信息
    pub query_context: QueryContext,
    /// 用户偏好权重
    pub user_preference_weights: UserPreferenceWeights,
    /// 文档特征权重
    pub document_feature_weights: DocumentFeatureWeights,
    /// 动态调整参数
    pub dynamic_adjustment_params: DynamicAdjustmentParams,
}

/// 查询上下文信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryContext {
    /// 查询文本
    pub query_text: String,
    /// 知识库ID
    pub knowledge_base_id: Option<String>,
    /// 查询长度
    pub query_length: usize,
    /// 查询复杂度
    pub query_complexity: f64,
    /// 查询类型（事实型、分析型、探索性等）
    pub query_type: QueryType,
    /// 查询意图
    pub query_intent: QueryIntent,
    /// 语言类型
    pub language: String,
}

/// 查询类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum QueryType {
    /// 事实查询
    Factual,
    /// 分析查询
    Analytical,
    /// 探索性查询
    Exploratory,
    /// 导航查询
    Navigational,
    /// 交易查询
    Transactional,
}

/// 查询意图
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum QueryIntent {
    /// 查找信息
    Information,
    /// 比较分析
    Comparison,
    /// 操作指导
    HowTo,
    /// 定义解释
    Definition,
    /// 列表枚举
    List,
    /// 其他
    Other,
}

/// 用户偏好权重
#[derive(Debug, Clone)]
pub struct UserPreferenceWeights {
    /// 新鲜度偏好
    pub freshness_preference: f64,
    /// 权威性偏好
    pub authority_preference: f64,
    /// 详细程度偏好
    pub detail_preference: f64,
    /// 语言偏好
    pub language_preference: f64,
    /// 文档类型偏好
    pub document_type_preference: HashMap<String, f64>,
}

impl Default for UserPreferenceWeights {
    fn default() -> Self {
        Self {
            freshness_preference: 0.5,
            authority_preference: 0.7,
            detail_preference: 0.6,
            language_preference: 1.0,
            document_type_preference: HashMap::new(),
        }
    }
}

/// 文档特征权重
#[derive(Debug, Clone)]
pub struct DocumentFeatureWeights {
    /// 标题权重
    pub title_weight: f64,
    /// 内容权重
    pub content_weight: f64,
    /// 摘要权重
    pub summary_weight: f64,
    /// 关键词权重
    pub keywords_weight: f64,
    /// 分类权重
    pub category_weight: f64,
    /// 标签权重
    pub tags_weight: f64,
}

impl Default for DocumentFeatureWeights {
    fn default() -> Self {
        Self {
            title_weight: 1.5,
            content_weight: 1.0,
            summary_weight: 1.2,
            keywords_weight: 1.3,
            category_weight: 0.8,
            tags_weight: 0.9,
        }
    }
}

/// 动态调整参数
#[derive(Debug, Clone)]
pub struct DynamicAdjustmentParams {
    /// 学习率
    pub learning_rate: f64,
    /// 调整幅度
    pub adjustment_magnitude: f64,
    /// 最小调整阈值
    pub min_adjustment_threshold: f64,
    /// 最大调整幅度
    pub max_adjustment_magnitude: f64,
}

impl Default for DynamicAdjustmentParams {
    fn default() -> Self {
        Self {
            learning_rate: 0.01,
            adjustment_magnitude: 0.1,
            min_adjustment_threshold: 0.05,
            max_adjustment_magnitude: 0.3,
        }
    }
}

impl ResultFusionManager {
    /// 创建新的结果融合管理器
    pub fn new(config: FusionAlgorithmConfig) -> Self {
        Self {
            config,
            performance_stats: HashMap::new(),
        }
    }

    /// 创建带默认配置的融合管理器
    pub fn new_with_defaults() -> Self {
        Self::new(FusionAlgorithmConfig::default())
    }

    /// 执行结果融合
    pub async fn fuse_results(
        &mut self,
        results: &mut [HybridSearchResult],
        strategy: &ResultFusionStrategy,
        keyword_weight: f64,
        vector_weight: f64,
        advanced_params: Option<AdvancedFusionParams>,
    ) -> Result<FusionMetrics> {
        let start_time = std::time::Instant::now();

        let metrics = match strategy {
            ResultFusionStrategy::WeightedLinearCombination => {
                self.weighted_linear_combination(
                    results,
                    keyword_weight,
                    vector_weight,
                    &advanced_params,
                )
                .await?
            }
            ResultFusionStrategy::ReciprocalRankFusion => {
                self.reciprocal_rank_fusion(results, &advanced_params)
                    .await?
            }
            ResultFusionStrategy::NormalizedScoreFusion => {
                self.normalized_score_fusion(
                    results,
                    keyword_weight,
                    vector_weight,
                    &advanced_params,
                )
                .await?
            }
            ResultFusionStrategy::HybridReranking => {
                self.hybrid_reranking(results, keyword_weight, vector_weight, &advanced_params)
                    .await?
            }
            ResultFusionStrategy::DynamicWeightAdjustment => {
                self.dynamic_weight_adjustment(results, &advanced_params)
                    .await?
            }
        };

        let fusion_time = start_time.elapsed().as_micros() as f64;

        // 更新性能统计
        self.update_performance_stats(strategy, fusion_time, &metrics);

        Ok(metrics)
    }

    /// 加权线性组合融合
    async fn weighted_linear_combination(
        &self,
        results: &mut [HybridSearchResult],
        keyword_weight: f64,
        vector_weight: f64,
        advanced_params: &Option<AdvancedFusionParams>,
    ) -> Result<FusionMetrics> {
        let mut metrics = FusionMetrics::default();

        for result in results.iter_mut() {
            let keyword_score = result.keyword_score.unwrap_or(0.0);
            let vector_score = result.vector_score.unwrap_or(0.0);

            // 基础加权组合
            let base_score = keyword_score * keyword_weight + vector_score * vector_weight;

            // 应用高级参数调整
            let adjusted_score = if let Some(params) = advanced_params {
                self.apply_advanced_adjustments(base_score, result, params)
                    .await
            } else {
                base_score
            };

            // 应用排序因子
            let final_score = adjusted_score
                * result.ranking_factors.time_decay
                * (1.0
                    + result.ranking_factors.document_quality * self.config.quality_weight_factor)
                * (1.0 + result.ranking_factors.user_preference * 0.1)
                * (1.0
                    + result.ranking_factors.popularity_score
                        * self.config.popularity_weight_factor);

            result.final_score = final_score;

            // 更新指标
            metrics.processed_results += 1;
            metrics.avg_score += final_score;
        }

        if !results.is_empty() {
            metrics.avg_score /= results.len() as f64;
        }

        Ok(metrics)
    }

    /// 排序融合（RRF）
    async fn reciprocal_rank_fusion(
        &self,
        results: &mut [HybridSearchResult],
        advanced_params: &Option<AdvancedFusionParams>,
    ) -> Result<FusionMetrics> {
        let mut metrics = FusionMetrics::default();

        // 按关键词得分排序
        let keyword_ranks = self.calculate_keyword_ranks(results).await;

        // 按向量得分排序
        let vector_ranks = self.calculate_vector_ranks(results).await;

        for result in results.iter_mut() {
            let keyword_rrf = keyword_ranks.get(&result.document.id).unwrap_or(&0.0);
            let vector_rrf = vector_ranks.get(&result.document.id).unwrap_or(&0.0);

            let base_score = keyword_rrf + vector_rrf;

            // 应用高级参数调整
            let adjusted_score = if let Some(params) = advanced_params {
                self.apply_advanced_adjustments(base_score, result, params)
                    .await
            } else {
                base_score
            };

            result.final_score = adjusted_score;

            // 更新指标
            metrics.processed_results += 1;
            metrics.avg_score += adjusted_score;
        }

        if !results.is_empty() {
            metrics.avg_score /= results.len() as f64;
        }

        Ok(metrics)
    }

    /// 分数归一化融合
    async fn normalized_score_fusion(
        &self,
        results: &mut [HybridSearchResult],
        keyword_weight: f64,
        vector_weight: f64,
        advanced_params: &Option<AdvancedFusionParams>,
    ) -> Result<FusionMetrics> {
        let mut metrics = FusionMetrics::default();

        // 收集得分用于归一化
        let keyword_scores: Vec<f64> = results.iter().filter_map(|r| r.keyword_score).collect();
        let vector_scores: Vec<f64> = results.iter().filter_map(|r| r.vector_score).collect();

        // 应用归一化
        let (keyword_normalizer, vector_normalizer) =
            self.create_normalizers(&keyword_scores, &vector_scores);

        for result in results.iter_mut() {
            let normalized_keyword = result
                .keyword_score
                .map(|score| keyword_normalizer.normalize(score))
                .unwrap_or(0.0);
            let normalized_vector = result
                .vector_score
                .map(|score| vector_normalizer.normalize(score))
                .unwrap_or(0.0);

            let base_score =
                normalized_keyword * keyword_weight + normalized_vector * vector_weight;

            // 应用高级参数调整
            let adjusted_score = if let Some(params) = advanced_params {
                self.apply_advanced_adjustments(base_score, result, params)
                    .await
            } else {
                base_score
            };

            result.final_score = adjusted_score;

            // 更新指标
            metrics.processed_results += 1;
            metrics.avg_score += adjusted_score;
        }

        if !results.is_empty() {
            metrics.avg_score /= results.len() as f64;
        }

        Ok(metrics)
    }

    /// 混合重排序
    async fn hybrid_reranking(
        &self,
        results: &mut [HybridSearchResult],
        keyword_weight: f64,
        vector_weight: f64,
        advanced_params: &Option<AdvancedFusionParams>,
    ) -> Result<FusionMetrics> {
        let mut metrics = FusionMetrics::default();

        // 第一步：基础加权组合
        for result in results.iter_mut() {
            let keyword_score = result.keyword_score.unwrap_or(0.0);
            let vector_score = result.vector_score.unwrap_or(0.0);
            result.final_score = keyword_score * keyword_weight + vector_score * vector_weight;
        }

        // 计算多样性惩罚
        let mut diversity_penalties = Vec::new();
        for i in 0..results.len() {
            let diversity_penalty = self.calculate_diversity_penalty(&results[i], results).await;
            diversity_penalties.push(diversity_penalty);
        }

        // 第二步：应用重排序算法
        for (i, result) in results.iter_mut().enumerate() {
            let mut rerank_score = result.final_score;

            // 多样性调整
            rerank_score *= 1.0 - diversity_penalties[i];

            // 查询意图匹配
            let intent_boost = self.calculate_intent_boost(result, advanced_params).await;
            rerank_score *= 1.0 + intent_boost;

            // 新鲜度加权
            if let Some(params) = advanced_params {
                let freshness_boost = self.calculate_freshness_boost(result, params).await;
                rerank_score *= 1.0 + freshness_boost;
            }

            // 权威性调整
            let authority_boost = self.calculate_authority_boost(result).await;
            rerank_score *= 1.0 + authority_boost;

            result.final_score = rerank_score;

            // 更新指标
            metrics.processed_results += 1;
            metrics.avg_score += rerank_score;
        }

        if !results.is_empty() {
            metrics.avg_score /= results.len() as f64;
        }

        Ok(metrics)
    }

    /// 动态权重调整
    async fn dynamic_weight_adjustment(
        &self,
        results: &mut [HybridSearchResult],
        advanced_params: &Option<AdvancedFusionParams>,
    ) -> Result<FusionMetrics> {
        let mut metrics = FusionMetrics::default();

        // 根据查询上下文动态计算权重
        let (dynamic_keyword_weight, dynamic_vector_weight) = if let Some(params) = advanced_params
        {
            self.calculate_dynamic_weights(&params.query_context).await
        } else {
            (0.5, 0.5) // 默认平衡权重
        };

        for result in results.iter_mut() {
            let keyword_score = result.keyword_score.unwrap_or(0.0);
            let vector_score = result.vector_score.unwrap_or(0.0);

            let base_score =
                keyword_score * dynamic_keyword_weight + vector_score * dynamic_vector_weight;

            // 应用高级参数调整
            let adjusted_score = if let Some(params) = advanced_params {
                self.apply_advanced_adjustments(base_score, result, params)
                    .await
            } else {
                base_score
            };

            result.final_score = adjusted_score;

            // 更新指标
            metrics.processed_results += 1;
            metrics.avg_score += adjusted_score;
        }

        if !results.is_empty() {
            metrics.avg_score /= results.len() as f64;
        }

        metrics.dynamic_keyword_weight = Some(dynamic_keyword_weight);
        metrics.dynamic_vector_weight = Some(dynamic_vector_weight);

        Ok(metrics)
    }

    /// 应用高级调整参数
    async fn apply_advanced_adjustments(
        &self,
        base_score: f64,
        result: &HybridSearchResult,
        params: &AdvancedFusionParams,
    ) -> f64 {
        let mut adjusted_score = base_score;

        // 用户偏好调整
        adjusted_score *= self
            .calculate_user_preference_factor(result, &params.user_preference_weights)
            .await;

        // 文档特征调整
        adjusted_score *= self
            .calculate_document_feature_factor(result, &params.document_feature_weights)
            .await;

        // 动态调整
        adjusted_score = self
            .apply_dynamic_adjustments(adjusted_score, result, &params.dynamic_adjustment_params)
            .await;

        adjusted_score
    }

    /// 计算关键词排名
    async fn calculate_keyword_ranks(
        &self,
        results: &[HybridSearchResult],
    ) -> HashMap<String, f64> {
        let mut sorted_results: Vec<_> = results.iter().collect();
        sorted_results.sort_by(|a, b| {
            b.keyword_score
                .unwrap_or(0.0)
                .partial_cmp(&a.keyword_score.unwrap_or(0.0))
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        let mut ranks = HashMap::new();
        for (rank, result) in sorted_results.iter().enumerate() {
            if result.keyword_score.is_some() {
                let rrf_score = 1.0 / (self.config.rrf_k_parameter + rank as f64 + 1.0);
                ranks.insert(result.document.id.clone(), rrf_score);
            }
        }

        ranks
    }

    /// 计算向量排名
    async fn calculate_vector_ranks(&self, results: &[HybridSearchResult]) -> HashMap<String, f64> {
        let mut sorted_results: Vec<_> = results.iter().collect();
        sorted_results.sort_by(|a, b| {
            b.vector_score
                .unwrap_or(0.0)
                .partial_cmp(&a.vector_score.unwrap_or(0.0))
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        let mut ranks = HashMap::new();
        for (rank, result) in sorted_results.iter().enumerate() {
            if result.vector_score.is_some() {
                let rrf_score = 1.0 / (self.config.rrf_k_parameter + rank as f64 + 1.0);
                ranks.insert(result.document.id.clone(), rrf_score);
            }
        }

        ranks
    }

    /// 创建分数归一化器
    fn create_normalizers(
        &self,
        keyword_scores: &[f64],
        vector_scores: &[f64],
    ) -> (ScoreNormalizer, ScoreNormalizer) {
        let keyword_normalizer =
            ScoreNormalizer::new(keyword_scores, &self.config.score_normalization_method);
        let vector_normalizer =
            ScoreNormalizer::new(vector_scores, &self.config.score_normalization_method);
        (keyword_normalizer, vector_normalizer)
    }

    /// 计算多样性惩罚
    async fn calculate_diversity_penalty(
        &self,
        target: &HybridSearchResult,
        all_results: &[HybridSearchResult],
    ) -> f64 {
        let mut max_similarity: f64 = 0.0;

        for other in all_results {
            if other.document.id != target.document.id {
                let similarity = self.calculate_result_similarity(target, other).await;
                max_similarity = max_similarity.max(similarity);
            }
        }

        if max_similarity > self.config.diversity_threshold {
            0.3 // 30%惩罚
        } else if max_similarity > 0.6 {
            0.1 // 10%惩罚
        } else {
            0.0
        }
    }

    /// 计算意图提升
    async fn calculate_intent_boost(
        &self,
        result: &HybridSearchResult,
        advanced_params: &Option<AdvancedFusionParams>,
    ) -> f64 {
        if let Some(params) = advanced_params {
            match params.query_context.query_intent {
                QueryIntent::Information if result.matched_terms.len() > 2 => 0.15,
                QueryIntent::HowTo
                    if result.document.title.to_lowercase().contains("如何")
                        || result.document.title.to_lowercase().contains("怎么") =>
                {
                    0.2
                }
                QueryIntent::Definition if result.document.content.len() < 1000 => 0.1, /* 短文档可能是定义 */
                QueryIntent::List if result.matched_terms.len() > 1 => 0.12,
                _ => 0.0,
            }
        } else {
            // 基于匹配关键词数量的简单提升
            match result.matched_terms.len() {
                n if n > 3 => 0.15,
                n if n > 1 => 0.08,
                _ => 0.0,
            }
        }
    }

    /// 计算新鲜度提升
    async fn calculate_freshness_boost(
        &self,
        result: &HybridSearchResult,
        params: &AdvancedFusionParams,
    ) -> f64 {
        let now = chrono::Utc::now();
        let doc_age = now.signed_duration_since(to_datetime(result.document.created_at));
        let days_old = doc_age.num_days() as f64;

        let freshness_factor = (-days_old / 365.0).exp(); // 一年半衰期
        freshness_factor * params.user_preference_weights.freshness_preference * 0.2
    }

    /// 计算权威性提升
    async fn calculate_authority_boost(&self, result: &HybridSearchResult) -> f64 {
        let mut authority_score = 0.0;

        // 基于文档类型
        match result.document.file_type.as_str() {
            "pdf" => authority_score += 0.1,
            "docx" | "doc" => authority_score += 0.08,
            "html" => authority_score += 0.05,
            _ => {}
        }

        // 基于文档长度（适中长度通常更权威）
        let content_length = result.document.content.len();
        if content_length > 500 && content_length < 10000 {
            authority_score += 0.05;
        }

        // 基于是否有摘要
        if result.document.summary.is_some() {
            authority_score += 0.03;
        }

        authority_score
    }

    /// 计算动态权重
    async fn calculate_dynamic_weights(&self, query_context: &QueryContext) -> (f64, f64) {
        let mut keyword_weight = 0.5;
        let mut vector_weight = 0.5;

        // 基于查询长度调整
        match query_context.query_length {
            len if len < 10 => {
                keyword_weight = 0.7; // 短查询偏向关键词
                vector_weight = 0.3;
            }
            len if len > 50 => {
                keyword_weight = 0.3; // 长查询偏向语义
                vector_weight = 0.7;
            }
            _ => {} // 中等长度保持默认
        }

        // 基于查询类型调整
        match query_context.query_type {
            QueryType::Factual => {
                keyword_weight += 0.1; // 事实查询偏向关键词
                vector_weight -= 0.1;
            }
            QueryType::Exploratory => {
                keyword_weight -= 0.1; // 探索性查询偏向语义
                vector_weight += 0.1;
            }
            _ => {} // 其他类型保持不变
        }

        // 基于查询复杂度调整
        let complexity_adjustment = (query_context.query_complexity - 0.5) * 0.2;
        keyword_weight -= complexity_adjustment;
        vector_weight += complexity_adjustment;

        // 确保权重在合理范围内
        keyword_weight = keyword_weight.clamp(0.1, 0.9);
        vector_weight = 1.0 - keyword_weight;

        (keyword_weight, vector_weight)
    }

    /// 计算用户偏好因子
    async fn calculate_user_preference_factor(
        &self,
        result: &HybridSearchResult,
        weights: &UserPreferenceWeights,
    ) -> f64 {
        let mut factor = 1.0;

        // 文档类型偏好
        if let Some(type_preference) = weights
            .document_type_preference
            .get(&result.document.file_type)
        {
            factor *= 1.0 + type_preference * 0.1;
        }

        // 新鲜度偏好（已在新鲜度提升中处理）

        // 权威性偏好
        let authority_score = result.ranking_factors.document_quality;
        factor *= 1.0 + authority_score * weights.authority_preference * 0.1;

        factor
    }

    /// 计算文档特征因子
    async fn calculate_document_feature_factor(
        &self,
        result: &HybridSearchResult,
        weights: &DocumentFeatureWeights,
    ) -> f64 {
        let mut factor = 1.0;

        // 标题匹配加权
        if !result.matched_terms.is_empty() {
            let title_matches = result
                .matched_terms
                .iter()
                .filter(|term| {
                    result
                        .document
                        .title
                        .to_lowercase()
                        .contains(&term.to_lowercase())
                })
                .count() as f64;
            if title_matches > 0.0 {
                factor *= 1.0
                    + (title_matches / result.matched_terms.len() as f64)
                        * weights.title_weight
                        * 0.1;
            }
        }

        // 内容质量评估
        let content_quality = self.assess_content_quality(&result.document);
        factor *= 1.0 + content_quality * weights.content_weight * 0.05;

        factor
    }

    /// 应用动态调整
    async fn apply_dynamic_adjustments(
        &self,
        score: f64,
        result: &HybridSearchResult,
        params: &DynamicAdjustmentParams,
    ) -> f64 {
        // 基于历史性能的动态调整（简化实现）
        let adjustment = if result.final_score > 0.8 {
            params.adjustment_magnitude * params.learning_rate
        } else if result.final_score < 0.3 {
            -params.adjustment_magnitude * params.learning_rate
        } else {
            0.0
        };

        let adjusted_score = score * (1.0 + adjustment);
        adjusted_score.clamp(0.0, 1.0)
    }

    /// 计算结果相似度
    async fn calculate_result_similarity(
        &self,
        result1: &HybridSearchResult,
        result2: &HybridSearchResult,
    ) -> f64 {
        // 标题相似度
        let title_sim =
            self.calculate_text_similarity(&result1.document.title, &result2.document.title);

        // 关键词重叠度
        let keyword_sim = self
            .calculate_keyword_overlap_similarity(&result1.matched_terms, &result2.matched_terms);

        // 综合相似度
        title_sim * 0.6 + keyword_sim * 0.4
    }

    /// 计算文本相似度
    fn calculate_text_similarity(&self, text1: &str, text2: &str) -> f64 {
        let text1_lower = text1.to_lowercase();
        let text2_lower = text2.to_lowercase();
        let words1: std::collections::HashSet<_> = text1_lower.split_whitespace().collect();
        let words2: std::collections::HashSet<_> = text2_lower.split_whitespace().collect();

        let intersection = words1.intersection(&words2).count() as f64;
        let union = words1.union(&words2).count() as f64;

        if union > 0.0 {
            intersection / union
        } else {
            0.0
        }
    }

    /// 计算关键词重叠相似度
    fn calculate_keyword_overlap_similarity(
        &self,
        keywords1: &[String],
        keywords2: &[String],
    ) -> f64 {
        if keywords1.is_empty() || keywords2.is_empty() {
            return 0.0;
        }

        let set1: std::collections::HashSet<_> = keywords1.iter().collect();
        let set2: std::collections::HashSet<_> = keywords2.iter().collect();

        let intersection = set1.intersection(&set2).count() as f64;
        let union = set1.union(&set2).count() as f64;

        if union > 0.0 {
            intersection / union
        } else {
            0.0
        }
    }

    /// 评估内容质量
    fn assess_content_quality(&self, document: &wisdom_vault_database::models::Document) -> f64 {
        let mut quality: f64 = 0.5; // 基础质量

        // 基于内容长度
        let content_len = document.content.len();
        if content_len > 200 && content_len < 5000 {
            quality += 0.2;
        } else if content_len >= 5000 && content_len < 20000 {
            quality += 0.3;
        }

        // 基于结构化程度（简单检测）
        let paragraph_count = document.content.matches('\n').count();
        if paragraph_count > 2 {
            quality += 0.1;
        }

        quality.clamp(0.0, 1.0)
    }

    /// 更新性能统计
    fn update_performance_stats(
        &mut self,
        strategy: &ResultFusionStrategy,
        fusion_time: f64,
        metrics: &FusionMetrics,
    ) {
        let strategy_name = format!("{:?}", strategy);
        let stats = self.performance_stats.entry(strategy_name).or_default();

        stats.usage_count += 1;

        // 更新平均融合时间
        stats.avg_fusion_time_us = (stats.avg_fusion_time_us * (stats.usage_count - 1) as f64
            + fusion_time)
            / stats.usage_count as f64;

        // 更新平均结果质量
        stats.avg_result_quality = (stats.avg_result_quality * (stats.usage_count - 1) as f64
            + metrics.avg_score)
            / stats.usage_count as f64;
    }

    /// 获取性能统计
    pub fn get_performance_stats(&self) -> &HashMap<String, FusionPerformanceStats> {
        &self.performance_stats
    }

    /// 获取配置
    pub fn get_config(&self) -> &FusionAlgorithmConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, new_config: FusionAlgorithmConfig) {
        self.config = new_config;
    }
}

/// 分数归一化器
pub struct ScoreNormalizer {
    method: ScoreNormalizationMethod,
    min_val: f64,
    max_val: f64,
    mean_val: f64,
    std_val: f64,
}

impl ScoreNormalizer {
    pub fn new(scores: &[f64], method: &ScoreNormalizationMethod) -> Self {
        let min_val = scores.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_val = scores.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let mean_val = scores.iter().sum::<f64>() / scores.len().max(1) as f64;
        let variance = scores.iter().map(|&x| (x - mean_val).powi(2)).sum::<f64>()
            / scores.len().max(1) as f64;
        let std_val = variance.sqrt();

        Self {
            method: method.clone(),
            min_val,
            max_val,
            mean_val,
            std_val,
        }
    }

    pub fn normalize(&self, score: f64) -> f64 {
        match self.method {
            ScoreNormalizationMethod::MinMaxNormalization => {
                if self.max_val > self.min_val {
                    (score - self.min_val) / (self.max_val - self.min_val)
                } else {
                    0.5
                }
            }
            ScoreNormalizationMethod::ZScoreNormalization => {
                if self.std_val > 0.0 {
                    (score - self.mean_val) / self.std_val
                } else {
                    0.0
                }
            }
            ScoreNormalizationMethod::SigmoidNormalization => 1.0 / (1.0 + (-score).exp()),
            ScoreNormalizationMethod::QuantileNormalization => {
                // 简化的分位数归一化
                (score / (self.max_val + 1e-10)).clamp(0.0, 1.0)
            }
        }
    }
}

/// 融合指标
#[derive(Debug, Clone, Default)]
pub struct FusionMetrics {
    /// 处理的结果数量
    pub processed_results: u64,
    /// 平均得分
    pub avg_score: f64,
    /// 动态关键词权重
    pub dynamic_keyword_weight: Option<f64>,
    /// 动态向量权重
    pub dynamic_vector_weight: Option<f64>,
    /// 多样性指标
    pub diversity_score: Option<f64>,
    /// 质量提升指标
    pub quality_improvement: Option<f64>,
}

/// 用户搜索行为记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchBehavior {
    /// 查询文本
    pub query_text: String,
    /// 查询时间
    pub query_time: DateTime<Utc>,
    /// 查询上下文
    pub query_context: QueryContext,
    /// 结果点击记录
    pub result_clicks: Vec<ResultClick>,
    /// 用户满意度评分
    pub satisfaction_score: Option<f64>,
}

/// 结果点击记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResultClick {
    /// 文档ID
    pub document_id: String,
    /// 点击时间
    pub click_time: DateTime<Utc>,
    /// 文档在结果中的排名
    pub rank_position: u32,
    /// 用户停留时间（秒）
    pub dwell_time: Option<u64>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_fusion_algorithm_config_default() {
        let config = FusionAlgorithmConfig::default();
        assert_eq!(config.rrf_k_parameter, 60.0);
        assert_eq!(config.diversity_threshold, 0.8);
    }

    #[test]
    fn test_score_normalizer_min_max() {
        let scores = vec![1.0, 2.0, 3.0, 4.0, 5.0];
        let normalizer =
            ScoreNormalizer::new(&scores, &ScoreNormalizationMethod::MinMaxNormalization);

        assert_eq!(normalizer.normalize(1.0), 0.0);
        assert_eq!(normalizer.normalize(5.0), 1.0);
        assert_eq!(normalizer.normalize(3.0), 0.5);
    }

    #[test]
    fn test_query_type_equality() {
        assert_eq!(QueryType::Factual, QueryType::Factual);
        assert_ne!(QueryType::Factual, QueryType::Analytical);
    }

    #[test]
    fn test_user_preference_weights_default() {
        let weights = UserPreferenceWeights::default();
        assert_eq!(weights.freshness_preference, 0.5);
        assert_eq!(weights.authority_preference, 0.7);
    }
}
