use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::services::{ChatMessage, LLMService, MessageRole};

/// 答案质量评估配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnswerQualityConfig {
    /// 启用自动质量评估
    pub enable_auto_assessment: bool,
    /// 相关性权重
    pub relevance_weight: f64,
    /// 完整性权重
    pub completeness_weight: f64,
    /// 准确性权重
    pub accuracy_weight: f64,
    /// 可读性权重
    pub readability_weight: f64,
    /// 最小置信度阈值
    pub min_confidence_threshold: f64,
    /// 启用基于LLM的质量评估
    pub enable_llm_evaluation: bool,
    /// 用于质量评估的模型
    pub evaluation_model: String,
}

impl Default for AnswerQualityConfig {
    fn default() -> Self {
        Self {
            enable_auto_assessment: true,
            relevance_weight: 0.3,
            completeness_weight: 0.25,
            accuracy_weight: 0.25,
            readability_weight: 0.2,
            min_confidence_threshold: 0.6,
            enable_llm_evaluation: true,
            evaluation_model: "gpt-4o-mini".to_string(),
        }
    }
}

/// 答案质量指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnswerQualityMetrics {
    /// 总体质量分数 (0.0-1.0)
    pub overall_score: f64,
    /// 相关性分数 (0.0-1.0)
    pub relevance_score: f64,
    /// 完整性分数 (0.0-1.0)
    pub completeness_score: f64,
    /// 准确性分数 (0.0-1.0)
    pub accuracy_score: f64,
    /// 可读性分数 (0.0-1.0)
    pub readability_score: f64,
    /// 置信度 (0.0-1.0)
    pub confidence: f64,
    /// 质量等级
    pub quality_level: QualityLevel,
    /// 改进建议
    pub improvement_suggestions: Vec<String>,
    /// 评估时间戳
    pub assessed_at: chrono::DateTime<chrono::Utc>,
}

/// 答案质量等级
#[derive(Debug, Copy, Clone, Serialize, Deserialize, PartialEq)]
pub enum QualityLevel {
    /// 优秀 (0.9-1.0)
    Excellent,
    /// 良好 (0.7-0.89)
    Good,
    /// 一般 (0.5-0.69)
    Fair,
    /// 较差 (0.3-0.49)
    Poor,
    /// 很差 (0.0-0.29)
    VeryPoor,
}

impl QualityLevel {
    pub fn from_score(score: f64) -> Self {
        match score {
            s if s >= 0.9 => QualityLevel::Excellent,
            s if s >= 0.7 => QualityLevel::Good,
            s if s >= 0.5 => QualityLevel::Fair,
            s if s >= 0.3 => QualityLevel::Poor,
            _ => QualityLevel::VeryPoor,
        }
    }

    pub fn description(&self) -> &'static str {
        match self {
            QualityLevel::Excellent => "答案质量优秀，完全满足要求",
            QualityLevel::Good => "答案质量良好，基本满足要求",
            QualityLevel::Fair => "答案质量一般，部分满足要求",
            QualityLevel::Poor => "答案质量较差，需要改进",
            QualityLevel::VeryPoor => "答案质量很差，不满足要求",
        }
    }
}

/// 答案评估上下文
#[derive(Debug, Clone)]
pub struct AnswerAssessmentContext {
    /// 原始问题
    pub question: String,
    /// 生成的答案
    pub answer: String,
    /// 检索到的文档内容
    pub source_documents: Vec<String>,
    /// 相关性分数
    pub relevance_scores: Vec<f64>,
    /// LLM 使用的模型
    pub model_used: String,
    /// Token 使用情况
    pub token_usage: Option<TokenUsage>,
    /// 响应时间
    pub response_time_ms: u64,
}

/// Token 使用情况
#[derive(Debug, Clone)]
pub struct TokenUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// 答案质量评估服务
pub struct AnswerQualityService {
    config: AnswerQualityConfig,
    llm_service: Option<Arc<dyn LLMService>>,
    assessment_cache: Arc<RwLock<HashMap<String, AnswerQualityMetrics>>>,
}

impl AnswerQualityService {
    /// 创建新的答案质量评估服务
    pub fn new(config: AnswerQualityConfig, llm_service: Option<Arc<dyn LLMService>>) -> Self {
        Self {
            config,
            llm_service,
            assessment_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 评估答案质量
    pub async fn assess_answer_quality(
        &self,
        context: &AnswerAssessmentContext,
    ) -> Result<AnswerQualityMetrics> {
        info!(
            "Assessing answer quality for question: {}",
            context.question
        );

        // 生成缓存键
        let cache_key = self.generate_cache_key(context);

        // 检查缓存
        {
            let cache = self.assessment_cache.read().await;
            if let Some(cached_metrics) = cache.get(&cache_key) {
                debug!("Using cached quality assessment");
                return Ok(cached_metrics.clone());
            }
        }

        // 计算各项指标
        let relevance_score = self.assess_relevance(context).await?;
        let completeness_score = self.assess_completeness(context).await?;
        let accuracy_score = self.assess_accuracy(context).await?;
        let readability_score = self.assess_readability(context).await?;

        // 计算总体分数
        let overall_score = self.calculate_overall_score(
            relevance_score,
            completeness_score,
            accuracy_score,
            readability_score,
        );

        // 确定质量等级
        let quality_level = QualityLevel::from_score(overall_score);

        // 生成改进建议
        let improvement_suggestions = self
            .generate_improvement_suggestions(
                &quality_level,
                relevance_score,
                completeness_score,
                accuracy_score,
                readability_score,
            )
            .await?;

        // 计算置信度
        let confidence = self.calculate_confidence(context, overall_score).await?;

        let metrics = AnswerQualityMetrics {
            overall_score,
            relevance_score,
            completeness_score,
            accuracy_score,
            readability_score,
            confidence,
            quality_level,
            improvement_suggestions,
            assessed_at: chrono::Utc::now(),
        };

        // 缓存结果
        {
            let mut cache = self.assessment_cache.write().await;
            cache.insert(cache_key, metrics.clone());
        }

        info!(
            "Answer quality assessment completed: overall_score={:.2}, quality_level={:?}",
            overall_score, quality_level
        );

        Ok(metrics)
    }

    /// 评估答案相关性
    async fn assess_relevance(&self, context: &AnswerAssessmentContext) -> Result<f64> {
        // 基础相关性评估：基于关键词匹配
        let question_words = self.extract_keywords(&context.question);
        let answer_words = self.extract_keywords(&context.answer);

        let common_words = question_words
            .iter()
            .filter(|word| answer_words.contains(word))
            .count();

        let keyword_relevance = if question_words.is_empty() {
            0.0
        } else {
            common_words as f64 / question_words.len() as f64
        };

        // 如果有源文档，考虑源文档相关性
        let source_relevance = if !context.relevance_scores.is_empty() {
            context.relevance_scores.iter().sum::<f64>() / context.relevance_scores.len() as f64
        } else {
            0.5 // 默认中等相关性
        };

        // 如果启用了LLM评估，使用LLM进行更精确的相关性评估
        let llm_relevance = if self.config.enable_llm_evaluation {
            self.llm_assess_relevance(context).await.unwrap_or(0.5)
        } else {
            0.5
        };

        // 综合评分
        let relevance_score =
            (keyword_relevance * 0.3 + source_relevance * 0.4 + llm_relevance * 0.3)
                .min(1.0)
                .max(0.0);

        debug!("Relevance assessment: {:.2}", relevance_score);
        Ok(relevance_score)
    }

    /// 评估答案完整性
    async fn assess_completeness(&self, context: &AnswerAssessmentContext) -> Result<f64> {
        // 基础完整性评估：答案长度和结构
        let answer_length = context.answer.len();
        let sentence_count = context
            .answer
            .split('.')
            .filter(|s| !s.trim().is_empty())
            .count();

        // 长度评分 (100-500字符为最佳)
        let length_score = if answer_length < 50 {
            answer_length as f64 / 50.0
        } else if answer_length <= 500 {
            1.0
        } else {
            1.0 - ((answer_length - 500) as f64 / 1000.0).min(0.5)
        };

        // 结构评分 (2-5句话为最佳)
        let structure_score = if sentence_count < 2 {
            sentence_count as f64 / 2.0
        } else if sentence_count <= 5 {
            1.0
        } else {
            1.0 - ((sentence_count - 5) as f64 / 10.0).min(0.5)
        };

        // 如果启用了LLM评估
        let llm_completeness = if self.config.enable_llm_evaluation {
            self.llm_assess_completeness(context).await.unwrap_or(0.5)
        } else {
            0.5
        };

        let completeness_score =
            (length_score * 0.3 + structure_score * 0.3 + llm_completeness * 0.4)
                .min(1.0)
                .max(0.0);

        debug!("Completeness assessment: {:.2}", completeness_score);
        Ok(completeness_score)
    }

    /// 评估答案准确性
    async fn assess_accuracy(&self, context: &AnswerAssessmentContext) -> Result<f64> {
        // 基础准确性评估：检查答案是否包含不确定性表述
        let uncertainty_patterns = vec![
            "不确定",
            "可能",
            "大概",
            "估计",
            "似乎",
            "或许",
            "也许",
            "不太清楚",
            "我不知道",
            "无法确定",
        ];

        let uncertainty_count = uncertainty_patterns
            .iter()
            .map(|pattern| context.answer.matches(pattern).count())
            .sum::<usize>();

        // 不确定性越多，准确性越低
        let uncertainty_penalty = (uncertainty_count as f64 * 0.1).min(0.5);
        let base_accuracy = 0.8 - uncertainty_penalty;

        // 如果答案明确表示找不到信息，给予中等评分
        let no_info_patterns = vec!["没有找到", "无相关信息", "文档中没有"];
        let has_no_info = no_info_patterns
            .iter()
            .any(|pattern| context.answer.contains(pattern));

        let accuracy_score = if has_no_info {
            0.6 // 诚实地表示找不到信息也是一种准确性
        } else if self.config.enable_llm_evaluation {
            // 使用LLM进行准确性评估
            self.llm_assess_accuracy(context)
                .await
                .unwrap_or(base_accuracy)
        } else {
            base_accuracy
        };

        debug!("Accuracy assessment: {:.2}", accuracy_score);
        Ok(accuracy_score.min(1.0).max(0.0))
    }

    /// 评估答案可读性
    async fn assess_readability(&self, context: &AnswerAssessmentContext) -> Result<f64> {
        // 基础可读性评估
        let text = &context.answer;

        // 平均句子长度
        let sentences: Vec<&str> = text.split('.').filter(|s| !s.trim().is_empty()).collect();
        let avg_sentence_length = if sentences.is_empty() {
            0.0
        } else {
            text.len() as f64 / sentences.len() as f64
        };

        // 句子长度评分 (50-150字符为佳)
        let sentence_length_score = if avg_sentence_length < 20.0 {
            avg_sentence_length / 20.0
        } else if avg_sentence_length <= 150.0 {
            1.0
        } else {
            1.0 - ((avg_sentence_length - 150.0) / 200.0).min(0.5)
        };

        // 段落结构评分
        let paragraph_count = text.split('\n').filter(|p| !p.trim().is_empty()).count();
        let structure_score = if paragraph_count >= 1 && paragraph_count <= 5 {
            1.0
        } else {
            0.7
        };

        // 特殊字符和格式评分
        let has_formatting = text.contains('\n') || text.contains("：") || text.contains("、");
        let formatting_score = if has_formatting { 1.0 } else { 0.8 };

        let readability_score =
            (sentence_length_score * 0.4 + structure_score * 0.3 + formatting_score * 0.3)
                .min(1.0)
                .max(0.0);

        debug!("Readability assessment: {:.2}", readability_score);
        Ok(readability_score)
    }

    /// 计算总体分数
    fn calculate_overall_score(
        &self,
        relevance: f64,
        completeness: f64,
        accuracy: f64,
        readability: f64,
    ) -> f64 {
        let overall = relevance * self.config.relevance_weight
            + completeness * self.config.completeness_weight
            + accuracy * self.config.accuracy_weight
            + readability * self.config.readability_weight;

        overall.min(1.0).max(0.0)
    }

    /// 计算置信度
    async fn calculate_confidence(
        &self,
        context: &AnswerAssessmentContext,
        overall_score: f64,
    ) -> Result<f64> {
        // 基于多个因素计算置信度
        let mut confidence_factors = Vec::new();

        // 1. 整体质量分数
        confidence_factors.push(overall_score);

        // 2. 源文档质量
        if !context.relevance_scores.is_empty() {
            let avg_relevance = context.relevance_scores.iter().sum::<f64>()
                / context.relevance_scores.len() as f64;
            confidence_factors.push(avg_relevance);
        }

        // 3. 答案长度适中性
        let length_factor = if context.answer.len() >= 50 && context.answer.len() <= 500 {
            1.0
        } else {
            0.7
        };
        confidence_factors.push(length_factor);

        // 4. 响应时间因素 (过快或过慢都可能影响质量)
        let time_factor = if context.response_time_ms >= 500 && context.response_time_ms <= 5000 {
            1.0
        } else {
            0.8
        };
        confidence_factors.push(time_factor);

        // 计算平均置信度
        let confidence = confidence_factors.iter().sum::<f64>() / confidence_factors.len() as f64;

        Ok(confidence.min(1.0).max(0.0))
    }

    /// 生成改进建议
    async fn generate_improvement_suggestions(
        &self,
        quality_level: &QualityLevel,
        relevance: f64,
        completeness: f64,
        accuracy: f64,
        readability: f64,
    ) -> Result<Vec<String>> {
        let mut suggestions = Vec::new();

        // 基于各项指标提供建议
        if relevance < 0.7 {
            suggestions.push("建议：提高答案与问题的相关性，确保直接回答用户的问题".to_string());
        }

        if completeness < 0.7 {
            suggestions.push("建议：提供更完整的答案，补充必要的细节和背景信息".to_string());
        }

        if accuracy < 0.7 {
            suggestions
                .push("建议：提高答案的准确性，减少不确定性表述，基于可靠来源提供信息".to_string());
        }

        if readability < 0.7 {
            suggestions
                .push("建议：改善答案的可读性，使用清晰的段落结构和适当的句子长度".to_string());
        }

        // 基于质量等级提供总体建议
        match quality_level {
            QualityLevel::VeryPoor | QualityLevel::Poor => {
                suggestions.push("建议：答案质量较低，建议重新生成或人工审核".to_string());
            }
            QualityLevel::Fair => {
                suggestions.push("建议：答案基本可用，但有改进空间，建议优化表达方式".to_string());
            }
            QualityLevel::Good => {
                suggestions.push("建议：答案质量良好，可以考虑添加更多细节或例子".to_string());
            }
            QualityLevel::Excellent => {
                suggestions.push("优秀：答案质量很高，符合预期要求".to_string());
            }
        }

        Ok(suggestions)
    }

    /// 使用LLM评估相关性
    async fn llm_assess_relevance(&self, context: &AnswerAssessmentContext) -> Result<f64> {
        if let Some(llm_service) = &self.llm_service {
            let prompt = format!(
                "请评估以下答案与问题的相关性，给出0-1之间的分数（1表示完全相关，0表示完全不相关）：

问题：{}

答案：{}

请只返回数字分数，不要其他解释。",
                context.question, context.answer
            );

            let messages = vec![ChatMessage {
                role: MessageRole::User,
                content: prompt,
            }];

            match llm_service
                .chat_completion(
                    messages,
                    Some(self.config.evaluation_model.clone()),
                    Some(50),
                    Some(0.1),
                )
                .await
            {
                Ok(response) => {
                    if let Ok(score) = response.content.trim().parse::<f64>() {
                        return Ok(score.min(1.0).max(0.0));
                    }
                }
                Err(e) => {
                    warn!("LLM relevance assessment failed: {}", e);
                }
            }
        }
        Ok(0.5) // 默认值
    }

    /// 使用LLM评估完整性
    async fn llm_assess_completeness(&self, context: &AnswerAssessmentContext) -> Result<f64> {
        if let Some(llm_service) = &self.llm_service {
            let prompt = format!(
                "请评估以下答案的完整性，给出0-1之间的分数（1表示完全完整，0表示严重不完整）：

问题：{}

答案：{}

评估标准：答案是否充分回答了问题的各个方面？是否提供了足够的细节？

请只返回数字分数，不要其他解释。",
                context.question, context.answer
            );

            let messages = vec![ChatMessage {
                role: MessageRole::User,
                content: prompt,
            }];

            match llm_service
                .chat_completion(
                    messages,
                    Some(self.config.evaluation_model.clone()),
                    Some(50),
                    Some(0.1),
                )
                .await
            {
                Ok(response) => {
                    if let Ok(score) = response.content.trim().parse::<f64>() {
                        return Ok(score.min(1.0).max(0.0));
                    }
                }
                Err(e) => {
                    warn!("LLM completeness assessment failed: {}", e);
                }
            }
        }
        Ok(0.5) // 默认值
    }

    /// 使用LLM评估准确性
    async fn llm_assess_accuracy(&self, context: &AnswerAssessmentContext) -> Result<f64> {
        if let Some(llm_service) = &self.llm_service {
            let source_text = context.source_documents.join("\n\n---\n\n");
            let prompt = format!(
                "请基于提供的源文档评估答案的准确性，给出0-1之间的分数（1表示完全准确，0表示完全错误）：

问题：{}

答案：{}

源文档：
{}

评估标准：答案是否与源文档中的信息一致？是否存在明显错误？

请只返回数字分数，不要其他解释。",
                context.question, context.answer, source_text
            );

            let messages = vec![ChatMessage {
                role: MessageRole::User,
                content: prompt,
            }];

            match llm_service
                .chat_completion(
                    messages,
                    Some(self.config.evaluation_model.clone()),
                    Some(50),
                    Some(0.1),
                )
                .await
            {
                Ok(response) => {
                    if let Ok(score) = response.content.trim().parse::<f64>() {
                        return Ok(score.min(1.0).max(0.0));
                    }
                }
                Err(e) => {
                    warn!("LLM accuracy assessment failed: {}", e);
                }
            }
        }
        Ok(0.5) // 默认值
    }

    /// 提取关键词
    fn extract_keywords(&self, text: &str) -> Vec<String> {
        // 简单的关键词提取：去除常见停用词
        let stop_words = vec![
            "的",
            "是",
            "在",
            "有",
            "和",
            "了",
            "与",
            "及",
            "或",
            "但",
            "而",
            "这",
            "那",
            "什么",
            "如何",
            "为什么",
            "怎么",
            "哪里",
            "什么时候",
            "一个",
            "一些",
            "很多",
            "非常",
            "比较",
            "最",
        ];

        text.split_whitespace()
            .filter(|word| !stop_words.contains(&word.to_lowercase().as_str()))
            .filter(|word| word.len() > 1)
            .map(|word| word.to_lowercase())
            .collect()
    }

    /// 生成缓存键
    fn generate_cache_key(&self, context: &AnswerAssessmentContext) -> String {
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{Hash, Hasher},
        };

        let mut hasher = DefaultHasher::new();
        context.question.hash(&mut hasher);
        context.answer.hash(&mut hasher);
        format!("quality_assessment_{}", hasher.finish())
    }

    /// 清理缓存
    pub async fn clear_cache(&self) {
        let mut cache = self.assessment_cache.write().await;
        cache.clear();
        info!("Answer quality assessment cache cleared");
    }

    /// 获取缓存统计
    pub async fn get_cache_stats(&self) -> (usize, usize) {
        let cache = self.assessment_cache.read().await;
        (cache.len(), 1000) // (当前大小, 最大大小)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_quality_level_from_score() {
        assert_eq!(QualityLevel::from_score(0.95), QualityLevel::Excellent);
        assert_eq!(QualityLevel::from_score(0.8), QualityLevel::Good);
        assert_eq!(QualityLevel::from_score(0.6), QualityLevel::Fair);
        assert_eq!(QualityLevel::from_score(0.4), QualityLevel::Poor);
        assert_eq!(QualityLevel::from_score(0.2), QualityLevel::VeryPoor);
    }

    #[tokio::test]
    async fn test_answer_quality_service_creation() {
        let config = AnswerQualityConfig::default();
        let service = AnswerQualityService::new(config, None);

        assert!(service.config.enable_auto_assessment);
        assert_eq!(service.config.min_confidence_threshold, 0.6);
    }

    #[test]
    fn test_extract_keywords() {
        let config = AnswerQualityConfig::default();
        let service = AnswerQualityService::new(config, None);

        let text = "什么是人工智能的发展历程";
        let keywords = service.extract_keywords(text);

        assert!(keywords.contains(&"人工智能".to_string()));
        assert!(keywords.contains(&"发展历程".to_string()));
        assert!(!keywords.contains(&"什么".to_string()));
        assert!(!keywords.contains(&"是".to_string()));
    }
}
