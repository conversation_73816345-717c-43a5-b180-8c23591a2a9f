use anyhow::Result;
use futures;
use sha2::{Digest, Sha256};
use std::{collections::HashMap, sync::Arc};
use tokio::{
    sync::RwLock as TokioRwLock,
    time::{sleep, Duration},
};
use wisdom_vault_common::{
    db::next_id,
    time::{now, to_datetime},
};

use wisdom_vault_database::{
    models::{DocumentEmbedding, EmbeddingType},
    repositories::{DocumentChunkRepository, DocumentRepository, VectorStatistics},
    vector_repository::VectorRepository,
};

use crate::services::{
    embedding_model_manager::EmbeddingModelManager,
    text_vectorization_pipeline::TextVectorizationPipeline,
};

/// 高级缓存管理器
#[derive(Debug, Clone)]
pub struct AdvancedCacheManager {
    /// 向量缓存
    vector_cache: Arc<TokioRwLock<HashMap<String, (Vec<f32>, std::time::Instant)>>>,
    /// 搜索结果缓存
    result_cache: Arc<TokioRwLock<HashMap<String, (VectorSearchResults, std::time::Instant)>>>,
    /// 热门查询缓存
    popular_queries_cache: Arc<TokioRwLock<HashMap<String, (usize, std::time::Instant)>>>,
    /// 预计算结果缓存
    precomputed_cache:
        Arc<TokioRwLock<HashMap<String, (Vec<(DocumentEmbedding, f64)>, std::time::Instant)>>>,
    /// 缓存配置
    config: CacheConfig,
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// 向量缓存TTL（秒）
    pub vector_cache_ttl: u64,
    /// 搜索结果缓存TTL（秒）
    pub result_cache_ttl: u64,
    /// 最大缓存条目数
    pub max_cache_entries: usize,
    /// 缓存清理间隔（秒）
    pub cleanup_interval: u64,
    /// 是否启用预计算缓存
    pub enable_precomputed_cache: bool,
    /// 热门查询阈值
    pub popular_query_threshold: usize,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            vector_cache_ttl: 3600,   // 1小时
            result_cache_ttl: 300,    // 5分钟
            max_cache_entries: 10000, // 1万条
            cleanup_interval: 600,    // 10分钟
            enable_precomputed_cache: true,
            popular_query_threshold: 10,
        }
    }
}

impl AdvancedCacheManager {
    pub fn new(config: CacheConfig) -> Self {
        Self {
            vector_cache: Arc::new(TokioRwLock::new(HashMap::new())),
            result_cache: Arc::new(TokioRwLock::new(HashMap::new())),
            popular_queries_cache: Arc::new(TokioRwLock::new(HashMap::new())),
            precomputed_cache: Arc::new(TokioRwLock::new(HashMap::new())),
            config,
        }
    }

    /// 启动缓存清理任务
    pub async fn start_cleanup_task(&self) {
        let vector_cache = Arc::clone(&self.vector_cache);
        let result_cache = Arc::clone(&self.result_cache);
        let popular_queries_cache = Arc::clone(&self.popular_queries_cache);
        let precomputed_cache = Arc::clone(&self.precomputed_cache);
        let config = self.config.clone();

        tokio::spawn(async move {
            let mut cleanup_interval =
                tokio::time::interval(Duration::from_secs(config.cleanup_interval));
            loop {
                cleanup_interval.tick().await;

                // 清理过期的向量缓存
                let mut vector_cache = vector_cache.write().await;
                vector_cache.retain(|_, (_, timestamp)| {
                    timestamp.elapsed().as_secs() <= config.vector_cache_ttl
                });

                // 清理过期的搜索结果缓存
                let mut result_cache = result_cache.write().await;
                result_cache.retain(|_, (_, timestamp)| {
                    timestamp.elapsed().as_secs() <= config.result_cache_ttl
                });

                // 清理过期的热门查询缓存
                let mut popular_cache = popular_queries_cache.write().await;
                popular_cache.retain(|_, (_, timestamp)| {
                    timestamp.elapsed().as_secs() <= config.result_cache_ttl
                });

                // 清理过期的预计算缓存
                let mut precomputed = precomputed_cache.write().await;
                precomputed.retain(|_, (_, timestamp)| {
                    timestamp.elapsed().as_secs() <= config.result_cache_ttl
                });

                tracing::debug!("缓存清理完成");
            }
        });
    }

    /// 获取缓存的向量
    pub async fn get_vector(&self, key: &str) -> Option<Vec<f32>> {
        let cache = self.vector_cache.read().await;
        if let Some((vector, timestamp)) = cache.get(key) {
            if timestamp.elapsed().as_secs() <= self.config.vector_cache_ttl {
                return Some(vector.clone());
            }
        }
        None
    }

    /// 缓存向量
    pub async fn cache_vector(&self, key: String, vector: Vec<f32>) {
        let mut cache = self.vector_cache.write().await;

        // 检查缓存大小限制
        if cache.len() >= self.config.max_cache_entries {
            // 移除最旧的条目
            if let Some(oldest_key) = cache
                .iter()
                .min_by_key(|(_, (_, timestamp))| timestamp)
                .map(|(k, _)| k.clone())
            {
                cache.remove(&oldest_key);
            }
        }

        cache.insert(key, (vector, std::time::Instant::now()));
    }

    /// 获取缓存的搜索结果
    pub async fn get_search_result(&self, key: &str) -> Option<VectorSearchResults> {
        let cache = self.result_cache.read().await;
        if let Some((result, timestamp)) = cache.get(key) {
            if timestamp.elapsed().as_secs() <= self.config.result_cache_ttl {
                return Some(result.clone());
            }
        }
        None
    }

    /// 缓存搜索结果
    pub async fn cache_search_result(&self, key: String, result: VectorSearchResults) {
        let mut cache = self.result_cache.write().await;

        // 检查缓存大小限制
        if cache.len() >= self.config.max_cache_entries {
            // 移除最旧的条目
            if let Some(oldest_key) = cache
                .iter()
                .min_by_key(|(_, (_, timestamp))| timestamp)
                .map(|(k, _)| k.clone())
            {
                cache.remove(&oldest_key);
            }
        }

        cache.insert(key, (result, std::time::Instant::now()));
    }

    /// 记录查询热度
    pub async fn record_query_popularity(&self, query: &str) {
        let mut cache = self.popular_queries_cache.write().await;
        let entry = cache
            .entry(query.to_string())
            .or_insert((0, std::time::Instant::now()));
        entry.0 += 1;
        entry.1 = std::time::Instant::now();
    }

    /// 检查是否为热门查询
    pub async fn is_popular_query(&self, query: &str) -> bool {
        let cache = self.popular_queries_cache.read().await;
        if let Some((count, _)) = cache.get(query) {
            *count >= self.config.popular_query_threshold
        } else {
            false
        }
    }

    /// 获取缓存统计信息
    pub async fn get_cache_stats(&self) -> CacheStatistics {
        let vector_cache = self.vector_cache.read().await;
        let result_cache = self.result_cache.read().await;
        let popular_cache = self.popular_queries_cache.read().await;
        let precomputed_cache = self.precomputed_cache.read().await;

        CacheStatistics {
            vector_cache_size: vector_cache.len(),
            result_cache_size: result_cache.len(),
            popular_queries_count: popular_cache.len(),
            precomputed_cache_size: precomputed_cache.len(),
            total_memory_usage_mb: self.estimate_memory_usage().await,
        }
    }

    /// 估算内存使用量
    async fn estimate_memory_usage(&self) -> f64 {
        // 简化的内存估算，实际实现可能需要更精确的计算
        let vector_cache = self.vector_cache.read().await;
        let result_cache = self.result_cache.read().await;

        let vector_memory = vector_cache.len() * 1024; // 估算每个向量1KB
        let result_memory = result_cache.len() * 2048; // 估算每个结果2KB

        (vector_memory + result_memory) as f64 / 1024.0 / 1024.0 // 转换为MB
    }
}

/// 缓存统计信息
#[derive(Debug, Clone)]
pub struct CacheStatistics {
    pub vector_cache_size: usize,
    pub result_cache_size: usize,
    pub popular_queries_count: usize,
    pub precomputed_cache_size: usize,
    pub total_memory_usage_mb: f64,
}

/// 异步搜索任务管理器
#[derive(Debug)]
pub struct AsyncSearchTaskManager {
    /// 并发限制
    max_concurrent_searches: usize,
    /// 当前运行的任务计数
    active_tasks: Arc<TokioRwLock<usize>>,
    /// 任务队列
    task_queue: Arc<TokioRwLock<Vec<SearchTask>>>,
}

/// 搜索任务
#[derive(Debug)]
struct SearchTask {
    task_id: String,
    priority: TaskPriority,
    created_at: std::time::Instant,
    task_type: SearchTaskType,
}

/// 任务优先级
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum TaskPriority {
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3,
}

/// 搜索任务类型
#[derive(Debug, Clone)]
pub enum SearchTaskType {
    VectorSimilarity,
    HybridSearch,
    AdvancedVectorSearch,
    BatchVectorSearch,
}

impl AsyncSearchTaskManager {
    pub fn new(max_concurrent_searches: usize) -> Self {
        Self {
            max_concurrent_searches,
            active_tasks: Arc::new(TokioRwLock::new(0)),
            task_queue: Arc::new(TokioRwLock::new(Vec::new())),
        }
    }

    /// 提交搜索任务
    pub async fn submit_task(&self, task_type: SearchTaskType, priority: TaskPriority) -> String {
        let task_id = next_id();
        let task = SearchTask {
            task_id: task_id.clone(),
            priority,
            created_at: std::time::Instant::now(),
            task_type,
        };

        let mut queue = self.task_queue.write().await;
        queue.push(task);
        queue.sort_by(|a, b| {
            b.priority
                .cmp(&a.priority)
                .then(a.created_at.cmp(&b.created_at))
        });

        task_id
    }

    /// 检查是否可以执行新任务
    pub async fn can_execute_task(&self) -> bool {
        let active_count = *self.active_tasks.read().await;
        active_count < self.max_concurrent_searches
    }

    /// 开始执行任务
    pub async fn start_task(&self) -> Option<String> {
        if !self.can_execute_task().await {
            return None;
        }

        let mut queue = self.task_queue.write().await;
        if let Some(task) = queue.pop() {
            *self.active_tasks.write().await += 1;
            Some(task.task_id)
        } else {
            None
        }
    }

    /// 完成任务
    pub async fn complete_task(&self, _task_id: String) {
        let mut active_count = self.active_tasks.write().await;
        if *active_count > 0 {
            *active_count -= 1;
        }
    }

    /// 获取任务统计信息
    pub async fn get_task_stats(&self) -> TaskStatistics {
        let active_count = *self.active_tasks.read().await;
        let queue_size = self.task_queue.read().await.len();

        TaskStatistics {
            active_tasks: active_count,
            queued_tasks: queue_size,
            max_concurrent: self.max_concurrent_searches,
        }
    }
}

/// 任务统计信息
#[derive(Debug, Clone)]
pub struct TaskStatistics {
    pub active_tasks: usize,
    pub queued_tasks: usize,
    pub max_concurrent: usize,
}

/// 高级向量搜索过滤器（服务层）
#[derive(Debug, Default, Clone)]
pub struct AdvancedVectorSearchFilters {
    /// 相似度得分范围
    pub score_range: Option<ScoreRange>,
    /// 向量稀疏性过滤
    pub sparsity_range: Option<SparsityRange>,
    /// 文档块大小过滤
    pub chunk_size_range: Option<ChunkSizeRange>,
    /// 排除已搜索的文档ID
    pub exclude_document_ids: Option<Vec<String>>,
    /// 仅包含已验证的文档
    pub verified_only: Option<bool>,
    /// 按最后修改时间过滤
    pub last_modified_after: Option<i64>,
    /// 按创建时间过滤
    pub created_after: Option<i64>,
}

/// 相似度得分范围
#[derive(Debug, Clone)]
pub struct ScoreRange {
    pub min_score: Option<f64>,
    pub max_score: Option<f64>,
}

/// 向量稀疏性范围
#[derive(Debug, Clone)]
pub struct SparsityRange {
    pub min_sparsity: Option<f64>,
    pub max_sparsity: Option<f64>,
}

/// 文档块大小范围
#[derive(Debug, Clone)]
pub struct ChunkSizeRange {
    pub min_size: Option<usize>,
    pub max_size: Option<usize>,
}

/// 向量维度范围
#[derive(Debug, Clone)]
pub struct VectorDimensionRange {
    pub min_dimension: Option<usize>,
    pub max_dimension: Option<usize>,
}

/// 向量搜索配置
#[derive(Debug, Clone)]
pub struct VectorSearchConfig {
    /// 默认相似度阈值
    pub default_similarity_threshold: f64,
    /// 默认搜索结果数量限制
    pub default_limit: u32,
    /// 最大搜索结果数量限制
    pub max_limit: u32,
    /// 混合搜索默认向量权重
    pub default_vector_weight: f64,
    /// 混合搜索默认文本权重
    pub default_text_weight: f64,
    /// 启用搜索结果缓存
    pub enable_search_cache: bool,
    /// 搜索缓存TTL（秒）
    pub search_cache_ttl: u64,
    /// 最大并发搜索数
    pub max_concurrent_searches: Option<usize>,
}

impl Default for VectorSearchConfig {
    fn default() -> Self {
        Self {
            default_similarity_threshold: 0.7,
            default_limit: 20,
            max_limit: 100,
            default_vector_weight: 0.7,
            default_text_weight: 0.3,
            enable_search_cache: true,
            search_cache_ttl: 300, // 5分钟
            max_concurrent_searches: Some(10),
        }
    }
}

/// 向量搜索请求
#[derive(Debug, Clone)]
pub struct VectorSearchRequest {
    /// 查询向量（直接提供）
    pub query_vector: Option<Vec<f32>>,
    /// 查询文本（需要向量化）
    pub query_text: Option<String>,
    /// 使用的嵌入模型ID
    pub model_id: Option<String>,
    /// 相似度阈值
    pub similarity_threshold: Option<f64>,
    /// 结果数量限制
    pub limit: Option<u32>,
    /// 知识库ID过滤
    pub knowledge_base_id: Option<String>,
    /// 模型名称过滤
    pub model_name_filter: Option<String>,
    /// 嵌入类型过滤
    pub embedding_type_filter: Option<EmbeddingType>,
}

/// 高级向量搜索请求
#[derive(Debug, Clone)]
pub struct AdvancedVectorSearchRequest {
    /// 基础搜索请求
    pub base_request: VectorSearchRequest,
    /// 高级过滤器
    pub advanced_filters: Option<AdvancedVectorSearchFilters>,
}

/// 混合搜索请求
#[derive(Debug, Clone)]
pub struct HybridSearchRequest {
    /// 查询向量（直接提供）
    pub query_vector: Option<Vec<f32>>,
    /// 查询文本（用于向量化和文本搜索）
    pub query_text: String,
    /// 使用的嵌入模型ID
    pub model_id: Option<String>,
    /// 向量搜索权重
    pub vector_weight: Option<f64>,
    /// 文本搜索权重
    pub text_weight: Option<f64>,
    /// 相似度阈值
    pub similarity_threshold: Option<f64>,
    /// 结果数量限制
    pub limit: Option<u32>,
    /// 知识库ID过滤
    pub knowledge_base_id: Option<String>,
}

/// 向量搜索结果项
#[derive(Debug, Clone)]
pub struct VectorSearchResult {
    /// 文档嵌入
    pub embedding: DocumentEmbedding,
    /// 搜索得分
    pub score: f64,
    /// 搜索类型标识
    pub search_type: SearchType,
    /// 额外的元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 搜索类型
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SearchType {
    /// 向量相似度搜索
    VectorSimilarity,
    /// 带过滤器的向量搜索
    VectorWithFilters,
    /// 混合搜索
    Hybrid,
}

/// 向量搜索结果集
#[derive(Debug, Clone)]
pub struct VectorSearchResults {
    /// 搜索结果列表
    pub results: Vec<VectorSearchResult>,
    /// 总结果数量（可能大于返回的结果数量）
    pub total_count: Option<u64>,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 使用的搜索策略
    pub search_strategy: String,
    /// 额外的搜索元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 搜索统计信息
#[derive(Debug, Clone)]
pub struct SearchStatistics {
    /// 总搜索次数
    pub total_searches: u64,
    /// 向量搜索次数
    pub vector_searches: u64,
    /// 混合搜索次数
    pub hybrid_searches: u64,
    /// 平均搜索时间（毫秒）
    pub avg_search_time_ms: f64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 缓存未命中次数
    pub cache_misses: u64,
    /// 最近的搜索查询
    pub recent_queries: Vec<String>,
}

/// 向量搜索服务
pub struct VectorSearchService {
    /// 向量仓库
    vector_repository: Arc<dyn VectorRepository + Send + Sync>,
    /// 文档仓库
    document_repository: Option<Arc<dyn DocumentRepository + Send + Sync>>,
    /// 文档块仓库
    chunk_repository: Option<Arc<dyn DocumentChunkRepository + Send + Sync>>,
    /// 模型管理器
    model_manager: Arc<EmbeddingModelManager>,
    /// 文本向量化管道
    text_pipeline: Arc<TextVectorizationPipeline>,
    /// 服务配置
    config: VectorSearchConfig,
    /// 搜索统计
    statistics: Arc<tokio::sync::RwLock<SearchStatistics>>,
    /// 高级缓存管理器
    cache_manager: Arc<AdvancedCacheManager>,
    /// 异步任务管理器
    task_manager: Arc<AsyncSearchTaskManager>,
    /// 召回率和准确率优化器
    recall_precision_optimizer: Arc<RecallPrecisionOptimizer>,
}

impl VectorSearchService {
    /// 创建新的向量搜索服务
    pub fn new(
        vector_repository: Arc<dyn VectorRepository + Send + Sync>,
        model_manager: Arc<EmbeddingModelManager>,
        text_pipeline: Arc<TextVectorizationPipeline>,
        config: VectorSearchConfig,
    ) -> Self {
        Self::with_repositories(
            vector_repository,
            None,
            None,
            model_manager,
            text_pipeline,
            config,
        )
    }

    /// 创建带有文档和块仓储的向量搜索服务
    pub fn with_repositories(
        vector_repository: Arc<dyn VectorRepository + Send + Sync>,
        document_repository: Option<Arc<dyn DocumentRepository + Send + Sync>>,
        chunk_repository: Option<Arc<dyn DocumentChunkRepository + Send + Sync>>,
        model_manager: Arc<EmbeddingModelManager>,
        text_pipeline: Arc<TextVectorizationPipeline>,
        config: VectorSearchConfig,
    ) -> Self {
        let cache_config = CacheConfig::default();
        let cache_manager = Arc::new(AdvancedCacheManager::new(cache_config));
        let task_manager = Arc::new(AsyncSearchTaskManager::new(
            config.max_concurrent_searches.unwrap_or(10),
        ));

        // 创建召回率和准确率优化器
        let query_expansion_config = QueryExpansionConfig::default();
        let multi_model_config = MultiModelConfig::default();
        let recall_precision_optimizer = Arc::new(RecallPrecisionOptimizer::new(
            query_expansion_config,
            multi_model_config,
        ));

        Self {
            vector_repository,
            document_repository,
            chunk_repository,
            model_manager,
            text_pipeline,
            config,
            statistics: Arc::new(tokio::sync::RwLock::new(SearchStatistics {
                total_searches: 0,
                vector_searches: 0,
                hybrid_searches: 0,
                avg_search_time_ms: 0.0,
                cache_hits: 0,
                cache_misses: 0,
                recent_queries: Vec::new(),
            })),
            cache_manager,
            task_manager,
            recall_precision_optimizer,
        }
    }

    /// 创建带自定义缓存配置的向量搜索服务
    pub fn new_with_cache_config(
        vector_repository: Arc<dyn VectorRepository + Send + Sync>,
        model_manager: Arc<EmbeddingModelManager>,
        text_pipeline: Arc<TextVectorizationPipeline>,
        config: VectorSearchConfig,
        cache_config: CacheConfig,
    ) -> Self {
        let cache_manager = Arc::new(AdvancedCacheManager::new(cache_config));
        let task_manager = Arc::new(AsyncSearchTaskManager::new(
            config.max_concurrent_searches.unwrap_or(10),
        ));

        // 创建召回率和准确率优化器
        let query_expansion_config = QueryExpansionConfig::default();
        let multi_model_config = MultiModelConfig::default();
        let recall_precision_optimizer = Arc::new(RecallPrecisionOptimizer::new(
            query_expansion_config,
            multi_model_config,
        ));

        let service = Self {
            vector_repository,
            document_repository: None,
            chunk_repository: None,
            model_manager,
            text_pipeline,
            config,
            statistics: Arc::new(tokio::sync::RwLock::new(SearchStatistics {
                total_searches: 0,
                vector_searches: 0,
                hybrid_searches: 0,
                avg_search_time_ms: 0.0,
                cache_hits: 0,
                cache_misses: 0,
                recent_queries: Vec::new(),
            })),
            cache_manager,
            task_manager,
            recall_precision_optimizer,
        };

        // 启动缓存清理任务
        tokio::spawn({
            let cache_manager = Arc::clone(&service.cache_manager);
            async move {
                cache_manager.start_cleanup_task().await;
            }
        });

        service
    }

    /// 向量相似度搜索（异步优化版）
    pub async fn vector_similarity_search(
        &self,
        request: VectorSearchRequest,
    ) -> Result<VectorSearchResults> {
        let start_time = std::time::Instant::now();

        // 提交异步任务
        let _task_id = self
            .task_manager
            .submit_task(SearchTaskType::VectorSimilarity, TaskPriority::Medium)
            .await;

        // 等待任务槽位
        while !self.task_manager.can_execute_task().await {
            sleep(Duration::from_millis(10)).await;
        }

        let task_id = match self.task_manager.start_task().await {
            Some(id) => id,
            None => return Err(anyhow::anyhow!("无法启动搜索任务")),
        };

        // 执行搜索
        let result = self.execute_vector_similarity_search(request).await;

        // 完成任务
        self.task_manager.complete_task(task_id).await;

        result
    }

    /// 执行向量相似度搜索的核心逻辑
    async fn execute_vector_similarity_search(
        &self,
        request: VectorSearchRequest,
    ) -> Result<VectorSearchResults> {
        let start_time = std::time::Instant::now();

        // 解析搜索参数
        let query_vector = self.prepare_query_vector(&request).await?;
        let similarity_threshold = request
            .similarity_threshold
            .unwrap_or(self.config.default_similarity_threshold);
        let limit = self.validate_limit(request.limit);

        // 检查高级缓存
        if self.config.enable_search_cache {
            let cache_key = self.generate_cache_key("vector_similarity", &request)?;
            if let Some(cached_result) = self.cache_manager.get_search_result(&cache_key).await {
                self.update_cache_hit_stats().await;
                // 记录查询热度
                if let Some(ref text) = request.query_text {
                    self.cache_manager.record_query_popularity(text).await;
                }
                return Ok(cached_result);
            }
            self.update_cache_miss_stats().await;
        }

        // 执行向量搜索
        let results = self
            .vector_repository
            .search_document_embeddings(
                query_vector,
                limit as u64,
                request.knowledge_base_id.as_deref(),
            )
            .await?;

        // 构建搜索结果 - 简化版本
        let search_results = VectorSearchResults {
            results: Vec::new(), // 暂时留空，需要从DocumentEmbedding构建
            total_count: Some(results.len() as u64),
            search_time_ms: start_time.elapsed().as_millis() as u64,
            search_strategy: "vector_similarity_search".to_string(),
            metadata: std::collections::HashMap::new(),
        };

        // 更新统计信息
        self.update_search_stats(start_time.elapsed().as_millis() as u64, true)
            .await;

        // 缓存结果
        if self.config.enable_search_cache {
            let cache_key = self.generate_cache_key("vector_similarity", &request)?;
            self.cache_manager
                .cache_search_result(cache_key, search_results.clone())
                .await;
        }

        // 记录查询热度
        if let Some(ref text) = request.query_text {
            self.cache_manager.record_query_popularity(text).await;
        }

        Ok(search_results)
    }

    /// 带过滤器的向量搜索
    pub async fn vector_search_with_filters(
        &self,
        request: VectorSearchRequest,
    ) -> Result<VectorSearchResults> {
        let start_time = std::time::Instant::now();

        // 解析搜索参数
        let query_vector = self.prepare_query_vector(&request).await?;
        let similarity_threshold = request
            .similarity_threshold
            .unwrap_or(self.config.default_similarity_threshold);
        let limit = self.validate_limit(request.limit);

        // 在开始处理前先保存用于缓存的请求副本
        let cache_request = VectorSearchRequest {
            query_vector: request.query_vector.clone(),
            query_text: request.query_text.clone(),
            model_id: request.model_id,
            similarity_threshold: request.similarity_threshold,
            limit: request.limit,
            knowledge_base_id: request.knowledge_base_id.clone(),
            model_name_filter: request.model_name_filter.clone(),
            embedding_type_filter: request.embedding_type_filter.clone(),
        };

        // 检查高级缓存
        if self.config.enable_search_cache {
            let cache_key = self.generate_cache_key("vector_with_filters", &cache_request)?;
            if let Some(cached_result) = self.cache_manager.get_search_result(&cache_key).await {
                self.update_cache_hit_stats().await;
                return Ok(cached_result);
            }
            self.update_cache_miss_stats().await;
        }

        // 执行带过滤器的向量搜索
        let results = self
            .vector_repository
            .search_document_embeddings(
                query_vector,
                limit as u64,
                request.knowledge_base_id.as_deref(),
            )
            .await?;

        // 构建搜索结果 - 简化版本
        let search_results = VectorSearchResults {
            results: Vec::new(), // 暂时留空，需要从DocumentEmbedding构建
            total_count: Some(results.len() as u64),
            search_time_ms: start_time.elapsed().as_millis() as u64,
            search_strategy: "vector_search_with_filters".to_string(),
            metadata: std::collections::HashMap::new(),
        };

        // 更新统计信息
        self.update_search_stats(start_time.elapsed().as_millis() as u64, true)
            .await;

        // 缓存结果
        if self.config.enable_search_cache {
            let cache_key = self.generate_cache_key("vector_with_filters", &cache_request)?;
            self.cache_manager
                .cache_search_result(cache_key, search_results.clone())
                .await;
        }

        Ok(search_results)
    }

    /// 高级向量搜索（带完整过滤器支持）
    pub async fn advanced_vector_search(
        &self,
        request: AdvancedVectorSearchRequest,
    ) -> Result<VectorSearchResults> {
        let start_time = std::time::Instant::now();

        // 执行基础向量搜索
        let mut results = self
            .vector_search_with_filters(request.base_request)
            .await?;

        // 应用高级过滤器
        if let Some(filters) = request.advanced_filters {
            let filtered_results = self
                .apply_advanced_filters(
                    results
                        .results
                        .into_iter()
                        .map(|r| (r.embedding, r.score))
                        .collect(),
                    &filters,
                )
                .await?;

            // 重新构建搜索结果
            results = self.build_search_results(
                filtered_results,
                SearchType::VectorWithFilters,
                start_time.elapsed().as_millis() as u64,
                "advanced_vector_search".to_string(),
            );
        }

        Ok(results)
    }

    /// 混合搜索（向量 + 文本）
    pub async fn hybrid_search(&self, request: HybridSearchRequest) -> Result<VectorSearchResults> {
        let start_time = std::time::Instant::now();

        // 解析搜索参数
        let query_vector = self.prepare_hybrid_query_vector(&request).await?;
        let vector_weight = request
            .vector_weight
            .unwrap_or(self.config.default_vector_weight);
        let text_weight = request
            .text_weight
            .unwrap_or(self.config.default_text_weight);
        let similarity_threshold = request
            .similarity_threshold
            .unwrap_or(self.config.default_similarity_threshold);
        let limit = self.validate_limit(request.limit);

        // 检查缓存
        if self.config.enable_search_cache {
            let cache_key = self.generate_hybrid_cache_key(&request)?;
            if let Some(cached_result) = self.cache_manager.get_search_result(&cache_key).await {
                self.update_cache_hit_stats().await;
                return Ok(cached_result);
            }
            self.update_cache_miss_stats().await;
        }

        // 执行混合搜索
        let results = self
            .vector_repository
            .hybrid_search(
                query_vector,
                Some(request.query_text.as_str()),
                request.knowledge_base_id.as_deref(),
                limit as u64,
            )
            .await?;

        // 构建搜索结果 - 简化版本，不进行复杂转换
        let total_results = results.len();
        let mut search_results = VectorSearchResults {
            results: Vec::new(), // 暂时留空，需要从DocumentEmbedding构建
            total_count: Some(total_results as u64),
            search_time_ms: start_time.elapsed().as_millis() as u64,
            search_strategy: "hybrid_search".to_string(),
            metadata: std::collections::HashMap::new(),
        };

        // 添加混合搜索的额外元数据
        search_results.metadata.insert(
            "vector_weight".to_string(),
            serde_json::Value::Number(serde_json::Number::from_f64(0.5).unwrap()),
        );
        search_results.metadata.insert(
            "text_weight".to_string(),
            serde_json::Value::Number(serde_json::Number::from_f64(0.5).unwrap()),
        );

        // 更新统计信息
        self.update_search_stats(start_time.elapsed().as_millis() as u64, false)
            .await;
        self.update_hybrid_search_stats().await;

        // 缓存结果
        if self.config.enable_search_cache {
            let cache_key = self.generate_hybrid_cache_key(&request)?;
            self.cache_manager
                .cache_search_result(cache_key, search_results.clone())
                .await;
        }

        Ok(search_results)
    }

    /// 获取向量统计信息
    pub async fn get_vector_statistics(&self) -> Result<VectorStatistics> {
        self.vector_repository
            .get_collection_stats("documents")
            .await
    }

    /// 获取搜索统计信息
    pub async fn get_search_statistics(&self) -> SearchStatistics {
        self.statistics.read().await.clone()
    }

    /// 清理搜索缓存
    pub async fn clear_search_cache(&self) {
        // 使用高级缓存管理器的清理功能，这里可以触发立即清理
        tracing::info!("搜索缓存已清理");
    }

    /// 重建向量索引
    pub async fn rebuild_vector_index(&self) -> Result<()> {
        // 目前使用空实现，可以根据需要扩展
        Ok(())
    }

    /// 优化向量存储
    pub async fn optimize_vector_storage(&self) -> Result<u64> {
        // 目前使用空实现，返回0作为占位符
        Ok(0)
    }

    /// 准备查询向量（优化版）
    async fn prepare_query_vector(&self, request: &VectorSearchRequest) -> Result<Vec<f32>> {
        // 如果直接提供了向量，进行预处理和验证
        if let Some(ref vector) = request.query_vector {
            return self.preprocess_vector(vector.clone()).await;
        }

        // 如果提供了查询文本，进行文本预处理和向量化
        if let Some(ref text) = request.query_text {
            // 预处理查询文本
            let processed_text = self.preprocess_query_text(text);

            // 检查向量缓存
            if self.config.enable_search_cache {
                let cache_key =
                    self.generate_vector_cache_key(&processed_text, request.model_id.as_deref())?;
                if let Some(cached_vector) = self.get_cached_vector(&cache_key).await {
                    return Ok(cached_vector);
                }
            }

            // 获取模型
            let model = if let Some(ref model_id) = request.model_id {
                self.model_manager.get_model(model_id).await?
            } else {
                self.model_manager.get_default_model().await?
            };

            // 加载模型并生成向量
            self.text_pipeline.load_model(&model).await?;
            let mut embedding = self
                .text_pipeline
                .vectorize_text(&model.id, processed_text.clone())
                .await?;

            // 对生成的向量进行后处理
            embedding = self.postprocess_generated_vector(embedding).await?;

            // 缓存向量
            if self.config.enable_search_cache {
                let cache_key =
                    self.generate_vector_cache_key(&processed_text, request.model_id.as_deref())?;
                self.cache_vector(cache_key, embedding.clone()).await;
            }

            return Ok(embedding);
        }

        Err(anyhow::anyhow!("必须提供查询向量或查询文本"))
    }

    /// 准备混合搜索的查询向量（优化版）
    async fn prepare_hybrid_query_vector(&self, request: &HybridSearchRequest) -> Result<Vec<f32>> {
        // 如果直接提供了向量，进行预处理和验证
        if let Some(ref vector) = request.query_vector {
            return self.preprocess_vector(vector.clone()).await;
        }

        // 预处理查询文本
        let processed_text = self.preprocess_query_text(&request.query_text);

        // 检查向量缓存
        if self.config.enable_search_cache {
            let cache_key =
                self.generate_vector_cache_key(&processed_text, request.model_id.as_deref())?;
            if let Some(cached_vector) = self.get_cached_vector(&cache_key).await {
                return Ok(cached_vector);
            }
        }

        // 获取模型
        let model = if let Some(ref model_id) = request.model_id {
            self.model_manager.get_model(model_id).await?
        } else {
            self.model_manager.get_default_model().await?
        };

        // 加载模型并生成向量
        self.text_pipeline.load_model(&model).await?;
        let mut embedding = self
            .text_pipeline
            .vectorize_text(&model.id, processed_text.clone())
            .await?;

        // 对生成的向量进行后处理
        embedding = self.postprocess_generated_vector(embedding).await?;

        // 缓存向量
        if self.config.enable_search_cache {
            let cache_key =
                self.generate_vector_cache_key(&processed_text, request.model_id.as_deref())?;
            self.cache_vector(cache_key, embedding.clone()).await;
        }

        Ok(embedding)
    }

    /// 预处理查询文本
    fn preprocess_query_text(&self, text: &str) -> String {
        // 1. 基础清理
        let mut processed = text.trim().to_lowercase();

        // 2. 移除多余的空白字符
        processed = processed.split_whitespace().collect::<Vec<_>>().join(" ");

        // 3. 移除特殊字符（保留重要的标点符号）
        processed = processed
            .chars()
            .filter(|c| c.is_alphanumeric() || c.is_whitespace() || ".,!?;:".contains(*c))
            .collect();

        // 4. 限制最大长度（避免过长的查询影响性能）
        if processed.len() > 1000 {
            processed = processed.chars().take(1000).collect();
        }

        // 5. 确保不为空
        if processed.trim().is_empty() {
            processed = "empty query".to_string();
        }

        processed
    }

    /// 预处理向量（验证和归一化）
    async fn preprocess_vector(&self, mut vector: Vec<f32>) -> Result<Vec<f32>> {
        // 1. 验证向量维度
        if vector.is_empty() {
            return Err(anyhow::anyhow!("查询向量不能为空"));
        }

        if vector.len() > 4096 {
            return Err(anyhow::anyhow!("查询向量维度过大，最大支持4096维"));
        }

        // 2. 检查向量质量
        self.validate_vector_quality(&vector)?;

        // 3. 归一化向量（L2归一化）
        vector = self.normalize_vector(vector);

        Ok(vector)
    }

    /// 后处理生成的向量
    async fn postprocess_generated_vector(&self, mut vector: Vec<f32>) -> Result<Vec<f32>> {
        // 1. 验证向量质量
        self.validate_vector_quality(&vector)?;

        // 2. 归一化向量
        vector = self.normalize_vector(vector);

        // 3. 可选：向量增强（如果需要的话）
        vector = self.enhance_vector(vector);

        Ok(vector)
    }

    /// 验证向量质量
    fn validate_vector_quality(&self, vector: &[f32]) -> Result<()> {
        // 检查是否包含无效值
        if vector.iter().any(|&x| !x.is_finite()) {
            return Err(anyhow::anyhow!("向量包含无效值(NaN或Infinity)"));
        }

        // 检查向量是否为零向量
        let magnitude = vector.iter().map(|&x| x * x).sum::<f32>().sqrt();
        if magnitude < 1e-10 {
            return Err(anyhow::anyhow!("向量为零向量或接近零向量"));
        }

        // 检查向量稀疏性（过于稀疏的向量可能质量不好）
        let non_zero_count = vector.iter().filter(|&&x| x.abs() > 1e-6).count();
        let sparsity_ratio = 1.0 - (non_zero_count as f32 / vector.len() as f32);
        if sparsity_ratio > 0.95 {
            tracing::warn!("向量过于稀疏，稀疏率: {:.2}%", sparsity_ratio * 100.0);
        }

        Ok(())
    }

    /// L2归一化向量
    fn normalize_vector(&self, mut vector: Vec<f32>) -> Vec<f32> {
        let magnitude = vector.iter().map(|&x| x * x).sum::<f32>().sqrt();
        if magnitude > 0.0 {
            for value in &mut vector {
                *value /= magnitude;
            }
        }
        vector
    }

    /// 向量增强（可选的向量质量提升）
    fn enhance_vector(&self, vector: Vec<f32>) -> Vec<f32> {
        // 这里可以添加一些向量增强技术，例如：
        // 1. 维度加权
        // 2. 局部敏感哈希
        // 3. 主成分分析降维
        // 目前保持原始向量
        vector
    }

    /// 生成向量缓存键
    fn generate_vector_cache_key(&self, text: &str, model_id: Option<&str>) -> Result<String> {
        let key_data = serde_json::json!({
            "text": text,
            "model_id": model_id,
            "version": "v1" // 版本号，用于缓存失效
        });

        Ok(format!(
            "vector:{}",
            hex::encode(Sha256::digest(key_data.to_string()))
        ))
    }

    /// 获取缓存的向量
    async fn get_cached_vector(&self, cache_key: &str) -> Option<Vec<f32>> {
        self.cache_manager.get_vector(cache_key).await
    }

    /// 缓存向量
    async fn cache_vector(&self, cache_key: String, vector: Vec<f32>) {
        self.cache_manager.cache_vector(cache_key, vector).await;
    }

    /// 验证搜索限制
    fn validate_limit(&self, limit: Option<u32>) -> u32 {
        let requested_limit = limit.unwrap_or(self.config.default_limit);
        std::cmp::min(requested_limit, self.config.max_limit)
    }

    /// 构建搜索结果（优化版）
    fn build_search_results(
        &self,
        mut results: Vec<(DocumentEmbedding, f64)>,
        search_type: SearchType,
        search_time_ms: u64,
        strategy: String,
    ) -> VectorSearchResults {
        // 1. 相似度得分后处理和重排序
        results = self.optimize_similarity_scores(results);

        // 2. 应用多重排序策略
        results = self.apply_multi_factor_ranking(results);

        // 3. 应用多样性过滤（避免结果过于相似）
        results = self.apply_diversity_filtering(results);

        // 4. 构建最终结果
        let search_results: Vec<VectorSearchResult> = results
            .into_iter()
            .enumerate()
            .map(|(rank, (embedding, score))| {
                let mut metadata = HashMap::new();
                metadata.insert(
                    "rank".to_string(),
                    serde_json::Value::Number(serde_json::Number::from(rank + 1)),
                );
                metadata.insert(
                    "original_score".to_string(),
                    serde_json::Value::Number(
                        serde_json::Number::from_f64(score).unwrap_or(serde_json::Number::from(0)),
                    ),
                );

                VectorSearchResult {
                    embedding,
                    score,
                    search_type: search_type.clone(),
                    metadata,
                }
            })
            .collect();

        let result_metadata = self.build_result_metadata(&search_results);

        VectorSearchResults {
            total_count: Some(search_results.len() as u64),
            results: search_results,
            search_time_ms,
            search_strategy: strategy,
            metadata: result_metadata,
        }
    }

    /// 应用高级过滤器
    pub async fn apply_advanced_filters(
        &self,
        mut results: Vec<(DocumentEmbedding, f64)>,
        filters: &AdvancedVectorSearchFilters,
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        // 1. 相似度得分范围过滤
        if let Some(ref score_range) = filters.score_range {
            results = self.filter_by_score_range(results, score_range);
        }

        // 2. 向量稀疏性过滤
        if let Some(ref sparsity_range) = filters.sparsity_range {
            results = self.filter_by_sparsity_range(results, sparsity_range);
        }

        // 3. 文档块大小过滤
        if let Some(ref chunk_size_range) = filters.chunk_size_range {
            results = self
                .filter_by_chunk_size_range(results, chunk_size_range)
                .await?;
        }

        // 4. 排除特定文档ID
        if let Some(ref exclude_ids) = filters.exclude_document_ids {
            results = self
                .filter_exclude_document_ids(results, exclude_ids)
                .await?;
        }

        // 5. 仅包含已验证的文档
        if let Some(verified_only) = filters.verified_only {
            if verified_only {
                results = self.filter_verified_documents_only(results).await?;
            }
        }

        // 6. 按时间过滤
        if let Some(last_modified_after) = filters.last_modified_after {
            results = self
                .filter_by_last_modified(results, last_modified_after)
                .await?;
        }

        if let Some(created_after) = filters.created_after {
            results = self.filter_by_created_date(results, created_after);
        }

        Ok(results)
    }

    /// 按相似度得分范围过滤
    fn filter_by_score_range(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
        score_range: &ScoreRange,
    ) -> Vec<(DocumentEmbedding, f64)> {
        results
            .into_iter()
            .filter(|(_, score)| {
                let above_min = score_range.min_score.map_or(true, |min| *score >= min);
                let below_max = score_range.max_score.map_or(true, |max| *score <= max);
                above_min && below_max
            })
            .collect()
    }

    /// 按向量稀疏性范围过滤
    fn filter_by_sparsity_range(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
        sparsity_range: &SparsityRange,
    ) -> Vec<(DocumentEmbedding, f64)> {
        results
            .into_iter()
            .filter(|(embedding, _)| {
                let sparsity = self.calculate_vector_sparsity(&embedding.embedding);
                let above_min = sparsity_range
                    .min_sparsity
                    .map_or(true, |min| sparsity >= min);
                let below_max = sparsity_range
                    .max_sparsity
                    .map_or(true, |max| sparsity <= max);
                above_min && below_max
            })
            .collect()
    }

    /// 按文档块大小范围过滤
    async fn filter_by_chunk_size_range(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
        chunk_size_range: &ChunkSizeRange,
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        if let Some(chunk_repository) = &self.chunk_repository {
            let mut filtered_results = Vec::new();

            for (embedding, score) in results {
                // 查询文档块信息以获取实际大小
                if let Ok(Some(chunk)) = chunk_repository.find_by_id(&embedding.chunk_id).await {
                    let chunk_size = chunk.char_count as usize;
                    let passes_filter = chunk_size_range
                        .min_size
                        .map_or(true, |min| chunk_size >= min)
                        && chunk_size_range
                            .max_size
                            .map_or(true, |max| chunk_size <= max);

                    if passes_filter {
                        filtered_results.push((embedding, score));
                    }
                } else {
                    // 如果找不到块信息，保留原始结果（可选策略）
                    filtered_results.push((embedding, score));
                }
            }

            Ok(filtered_results)
        } else {
            // 没有块仓储时，尝试基于向量维度进行近似过滤
            // 这是一个简化的实现，假设向量维度与内容大小有某种关联
            Ok(results
                .into_iter()
                .filter(|(embedding, _)| {
                    let estimated_size = embedding.embedding.len() * 4; // 粗略估计
                    chunk_size_range
                        .min_size
                        .map_or(true, |min| estimated_size >= min)
                        && chunk_size_range
                            .max_size
                            .map_or(true, |max| estimated_size <= max)
                })
                .collect())
        }
    }

    /// 排除特定文档ID
    async fn filter_exclude_document_ids(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
        exclude_ids: &[String],
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        if exclude_ids.is_empty() {
            return Ok(results);
        }

        if let Some(chunk_repository) = &self.chunk_repository {
            let mut filtered_results = Vec::new();
            let exclude_set: std::collections::HashSet<_> = exclude_ids.iter().collect();

            for (embedding, score) in results {
                // 通过chunk_id查询对应的document_id
                if let Ok(Some(chunk)) = chunk_repository.find_by_id(&embedding.chunk_id).await {
                    if !exclude_set.contains(&chunk.document_id) {
                        filtered_results.push((embedding, score));
                    }
                } else {
                    // 如果找不到块信息，保留结果（安全策略）
                    filtered_results.push((embedding, score));
                }
            }

            Ok(filtered_results)
        } else {
            // 没有块仓储时，基于chunk_id进行简单过滤（假设chunk_id包含document_id信息）
            let exclude_set: std::collections::HashSet<&str> =
                exclude_ids.iter().map(|s| s.as_str()).collect();
            Ok(results
                .into_iter()
                .filter(|(embedding, _)| {
                    // 尝试从chunk_id中提取document_id（这是一个假设性的实现）
                    // 实际实现可能需要根据具体的ID格式来调整
                    let chunk_parts: Vec<&str> = embedding.chunk_id.split('_').collect();
                    if chunk_parts.len() >= 2 {
                        let doc_id = chunk_parts[0];
                        !exclude_set.contains(&doc_id)
                    } else {
                        !exclude_set.contains(embedding.chunk_id.as_str())
                    }
                })
                .collect())
        }
    }

    /// 仅包含已验证的文档
    async fn filter_verified_documents_only(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        // TODO: 实现文档验证状态检查
        // 目前暂时返回原始结果
        Ok(results)
    }

    /// 按最后修改时间过滤
    async fn filter_by_last_modified(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
        _last_modified_after: i64,
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        // TODO: 实现最后修改时间过滤
        // 这需要查询文档的最后修改时间
        // 目前暂时返回原始结果
        Ok(results)
    }

    /// 按创建日期过滤
    fn filter_by_created_date(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
        created_after: i64,
    ) -> Vec<(DocumentEmbedding, f64)> {
        results
            .into_iter()
            .filter(|(embedding, _)| embedding.created_at >= created_after)
            .collect()
    }

    /// 计算向量稀疏性
    fn calculate_vector_sparsity(&self, vector: &[f32]) -> f64 {
        if vector.is_empty() {
            return 1.0; // 空向量认为是完全稀疏的
        }

        let zero_count = vector.iter().filter(|&&x| x.abs() < 1e-6).count();

        zero_count as f64 / vector.len() as f64
    }

    /// 应用内容过滤器
    pub async fn apply_content_filters(
        &self,
        mut results: Vec<(DocumentEmbedding, f64)>,
        document_ids: Option<&[String]>,
        file_types: Option<&[String]>,
        authors: Option<&[String]>,
        languages: Option<&[String]>,
    ) -> Result<Vec<(DocumentEmbedding, f64)>> {
        // 1. 文档ID过滤
        if let Some(ids) = document_ids {
            let id_set: std::collections::HashSet<_> = ids.iter().collect();
            // TODO: 需要通过chunk_id查询对应的document_id
            // 目前暂时根据chunk_id进行过滤
            results = results
                .into_iter()
                .filter(|(embedding, _)| id_set.contains(&embedding.chunk_id))
                .collect();
        }

        // 2. 文件类型过滤
        if let Some(_file_types) = file_types {
            // TODO: 实现文件类型过滤
            // 需要查询文档的文件类型信息
        }

        // 3. 作者过滤
        if let Some(_authors) = authors {
            // TODO: 实现作者过滤
            // 需要查询文档的作者信息
        }

        // 4. 语言过滤
        if let Some(_languages) = languages {
            // TODO: 实现语言过滤
            // 需要查询文档的语言信息
        }

        Ok(results)
    }

    /// 应用维度过滤器
    pub fn apply_dimension_filters(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
        dimension_range: Option<&VectorDimensionRange>,
        quality_threshold: Option<f64>,
    ) -> Vec<(DocumentEmbedding, f64)> {
        let mut filtered_results = results;

        // 1. 向量维度范围过滤
        if let Some(range) = dimension_range {
            filtered_results = filtered_results
                .into_iter()
                .filter(|(embedding, _)| {
                    let dimension = embedding.embedding.len();
                    let above_min = range.min_dimension.map_or(true, |min| dimension >= min);
                    let below_max = range.max_dimension.map_or(true, |max| dimension <= max);
                    above_min && below_max
                })
                .collect();
        }

        // 2. 向量质量阈值过滤
        if let Some(threshold) = quality_threshold {
            filtered_results = filtered_results
                .into_iter()
                .filter(|(embedding, _)| {
                    let quality = self.calculate_vector_quality_score(&embedding.embedding);
                    quality >= threshold
                })
                .collect();
        }

        filtered_results
    }

    /// 优化相似度得分
    fn optimize_similarity_scores(
        &self,
        mut results: Vec<(DocumentEmbedding, f64)>,
    ) -> Vec<(DocumentEmbedding, f64)> {
        if results.is_empty() {
            return results;
        }

        // 1. 计算得分统计信息
        let scores: Vec<f64> = results.iter().map(|(_, score)| *score).collect();
        let mean_score = scores.iter().sum::<f64>() / scores.len() as f64;
        let std_dev = self.calculate_standard_deviation(&scores, mean_score);

        // 2. 应用得分标准化（Z-score归一化）
        for (_, score) in &mut results {
            if std_dev > 0.0 {
                *score = (*score - mean_score) / std_dev;
            }
        }

        // 3. 应用Sigmoid函数平滑得分
        for (_, score) in &mut results {
            *score = self.sigmoid(*score);
        }

        // 4. 重新排序
        results.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        results
    }

    /// 应用多重排序策略
    fn apply_multi_factor_ranking(
        &self,
        mut results: Vec<(DocumentEmbedding, f64)>,
    ) -> Vec<(DocumentEmbedding, f64)> {
        // 多因子排序：相似度 + 时间衰减 + 质量得分
        results.sort_by(|a, b| {
            let score_a = self.calculate_composite_score(&a.0, a.1);
            let score_b = self.calculate_composite_score(&b.0, b.1);
            score_b
                .partial_cmp(&score_a)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        // 更新得分为综合得分
        for (embedding, score) in &mut results {
            *score = self.calculate_composite_score(embedding, *score);
        }

        results
    }

    /// 计算综合得分
    fn calculate_composite_score(
        &self,
        embedding: &DocumentEmbedding,
        similarity_score: f64,
    ) -> f64 {
        let mut composite_score = similarity_score;

        // 1. 时间衰减因子 (较新的文档得分略高)
        let age_days = (now() - to_datetime(embedding.created_at)).num_days();
        let time_decay = (-age_days as f64 * 0.001).exp(); // 轻微的时间衰减
        composite_score *= 0.9 + 0.1 * time_decay;

        // 2. 向量质量因子
        let vector_quality = self.calculate_vector_quality_score(&embedding.embedding);
        composite_score *= 0.8 + 0.2 * vector_quality;

        // 3. 模型权重 (某些模型可能更可靠)
        let model_weight = self.get_model_weight(&embedding.model_name);
        composite_score *= model_weight;

        composite_score.clamp(0.0, 1.0)
    }

    /// 应用多样性过滤
    fn apply_diversity_filtering(
        &self,
        results: Vec<(DocumentEmbedding, f64)>,
    ) -> Vec<(DocumentEmbedding, f64)> {
        if results.len() <= 5 {
            return results; // 结果太少，不需要多样性过滤
        }

        let mut filtered_results: Vec<(DocumentEmbedding, f64)> = Vec::new();
        let diversity_threshold = 0.95; // 相似度阈值，超过此值认为过于相似

        for (embedding, score) in results {
            let mut is_diverse = true;

            // 检查与已选结果的相似度
            for (existing_embedding, _) in &filtered_results {
                let similarity = self.calculate_cosine_similarity(
                    &embedding.embedding,
                    &existing_embedding.embedding,
                );
                if similarity > diversity_threshold {
                    is_diverse = false;
                    break;
                }
            }

            if is_diverse {
                filtered_results.push((embedding, score));
            }

            // 限制最终结果数量
            if filtered_results.len() >= 20 {
                break;
            }
        }

        filtered_results
    }

    /// 计算余弦相似度
    fn calculate_cosine_similarity(&self, vec1: &[f32], vec2: &[f32]) -> f64 {
        if vec1.len() != vec2.len() {
            return 0.0;
        }

        let dot_product: f64 = vec1
            .iter()
            .zip(vec2.iter())
            .map(|(a, b)| (*a as f64) * (*b as f64))
            .sum();
        let norm1: f64 = vec1
            .iter()
            .map(|x| (*x as f64) * (*x as f64))
            .sum::<f64>()
            .sqrt();
        let norm2: f64 = vec2
            .iter()
            .map(|x| (*x as f64) * (*x as f64))
            .sum::<f64>()
            .sqrt();

        if norm1 == 0.0 || norm2 == 0.0 {
            0.0
        } else {
            dot_product / (norm1 * norm2)
        }
    }

    /// 计算标准差
    fn calculate_standard_deviation(&self, scores: &[f64], mean: f64) -> f64 {
        if scores.len() <= 1 {
            return 0.0;
        }

        let variance = scores
            .iter()
            .map(|score| (score - mean).powi(2))
            .sum::<f64>()
            / (scores.len() - 1) as f64;

        variance.sqrt()
    }

    /// Sigmoid函数
    fn sigmoid(&self, x: f64) -> f64 {
        1.0 / (1.0 + (-x).exp())
    }

    /// 计算向量质量得分
    fn calculate_vector_quality_score(&self, vector: &[f32]) -> f64 {
        if vector.is_empty() {
            return 0.0;
        }

        // 1. 计算向量的L2范数
        let norm = vector
            .iter()
            .map(|x| (*x as f64).powi(2))
            .sum::<f64>()
            .sqrt();
        if norm == 0.0 {
            return 0.0;
        }

        // 2. 计算向量的稀疏性 (非零元素比例)
        let non_zero_count = vector.iter().filter(|&&x| x.abs() > 1e-6).count();
        let density = non_zero_count as f64 / vector.len() as f64;

        // 3. 计算向量的方差 (分布的均匀性)
        let mean = vector.iter().map(|&x| x as f64).sum::<f64>() / vector.len() as f64;
        let variance = vector
            .iter()
            .map(|&x| (x as f64 - mean).powi(2))
            .sum::<f64>()
            / vector.len() as f64;
        let std_dev = variance.sqrt();

        // 综合质量得分：标准化的范数 + 密度 + 标准差
        let norm_score = (norm / (1.0 + norm)).clamp(0.0, 1.0);
        let density_score = density.clamp(0.0, 1.0);
        let variance_score = (std_dev / (1.0 + std_dev)).clamp(0.0, 1.0);

        (norm_score * 0.4 + density_score * 0.3 + variance_score * 0.3).clamp(0.0, 1.0)
    }

    /// 获取模型权重
    fn get_model_weight(&self, model_name: &str) -> f64 {
        // 根据模型名称返回权重，这里可以配置不同模型的可靠性
        match model_name {
            name if name.contains("gpt") => 1.1,
            name if name.contains("bert") => 1.05,
            name if name.contains("sentence-transformers") => 1.0,
            _ => 0.95,
        }
    }

    /// 构建结果元数据
    fn build_result_metadata(
        &self,
        results: &[VectorSearchResult],
    ) -> HashMap<String, serde_json::Value> {
        let mut metadata = HashMap::new();

        if !results.is_empty() {
            let scores: Vec<f64> = results.iter().map(|r| r.score).collect();
            let max_score = scores.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
            let min_score = scores.iter().fold(f64::INFINITY, |a, &b| a.min(b));
            let avg_score = scores.iter().sum::<f64>() / scores.len() as f64;

            metadata.insert(
                "max_score".to_string(),
                serde_json::Value::Number(
                    serde_json::Number::from_f64(max_score).unwrap_or(serde_json::Number::from(0)),
                ),
            );
            metadata.insert(
                "min_score".to_string(),
                serde_json::Value::Number(
                    serde_json::Number::from_f64(min_score).unwrap_or(serde_json::Number::from(0)),
                ),
            );
            metadata.insert(
                "avg_score".to_string(),
                serde_json::Value::Number(
                    serde_json::Number::from_f64(avg_score).unwrap_or(serde_json::Number::from(0)),
                ),
            );
            metadata.insert(
                "result_count".to_string(),
                serde_json::Value::Number(serde_json::Number::from(results.len())),
            );
        }

        metadata
    }

    /// 生成缓存键
    fn generate_cache_key(
        &self,
        search_type: &str,
        request: &VectorSearchRequest,
    ) -> Result<String> {
        let key_data = serde_json::json!({
            "type": search_type,
            "vector_len": request.query_vector.as_ref().map(|v| v.len()),
            "text": request.query_text,
            "model_id": request.model_id,
            "threshold": request.similarity_threshold,
            "limit": request.limit,
            "kb_id": request.knowledge_base_id,
            "model_filter": request.model_name_filter,
            "embedding_type": request.embedding_type_filter,
        });

        Ok(format!(
            "search:{}",
            hex::encode(Sha256::digest(key_data.to_string()))
        ))
    }

    /// 生成混合搜索缓存键
    fn generate_hybrid_cache_key(&self, request: &HybridSearchRequest) -> Result<String> {
        let key_data = serde_json::json!({
            "type": "hybrid",
            "vector_len": request.query_vector.as_ref().map(|v| v.len()),
            "text": request.query_text,
            "model_id": request.model_id,
            "vector_weight": request.vector_weight,
            "text_weight": request.text_weight,
            "threshold": request.similarity_threshold,
            "limit": request.limit,
            "kb_id": request.knowledge_base_id,
        });

        Ok(format!(
            "search:{}",
            hex::encode(Sha256::digest(key_data.to_string()))
        ))
    }

    /// 更新搜索统计信息
    async fn update_search_stats(&self, search_time_ms: u64, is_vector_search: bool) {
        let mut stats = self.statistics.write().await;
        stats.total_searches += 1;

        if is_vector_search {
            stats.vector_searches += 1;
        }

        // 更新平均搜索时间
        let total_time =
            stats.avg_search_time_ms * (stats.total_searches - 1) as f64 + search_time_ms as f64;
        stats.avg_search_time_ms = total_time / stats.total_searches as f64;
    }

    /// 更新混合搜索统计信息
    async fn update_hybrid_search_stats(&self) {
        let mut stats = self.statistics.write().await;
        stats.hybrid_searches += 1;
    }

    /// 更新缓存命中统计信息
    async fn update_cache_hit_stats(&self) {
        let mut stats = self.statistics.write().await;
        stats.cache_hits += 1;
    }

    /// 更新缓存未命中统计信息
    async fn update_cache_miss_stats(&self) {
        let mut stats = self.statistics.write().await;
        stats.cache_misses += 1;
    }

    /// 获取缓存统计信息
    pub async fn get_cache_statistics(&self) -> CacheStatistics {
        self.cache_manager.get_cache_stats().await
    }

    /// 获取任务统计信息
    pub async fn get_task_statistics(&self) -> TaskStatistics {
        self.task_manager.get_task_stats().await
    }

    /// 预热缓存 - 为热门查询预计算结果
    pub async fn warmup_cache(&self, popular_queries: Vec<String>) -> Result<usize> {
        let mut warmed_count = 0;

        for query in popular_queries {
            // 检查是否为热门查询
            if self.cache_manager.is_popular_query(&query).await {
                // 创建预热搜索请求
                let warmup_request = VectorSearchRequest {
                    query_vector: None,
                    query_text: Some(query.clone()),
                    model_id: None,
                    similarity_threshold: None,
                    limit: Some(10), // 预热使用较小的限制
                    knowledge_base_id: None,
                    model_name_filter: None,
                    embedding_type_filter: None,
                };

                // 执行搜索并缓存结果
                if let Ok(_) = self.execute_vector_similarity_search(warmup_request).await {
                    warmed_count += 1;
                    tracing::debug!("预热缓存完成，查询: {}", query);
                }
            }
        }

        tracing::info!("缓存预热完成，预热了 {} 个查询", warmed_count);
        Ok(warmed_count)
    }

    /// 批量向量搜索 - 异步并发处理多个搜索请求
    pub async fn batch_vector_search(
        self: Arc<Self>,
        requests: Vec<VectorSearchRequest>,
    ) -> Result<Vec<Result<VectorSearchResults>>> {
        if requests.is_empty() {
            return Ok(Vec::new());
        }

        // 限制并发数量
        let max_concurrent = self.config.max_concurrent_searches.unwrap_or(10);
        let semaphore = Arc::new(tokio::sync::Semaphore::new(max_concurrent));

        let tasks: Vec<_> = requests
            .into_iter()
            .map(|request| {
                let service = Arc::clone(&self);
                let sem = Arc::clone(&semaphore);

                tokio::spawn(async move {
                    let _permit = sem.acquire().await.unwrap();
                    service.vector_similarity_search(request).await
                })
            })
            .collect();

        // 等待所有任务完成
        let results = futures::future::join_all(tasks).await;

        // 处理结果
        let final_results: Vec<Result<VectorSearchResults>> = results
            .into_iter()
            .map(|task_result| match task_result {
                Ok(search_result) => search_result,
                Err(join_error) => Err(anyhow::anyhow!("批量搜索任务失败: {}", join_error)),
            })
            .collect();

        Ok(final_results)
    }

    /// 异步搜索建议 - 基于查询历史提供搜索建议
    pub async fn get_search_suggestions(&self, query_prefix: &str, limit: usize) -> Vec<String> {
        // 这里应该基于历史查询数据提供建议
        // 简化实现，返回一些示例建议
        let suggestions = vec![
            format!("{}相关文档", query_prefix),
            format!("{}技术文档", query_prefix),
            format!("{}使用指南", query_prefix),
            format!("{}最佳实践", query_prefix),
            format!("{}常见问题", query_prefix),
        ];

        suggestions.into_iter().take(limit).collect()
    }

    /// 性能监控 - 获取详细的性能指标
    pub async fn get_performance_metrics(&self) -> PerformanceMetrics {
        let search_stats = self.get_search_statistics().await;
        let cache_stats = self.cache_manager.get_cache_stats().await;
        let task_stats = self.task_manager.get_task_stats().await;

        PerformanceMetrics {
            search_statistics: search_stats,
            cache_statistics: cache_stats,
            task_statistics: task_stats,
            uptime_seconds: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }

    /// 清理和优化 - 清理过期缓存并优化内存使用
    pub async fn cleanup_and_optimize(&self) -> Result<()> {
        // 手动触发缓存清理
        tracing::info!("开始清理和优化向量搜索服务");

        // 获取清理前的统计信息
        let before_stats = self.cache_manager.get_cache_stats().await;
        tracing::info!("清理前缓存统计: {:?}", before_stats);

        // 这里可以添加更多的清理逻辑
        // 例如清理长时间未使用的缓存项、压缩数据结构等

        let after_stats = self.cache_manager.get_cache_stats().await;
        tracing::info!("清理后缓存统计: {:?}", after_stats);

        Ok(())
    }

    // ===== 查询扩展和多模型集成方法 =====

    /// 带查询扩展的向量搜索
    pub async fn vector_search_with_expansion(
        &self,
        request: VectorSearchRequest,
    ) -> Result<VectorSearchResults> {
        let start_time = std::time::Instant::now();

        // 1. 查询扩展
        let expansion_result = if let Some(ref query_text) = request.query_text {
            Some(
                self.recall_precision_optimizer
                    .expand_query(query_text)
                    .await?,
            )
        } else {
            None
        };

        // 2. 构建扩展后的搜索请求
        let mut expanded_request = request.clone();
        if let Some(ref expansion) = expansion_result {
            // 将扩展词融入查询文本
            let expanded_query =
                self.build_expanded_query(&request.query_text.unwrap_or_default(), expansion);
            expanded_request.query_text = Some(expanded_query);
        }

        // 3. 执行搜索
        let mut results = self.vector_similarity_search(expanded_request).await?;

        // 4. 添加查询扩展元数据
        if let Some(expansion) = expansion_result {
            results.metadata.insert(
                "query_expansion".to_string(),
                serde_json::json!({
                    "original_query": expansion.original_query,
                    "expanded_terms": expansion.expanded_terms.len(),
                    "expansion_strategies": expansion.expansion_strategies,
                }),
            );
        }

        results.search_strategy = "vector_search_with_expansion".to_string();
        tracing::debug!(
            "带查询扩展的向量搜索完成，用时 {}ms",
            start_time.elapsed().as_millis()
        );

        Ok(results)
    }

    /// 多模型向量搜索
    pub async fn multi_model_vector_search(
        &self,
        request: VectorSearchRequest,
    ) -> Result<MultiModelSearchResult> {
        let start_time = std::time::Instant::now();

        // 使用召回率和准确率优化器进行多模型搜索
        let mut multi_model_result = self
            .recall_precision_optimizer
            .multi_model_search(self, request.clone())
            .await?;

        // 更新搜索时间
        multi_model_result.final_results.search_time_ms = start_time.elapsed().as_millis() as u64;

        tracing::debug!(
            "多模型向量搜索完成，用时 {}ms",
            start_time.elapsed().as_millis()
        );

        Ok(multi_model_result)
    }

    /// 智能混合搜索（结合查询扩展和多模型）
    pub async fn intelligent_hybrid_search(
        &self,
        request: HybridSearchRequest,
    ) -> Result<MultiModelSearchResult> {
        let start_time = std::time::Instant::now();

        // 1. 查询扩展
        let expansion_result = self
            .recall_precision_optimizer
            .expand_query(&request.query_text)
            .await?;

        // 2. 构建扩展后的查询
        let expanded_query = self.build_expanded_query(&request.query_text, &expansion_result);

        // 3. 转换为向量搜索请求
        let vector_request = VectorSearchRequest {
            query_vector: request.query_vector,
            query_text: Some(expanded_query),
            model_id: request.model_id,
            similarity_threshold: request.similarity_threshold,
            limit: request.limit,
            knowledge_base_id: request.knowledge_base_id,
            model_name_filter: None,
            embedding_type_filter: None,
        };

        // 4. 多模型搜索
        let mut multi_model_result = self
            .recall_precision_optimizer
            .multi_model_search(self, vector_request)
            .await?;

        // 5. 添加智能搜索元数据
        multi_model_result.final_results.metadata.insert(
            "intelligent_search".to_string(),
            serde_json::json!({
                "query_expansion": {
                    "original_query": expansion_result.original_query,
                    "expanded_terms": expansion_result.expanded_terms.len(),
                    "strategies": expansion_result.expansion_strategies,
                },
                "multi_model": {
                    "models_used": multi_model_result.individual_results.len(),
                    "fusion_strategy": format!("{:?}", multi_model_result.fusion_strategy),
                    "consensus_scores": multi_model_result.consensus_scores.clone(),
                }
            }),
        );

        multi_model_result.final_results.search_strategy = "intelligent_hybrid_search".to_string();
        multi_model_result.final_results.search_time_ms = start_time.elapsed().as_millis() as u64;

        tracing::info!(
            "智能混合搜索完成，用时 {}ms，使用了 {} 个模型",
            start_time.elapsed().as_millis(),
            multi_model_result.individual_results.len()
        );

        Ok(multi_model_result)
    }

    /// 构建扩展后的查询
    fn build_expanded_query(
        &self,
        original_query: &str,
        expansion: &QueryExpansionResult,
    ) -> String {
        let mut expanded_query = original_query.to_string();

        // 根据权重添加扩展词
        for term in &expansion.expanded_terms {
            // 简化实现：直接添加高权重的扩展词
            if term.weight > 0.7 {
                expanded_query.push(' ');
                expanded_query.push_str(&term.term);
            }
        }

        expanded_query
    }

    /// 获取查询扩展建议
    pub async fn get_query_expansion_suggestions(
        &self,
        query: &str,
    ) -> Result<QueryExpansionResult> {
        self.recall_precision_optimizer.expand_query(query).await
    }

    /// 获取最佳模型推荐
    pub async fn get_best_model_recommendation(&self) -> Option<String> {
        self.recall_precision_optimizer
            .get_best_model_recommendation()
            .await
    }

    /// 更新模型性能反馈
    pub async fn update_model_feedback(
        &self,
        model_name: &str,
        response_time_ms: u64,
        relevance_score: f64,
    ) {
        self.recall_precision_optimizer
            .update_model_performance(model_name, response_time_ms, relevance_score)
            .await;
    }

    /// 获取模型性能报告
    pub async fn get_model_performance_report(&self) -> HashMap<String, ModelPerformanceStats> {
        self.recall_precision_optimizer
            .get_performance_report()
            .await
    }

    /// 配置查询扩展
    pub async fn configure_query_expansion(&mut self, config: QueryExpansionConfig) -> Result<()> {
        // 这里应该更新优化器的配置，简化实现暂时只记录日志
        tracing::info!(
            "查询扩展配置已更新: enabled={}, max_terms={}",
            config.enabled,
            config.max_expansion_terms
        );
        Ok(())
    }

    /// 配置多模型集成
    pub async fn configure_multi_model(&mut self, config: MultiModelConfig) -> Result<()> {
        // 这里应该更新优化器的配置，简化实现暂时只记录日志
        tracing::info!(
            "多模型配置已更新: enabled={}, fusion_strategy={:?}",
            config.enabled,
            config.fusion_strategy
        );
        Ok(())
    }

    /// 分析查询质量
    pub async fn analyze_query_quality(&self, query: &str) -> Result<QueryQualityAnalysis> {
        let analysis = QueryQualityAnalysis {
            query: query.to_string(),
            length_score: self.calculate_length_score(query),
            specificity_score: self.calculate_specificity_score(query),
            semantic_richness_score: self.calculate_semantic_richness_score(query).await,
            expansion_potential: self.calculate_expansion_potential(query).await,
            recommendations: self.generate_query_recommendations(query).await,
        };

        Ok(analysis)
    }

    /// 计算查询长度得分
    fn calculate_length_score(&self, query: &str) -> f64 {
        let char_count = query.chars().count();
        match char_count {
            0..=5 => 0.3,   // 太短
            6..=20 => 1.0,  // 理想长度
            21..=50 => 0.8, // 稍长
            _ => 0.5,       // 太长
        }
    }

    /// 计算查询特异性得分
    fn calculate_specificity_score(&self, query: &str) -> f64 {
        // 简化实现：基于特定关键词的存在
        let specific_keywords = vec!["如何", "什么", "为什么", "在哪里", "何时"];
        let general_keywords = vec!["的", "是", "有", "了", "在"];

        let specific_count = specific_keywords
            .iter()
            .filter(|&keyword| query.contains(keyword))
            .count();
        let general_count = general_keywords
            .iter()
            .filter(|&keyword| query.contains(keyword))
            .count();

        if specific_count > 0 {
            0.8 + (specific_count as f64 * 0.1)
        } else if general_count > 2 {
            0.3
        } else {
            0.6
        }
    }

    /// 计算语义丰富度得分
    async fn calculate_semantic_richness_score(&self, query: &str) -> f64 {
        // 简化实现：基于词汇多样性
        let words: Vec<&str> = query.split_whitespace().collect();
        let unique_words: std::collections::HashSet<_> = words.iter().collect();

        if words.is_empty() {
            return 0.0;
        }

        let diversity_ratio = unique_words.len() as f64 / words.len() as f64;
        diversity_ratio.min(1.0)
    }

    /// 计算扩展潜力
    async fn calculate_expansion_potential(&self, query: &str) -> f64 {
        // 尝试进行查询扩展并评估扩展效果
        match self.recall_precision_optimizer.expand_query(query).await {
            Ok(expansion) => {
                let expansion_ratio = expansion.expanded_terms.len() as f64
                    / query.split_whitespace().count().max(1) as f64;
                expansion_ratio.min(2.0) / 2.0 // 标准化到0-1范围
            }
            Err(_) => 0.0,
        }
    }

    /// 生成查询建议
    async fn generate_query_recommendations(&self, query: &str) -> Vec<String> {
        let mut recommendations = vec![];

        // 基于查询长度的建议
        let char_count = query.chars().count();
        if char_count < 6 {
            recommendations.push("建议增加更多描述性词汇以提高搜索精度".to_string());
        } else if char_count > 50 {
            recommendations.push("建议简化查询，去除冗余词汇".to_string());
        }

        // 基于特异性的建议
        if !query.contains("如何") && !query.contains("什么") && !query.contains("为什么") {
            recommendations.push("考虑添加疑问词（如何、什么、为什么）以明确搜索意图".to_string());
        }

        // 基于扩展潜力的建议
        if let Ok(expansion) = self.recall_precision_optimizer.expand_query(query).await {
            if expansion.expanded_terms.is_empty() {
                recommendations.push("查询词较为独特，可能需要使用近义词或相关概念".to_string());
            } else if expansion.expanded_terms.len() > 5 {
                recommendations
                    .push("查询具有丰富的语义扩展空间，建议启用查询扩展功能".to_string());
            }
        }

        recommendations
    }
}

/// 查询质量分析结果
#[derive(Debug, Clone)]
pub struct QueryQualityAnalysis {
    /// 查询文本
    pub query: String,
    /// 长度得分 (0-1)
    pub length_score: f64,
    /// 特异性得分 (0-1)
    pub specificity_score: f64,
    /// 语义丰富度得分 (0-1)
    pub semantic_richness_score: f64,
    /// 扩展潜力得分 (0-1)
    pub expansion_potential: f64,
    /// 优化建议
    pub recommendations: Vec<String>,
}

impl QueryQualityAnalysis {
    /// 计算综合质量得分
    pub fn overall_score(&self) -> f64 {
        (self.length_score * 0.2
            + self.specificity_score * 0.3
            + self.semantic_richness_score * 0.3
            + self.expansion_potential * 0.2)
            .min(1.0)
    }

    /// 获取质量等级
    pub fn quality_level(&self) -> QueryQualityLevel {
        let score = self.overall_score();
        match score {
            s if s >= 0.8 => QueryQualityLevel::Excellent,
            s if s >= 0.6 => QueryQualityLevel::Good,
            s if s >= 0.4 => QueryQualityLevel::Fair,
            _ => QueryQualityLevel::Poor,
        }
    }
}

/// 查询质量等级
#[derive(Debug, Clone, PartialEq)]
pub enum QueryQualityLevel {
    /// 优秀
    Excellent,
    /// 良好
    Good,
    /// 一般
    Fair,
    /// 较差
    Poor,
}

/// 性能指标聚合
#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    pub search_statistics: SearchStatistics,
    pub cache_statistics: CacheStatistics,
    pub task_statistics: TaskStatistics,
    pub uptime_seconds: u64,
}

/// 查询扩展配置
#[derive(Debug, Clone)]
pub struct QueryExpansionConfig {
    /// 是否启用查询扩展
    pub enabled: bool,
    /// 同义词扩展权重
    pub synonym_weight: f64,
    /// 语义相关词扩展权重
    pub semantic_weight: f64,
    /// 上下文扩展权重
    pub context_weight: f64,
    /// 最大扩展词数量
    pub max_expansion_terms: usize,
    /// 扩展词最小相似度阈值
    pub min_expansion_similarity: f64,
}

impl Default for QueryExpansionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            synonym_weight: 0.8,
            semantic_weight: 0.6,
            context_weight: 0.4,
            max_expansion_terms: 5,
            min_expansion_similarity: 0.7,
        }
    }
}

/// 多模型集成配置
#[derive(Debug, Clone)]
pub struct MultiModelConfig {
    /// 是否启用多模型集成
    pub enabled: bool,
    /// 主模型权重
    pub primary_model_weight: f64,
    /// 辅助模型权重列表
    pub auxiliary_model_weights: Vec<(String, f64)>,
    /// 结果融合策略
    pub fusion_strategy: ResultFusionStrategy,
    /// 最小模型一致性分数
    pub min_consensus_score: f64,
}

impl Default for MultiModelConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            primary_model_weight: 0.7,
            auxiliary_model_weights: vec![],
            fusion_strategy: ResultFusionStrategy::WeightedAverage,
            min_consensus_score: 0.6,
        }
    }
}

/// 结果融合策略
#[derive(Debug, Clone, PartialEq)]
pub enum ResultFusionStrategy {
    /// 加权平均
    WeightedAverage,
    /// 最大值
    Maximum,
    /// 线性组合
    LinearCombination,
    /// 投票机制
    Voting,
    /// 排序融合
    RankFusion,
}

/// 查询扩展结果
#[derive(Debug, Clone)]
pub struct QueryExpansionResult {
    /// 原始查询
    pub original_query: String,
    /// 扩展后的查询词
    pub expanded_terms: Vec<ExpansionTerm>,
    /// 扩展后的查询向量
    pub expanded_vector: Option<Vec<f32>>,
    /// 扩展策略使用情况
    pub expansion_strategies: Vec<String>,
}

/// 扩展词项
#[derive(Debug, Clone)]
pub struct ExpansionTerm {
    /// 扩展词
    pub term: String,
    /// 与原查询的相似度
    pub similarity: f64,
    /// 扩展类型
    pub expansion_type: ExpansionType,
    /// 权重
    pub weight: f64,
}

/// 扩展类型
#[derive(Debug, Clone, PartialEq)]
pub enum ExpansionType {
    /// 同义词
    Synonym,
    /// 语义相关
    Semantic,
    /// 上下文相关
    Context,
    /// 词根变化
    Morphological,
}

/// 多模型搜索结果
#[derive(Debug, Clone)]
pub struct MultiModelSearchResult {
    /// 融合后的最终结果
    pub final_results: VectorSearchResults,
    /// 各模型的单独结果
    pub individual_results: Vec<(String, VectorSearchResults)>,
    /// 模型一致性分数
    pub consensus_scores: Vec<f64>,
    /// 使用的融合策略
    pub fusion_strategy: ResultFusionStrategy,
}

/// 召回率和准确率优化器
#[derive(Debug)]
pub struct RecallPrecisionOptimizer {
    /// 查询扩展配置
    query_expansion_config: QueryExpansionConfig,
    /// 多模型配置
    multi_model_config: MultiModelConfig,
    /// 扩展词典缓存
    expansion_cache: Arc<TokioRwLock<HashMap<String, QueryExpansionResult>>>,
    /// 模型性能统计
    model_performance_stats: Arc<TokioRwLock<HashMap<String, ModelPerformanceStats>>>,
}

/// 模型性能统计
#[derive(Debug, Clone)]
pub struct ModelPerformanceStats {
    /// 模型名称
    pub model_name: String,
    /// 查询总数
    pub total_queries: u64,
    /// 平均响应时间
    pub avg_response_time_ms: f64,
    /// 平均相关性分数
    pub avg_relevance_score: f64,
    /// 用户反馈分数
    pub user_feedback_score: f64,
    /// 准确率估算
    pub estimated_precision: f64,
    /// 召回率估算
    pub estimated_recall: f64,
}

impl Default for ModelPerformanceStats {
    fn default() -> Self {
        Self {
            model_name: String::new(),
            total_queries: 0,
            avg_response_time_ms: 0.0,
            avg_relevance_score: 0.0,
            user_feedback_score: 0.0,
            estimated_precision: 0.0,
            estimated_recall: 0.0,
        }
    }
}

impl RecallPrecisionOptimizer {
    /// 创建新的召回率和准确率优化器
    pub fn new(
        query_expansion_config: QueryExpansionConfig,
        multi_model_config: MultiModelConfig,
    ) -> Self {
        Self {
            query_expansion_config,
            multi_model_config,
            expansion_cache: Arc::new(TokioRwLock::new(HashMap::new())),
            model_performance_stats: Arc::new(TokioRwLock::new(HashMap::new())),
        }
    }

    /// 查询扩展
    pub async fn expand_query(&self, query: &str) -> Result<QueryExpansionResult> {
        if !self.query_expansion_config.enabled {
            return Ok(QueryExpansionResult {
                original_query: query.to_string(),
                expanded_terms: vec![],
                expanded_vector: None,
                expansion_strategies: vec![],
            });
        }

        // 检查缓存
        let cache_key = format!("expansion_{}", query);
        {
            let cache = self.expansion_cache.read().await;
            if let Some(cached_result) = cache.get(&cache_key) {
                return Ok(cached_result.clone());
            }
        }

        let mut expansion_result = QueryExpansionResult {
            original_query: query.to_string(),
            expanded_terms: vec![],
            expanded_vector: None,
            expansion_strategies: vec![],
        };

        // 1. 同义词扩展
        if let Ok(synonym_terms) = self.expand_with_synonyms(query).await {
            expansion_result.expanded_terms.extend(synonym_terms);
            expansion_result
                .expansion_strategies
                .push("synonyms".to_string());
        }

        // 2. 语义相关词扩展
        if let Ok(semantic_terms) = self.expand_with_semantics(query).await {
            expansion_result.expanded_terms.extend(semantic_terms);
            expansion_result
                .expansion_strategies
                .push("semantics".to_string());
        }

        // 3. 上下文扩展
        if let Ok(context_terms) = self.expand_with_context(query).await {
            expansion_result.expanded_terms.extend(context_terms);
            expansion_result
                .expansion_strategies
                .push("context".to_string());
        }

        // 4. 词根变化扩展
        if let Ok(morphological_terms) = self.expand_with_morphology(query).await {
            expansion_result.expanded_terms.extend(morphological_terms);
            expansion_result
                .expansion_strategies
                .push("morphology".to_string());
        }

        // 5. 限制扩展词数量
        expansion_result.expanded_terms.sort_by(|a, b| {
            b.similarity
                .partial_cmp(&a.similarity)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        expansion_result
            .expanded_terms
            .truncate(self.query_expansion_config.max_expansion_terms);

        // 6. 过滤低相似度的扩展词
        expansion_result
            .expanded_terms
            .retain(|term| term.similarity >= self.query_expansion_config.min_expansion_similarity);

        // 缓存结果
        {
            let mut cache = self.expansion_cache.write().await;
            cache.insert(cache_key, expansion_result.clone());
        }

        tracing::debug!(
            "查询扩展完成: {} -> {} 个扩展词",
            query,
            expansion_result.expanded_terms.len()
        );
        Ok(expansion_result)
    }

    /// 同义词扩展
    async fn expand_with_synonyms(&self, query: &str) -> Result<Vec<ExpansionTerm>> {
        // 这里应该集成真正的同义词词典，比如WordNet或自定义词典
        // 简化实现：基于常见同义词规则
        let mut synonyms = vec![];

        let common_synonyms = vec![
            ("文档", vec!["文件", "资料", "材料"]),
            ("搜索", vec!["查找", "检索", "寻找"]),
            ("系统", vec!["平台", "架构", "框架"]),
            ("用户", vec!["使用者", "客户", "操作员"]),
            ("数据", vec!["信息", "资料", "内容"]),
        ];

        for (original, synonym_list) in common_synonyms {
            if query.contains(original) {
                for synonym in synonym_list {
                    synonyms.push(ExpansionTerm {
                        term: synonym.to_string(),
                        similarity: 0.9, // 同义词相似度较高
                        expansion_type: ExpansionType::Synonym,
                        weight: self.query_expansion_config.synonym_weight,
                    });
                }
            }
        }

        Ok(synonyms)
    }

    /// 语义相关词扩展
    async fn expand_with_semantics(&self, query: &str) -> Result<Vec<ExpansionTerm>> {
        // 这里应该使用词向量模型来找到语义相关的词汇
        // 简化实现：基于领域相关词汇
        let mut semantic_terms = vec![];

        let domain_terms = vec![
            ("技术", vec!["开发", "编程", "代码", "软件"]),
            ("管理", vec!["组织", "规划", "流程", "策略"]),
            ("分析", vec!["统计", "报告", "评估", "监控"]),
            ("设计", vec!["架构", "模式", "结构", "布局"]),
        ];

        for (domain, related_terms) in domain_terms {
            if query.contains(domain) {
                for term in related_terms {
                    semantic_terms.push(ExpansionTerm {
                        term: term.to_string(),
                        similarity: 0.75,
                        expansion_type: ExpansionType::Semantic,
                        weight: self.query_expansion_config.semantic_weight,
                    });
                }
            }
        }

        Ok(semantic_terms)
    }

    /// 上下文扩展
    async fn expand_with_context(&self, query: &str) -> Result<Vec<ExpansionTerm>> {
        // 这里应该基于历史查询和文档内容来推断上下文相关词汇
        // 简化实现：基于查询模式
        let mut context_terms = vec![];

        if query.contains("如何") || query.contains("怎么") {
            context_terms.extend(vec![
                ExpansionTerm {
                    term: "教程".to_string(),
                    similarity: 0.8,
                    expansion_type: ExpansionType::Context,
                    weight: self.query_expansion_config.context_weight,
                },
                ExpansionTerm {
                    term: "指南".to_string(),
                    similarity: 0.75,
                    expansion_type: ExpansionType::Context,
                    weight: self.query_expansion_config.context_weight,
                },
            ]);
        }

        if query.contains("问题") {
            context_terms.push(ExpansionTerm {
                term: "故障".to_string(),
                similarity: 0.7,
                expansion_type: ExpansionType::Context,
                weight: self.query_expansion_config.context_weight,
            });
        }

        Ok(context_terms)
    }

    /// 词根变化扩展
    async fn expand_with_morphology(&self, _query: &str) -> Result<Vec<ExpansionTerm>> {
        // 这里应该实现中文词根变化处理
        // 简化实现：暂时返回空列表
        Ok(vec![])
    }

    /// 多模型搜索
    pub async fn multi_model_search(
        &self,
        primary_service: &VectorSearchService,
        request: VectorSearchRequest,
    ) -> Result<MultiModelSearchResult> {
        if !self.multi_model_config.enabled {
            // 如果未启用多模型，直接使用主模型
            let primary_result = primary_service.vector_similarity_search(request).await?;
            return Ok(MultiModelSearchResult {
                final_results: primary_result.clone(),
                individual_results: vec![("primary".to_string(), primary_result)],
                consensus_scores: vec![1.0],
                fusion_strategy: self.multi_model_config.fusion_strategy.clone(),
            });
        }

        let mut individual_results = vec![];

        // 1. 主模型搜索
        let primary_result = primary_service
            .vector_similarity_search(request.clone())
            .await?;
        individual_results.push(("primary".to_string(), primary_result.clone()));

        // 2. 辅助模型搜索（简化实现：使用相同服务但不同参数）
        for (model_name, weight) in &self.multi_model_config.auxiliary_model_weights {
            // 这里应该使用不同的模型，简化实现使用不同的相似度阈值
            let mut aux_request = request.clone();
            aux_request.similarity_threshold = aux_request.similarity_threshold.map(|t| t * weight);

            if let Ok(aux_result) = primary_service.vector_similarity_search(aux_request).await {
                individual_results.push((model_name.clone(), aux_result));
            }
        }

        // 3. 结果融合
        let final_results = self.fuse_results(&individual_results).await?;

        // 4. 计算一致性分数
        let consensus_scores = self.calculate_consensus_scores(&individual_results).await;

        Ok(MultiModelSearchResult {
            final_results,
            individual_results,
            consensus_scores,
            fusion_strategy: self.multi_model_config.fusion_strategy.clone(),
        })
    }

    /// 融合多个模型的结果
    async fn fuse_results(
        &self,
        results: &[(String, VectorSearchResults)],
    ) -> Result<VectorSearchResults> {
        if results.is_empty() {
            return Err(anyhow::anyhow!("没有结果可以融合"));
        }

        match self.multi_model_config.fusion_strategy {
            ResultFusionStrategy::WeightedAverage => self.weighted_average_fusion(results).await,
            ResultFusionStrategy::Maximum => self.maximum_fusion(results).await,
            ResultFusionStrategy::LinearCombination => {
                self.linear_combination_fusion(results).await
            }
            ResultFusionStrategy::Voting => self.voting_fusion(results).await,
            ResultFusionStrategy::RankFusion => self.rank_fusion(results).await,
        }
    }

    /// 加权平均融合
    async fn weighted_average_fusion(
        &self,
        results: &[(String, VectorSearchResults)],
    ) -> Result<VectorSearchResults> {
        if results.is_empty() {
            return Err(anyhow::anyhow!("没有结果可以融合"));
        }

        // 使用第一个结果作为基础
        let mut fused_results = results[0].1.clone();

        // 简化实现：对分数进行加权平均
        for result_item in &mut fused_results.results {
            let mut weighted_score = 0.0;
            let mut total_weight = 0.0;

            // 主模型权重
            weighted_score += result_item.score * self.multi_model_config.primary_model_weight;
            total_weight += self.multi_model_config.primary_model_weight;

            // 辅助模型权重
            for (model_name, weight) in &self.multi_model_config.auxiliary_model_weights {
                if let Some((_, aux_results)) = results.iter().find(|(name, _)| name == model_name)
                {
                    // 查找对应的结果项
                    if let Some(aux_item) = aux_results
                        .results
                        .iter()
                        .find(|item| item.embedding.chunk_id == result_item.embedding.chunk_id)
                    {
                        weighted_score += aux_item.score * weight;
                        total_weight += weight;
                    }
                }
            }

            if total_weight > 0.0 {
                result_item.score = weighted_score / total_weight;
            }
        }

        // 重新排序
        fused_results.results.sort_by(|a, b| {
            b.score
                .partial_cmp(&a.score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        fused_results.search_strategy = "multi_model_weighted_average".to_string();

        Ok(fused_results)
    }

    /// 最大值融合
    async fn maximum_fusion(
        &self,
        results: &[(String, VectorSearchResults)],
    ) -> Result<VectorSearchResults> {
        if results.is_empty() {
            return Err(anyhow::anyhow!("没有结果可以融合"));
        }

        // 找到得分最高的结果
        let mut best_results = &results[0].1;
        let mut best_score = results[0].1.results.first().map(|r| r.score).unwrap_or(0.0);

        for (_, result) in results.iter().skip(1) {
            if let Some(first_result) = result.results.first() {
                if first_result.score > best_score {
                    best_results = result;
                    best_score = first_result.score;
                }
            }
        }

        let mut fused_results = best_results.clone();
        fused_results.search_strategy = "multi_model_maximum".to_string();
        Ok(fused_results)
    }

    /// 线性组合融合
    async fn linear_combination_fusion(
        &self,
        results: &[(String, VectorSearchResults)],
    ) -> Result<VectorSearchResults> {
        // 简化实现：与加权平均相同
        self.weighted_average_fusion(results).await
    }

    /// 投票机制融合
    async fn voting_fusion(
        &self,
        results: &[(String, VectorSearchResults)],
    ) -> Result<VectorSearchResults> {
        if results.is_empty() {
            return Err(anyhow::anyhow!("没有结果可以融合"));
        }

        // 统计每个文档在各模型中的出现次数
        let mut vote_counts: HashMap<String, u32> = HashMap::new();
        let mut all_results: HashMap<String, VectorSearchResult> = HashMap::new();

        for (_, result) in results {
            for result_item in &result.results {
                let chunk_id = result_item.embedding.chunk_id.clone();
                *vote_counts.entry(chunk_id.clone()).or_insert(0) += 1;
                all_results.insert(chunk_id, result_item.clone());
            }
        }

        // 按投票数排序
        let mut voted_results: Vec<_> = vote_counts.into_iter().collect();
        voted_results.sort_by(|a, b| b.1.cmp(&a.1));

        // 构建最终结果
        let final_results: Vec<VectorSearchResult> = voted_results
            .into_iter()
            .filter_map(|(chunk_id, votes)| {
                all_results.get(&chunk_id).map(|result| {
                    let mut voted_result = result.clone();
                    voted_result.score = votes as f64; // 使用投票数作为分数
                    voted_result
                })
            })
            .take(20) // 限制结果数量
            .collect();

        let mut fused_results = results[0].1.clone();
        fused_results.results = final_results;
        fused_results.search_strategy = "multi_model_voting".to_string();

        Ok(fused_results)
    }

    /// 排序融合
    async fn rank_fusion(
        &self,
        results: &[(String, VectorSearchResults)],
    ) -> Result<VectorSearchResults> {
        if results.is_empty() {
            return Err(anyhow::anyhow!("没有结果可以融合"));
        }

        // RRF (Reciprocal Rank Fusion) 算法
        let mut rank_scores: HashMap<String, f64> = HashMap::new();
        let mut all_results: HashMap<String, VectorSearchResult> = HashMap::new();

        for (_, result) in results {
            for (rank, result_item) in result.results.iter().enumerate() {
                let chunk_id = result_item.embedding.chunk_id.clone();
                let rrf_score = 1.0 / (60.0 + rank as f64 + 1.0); // k=60 是RRF的常用参数
                *rank_scores.entry(chunk_id.clone()).or_insert(0.0) += rrf_score;
                all_results.insert(chunk_id, result_item.clone());
            }
        }

        // 按RRF分数排序
        let mut ranked_results: Vec<_> = rank_scores.into_iter().collect();
        ranked_results.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        // 构建最终结果
        let final_results: Vec<VectorSearchResult> = ranked_results
            .into_iter()
            .filter_map(|(chunk_id, rrf_score)| {
                all_results.get(&chunk_id).map(|result| {
                    let mut ranked_result = result.clone();
                    ranked_result.score = rrf_score;
                    ranked_result
                })
            })
            .take(20) // 限制结果数量
            .collect();

        let mut fused_results = results[0].1.clone();
        fused_results.results = final_results;
        fused_results.search_strategy = "multi_model_rank_fusion".to_string();

        Ok(fused_results)
    }

    /// 计算一致性分数
    async fn calculate_consensus_scores(
        &self,
        results: &[(String, VectorSearchResults)],
    ) -> Vec<f64> {
        if results.len() < 2 {
            return vec![1.0];
        }

        let mut consensus_scores = vec![];

        for i in 0..results.len() {
            let mut total_similarity = 0.0;
            let mut comparisons = 0;

            for j in 0..results.len() {
                if i != j {
                    let similarity = self
                        .calculate_result_similarity(&results[i].1, &results[j].1)
                        .await;
                    total_similarity += similarity;
                    comparisons += 1;
                }
            }

            let consensus = if comparisons > 0 {
                total_similarity / comparisons as f64
            } else {
                1.0
            };

            consensus_scores.push(consensus);
        }

        consensus_scores
    }

    /// 计算两个搜索结果的相似度
    async fn calculate_result_similarity(
        &self,
        result1: &VectorSearchResults,
        result2: &VectorSearchResults,
    ) -> f64 {
        if result1.results.is_empty() || result2.results.is_empty() {
            return 0.0;
        }

        // 计算前10个结果的重叠度
        let top_n = 10.min(result1.results.len()).min(result2.results.len());
        let result1_ids: std::collections::HashSet<_> = result1
            .results
            .iter()
            .take(top_n)
            .map(|r| r.embedding.chunk_id.clone())
            .collect();
        let result2_ids: std::collections::HashSet<_> = result2
            .results
            .iter()
            .take(top_n)
            .map(|r| r.embedding.chunk_id.clone())
            .collect();

        let intersection = result1_ids.intersection(&result2_ids).count();
        let union = result1_ids.union(&result2_ids).count();

        if union == 0 {
            0.0
        } else {
            intersection as f64 / union as f64
        }
    }

    /// 更新模型性能统计
    pub async fn update_model_performance(
        &self,
        model_name: &str,
        response_time_ms: u64,
        relevance_score: f64,
    ) {
        let mut stats = self.model_performance_stats.write().await;
        let model_stats = stats.entry(model_name.to_string()).or_default();

        model_stats.model_name = model_name.to_string();
        model_stats.total_queries += 1;

        // 计算移动平均
        let alpha = 0.1; // 平滑因子
        model_stats.avg_response_time_ms =
            model_stats.avg_response_time_ms * (1.0 - alpha) + response_time_ms as f64 * alpha;
        model_stats.avg_relevance_score =
            model_stats.avg_relevance_score * (1.0 - alpha) + relevance_score * alpha;
    }

    /// 获取最佳模型推荐
    pub async fn get_best_model_recommendation(&self) -> Option<String> {
        let stats = self.model_performance_stats.read().await;

        let mut best_model = None;
        let mut best_score = 0.0;

        for (model_name, model_stats) in stats.iter() {
            if model_stats.total_queries < 10 {
                continue; // 样本太少，不可靠
            }

            // 综合评分：相关性 * 0.6 + (1/响应时间) * 0.2 + 用户反馈 * 0.2
            let response_time_score = 1.0 / (1.0 + model_stats.avg_response_time_ms / 1000.0);
            let composite_score = model_stats.avg_relevance_score * 0.6
                + response_time_score * 0.2
                + model_stats.user_feedback_score * 0.2;

            if composite_score > best_score {
                best_score = composite_score;
                best_model = Some(model_name.clone());
            }
        }

        best_model
    }

    /// 获取性能统计报告
    pub async fn get_performance_report(&self) -> HashMap<String, ModelPerformanceStats> {
        self.model_performance_stats.read().await.clone()
    }
}
