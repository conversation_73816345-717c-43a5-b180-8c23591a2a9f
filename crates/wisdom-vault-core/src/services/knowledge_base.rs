use anyhow::{Result, anyhow};
use std::collections::HashMap;
use wisdom_vault_common::{db::next_id, time::current_millis};

use wisdom_vault_database::{
    CategoryRepository, KnowledgeBaseRepository, TagRepository, models::*,
    repositories::UserRepository,
};

pub struct KnowledgeBaseService<K, C, T, U>
where
    K: KnowledgeBaseRepository,
    C: CategoryRepository,
    T: TagRepository,
    U: UserRepository,
{
    kb_repository: K,
    category_repository: C,
    tag_repository: T,
    user_repository: U,
}

impl<K, C, T, U> KnowledgeBaseService<K, C, T, U>
where
    K: KnowledgeBaseRepository,
    C: CategoryRepository,
    T: TagRepository,
    U: UserRepository,
{
    pub fn new(
        kb_repository: K,
        category_repository: C,
        tag_repository: T,
        user_repository: U,
    ) -> Self {
        Self {
            kb_repository,
            category_repository,
            tag_repository,
            user_repository,
        }
    }

    // Knowledge Base Management
    pub async fn create_knowledge_base(
        &self,
        name: String,
        description: Option<String>,
        organization_id: String,
        owner_id: String,
        visibility: KnowledgeBaseVisibility,
        settings: Option<KnowledgeBaseSettings>,
    ) -> Result<KnowledgeBase> {
        // Validate name
        if name.trim().is_empty() {
            return Err(anyhow!("Knowledge base name cannot be empty"));
        }

        if name.len() > 100 {
            return Err(anyhow!("Knowledge base name cannot exceed 100 characters"));
        }

        // Use default settings if not provided
        let settings = settings.unwrap_or_else(|| KnowledgeBaseSettings {
            allow_anonymous_access: false,
            auto_categorize: true,
            enable_versioning: true,
            max_file_size: 50 * 1024 * 1024, // 50MB
            allowed_file_types: vec![
                "pdf".to_string(),
                "docx".to_string(),
                "doc".to_string(),
                "txt".to_string(),
                "md".to_string(),
                "html".to_string(),
            ],
            language: "zh-CN".to_string(),
        });

        let knowledge_base = KnowledgeBase {
            id: next_id(),
            organization_id,
            name: name.trim().to_string(),
            description: description
                .map(|d| d.trim().to_string())
                .filter(|d| !d.is_empty()),
            owner_id,
            visibility,
            settings,
            statistics: KnowledgeBaseStatistics {
                document_count: 0,
                total_size: 0,
                last_indexed_at: None,
                view_count: 0,
                search_count: 0,
            },
            created_at: current_millis(),
            updated_at: current_millis(),
        };

        self.kb_repository.create(&knowledge_base).await
    }

    pub async fn get_knowledge_base(&self, id: &str) -> Result<Option<KnowledgeBase>> {
        self.kb_repository.find_by_id(id).await
    }

    pub async fn update_knowledge_base(
        &self,
        id: &str,
        name: Option<String>,
        description: Option<String>,
        visibility: Option<KnowledgeBaseVisibility>,
        settings: Option<KnowledgeBaseSettings>,
        user_id: &str,
    ) -> Result<KnowledgeBase> {
        let mut kb = self
            .kb_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        // Check ownership permission
        if kb.owner_id != user_id {
            return Err(anyhow!(
                "Permission denied: Only the owner can update this knowledge base"
            ));
        }

        // Update fields if provided
        if let Some(name) = name {
            if name.trim().is_empty() {
                return Err(anyhow!("Knowledge base name cannot be empty"));
            }
            if name.len() > 100 {
                return Err(anyhow!("Knowledge base name cannot exceed 100 characters"));
            }
            kb.name = name.trim().to_string();
        }

        if let Some(description) = description {
            kb.description = Some(description.trim().to_string()).filter(|d| !d.is_empty());
        }

        if let Some(visibility) = visibility {
            kb.visibility = visibility;
        }

        if let Some(settings) = settings {
            // Validate settings
            if settings.max_file_size > 200 * 1024 * 1024 {
                // 200MB max
                return Err(anyhow!("Maximum file size cannot exceed 200MB"));
            }
            kb.settings = settings;
        }

        kb.updated_at = current_millis();

        self.kb_repository.update(&kb).await
    }

    pub async fn delete_knowledge_base(&self, id: &str, user_id: &str) -> Result<bool> {
        let kb = self
            .kb_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        // Check ownership permission
        if kb.owner_id != user_id {
            return Err(anyhow!(
                "Permission denied: Only the owner can delete this knowledge base"
            ));
        }

        // TODO: In a real implementation, we should also:
        // 1. Delete all documents in the knowledge base
        // 2. Delete all categories and tags
        // 3. Clean up cache entries
        // 4. Handle foreign key constraints properly

        self.kb_repository.delete(id).await
    }

    pub async fn list_knowledge_bases(
        &self,
        user_id: Option<&str>,
        organization_id: Option<&str>,
        _visibility_filter: Option<Vec<KnowledgeBaseVisibility>>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<KnowledgeBase>> {
        // For now, implement basic listing
        // TODO: Add proper filtering based on user permissions and visibility
        if let Some(owner_id) = user_id {
            self.kb_repository.find_by_owner(owner_id).await
        } else if let Some(org_id) = organization_id {
            self.kb_repository.find_by_organization(org_id).await
        } else {
            self.kb_repository.list(limit, offset).await
        }
    }

    pub async fn search_knowledge_bases(
        &self,
        query: &str,
        _user_id: Option<&str>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<KnowledgeBase>> {
        if query.trim().is_empty() {
            return Err(anyhow!("Search query cannot be empty"));
        }

        // TODO: Add permission filtering
        self.kb_repository.search(query, limit, offset).await
    }

    pub async fn get_knowledge_base_statistics(&self, id: &str) -> Result<KnowledgeBaseStatistics> {
        let kb = self
            .kb_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        Ok(kb.statistics)
    }

    pub async fn update_knowledge_base_statistics(
        &self,
        id: &str,
        stats: KnowledgeBaseStatistics,
    ) -> Result<bool> {
        self.kb_repository.update_statistics(id, &stats).await
    }

    pub async fn increment_view_count(&self, id: &str) -> Result<()> {
        let kb = self
            .kb_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        let mut stats = kb.statistics;
        stats.view_count += 1;

        self.kb_repository.update_statistics(id, &stats).await?;
        Ok(())
    }

    pub async fn increment_search_count(&self, id: &str) -> Result<()> {
        let kb = self
            .kb_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        let mut stats = kb.statistics;
        stats.search_count += 1;

        self.kb_repository.update_statistics(id, &stats).await?;
        Ok(())
    }

    /// Category Management
    #[allow(clippy::too_many_arguments)]
    pub async fn create_category(
        &self,
        kb_id: String,
        name: String,
        description: Option<String>,
        parent_id: Option<String>,
        icon: Option<String>,
        sort_order: i32,
        created_by: String,
    ) -> Result<Category> {
        // Validate knowledge base exists
        self.kb_repository
            .find_by_id(&kb_id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        // Validate name
        if name.trim().is_empty() {
            return Err(anyhow!("Category name cannot be empty"));
        }

        if name.len() > 50 {
            return Err(anyhow!("Category name cannot exceed 50 characters"));
        }

        // Validate parent exists if provided
        if let Some(ref parent_id) = parent_id {
            let parent = self
                .category_repository
                .find_by_id(parent_id)
                .await?
                .ok_or_else(|| anyhow!("Parent category not found"))?;

            if parent.knowledge_base_id != kb_id {
                return Err(anyhow!(
                    "Parent category must be in the same knowledge base"
                ));
            }
        }

        let category = Category {
            id: next_id(),
            knowledge_base_id: kb_id,
            parent_id,
            name: name.trim().to_string(),
            description: description
                .map(|d| d.trim().to_string())
                .filter(|d| !d.is_empty()),
            icon,
            sort_order,
            document_count: 0,
            created_by,
            created_at: current_millis(),
            updated_at: current_millis(),
        };

        self.category_repository.create(&category).await
    }

    pub async fn get_categories_by_knowledge_base(&self, kb_id: &str) -> Result<Vec<Category>> {
        self.category_repository.find_by_knowledge_base(kb_id).await
    }

    pub async fn get_category_hierarchy(&self, kb_id: &str) -> Result<Vec<Category>> {
        self.category_repository.find_by_knowledge_base(kb_id).await
    }

    pub async fn update_category(
        &self,
        id: &str,
        name: Option<String>,
        description: Option<String>,
        icon: Option<String>,
        sort_order: Option<i32>,
    ) -> Result<Category> {
        let mut category = self
            .category_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("Category not found"))?;

        if let Some(name) = name {
            if name.trim().is_empty() {
                return Err(anyhow!("Category name cannot be empty"));
            }
            if name.len() > 50 {
                return Err(anyhow!("Category name cannot exceed 50 characters"));
            }
            category.name = name.trim().to_string();
        }

        if let Some(description) = description {
            category.description = Some(description.trim().to_string()).filter(|d| !d.is_empty());
        }

        if let Some(icon) = icon {
            category.icon = Some(icon).filter(|i| !i.is_empty());
        }

        if let Some(sort_order) = sort_order {
            category.sort_order = sort_order;
        }

        category.updated_at = current_millis();

        self.category_repository.update(&category).await
    }

    pub async fn delete_category(&self, id: &str) -> Result<bool> {
        // TODO: Check if category has children or documents
        // In a real implementation, we should prevent deletion if:
        // 1. Category has subcategories
        // 2. Category has documents assigned
        // Or provide options to handle these cases

        self.category_repository.delete(id).await
    }

    // Tag Management
    pub async fn create_tag(
        &self,
        kb_id: String,
        name: String,
        display_name: String,
        color: Option<String>,
        description: Option<String>,
        created_by: String,
    ) -> Result<Tag> {
        // Validate knowledge base exists
        self.kb_repository
            .find_by_id(&kb_id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        // Validate name uniqueness
        if let Some(_existing) = self.tag_repository.find_by_name(&kb_id, &name).await? {
            return Err(anyhow!(
                "Tag with this name already exists in the knowledge base"
            ));
        }

        // Validate name
        if name.trim().is_empty() {
            return Err(anyhow!("Tag name cannot be empty"));
        }

        if name.len() > 30 {
            return Err(anyhow!("Tag name cannot exceed 30 characters"));
        }

        if display_name.trim().is_empty() {
            return Err(anyhow!("Tag display name cannot be empty"));
        }

        if display_name.len() > 50 {
            return Err(anyhow!("Tag display name cannot exceed 50 characters"));
        }

        let tag = Tag {
            id: next_id(),
            knowledge_base_id: kb_id,
            name: name.trim().to_lowercase(), // Normalize name to lowercase
            display_name: display_name.trim().to_string(),
            color: color
                .map(|c| c.trim().to_string())
                .filter(|c| !c.is_empty()),
            description: description
                .map(|d| d.trim().to_string())
                .filter(|d| !d.is_empty()),
            usage_count: 0,
            created_by,
            created_at: current_millis(),
            updated_at: current_millis(),
        };

        self.tag_repository.create(&tag).await
    }

    pub async fn get_tags_by_knowledge_base(&self, kb_id: &str) -> Result<Vec<Tag>> {
        self.tag_repository.find_by_knowledge_base(kb_id).await
    }

    pub async fn search_tags(&self, kb_id: &str, query: &str) -> Result<Vec<Tag>> {
        if query.trim().is_empty() {
            return Err(anyhow!("Tag search query cannot be empty"));
        }

        self.tag_repository.search(kb_id, query).await
    }

    pub async fn get_popular_tags(&self, kb_id: &str, limit: u32) -> Result<Vec<Tag>> {
        let limit = if limit > 100 { 100 } else { limit }; // Cap at 100
        let mut tags = self.tag_repository.find_by_knowledge_base(kb_id).await?;
        // 根据使用次数排序并限制数量
        tags.sort_by(|a, b| b.usage_count.cmp(&a.usage_count));
        tags.truncate(limit as usize);
        Ok(tags)
    }

    pub async fn update_tag(
        &self,
        id: &str,
        display_name: Option<String>,
        color: Option<String>,
        description: Option<String>,
    ) -> Result<Tag> {
        let mut tag = self
            .tag_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("Tag not found"))?;

        if let Some(display_name) = display_name {
            if display_name.trim().is_empty() {
                return Err(anyhow!("Tag display name cannot be empty"));
            }
            if display_name.len() > 50 {
                return Err(anyhow!("Tag display name cannot exceed 50 characters"));
            }
            tag.display_name = display_name.trim().to_string();
        }

        if let Some(color) = color {
            tag.color = Some(color.trim().to_string()).filter(|c| !c.is_empty());
        }

        if let Some(description) = description {
            tag.description = Some(description.trim().to_string()).filter(|d| !d.is_empty());
        }

        tag.updated_at = current_millis();

        self.tag_repository.update(&tag).await
    }

    pub async fn delete_tag(&self, id: &str) -> Result<bool> {
        // TODO: In a real implementation, we should also:
        // 1. Remove tag associations from all documents
        // 2. Update document tag counts
        // 3. Clean up cache entries

        self.tag_repository.delete(id).await
    }

    pub async fn increment_tag_usage(&self, id: &str) -> Result<()> {
        let mut tag = self
            .tag_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| anyhow!("Tag not found"))?;

        tag.usage_count += 1;
        tag.updated_at = current_millis();

        self.tag_repository.update(&tag).await?;
        Ok(())
    }

    // Permission and Access Control Helpers
    pub async fn check_read_permission(
        &self,
        kb_id: &str,
        user_id: Option<&str>,
        user_organization_id: Option<&str>,
    ) -> Result<bool> {
        let kb = self
            .kb_repository
            .find_by_id(kb_id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        match kb.visibility {
            KnowledgeBaseVisibility::Public => Ok(true),
            KnowledgeBaseVisibility::Private => match user_id {
                Some(uid) => Ok(kb.owner_id == uid),
                None => Ok(false),
            },
            KnowledgeBaseVisibility::Organization => match user_organization_id {
                Some(org_id) => Ok(kb.organization_id == org_id),
                None => Ok(false),
            },
            KnowledgeBaseVisibility::Department => self.check_department_access(&kb, user_id).await,
        }
    }

    /// 检查部门级访问权限
    async fn check_department_access(
        &self,
        kb: &KnowledgeBase,
        user_id: Option<&str>,
    ) -> Result<bool> {
        let user_id = match user_id {
            Some(uid) => uid,
            None => return Ok(false),
        };

        // 获取用户信息
        let user = match self.user_repository.find_by_id(user_id).await? {
            Some(user) => user,
            None => return Ok(false),
        };

        // 检查用户是否属于同一组织 (简化实现，假设用户属于知识库所在组织)
        // TODO: 实际应该在User模型中添加organization_id字段
        // if user.organization_id != Some(kb.organization_id.clone()) {
        //     return Ok(false);
        // }

        // 获取知识库所有者信息来确定部门
        let owner = match self.user_repository.find_by_id(&kb.owner_id).await? {
            Some(owner) => owner,
            None => return Ok(false),
        };

        // 检查部门级访问权限
        match (
            user.organization_id.as_ref(),
            owner.organization_id.as_ref(),
        ) {
            (Some(user_dept), Some(owner_dept)) => {
                // 用户和知识库所有者在同一部门
                Ok(user_dept == owner_dept)
            }
            (None, None) => {
                // 两者都没有部门信息，属于同一组织即可访问
                Ok(true)
            }
            _ => {
                // 一个有部门信息一个没有，不允许访问
                Ok(false)
            }
        }
    }

    /// 增强的权限检查，支持角色和权限系统
    pub async fn check_enhanced_read_permission(
        &self,
        kb_id: &str,
        user_id: Option<&str>,
    ) -> Result<bool> {
        let user_id = match user_id {
            Some(uid) => uid,
            None => {
                // 检查是否允许匿名访问
                return self.check_anonymous_access(kb_id).await;
            }
        };

        let _kb = self
            .kb_repository
            .find_by_id(kb_id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        // 首先检查基本的可见性规则
        let user = self
            .user_repository
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| anyhow!("User not found"))?;

        let basic_access = self
            .check_read_permission(kb_id, Some(user_id), user.organization_id.as_deref())
            .await?;

        if basic_access {
            return Ok(true);
        }

        // TODO: 检查基于角色的权限
        // 这里可以扩展角色和权限系统
        // if self.check_role_based_permission(user_id, kb_id, "read").await? {
        //     return Ok(true);
        // }

        Ok(false)
    }

    /// 检查匿名访问权限
    async fn check_anonymous_access(&self, kb_id: &str) -> Result<bool> {
        let kb = self
            .kb_repository
            .find_by_id(kb_id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        // 只有公开的知识库且设置允许匿名访问才能匿名访问
        Ok(kb.visibility == KnowledgeBaseVisibility::Public && kb.settings.allow_anonymous_access)
    }

    pub async fn check_write_permission(&self, kb_id: &str, user_id: &str) -> Result<bool> {
        let kb = self
            .kb_repository
            .find_by_id(kb_id)
            .await?
            .ok_or_else(|| anyhow!("Knowledge base not found"))?;

        // 检查是否是所有者
        if kb.owner_id == user_id {
            return Ok(true);
        }

        // TODO: 实现更精细的写权限控制
        // 可以基于角色和权限系统来实现
        // if self.check_role_based_permission(user_id, kb_id, "write").await? {
        //     return Ok(true);
        // }

        Ok(false)
    }

    /// 按部门过滤知识库列表
    pub async fn filter_by_department(
        &self,
        knowledge_bases: Vec<KnowledgeBase>,
        user_id: &str,
    ) -> Result<Vec<KnowledgeBase>> {
        let user = self
            .user_repository
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| anyhow!("User not found"))?;

        let mut filtered = Vec::new();

        for kb in knowledge_bases {
            let has_access = match kb.visibility {
                KnowledgeBaseVisibility::Public => true,
                KnowledgeBaseVisibility::Private => kb.owner_id == user_id,
                KnowledgeBaseVisibility::Organization => {
                    // TODO: 实际应该检查user.organization_id == kb.organization_id
                    // 现在简化为允许访问
                    true
                }
                KnowledgeBaseVisibility::Department => {
                    self.check_department_access(&kb, Some(user_id)).await?
                }
            };

            if has_access {
                filtered.push(kb);
            }
        }

        Ok(filtered)
    }

    /// 按用户权限过滤知识库列表
    pub async fn list_accessible_knowledge_bases(
        &self,
        user_id: Option<&str>,
        organization_id: Option<&str>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<KnowledgeBase>> {
        // 获取组织或所有知识库
        let all_kbs = if let Some(org_id) = organization_id {
            self.kb_repository.find_by_organization(org_id).await?
        } else {
            self.kb_repository.list(limit, offset).await?
        };

        if user_id.is_none() {
            // 匿名用户只能访问公开的知识库
            return Ok(all_kbs
                .into_iter()
                .filter(|kb| {
                    kb.visibility == KnowledgeBaseVisibility::Public
                        && kb.settings.allow_anonymous_access
                })
                .collect());
        }

        let user_id = user_id.unwrap();
        let user = self
            .user_repository
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| anyhow!("User not found"))?;

        let mut accessible_kbs = Vec::new();

        for kb in all_kbs {
            let has_access = match kb.visibility {
                KnowledgeBaseVisibility::Public => true,
                KnowledgeBaseVisibility::Private => kb.owner_id == user_id,
                KnowledgeBaseVisibility::Organization => {
                    // TODO: 实际应该检查user.organization_id == kb.organization_id
                    // 现在简化为允许访问
                    true
                }
                KnowledgeBaseVisibility::Department => {
                    // 检查组织机构访问权限
                    if let Some(user_dept) = &user.organization_id {
                        // 用户有部门信息
                        // 获取知识库所有者的部门信息
                        if let Ok(Some(owner)) = self.user_repository.find_by_id(&kb.owner_id).await
                        {
                            owner.organization_id.as_ref() == Some(user_dept)
                        } else {
                            false
                        }
                    } else {
                        false
                    }
                }
            };

            if has_access {
                accessible_kbs.push(kb);
            }
        }

        Ok(accessible_kbs)
    }

    // Bulk Operations
    pub async fn bulk_update_category_counts(
        &self,
        kb_id: String,
        category_counts: HashMap<String, i64>,
    ) -> Result<()> {
        for (category_id, count) in category_counts {
            if let Ok(Some(mut category)) = self.category_repository.find_by_id(&category_id).await
            {
                if category.knowledge_base_id == kb_id {
                    category.document_count = count;
                    category.updated_at = current_millis();
                    let _ = self.category_repository.update(&category).await;
                }
            }
        }
        Ok(())
    }

    pub async fn bulk_update_tag_usage(
        &self,
        kb_id: String,
        tag_usage: HashMap<String, i64>,
    ) -> Result<()> {
        for (tag_id, usage) in tag_usage {
            if let Ok(Some(mut tag)) = self.tag_repository.find_by_id(&tag_id).await {
                if tag.knowledge_base_id == kb_id {
                    tag.usage_count = usage;
                    tag.updated_at = current_millis();
                    let _ = self.tag_repository.update(&tag).await;
                }
            }
        }
        Ok(())
    }
}
