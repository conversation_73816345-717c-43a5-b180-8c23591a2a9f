use anyhow::Result;
use chrono::{DateTime, Utc};
use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};
use tokio::sync::RwLock;
use wisdom_vault_common::time::current_millis;

use wisdom_vault_database::{
    models::{Document, DocumentChunk, DocumentStatus},
    repositories::{DocumentChunkRepository, DocumentRepository},
};

use crate::services::{DocumentChunkingService, VectorizationService};

/// 文档变更类型
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum DocumentChangeType {
    /// 内容修改
    ContentModified,
    /// 元数据修改
    MetadataModified,
    /// 新增文档
    DocumentAdded,
    /// 删除文档
    DocumentDeleted,
    /// 分块变更
    ChunksModified,
}

/// 变更检测结果
#[derive(Debug, Clone)]
pub struct ChangeDetectionResult {
    pub document_id: String,
    pub change_type: DocumentChangeType,
    pub affected_chunks: Vec<String>,
    pub change_timestamp: i64,
    pub change_hash: String,
    pub requires_vectorization: bool,
}

/// 增量更新配置
#[derive(Debug, Clone)]
pub struct IncrementalUpdateConfig {
    /// 变更检测间隔(秒)
    pub change_detection_interval_seconds: u64,
    /// 批量更新阈值 - 当变更文档数量超过此值时进行批量处理
    pub batch_update_threshold: usize,
    /// 是否启用智能依赖检测
    pub enable_dependency_detection: bool,
    /// 质量阈值 - 只有质量超过此值的向量才会被更新
    pub quality_threshold: f64,
    /// 相似度阈值 - 用于检测内容是否有实质性变化
    pub similarity_threshold: f64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 是否启用增量优化
    pub enable_optimization: bool,
}

impl Default for IncrementalUpdateConfig {
    fn default() -> Self {
        Self {
            change_detection_interval_seconds: 300, // 5分钟
            batch_update_threshold: 50,
            enable_dependency_detection: true,
            quality_threshold: 0.8,
            similarity_threshold: 0.95,
            max_retries: 3,
            enable_optimization: true,
        }
    }
}

/// 增量更新统计
#[derive(Debug, Default, Clone)]
pub struct IncrementalUpdateStatistics {
    pub total_changes_detected: u64,
    pub total_documents_updated: u64,
    pub total_chunks_updated: u64,
    pub total_vectors_updated: u64,
    pub total_optimization_saves: u64,
    pub avg_update_time_ms: f64,
    pub last_update_at: Option<DateTime<Utc>>,
}

/// 文档变更记录
#[derive(Debug, Clone)]
struct DocumentChangeRecord {
    pub document_id: String,
    pub last_hash: String,
    pub last_modified: i64,
    pub last_vectorized: Option<i64>,
    pub chunk_hashes: HashMap<String, String>,
}

/// 增量向量更新服务
pub struct IncrementalVectorUpdate {
    /// 向量化服务
    vectorization_service: Arc<VectorizationService>,
    /// 文档仓库
    document_repository: Arc<dyn DocumentRepository + Send + Sync>,
    /// 分块仓库
    chunk_repository: Arc<dyn DocumentChunkRepository + Send + Sync>,
    /// 文档分块服务
    chunking_service: Arc<DocumentChunkingService>,
    /// 配置
    config: IncrementalUpdateConfig,
    /// 文档变更记录
    change_records: Arc<RwLock<HashMap<String, DocumentChangeRecord>>>,
    /// 待更新队列
    update_queue: Arc<RwLock<Vec<ChangeDetectionResult>>>,
    /// 统计信息
    statistics: Arc<RwLock<IncrementalUpdateStatistics>>,
    /// 依赖关系图
    dependency_graph: Arc<RwLock<HashMap<String, HashSet<String>>>>,
}

impl IncrementalVectorUpdate {
    /// 创建新的增量向量更新服务
    pub fn new(
        vectorization_service: Arc<VectorizationService>,
        document_repository: Arc<dyn DocumentRepository + Send + Sync>,
        chunk_repository: Arc<dyn DocumentChunkRepository + Send + Sync>,
        chunking_service: Arc<DocumentChunkingService>,
        config: IncrementalUpdateConfig,
    ) -> Self {
        Self {
            vectorization_service,
            document_repository,
            chunk_repository,
            chunking_service,
            config,
            change_records: Arc::new(RwLock::new(HashMap::new())),
            update_queue: Arc::new(RwLock::new(Vec::new())),
            statistics: Arc::new(RwLock::new(IncrementalUpdateStatistics::default())),
            dependency_graph: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 检测文档变更
    pub async fn detect_changes(
        &self,
        knowledge_base_id: Option<&str>,
    ) -> Result<Vec<ChangeDetectionResult>> {
        tracing::info!("开始检测文档变更，知识库ID: {:?}", knowledge_base_id);

        let mut changes = Vec::new();

        // 获取需要检查的文档
        let documents = match knowledge_base_id {
            Some(kb_id) => {
                self.document_repository
                    .find_by_knowledge_base(kb_id, None, None)
                    .await?
            }
            None => {
                // 获取所有已索引的文档
                self.document_repository
                    .find_by_status(&DocumentStatus::Indexed, None, None)
                    .await?
            }
        };

        for document in documents {
            if let Some(change) = self.detect_document_change(&document).await? {
                changes.push(change);
            }
        }

        // 更新统计信息
        {
            let mut stats = self.statistics.write().await;
            stats.total_changes_detected += changes.len() as u64;
        }

        tracing::info!("检测到 {} 个文档变更", changes.len());
        Ok(changes)
    }

    /// 检测单个文档的变更
    async fn detect_document_change(
        &self,
        document: &Document,
    ) -> Result<Option<ChangeDetectionResult>> {
        let document_hash = self.calculate_document_hash(document);
        let change_records = self.change_records.read().await;

        match change_records.get(&document.id) {
            Some(record) => {
                // 检查内容是否变更
                if record.last_hash != document_hash {
                    // 检测变更类型和受影响的分块
                    let (change_type, affected_chunks) =
                        self.analyze_document_changes(document, record).await?;

                    drop(change_records);

                    // 更新变更记录
                    self.update_change_record(document, &document_hash).await?;

                    Ok(Some(ChangeDetectionResult {
                        document_id: document.id.clone(),
                        change_type: change_type.clone(),
                        affected_chunks,
                        change_timestamp: document.updated_at,
                        change_hash: document_hash,
                        requires_vectorization: self.requires_vectorization(&change_type),
                    }))
                } else {
                    Ok(None)
                }
            }
            None => {
                // 新文档，需要完全向量化
                drop(change_records);
                self.update_change_record(document, &document_hash).await?;

                let chunks = self
                    .chunk_repository
                    .find_by_document_id(&document.id)
                    .await?;
                let chunk_ids = chunks.iter().map(|c| c.id.clone()).collect();

                Ok(Some(ChangeDetectionResult {
                    document_id: document.id.clone(),
                    change_type: DocumentChangeType::DocumentAdded,
                    affected_chunks: chunk_ids,
                    change_timestamp: document.created_at,
                    change_hash: document_hash,
                    requires_vectorization: true,
                }))
            }
        }
    }

    /// 分析文档具体变更内容
    async fn analyze_document_changes(
        &self,
        document: &Document,
        record: &DocumentChangeRecord,
    ) -> Result<(DocumentChangeType, Vec<String>)> {
        // 获取当前文档的分块
        let current_chunks = self
            .chunk_repository
            .find_by_document_id(&document.id)
            .await?;
        let mut affected_chunks = Vec::new();

        // 检查分块级别的变更
        for chunk in &current_chunks {
            let chunk_hash = self.calculate_chunk_hash(chunk);

            if let Some(old_hash) = record.chunk_hashes.get(&chunk.id) {
                if old_hash != &chunk_hash {
                    affected_chunks.push(chunk.id.clone());
                }
            } else {
                // 新分块
                affected_chunks.push(chunk.id.clone());
            }
        }

        // 检查是否有分块被删除
        let current_chunk_ids: HashSet<String> =
            current_chunks.iter().map(|c| c.id.clone()).collect();
        let old_chunk_ids: HashSet<String> = record.chunk_hashes.keys().cloned().collect();

        for deleted_chunk_id in old_chunk_ids.difference(&current_chunk_ids) {
            // 删除的分块也需要处理（删除对应的向量）
            affected_chunks.push(deleted_chunk_id.clone());
        }

        // 确定变更类型
        let change_type = if !affected_chunks.is_empty() {
            if current_chunks.len() != record.chunk_hashes.len() {
                DocumentChangeType::ChunksModified
            } else {
                DocumentChangeType::ContentModified
            }
        } else {
            DocumentChangeType::MetadataModified
        };

        Ok((change_type, affected_chunks))
    }

    /// 执行增量更新
    pub async fn perform_incremental_update(
        &self,
        changes: Vec<ChangeDetectionResult>,
    ) -> Result<IncrementalUpdateResult> {
        if changes.is_empty() {
            return Ok(IncrementalUpdateResult::default());
        }

        tracing::info!("开始执行增量更新，变更数量: {}", changes.len());
        let start_time = std::time::Instant::now();

        let mut result = IncrementalUpdateResult::default();

        // 添加到更新队列
        {
            let mut queue = self.update_queue.write().await;
            queue.extend(changes.clone());
        }

        // 根据配置决定是批量还是单独处理
        if changes.len() >= self.config.batch_update_threshold {
            result = self.process_batch_update(changes).await?;
        } else {
            result = self.process_individual_updates(changes).await?;
        }

        let processing_time = start_time.elapsed().as_millis() as i64;
        result.total_processing_time_ms = processing_time;

        // 如果启用了依赖检测，处理依赖更新
        if self.config.enable_dependency_detection {
            self.process_dependent_updates(&result.updated_documents)
                .await?;
        }

        // 更新统计信息
        self.update_statistics(&result).await;

        tracing::info!(
            "增量更新完成: 更新文档 {}, 更新分块 {}, 耗时 {}ms",
            result.updated_documents.len(),
            result.updated_chunks.len(),
            processing_time
        );

        Ok(result)
    }

    /// 处理批量更新
    async fn process_batch_update(
        &self,
        changes: Vec<ChangeDetectionResult>,
    ) -> Result<IncrementalUpdateResult> {
        let mut result = IncrementalUpdateResult::default();

        // 按文档分组
        let mut document_changes: HashMap<String, Vec<ChangeDetectionResult>> = HashMap::new();
        for change in changes {
            document_changes
                .entry(change.document_id.clone())
                .or_default()
                .push(change);
        }

        // 并发处理文档更新
        let mut tasks = Vec::new();
        for (document_id, doc_changes) in document_changes {
            let vectorization_service = self.vectorization_service.clone();
            let config = self.config.clone();

            let task = tokio::spawn(async move {
                Self::update_document_vectors(
                    vectorization_service,
                    document_id,
                    doc_changes,
                    config,
                )
                .await
            });

            tasks.push(task);
        }

        // 等待所有任务完成
        for task in tasks {
            match task.await? {
                Ok(doc_result) => {
                    result.merge(doc_result);
                }
                Err(e) => {
                    tracing::error!("批量更新任务失败: {}", e);
                    result.failed_updates += 1;
                }
            }
        }

        Ok(result)
    }

    /// 处理单独更新
    async fn process_individual_updates(
        &self,
        changes: Vec<ChangeDetectionResult>,
    ) -> Result<IncrementalUpdateResult> {
        let mut result = IncrementalUpdateResult::default();

        for change in changes {
            match self.process_single_change(change).await {
                Ok(single_result) => result.merge(single_result),
                Err(e) => {
                    tracing::error!("单个变更处理失败: {}", e);
                    result.failed_updates += 1;
                }
            }
        }

        Ok(result)
    }

    /// 处理单个变更
    async fn process_single_change(
        &self,
        change: ChangeDetectionResult,
    ) -> Result<IncrementalUpdateResult> {
        if !change.requires_vectorization {
            return Ok(IncrementalUpdateResult::default());
        }

        let doc_changes = vec![change];
        Self::update_document_vectors(
            self.vectorization_service.clone(),
            doc_changes[0].document_id.clone(),
            doc_changes,
            self.config.clone(),
        )
        .await
    }

    /// 更新文档向量
    async fn update_document_vectors(
        vectorization_service: Arc<VectorizationService>,
        document_id: String,
        changes: Vec<ChangeDetectionResult>,
        _config: IncrementalUpdateConfig,
    ) -> Result<IncrementalUpdateResult> {
        let mut result = IncrementalUpdateResult::default();

        // 收集所有受影响的分块
        let mut all_affected_chunks = HashSet::new();
        for change in &changes {
            all_affected_chunks.extend(change.affected_chunks.iter().cloned());
        }

        // 对每个受影响的分块进行重新向量化
        for chunk_id in all_affected_chunks {
            match vectorization_service
                .vectorize_chunk(&chunk_id, None, true)
                .await
            {
                Ok(_) => {
                    result.updated_chunks.push(chunk_id);
                    result.vectors_updated += 1;
                }
                Err(e) => {
                    tracing::error!("分块 {} 向量化失败: {}", chunk_id, e);
                    result.failed_updates += 1;
                }
            }
        }

        if !result.updated_chunks.is_empty() {
            result.updated_documents.push(document_id);
        }

        Ok(result)
    }

    /// 处理依赖更新
    async fn process_dependent_updates(&self, updated_documents: &[String]) -> Result<()> {
        let dependency_graph = self.dependency_graph.read().await;

        let mut dependent_documents = HashSet::new();
        for document_id in updated_documents {
            if let Some(dependents) = dependency_graph.get(document_id) {
                dependent_documents.extend(dependents.iter().cloned());
            }
        }

        drop(dependency_graph);

        // 为依赖文档创建更新任务
        for dependent_id in dependent_documents {
            if let Ok(document) = self.document_repository.find_by_id(&dependent_id).await {
                if let Some(doc) = document {
                    if let Some(change) = self.detect_document_change(&doc).await? {
                        let _ = self.process_single_change(change).await;
                    }
                }
            }
        }

        Ok(())
    }

    /// 计算文档哈希
    fn calculate_document_hash(&self, document: &Document) -> String {
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{Hash, Hasher},
        };

        let mut hasher = DefaultHasher::new();
        document.content.hash(&mut hasher);
        document.updated_at.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// 计算分块哈希
    fn calculate_chunk_hash(&self, chunk: &DocumentChunk) -> String {
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{Hash, Hasher},
        };

        let mut hasher = DefaultHasher::new();
        chunk.content.hash(&mut hasher);
        chunk.chunk_index.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// 判断变更类型是否需要向量化
    fn requires_vectorization(&self, change_type: &DocumentChangeType) -> bool {
        matches!(
            change_type,
            DocumentChangeType::ContentModified
                | DocumentChangeType::DocumentAdded
                | DocumentChangeType::ChunksModified
        )
    }

    /// 更新变更记录
    async fn update_change_record(&self, document: &Document, hash: &str) -> Result<()> {
        // 获取当前分块
        let chunks = self
            .chunk_repository
            .find_by_document_id(&document.id)
            .await?;
        let mut chunk_hashes = HashMap::new();

        for chunk in chunks {
            let chunk_hash = self.calculate_chunk_hash(&chunk);
            chunk_hashes.insert(chunk.id, chunk_hash);
        }

        let record = DocumentChangeRecord {
            document_id: document.id.clone(),
            last_hash: hash.to_string(),
            last_modified: document.updated_at,
            last_vectorized: Some(current_millis()),
            chunk_hashes,
        };

        self.change_records
            .write()
            .await
            .insert(document.id.clone(), record);
        Ok(())
    }

    /// 更新统计信息
    async fn update_statistics(&self, result: &IncrementalUpdateResult) {
        let mut stats = self.statistics.write().await;
        stats.total_documents_updated += result.updated_documents.len() as u64;
        stats.total_chunks_updated += result.updated_chunks.len() as u64;
        stats.total_vectors_updated += result.vectors_updated;
        stats.last_update_at = Some(Utc::now());

        // 更新平均处理时间
        let total_updates = stats.total_documents_updated + stats.total_chunks_updated;
        if total_updates > 0 {
            stats.avg_update_time_ms =
                result.total_processing_time_ms as f64 / total_updates as f64;
        }
    }

    /// 获取统计信息
    pub async fn get_statistics(&self) -> IncrementalUpdateStatistics {
        self.statistics.read().await.clone()
    }

    /// 清理过期的变更记录
    pub async fn cleanup_old_records(&self, older_than_days: i64) {
        let cutoff = current_millis() - chrono::Duration::days(older_than_days).num_milliseconds();
        let mut records = self.change_records.write().await;

        records.retain(|_, record| record.last_modified > cutoff);

        tracing::info!("清理过期变更记录完成，保留 {} 条记录", records.len());
    }

    /// 启动定期变更检测
    pub async fn start_periodic_detection(&self, knowledge_base_id: Option<&str>) -> Result<()> {
        let interval = self.config.change_detection_interval_seconds;
        let service = Arc::new(self.clone_for_async());

        tokio::spawn({
            let knowledge_base_id = knowledge_base_id.map(ToOwned::to_owned);
            async move {
                let mut interval =
                    tokio::time::interval(tokio::time::Duration::from_secs(interval));

                loop {
                    interval.tick().await;

                    match service.detect_changes(knowledge_base_id.as_deref()).await {
                        Ok(changes) => {
                            if !changes.is_empty() {
                                if let Err(e) = service.perform_incremental_update(changes).await {
                                    tracing::error!("定期增量更新失败: {}", e);
                                }
                            }
                        }
                        Err(e) => {
                            tracing::error!("定期变更检测失败: {}", e);
                        }
                    }
                }
            }
        });

        tracing::info!("已启动定期变更检测，间隔: {} 秒", interval);
        Ok(())
    }

    /// 构建文档依赖关系图
    pub async fn build_dependency_graph(&self, knowledge_base_id: String) -> Result<()> {
        if !self.config.enable_dependency_detection {
            return Ok(());
        }

        tracing::info!("开始构建文档依赖关系图");

        let documents = self
            .document_repository
            .find_by_knowledge_base(&knowledge_base_id, None, None)
            .await?;
        let mut graph: HashMap<String, HashSet<String>> = HashMap::new();

        // 这里可以实现更复杂的依赖检测逻辑
        // 例如基于文档内容的引用、链接等
        for document in &documents {
            graph.insert(document.id.clone(), HashSet::new());
        }

        *self.dependency_graph.write().await = graph;
        tracing::info!("文档依赖关系图构建完成");
        Ok(())
    }

    /// 克隆用于异步操作
    fn clone_for_async(&self) -> Self {
        Self {
            vectorization_service: self.vectorization_service.clone(),
            document_repository: self.document_repository.clone(),
            chunk_repository: self.chunk_repository.clone(),
            chunking_service: self.chunking_service.clone(),
            config: self.config.clone(),
            change_records: self.change_records.clone(),
            update_queue: self.update_queue.clone(),
            statistics: self.statistics.clone(),
            dependency_graph: self.dependency_graph.clone(),
        }
    }
}

/// 增量更新结果
#[derive(Debug, Default, Clone)]
pub struct IncrementalUpdateResult {
    pub updated_documents: Vec<String>,
    pub updated_chunks: Vec<String>,
    pub vectors_updated: u64,
    pub failed_updates: u64,
    pub optimization_saves: u64,
    pub total_processing_time_ms: i64,
}

impl IncrementalUpdateResult {
    /// 合并其他结果
    pub fn merge(&mut self, other: IncrementalUpdateResult) {
        self.updated_documents.extend(other.updated_documents);
        self.updated_chunks.extend(other.updated_chunks);
        self.vectors_updated += other.vectors_updated;
        self.failed_updates += other.failed_updates;
        self.optimization_saves += other.optimization_saves;
        self.total_processing_time_ms += other.total_processing_time_ms;
    }
}

#[cfg(test)]
mod tests {
    use wisdom_vault_common::db::next_id;

    use super::*;

    #[test]
    fn test_incremental_config_default() {
        let config = IncrementalUpdateConfig::default();
        assert_eq!(config.change_detection_interval_seconds, 300);
        assert_eq!(config.batch_update_threshold, 50);
        assert!(config.enable_dependency_detection);
    }

    #[test]
    fn test_change_detection_result() {
        let change = ChangeDetectionResult {
            document_id: next_id(),
            change_type: DocumentChangeType::ContentModified,
            affected_chunks: vec![next_id()],
            change_timestamp: current_millis(),
            change_hash: "test_hash".to_string(),
            requires_vectorization: true,
        };

        assert_eq!(change.change_type, DocumentChangeType::ContentModified);
        assert!(change.requires_vectorization);
    }

    #[test]
    fn test_incremental_update_result_merge() {
        let mut result1 = IncrementalUpdateResult {
            updated_documents: vec![next_id()],
            vectors_updated: 5,
            ..Default::default()
        };

        let result2 = IncrementalUpdateResult {
            updated_documents: vec![next_id()],
            vectors_updated: 3,
            ..Default::default()
        };

        result1.merge(result2);

        assert_eq!(result1.updated_documents.len(), 2);
        assert_eq!(result1.vectors_updated, 8);
    }
}
