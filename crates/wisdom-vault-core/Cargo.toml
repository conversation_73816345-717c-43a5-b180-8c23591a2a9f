[package]
name = "wisdom-vault-core"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Core business logic and services for Wisdom Vault"

[dependencies]
# Workspace crates
wisdom-vault-common = { path = "../wisdom-vault-common" }
wisdom-vault-database = { path = "../wisdom-vault-database" }

# External dependencies
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
tokio = { workspace = true }
validator = { workspace = true }
uuid = { workspace = true }
jsonwebtoken = { workspace = true }
reqwest = { workspace = true }
async-trait = "0.1"
futures = "0.3"

# Text processing for document chunking
regex = { workspace = true }
unicode-segmentation = { workspace = true }

# AI and vectorization dependencies
candle-core = { workspace = true }
candle-transformers = { workspace = true }
candle-nn = { workspace = true }
tokenizers = { workspace = true }
ndarray = { workspace = true }
hf-hub = { workspace = true }
safetensors = { workspace = true }
approx = { workspace = true }
rand = { workspace = true }
openai-api-rs = { workspace = true }

# Cryptographic dependencies for hashing
sha2 = "0.10"
hex = "0.4"
base64 = "0.22.1"
redis = { workspace = true, features = ["tokio-comp"] }
argon2 = { workspace = true }
mongodb.workspace = true
tokio-util.workspace = true
tracing-serde = "0.2"

[dev-dependencies]
tempfile = { workspace = true }
