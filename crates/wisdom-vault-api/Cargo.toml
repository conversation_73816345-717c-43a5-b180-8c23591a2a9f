[package]
name = "wisdom-vault-api"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "API gateway and web server for Wisdom Vault"

[[bin]]
name = "wisdom-vault"
path = "src/main.rs"

[dependencies]
# Workspace crates
wisdom-vault-common = { path = "../wisdom-vault-common" }
wisdom-vault-core = { path = "../wisdom-vault-core" }
wisdom-vault-database = { path = "../wisdom-vault-database" }

# External dependencies
actix-web = { workspace = true }
actix-multipart = { workspace = true }
actix-cors = { workspace = true }
actix-session = { workspace = true }
tokio = { workspace = true }
futures-util = { workspace = true }
pin-project-lite = "0.2"
uuid = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = [
    "env-filter",
    "fmt",
    "registry",
    "json",
] }
tracing-actix-web = { workspace = true }
config = { workspace = true }
dotenvy = { workspace = true }
validator = { workspace = true }
tokio-util = { workspace = true }
mime = { workspace = true }
mime_guess = { workspace = true }
tempfile = { workspace = true }
openai-api-rs = { workspace = true }
hex = { workspace = true }
actix-web-httpauth = { workspace = true }
utoipa = { workspace = true }
utoipa-actix-web = { workspace = true }
utoipa-swagger-ui = { workspace = true }
rust-i18n = { workspace = true }
derive_more = { workspace = true }
regex = { workspace = true }
tracing-appender = "0.2.3"

reqwest = { workspace = true }
