use crate::{
    config::CorsConfig,
    session::{SESSION_NAME, UserSession},
};
use actix_cors::Cors;
use actix_session::Session;
use actix_web::dev::Service;
use actix_web::{
    Error, HttpMessage,
    body::MessageBody,
    dev::{ServiceRequest, ServiceResponse, Transform},
    http,
};
use futures_util::future::{Ready, ok};
use pin_project_lite::pin_project;
use std::{
    future::Future,
    net::IpAddr,
    pin::Pin,
    rc::Rc,
    task::{Context, Poll},
    time::Instant,
};
use uuid::Uuid;
use wisdom_vault_core::{AuditAction, AuditEventBuilder, AuditLogger, AuditResult};

pub fn cors_from_config(config: &CorsConfig) -> Cors {
    let mut cors = Cors::default();

    // Add allowed origins
    for origin in &config.allowed_origins {
        cors = cors.allowed_origin(origin);
    }

    // Add allowed methods
    let methods: Vec<http::Method> = config
        .allowed_methods
        .iter()
        .filter_map(|method| method.parse().ok())
        .collect();
    cors = cors.allowed_methods(methods);

    // Add allowed headers
    let headers: Vec<http::header::HeaderName> = config
        .allowed_headers
        .iter()
        .filter_map(|h| h.parse().ok())
        .collect();
    cors = cors.allowed_headers(headers);

    if config.supports_credentials {
        cors = cors.supports_credentials();
    }

    cors.max_age(Some(config.max_age as usize))
}

pub async fn validator(
    req: ServiceRequest,
    session: Session,
) -> Result<ServiceRequest, (actix_web::Error, ServiceRequest)> {
    let user = session.get::<UserSession>(SESSION_NAME).unwrap_or(None);

    if user.is_none() {
        return Err((actix_web::error::ErrorUnauthorized("Unauthorized"), req));
    }

    Ok(req)
}

/// 审计中间件，记录用户的HTTP请求操作
pub struct AuditMiddleware {
    audit_logger: Rc<AuditLogger>,
}

impl AuditMiddleware {
    pub fn new(audit_logger: AuditLogger) -> Self {
        Self {
            audit_logger: Rc::new(audit_logger),
        }
    }
}

impl<S, B> Transform<S, ServiceRequest> for AuditMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: MessageBody + 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = AuditMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ok(AuditMiddlewareService {
            service: Rc::new(service),
            audit_logger: self.audit_logger.clone(),
        })
    }
}

pub struct AuditMiddlewareService<S> {
    service: Rc<S>,
    audit_logger: Rc<AuditLogger>,
}

pin_project! {
    pub struct AuditMiddlewareFuture<F> {
        #[pin]
        fut: F,
        audit_logger: Rc<AuditLogger>,
        request_id: String,
        start_time: Instant,
        user_id: Option<String>,
        username: Option<String>,
        ip_address: Option<IpAddr>,
        user_agent: Option<String>,
        method: String,
        path: String,
        action: AuditAction,
    }
}

impl<S, B> Service<ServiceRequest> for AuditMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: MessageBody + 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = AuditMiddlewareFuture<S::Future>;

    fn poll_ready(&self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.service.poll_ready(cx)
    }

    fn call(&self, mut req: ServiceRequest) -> Self::Future {
        let start_time = Instant::now();
        let request_id = Uuid::new_v4().to_string();

        // 从请求头中提取信息
        let ip_address = extract_ip_address(&req);
        let user_agent = req
            .headers()
            .get("user-agent")
            .and_then(|v| v.to_str().ok())
            .map(|s| s.to_string());

        let method = req.method().to_string();
        let path = req.uri().path().to_string();

        // 从会话中提取用户信息
        let (user_id, username) = extract_user_info(&req);

        // 根据路径和方法确定审计动作
        let action = determine_audit_action(&method, &path);

        // 跳过不需要审计的操作
        if let AuditAction::Unknown(ref action_str) = action {
            if action_str == "skip" {
                // 直接调用服务，不记录审计日志
                let fut = self.service.call(req);
                return AuditMiddlewareFuture {
                    fut,
                    audit_logger: self.audit_logger.clone(),
                    request_id: "".to_string(), // 空字符串表示跳过审计
                    start_time,
                    user_id: None,
                    username: None,
                    ip_address: None,
                    user_agent: None,
                    method: "".to_string(),
                    path: "".to_string(),
                    action: AuditAction::Unknown("skip".to_string()),
                };
            }
        }

        // 在请求扩展中设置请求ID，便于其他地方使用
        req.extensions_mut().insert(request_id.clone());

        let fut = self.service.call(req);

        AuditMiddlewareFuture {
            fut,
            audit_logger: self.audit_logger.clone(),
            request_id,
            start_time,
            user_id,
            username,
            ip_address,
            user_agent,
            method,
            path,
            action,
        }
    }
}

impl<F, B> Future for AuditMiddlewareFuture<F>
where
    F: Future<Output = Result<ServiceResponse<B>, Error>>,
    B: MessageBody,
{
    type Output = Result<ServiceResponse<B>, Error>;

    fn poll(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Self::Output> {
        let this = self.project();

        match this.fut.poll(cx) {
            Poll::Ready(result) => {
                // 检查是否跳过审计
                if this.request_id.is_empty() {
                    return Poll::Ready(result);
                }

                let duration = this.start_time.elapsed();

                // 记录审计日志
                let audit_logger = this.audit_logger.clone();
                let request_id = this.request_id.clone();
                let user_id = this.user_id.clone();
                let username = this.username.clone();
                let ip_address = *this.ip_address;
                let user_agent = this.user_agent.clone();
                let method = this.method.clone();
                let path = this.path.clone();
                let action = this.action.clone();

                // 先把需要 move 进异步任务的数据提取出来，避免借用 result 的生命周期问题
                let (status_code_opt, error_msg_opt) = match &result {
                    Ok(response) => (Some(response.status().as_u16()), None),
                    Err(error) => (None, Some(error.to_string())),
                };

                let audit_logger_clone = audit_logger.clone();
                let request_id_c = request_id.clone();
                let user_id_c = user_id.clone();
                let username_c = username.clone();
                let ip_address_c = ip_address.clone();
                let user_agent_c = user_agent.clone();
                let method_c = method.clone();
                let path_c = path.clone();
                let action_c = action.clone();
                let duration_ms = duration.as_millis() as u64;

                actix_web::rt::spawn(async move {
                    let audit_result = match status_code_opt {
                        Some(code) if (200..400).contains(&code) => AuditResult::Success,
                        Some(code) if code == 401 || code == 403 => AuditResult::Denied,
                        Some(_) => AuditResult::Failure,
                        None => AuditResult::Failure,
                    };

                    let event = AuditEventBuilder::new(action_c)
                        .with_result(audit_result)
                        .with_user_info(user_id_c, username_c)
                        .with_session_info(None, Some(request_id_c))
                        .with_http_info(
                            ip_address_c,
                            user_agent_c,
                            Some(method_c),
                            Some(path_c),
                            status_code_opt,
                        )
                        .with_error_info(error_msg_opt)
                        .with_duration_ms(duration_ms)
                        .build();

                    if let Err(e) = audit_logger_clone.log_event(event).await {
                        tracing::error!("Failed to log audit event: {}", e);
                    }
                });

                Poll::Ready(result)
            }
            Poll::Pending => Poll::Pending,
        }
    }
}

/// 从请求中提取IP地址
fn extract_ip_address(req: &ServiceRequest) -> Option<IpAddr> {
    // 首先尝试从X-Forwarded-For头部获取
    if let Some(forwarded) = req.headers().get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded.to_str() {
            // X-Forwarded-For可能包含多个IP，取第一个
            if let Some(first_ip) = forwarded_str.split(',').next() {
                if let Ok(ip) = first_ip.trim().parse::<IpAddr>() {
                    return Some(ip);
                }
            }
        }
    }

    // 然后尝试从X-Real-IP头部获取
    if let Some(real_ip) = req.headers().get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Ok(ip) = ip_str.parse::<IpAddr>() {
                return Some(ip);
            }
        }
    }

    // 最后尝试从连接信息获取
    req.connection_info().remote_addr().and_then(|addr| {
        // 移除端口号
        if let Some(ip_part) = addr.split(':').next() {
            ip_part.parse::<IpAddr>().ok()
        } else {
            None
        }
    })
}

/// 从请求会话中提取用户信息
fn extract_user_info(req: &ServiceRequest) -> (Option<String>, Option<String>) {
    // 优先从扩展中获取（可由登录流程或其他中间件设置）
    if let Some(user_session) = req.request().extensions().get::<UserSession>() {
        return (
            Some(user_session.user_id.clone()),
            Some(user_session.username.clone()),
        );
    }

    // 无法从 ServiceRequest 直接提取 Session，这里返回 None
    (None, None)
}

/// 根据HTTP方法和路径确定审计动作
fn determine_audit_action(method: &str, path: &str) -> AuditAction {
    match (method, path) {
        // 认证相关
        ("POST", "/api/v1/auth/login") => AuditAction::Login,
        ("POST", "/api/v1/auth/logout") => AuditAction::Logout,

        // 用户管理 - 只记录变更操作
        ("POST", p) if p.starts_with("/api/v1/users") => AuditAction::UserCreate,
        ("PUT", p) if p.starts_with("/api/v1/users") => AuditAction::UserUpdate,
        ("PATCH", p) if p.starts_with("/api/v1/users") => AuditAction::UserUpdate,
        ("DELETE", p) if p.starts_with("/api/v1/users") => AuditAction::UserDelete,

        // 角色管理
        ("POST", p) if p.starts_with("/api/v1/roles") => AuditAction::RoleCreate,
        ("PUT", p) if p.starts_with("/api/v1/roles") => AuditAction::RoleUpdate,
        ("PATCH", p) if p.starts_with("/api/v1/roles") => AuditAction::RoleUpdate,
        ("DELETE", p) if p.starts_with("/api/v1/roles") => AuditAction::RoleDelete,

        // 知识库管理 - 只记录变更操作
        ("POST", p) if p.starts_with("/api/v1/knowledge-bases") => AuditAction::KnowledgeBaseCreate,
        ("PUT", p) if p.starts_with("/api/v1/knowledge-bases") => AuditAction::KnowledgeBaseUpdate,
        ("PATCH", p) if p.starts_with("/api/v1/knowledge-bases") => {
            AuditAction::KnowledgeBaseUpdate
        }
        ("DELETE", p) if p.starts_with("/api/v1/knowledge-bases") => {
            AuditAction::KnowledgeBaseDelete
        }

        // 文档管理 - 只记录变更操作，不记录查看
        ("POST", p) if p.starts_with("/api/v1/documents") => AuditAction::DocumentCreate,
        ("PUT", p) if p.starts_with("/api/v1/documents") => AuditAction::DocumentUpdate,
        ("PATCH", p) if p.starts_with("/api/v1/documents") => AuditAction::DocumentUpdate,
        ("DELETE", p) if p.starts_with("/api/v1/documents") => AuditAction::DocumentDelete,

        // 文件管理 - 上传、下载和删除，不记录查看
        ("POST", p) if p.starts_with("/api/v1/files") => AuditAction::FileUpload,
        ("GET", p) if p.starts_with("/api/v1/files") => AuditAction::FileDownload,
        ("DELETE", p) if p.starts_with("/api/v1/files") => AuditAction::FileDelete,

        // 系统配置变更
        ("PUT", p) if p.starts_with("/api/v1/config") => AuditAction::ConfigUpdate,
        ("PATCH", p) if p.starts_with("/api/v1/config") => AuditAction::ConfigUpdate,

        // 其他不需要审计的操作返回None，在中间件中过滤掉
        ("GET", _) => return AuditAction::Unknown("skip".to_string()), // 跳过所有GET请求
        ("POST", p) if p.contains("/search") => return AuditAction::Unknown("skip".to_string()), // 跳过搜索
        ("POST", p) if p.contains("/chat") => return AuditAction::Unknown("skip".to_string()), // 跳过聊天
        (_, p) if p.starts_with("/api/v1/health") => {
            return AuditAction::Unknown("skip".to_string());
        } // 跳过健康检查
        (_, p) if p.starts_with("/swagger") => return AuditAction::Unknown("skip".to_string()), // 跳过API文档
        (_, p) if p.starts_with("/metrics") => return AuditAction::Unknown("skip".to_string()), // 跳过监控指标

        // 默认为未知操作
        _ => AuditAction::Unknown(format!("{} {}", method, path)),
    }
}
