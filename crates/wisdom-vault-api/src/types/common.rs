//! Common API types shared across all handlers

use actix_web::{HttpRequest, HttpResponse, Responder, body::BoxBody};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

#[derive(Clone)]
struct ApiResponseMarker;

/// API 响应
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct ApiResponse<T: Serialize> {
    /// 操作是否成功
    pub success: bool,
    /// 响应数据
    pub data: Option<T>,
    /// 响应消息
    pub message: String,
    /// 错误码
    pub error_code: Option<String>,
}

impl<T> Responder for ApiResponse<T>
where
    T: Serialize,
{
    type Body = BoxBody;

    fn respond_to(self, _req: &HttpRequest) -> HttpResponse<Self::Body> {
        let body = serde_json::to_string(&self).unwrap();
        let mut resp = HttpResponse::Ok()
            .content_type("application/json")
            .body(body);

        resp.extensions_mut().insert(ApiResponseMarker);
        resp
    }
}

impl ApiResponse<()> {
    /// Create a successful response with empty data
    pub fn success_empty() -> Self {
        Self {
            success: true,
            data: None,
            message: "操作成功".to_string(),
            error_code: None,
        }
    }
}

impl<T> ApiResponse<T>
where
    T: Serialize,
{
    /// Create a successful response with data
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: "操作成功".to_string(),
            error_code: None,
        }
    }

    /// Create a successful response with data and custom message
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message,
            error_code: None,
        }
    }
}

impl ApiResponse<()> {
    /// Create an error response with message
    pub fn error(message: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message,
            error_code: None,
        }
    }

    /// Create an error response with message and error code
    pub fn error_with_code(message: String, error_code: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message,
            error_code: Some(error_code),
        }
    }

    /// Create a validation error response
    pub fn validation_error(details: serde_json::Value) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: format!("输入验证失败: {}", details),
            error_code: Some("VALIDATION_ERROR".to_string()),
        }
    }

    /// Create a not found error response
    pub fn not_found(resource: &str) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: format!("{} 不存在", resource),
            error_code: Some("NOT_FOUND".to_string()),
        }
    }

    /// Create an internal server error response
    pub fn internal_error(message: Option<String>) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: message.unwrap_or_else(|| "服务器内部错误".to_string()),
            error_code: Some("INTERNAL_ERROR".to_string()),
        }
    }

    /// Create a bad request error response
    pub fn bad_request(message: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message,
            error_code: Some("BAD_REQUEST".to_string()),
        }
    }

    /// Create an unauthorized error response
    pub fn unauthorized(message: Option<String>) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: message.unwrap_or_else(|| "未授权访问".to_string()),
            error_code: Some("UNAUTHORIZED".to_string()),
        }
    }

    /// Create a forbidden error response
    pub fn forbidden(message: Option<String>) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: message.unwrap_or_else(|| "权限不足".to_string()),
            error_code: Some("FORBIDDEN".to_string()),
        }
    }
}

/// Pagination query parameters
#[derive(Debug, Deserialize)]
pub struct PaginationQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
        }
    }
}

/// Pagination information
#[derive(Debug, Serialize)]
pub struct PaginationInfo {
    pub page: u32,
    pub per_page: u32,
    pub total: Option<i64>,
    pub has_more: bool,
}

/// Paginated response wrapper
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: Option<i64>,
    pub page: u32,
    pub per_page: u32,
    pub has_more: bool,
}

impl<T> PaginatedResponse<T> {
    pub fn new(items: Vec<T>, total: Option<i64>, page: u32, per_page: u32) -> Self {
        let has_more = match total {
            Some(t) => ((page - 1) * per_page + items.len() as u32) < t as u32,
            None => items.len() == per_page as usize,
        };
        Self {
            items,
            total,
            page,
            per_page,
            has_more,
        }
    }
}
