use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use wisdom_vault_common::db::next_id;
use wisdom_vault_core::services::{
    AdvancedSearchResult, IndexStatistics, IndexUpdateTask, KeywordSearchResults,
    PerformanceTuningRecommendation, PopularQuery, SearchPerformanceAnomaly,
    SearchPerformanceMetrics, SearchSortBy, SearchSortOrder,
};
use wisdom_vault_database::models::DocumentStatus;

use crate::types::document::DocumentSummaryResponse;

/// 文档状态 API 类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DocumentStatusRequest {
    #[serde(rename = "uploaded")]
    Uploaded,
    #[serde(rename = "processing")]
    Processing,
    #[serde(rename = "indexed")]
    Indexed,
    #[serde(rename = "failed")]
    Failed,
    #[serde(rename = "archived")]
    Archived,
}

impl From<DocumentStatusRequest> for DocumentStatus {
    fn from(status: DocumentStatusRequest) -> Self {
        match status {
            DocumentStatusRequest::Uploaded => DocumentStatus::Uploaded,
            DocumentStatusRequest::Processing => DocumentStatus::Processing,
            DocumentStatusRequest::Indexed => DocumentStatus::Indexed,
            DocumentStatusRequest::Failed => DocumentStatus::Failed,
            DocumentStatusRequest::Archived => DocumentStatus::Archived,
        }
    }
}

/// 搜索排序字段 API 类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SearchSortByRequest {
    #[serde(rename = "relevance")]
    Relevance,
    #[serde(rename = "created_at")]
    CreatedAt,
    #[serde(rename = "updated_at")]
    UpdatedAt,
    #[serde(rename = "title")]
    Title,
    #[serde(rename = "file_size")]
    FileSize,
    #[serde(rename = "author")]
    Author,
}

impl From<SearchSortByRequest> for SearchSortBy {
    fn from(sort_by: SearchSortByRequest) -> Self {
        match sort_by {
            SearchSortByRequest::Relevance => SearchSortBy::Relevance,
            SearchSortByRequest::CreatedAt => SearchSortBy::CreatedAt,
            SearchSortByRequest::UpdatedAt => SearchSortBy::UpdatedAt,
            SearchSortByRequest::Title => SearchSortBy::Title,
            SearchSortByRequest::FileSize => SearchSortBy::FileSize,
            SearchSortByRequest::Author => SearchSortBy::Author,
        }
    }
}

/// 搜索排序顺序 API 类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SearchSortOrderRequest {
    #[serde(rename = "asc")]
    Asc,
    #[serde(rename = "desc")]
    Desc,
}

impl From<SearchSortOrderRequest> for SearchSortOrder {
    fn from(sort_order: SearchSortOrderRequest) -> Self {
        match sort_order {
            SearchSortOrderRequest::Asc => SearchSortOrder::Asc,
            SearchSortOrderRequest::Desc => SearchSortOrder::Desc,
        }
    }
}

/// 日期范围过滤器请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DateRangeFilterRequest {
    pub start: Option<u64>,
    pub end: Option<u64>,
    pub date_field: Option<String>, // "created_at" 或 "updated_at"
}

/// 高级搜索请求
#[derive(Debug, Deserialize)]
pub struct AdvancedSearchRequest {
    pub query: Option<String>,
    pub knowledge_base_id: Option<String>,
    pub tag_ids: Option<Vec<String>>,
    pub category_ids: Option<Vec<String>>,
    pub file_types: Option<Vec<String>>,
    pub authors: Option<Vec<String>>,
    pub statuses: Option<Vec<DocumentStatusRequest>>,
    pub date_range: Option<DateRangeFilterRequest>,
    pub match_all_tags: Option<bool>,
    pub match_all_categories: Option<bool>,
    pub sort_by: Option<SearchSortByRequest>,
    pub sort_order: Option<SearchSortOrderRequest>,
    pub limit: Option<u32>,
    pub offset: Option<u32>,
}

/// 高级搜索响应
#[derive(Debug, Serialize)]
pub struct AdvancedSearchResponse {
    pub documents: Vec<DocumentSummaryResponse>,
    pub total_count: i64,
    pub current_page: i64,
    pub total_pages: i64,
    pub has_more: bool,
    pub filters_applied: Vec<String>,
    pub search_params: SearchParamsResponse,
}

impl From<AdvancedSearchResult> for AdvancedSearchResponse {
    fn from(result: AdvancedSearchResult) -> Self {
        let current_page = result.current_page();
        let total_pages = result.total_pages();
        let has_more = result.has_more();
        let total_count = result.total_count;
        let filters_applied = result.filters_applied.clone();
        let search_params = SearchParamsResponse::from(result.search_params.clone());

        let documents: Vec<DocumentSummaryResponse> = result
            .documents
            .into_iter()
            .map(DocumentSummaryResponse::from)
            .collect();

        Self {
            documents,
            total_count,
            current_page,
            total_pages,
            has_more,
            filters_applied,
            search_params,
        }
    }
}

/// 搜索参数响应（用于返回给客户端）
#[derive(Debug, Serialize)]
pub struct SearchParamsResponse {
    pub query: Option<String>,
    pub knowledge_base_id: Option<String>,
    pub tag_ids: Option<Vec<String>>,
    pub category_ids: Option<Vec<String>>,
    pub file_types: Option<Vec<String>>,
    pub authors: Option<Vec<String>>,
    pub match_all_tags: bool,
    pub match_all_categories: bool,
    pub sort_by: String,
    pub sort_order: String,
    pub limit: u32,
    pub offset: u32,
}

impl From<wisdom_vault_core::services::AdvancedSearchParams> for SearchParamsResponse {
    fn from(params: wisdom_vault_core::services::AdvancedSearchParams) -> Self {
        Self {
            query: params.query,
            knowledge_base_id: params.knowledge_base_id,
            tag_ids: params.tag_ids,
            category_ids: params.category_ids,
            file_types: params.file_types,
            authors: params.authors,
            match_all_tags: params.match_all_tags,
            match_all_categories: params.match_all_categories,
            sort_by: format!("{:?}", params.sort_by).to_lowercase(),
            sort_order: format!("{:?}", params.sort_order).to_lowercase(),
            limit: params.limit.unwrap_or(50),
            offset: params.offset.unwrap_or(0),
        }
    }
}

/// 搜索建议查询参数
#[derive(Debug, Deserialize)]
pub struct SearchSuggestionsQuery {
    pub query: String,
    pub knowledge_base_id: Option<String>,
    pub limit: Option<u32>,
}

/// 搜索建议响应
#[derive(Debug, Serialize)]
pub struct SearchSuggestionsResponse {
    pub suggestions: Vec<String>,
}

/// 搜索统计响应
#[derive(Debug, Serialize)]
pub struct SearchStatisticsResponse {
    pub knowledge_base_id: Option<String>,
    pub total_documents: i64,
    pub indexed_documents: i64,
    pub processing_documents: i64,
    pub failed_documents: i64,
    pub archived_documents: i64,
    pub ready_documents: i64,
    pub success_rate: f64,
}

/// 导出搜索结果请求
#[derive(Debug, Deserialize)]
pub struct ExportSearchRequest {
    pub search_params: AdvancedSearchRequest,
    pub format: ExportFormat,
    pub include_content: Option<bool>,
    pub include_metadata: Option<bool>,
}

/// 导出格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportFormat {
    #[serde(rename = "csv")]
    Csv,
    #[serde(rename = "json")]
    Json,
    #[serde(rename = "excel")]
    Excel,
    #[serde(rename = "pdf")]
    Pdf,
}

/// 导出搜索结果响应
#[derive(Debug, Serialize)]
pub struct ExportSearchResponse {
    pub format: ExportFormat,
    pub total_exported: i64,
    pub download_url: String,
    pub expires_at: u64,
}

/// 搜索历史记录
#[derive(Debug, Serialize)]
pub struct SearchHistoryResponse {
    pub query: String,
    pub knowledge_base_id: Option<String>,
    pub filters_used: Vec<String>,
    pub results_count: i64,
    pub searched_at: u64,
}

/// 热门搜索响应
#[derive(Debug, Serialize)]
pub struct PopularSearchesResponse {
    pub searches: Vec<PopularSearchItem>,
    pub period: String, // "today", "week", "month"
}

/// 热门搜索项
#[derive(Debug, Serialize)]
pub struct PopularSearchItem {
    pub query: String,
    pub search_count: i64,
    pub last_searched: u64,
}

/// 搜索过滤器选项响应
#[derive(Debug, Serialize)]
pub struct SearchFilterOptionsResponse {
    pub knowledge_base_id: Option<String>,
    pub available_file_types: Vec<String>,
    pub available_authors: Vec<String>,
    pub available_statuses: Vec<String>,
    pub date_range_presets: Vec<DateRangePreset>,
}

/// 日期范围预设
#[derive(Debug, Serialize)]
pub struct DateRangePreset {
    pub name: String,
    pub label: String,
    pub start: Option<u64>,
    pub end: Option<u64>,
}

// ===== 关键词搜索 API 类型 =====

/// 关键词搜索请求
#[derive(Debug, Deserialize)]
pub struct KeywordSearchApiRequest {
    /// 查询文本
    pub query: String,
    /// 知识库ID过滤
    pub knowledge_base_id: Option<String>,
    /// 结果数量限制
    pub limit: Option<u32>,
    /// 结果偏移量
    pub offset: Option<u32>,
    /// 最小相关性得分阈值
    pub min_score: Option<f64>,
    /// 文档状态过滤
    pub status_filter: Option<Vec<DocumentStatusRequest>>,
    /// 是否启用查询扩展
    pub enable_query_expansion: Option<bool>,
}

/// 关键词搜索响应
#[derive(Debug, Serialize)]
pub struct KeywordSearchApiResponse {
    /// 搜索结果列表
    pub results: Vec<KeywordSearchResultResponse>,
    /// 总结果数量
    pub total_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 查询统计信息
    pub query_stats: QueryStatisticsResponse,
}

impl From<KeywordSearchResults> for KeywordSearchApiResponse {
    fn from(results: KeywordSearchResults) -> Self {
        Self {
            results: results.results.into_iter().map(Into::into).collect(),
            total_count: results.total_count,
            search_time_ms: results.search_time_ms,
            query_stats: results.query_stats.into(),
        }
    }
}

/// 关键词搜索结果项响应
#[derive(Debug, Serialize)]
pub struct KeywordSearchResultResponse {
    /// 文档信息
    pub document: DocumentSummaryResponse,
    /// BM25 相关性得分
    pub score: f64,
    /// 匹配的关键词
    pub matched_terms: Vec<String>,
    /// 词频统计
    pub term_frequencies: HashMap<String, u32>,
    /// 高亮片段
    pub highlights: Vec<String>,
}

impl From<wisdom_vault_core::services::KeywordSearchResult> for KeywordSearchResultResponse {
    fn from(result: wisdom_vault_core::services::KeywordSearchResult) -> Self {
        Self {
            document: result.document.into(),
            score: result.score,
            matched_terms: result.matched_terms,
            term_frequencies: result.term_frequencies,
            highlights: result.highlights,
        }
    }
}

/// 查询统计信息响应
#[derive(Debug, Serialize)]
pub struct QueryStatisticsResponse {
    /// 原始查询
    pub original_query: String,
    /// 处理后的查询词
    pub processed_terms: Vec<String>,
    /// 被过滤的停用词
    pub filtered_stop_words: Vec<String>,
    /// 查询词的IDF值
    pub term_idf_scores: HashMap<String, f64>,
    /// 平均文档长度
    pub avg_doc_length: f64,
}

impl From<wisdom_vault_core::services::QueryStatistics> for QueryStatisticsResponse {
    fn from(stats: wisdom_vault_core::services::QueryStatistics) -> Self {
        Self {
            original_query: stats.original_query,
            processed_terms: stats.processed_terms,
            filtered_stop_words: stats.filtered_stop_words,
            term_idf_scores: stats.term_idf_scores,
            avg_doc_length: stats.avg_doc_length,
        }
    }
}

/// 索引重建查询参数
#[derive(Debug, Deserialize)]
pub struct RebuildIndexQuery {
    /// 知识库ID（可选，为空时重建所有索引）
    pub knowledge_base_id: Option<String>,
}

/// 索引重建响应
#[derive(Debug, Serialize)]
pub struct IndexRebuildResponse {
    /// 任务ID
    pub task_id: String,
    /// 响应消息
    pub message: String,
    /// 预估完成时间（分钟）
    pub estimated_time_minutes: Option<u32>,
}

/// 关键词索引统计信息响应
#[derive(Debug, Serialize)]
pub struct KeywordIndexStatisticsResponse {
    /// 总文档数
    pub total_documents: u32,
    /// 总词项数
    pub total_terms: u32,
    /// 平均文档长度
    pub avg_doc_length: f64,
    /// 索引大小（MB）
    pub index_size_mb: f64,
    /// 最后更新时间
    pub last_updated: i64,
}

impl From<IndexStatistics> for KeywordIndexStatisticsResponse {
    fn from(stats: IndexStatistics) -> Self {
        Self {
            total_documents: stats.total_documents,
            total_terms: stats.total_terms,
            avg_doc_length: stats.avg_doc_length,
            index_size_mb: stats.index_size_mb,
            last_updated: stats.last_updated,
        }
    }
}

/// 索引任务状态响应
#[derive(Debug, Serialize)]
pub struct IndexTaskStatusResponse {
    /// 任务ID
    pub task_id: String,
    /// 任务类型
    pub task_type: String,
    /// 目标文档ID（可选）
    pub document_id: Option<String>,
    /// 知识库ID（可选）
    pub knowledge_base_id: Option<String>,
    /// 任务状态
    pub status: String,
    /// 创建时间
    pub created_at: i64,
    /// 开始时间
    pub started_at: Option<i64>,
    /// 完成时间
    pub completed_at: Option<i64>,
    /// 错误信息
    pub error_message: Option<String>,
    /// 处理的文档数量
    pub processed_documents: u32,
    /// 执行时长（秒）
    pub duration_seconds: Option<f64>,
}

impl From<IndexUpdateTask> for IndexTaskStatusResponse {
    fn from(task: IndexUpdateTask) -> Self {
        let duration_seconds =
            if let (Some(start), Some(end)) = (task.started_at, task.completed_at) {
                Some((end - start) as f64 / 1000.0)
            } else {
                None
            };

        Self {
            task_id: task.task_id,
            task_type: format!("{:?}", task.task_type),
            document_id: task.document_id,
            knowledge_base_id: task.knowledge_base_id,
            status: format!("{:?}", task.status),
            created_at: task.created_at,
            started_at: task.started_at,
            completed_at: task.completed_at,
            error_message: task.error_message,
            processed_documents: task.processed_documents,
            duration_seconds,
        }
    }
}

// ===== 搜索性能监控 API 类型 =====

/// 搜索性能指标响应
#[derive(Debug, Serialize)]
pub struct SearchPerformanceMetricsResponse {
    /// 时间窗口
    pub time_window: String,
    /// 总查询数
    pub total_queries: u64,
    /// 成功查询数
    pub successful_queries: u64,
    /// 失败查询数
    pub failed_queries: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 最大响应时间（毫秒）
    pub max_response_time_ms: u64,
    /// 最小响应时间（毫秒）
    pub min_response_time_ms: u64,
    /// 95%响应时间（毫秒）
    pub p95_response_time_ms: f64,
    /// 99%响应时间（毫秒）
    pub p99_response_time_ms: f64,
    /// 慢查询数量
    pub slow_queries_count: u64,
    /// 慢查询率
    pub slow_query_rate: f64,
    /// 平均结果数量
    pub avg_result_count: f64,
    /// 查询频率（每秒）
    pub queries_per_second: f64,
    /// 最后更新时间
    pub last_updated: i64,
}

impl From<SearchPerformanceMetrics> for SearchPerformanceMetricsResponse {
    fn from(metrics: SearchPerformanceMetrics) -> Self {
        Self {
            time_window: metrics.time_window,
            total_queries: metrics.total_queries,
            successful_queries: metrics.successful_queries,
            failed_queries: metrics.failed_queries,
            avg_response_time_ms: metrics.avg_response_time_ms,
            max_response_time_ms: metrics.max_response_time_ms,
            min_response_time_ms: metrics.min_response_time_ms,
            p95_response_time_ms: metrics.p95_response_time_ms,
            p99_response_time_ms: metrics.p99_response_time_ms,
            slow_queries_count: metrics.slow_queries_count,
            slow_query_rate: metrics.slow_query_rate,
            avg_result_count: metrics.avg_result_count,
            queries_per_second: metrics.queries_per_second,
            last_updated: metrics.last_updated,
        }
    }
}

/// 热门查询响应
#[derive(Debug, Serialize)]
pub struct PopularQueriesResponse {
    /// 热门查询列表
    pub queries: Vec<PopularQueryResponse>,
}

/// 热门查询项响应
#[derive(Debug, Serialize)]
pub struct PopularQueryResponse {
    /// 查询文本
    pub query_text: String,
    /// 查询次数
    pub query_count: u64,
    /// 平均响应时间
    pub avg_response_time_ms: f64,
    /// 平均结果数量
    pub avg_result_count: f64,
    /// 最后查询时间
    pub last_queried: i64,
}

impl From<PopularQuery> for PopularQueryResponse {
    fn from(query: PopularQuery) -> Self {
        Self {
            query_text: query.query_text,
            query_count: query.query_count,
            avg_response_time_ms: query.avg_response_time_ms,
            avg_result_count: query.avg_result_count,
            last_queried: query.last_queried,
        }
    }
}

/// 性能异常响应
#[derive(Debug, Serialize)]
pub struct SearchAnomaliesResponse {
    /// 异常列表
    pub anomalies: Vec<SearchAnomalyResponse>,
}

/// 单个性能异常响应
#[derive(Debug, Serialize)]
pub struct SearchAnomalyResponse {
    /// 异常ID
    pub anomaly_id: String,
    /// 异常类型
    pub anomaly_type: String,
    /// 异常描述
    pub description: String,
    /// 严重程度
    pub severity: String,
    /// 检测时间
    pub detected_at: i64,
    /// 相关查询记录
    pub related_queries: Vec<String>,
    /// 建议措施
    pub recommendations: Vec<String>,
}

impl From<SearchPerformanceAnomaly> for SearchAnomalyResponse {
    fn from(anomaly: SearchPerformanceAnomaly) -> Self {
        Self {
            anomaly_id: anomaly.anomaly_id,
            anomaly_type: format!("{:?}", anomaly.anomaly_type),
            description: anomaly.description,
            severity: format!("{:?}", anomaly.severity),
            detected_at: anomaly.detected_at,
            related_queries: anomaly.related_queries,
            recommendations: anomaly.recommendations,
        }
    }
}

/// 调优建议响应
#[derive(Debug, Serialize)]
pub struct TuningRecommendationsResponse {
    /// 建议列表
    pub recommendations: Vec<TuningRecommendationResponse>,
}

/// 单个调优建议响应
#[derive(Debug, Serialize)]
pub struct TuningRecommendationResponse {
    /// 建议ID
    pub recommendation_id: String,
    /// 建议类型
    pub recommendation_type: String,
    /// 建议标题
    pub title: String,
    /// 建议描述
    pub description: String,
    /// 预期效果
    pub expected_impact: String,
    /// 实施难度
    pub implementation_difficulty: String,
    /// 优先级
    pub priority: String,
    /// 相关指标
    pub related_metrics: Vec<String>,
    /// 生成时间
    pub generated_at: i64,
}

impl From<PerformanceTuningRecommendation> for TuningRecommendationResponse {
    fn from(rec: PerformanceTuningRecommendation) -> Self {
        Self {
            recommendation_id: rec.recommendation_id,
            recommendation_type: format!("{:?}", rec.recommendation_type),
            title: rec.title,
            description: rec.description,
            expected_impact: rec.expected_impact,
            implementation_difficulty: format!("{:?}", rec.implementation_difficulty),
            priority: format!("{:?}", rec.priority),
            related_metrics: rec.related_metrics,
            generated_at: rec.generated_at,
        }
    }
}

// ===== 语义向量搜索 API 类型 =====

/// 向量搜索请求
#[derive(Debug, Deserialize)]
pub struct VectorSearchApiRequest {
    /// 查询向量（直接提供）
    pub query_vector: Option<Vec<f32>>,
    /// 查询文本（需要向量化）
    pub query_text: Option<String>,
    /// 使用的嵌入模型ID
    pub model_id: Option<String>,
    /// 相似度阈值
    pub similarity_threshold: Option<f64>,
    /// 结果数量限制
    pub limit: Option<u32>,
    /// 知识库ID过滤
    pub knowledge_base_id: Option<String>,
    /// 模型名称过滤
    pub model_name_filter: Option<String>,
    /// 嵌入类型过滤
    pub embedding_type_filter: Option<String>,

    // 扩展的过滤条件
    /// 文档ID列表过滤
    pub document_ids: Option<Vec<String>>,
    /// 文档状态过滤
    pub document_status_filter: Option<Vec<DocumentStatusRequest>>,
    /// 文件类型过滤
    pub file_type_filter: Option<Vec<String>>,
    /// 作者过滤
    pub author_filter: Option<Vec<String>>,
    /// 日期范围过滤
    pub date_range_filter: Option<DateRangeFilterRequest>,
    /// 标签ID过滤
    pub tag_ids: Option<Vec<String>>,
    /// 分类ID过滤
    pub category_ids: Option<Vec<String>>,
    /// 是否匹配所有标签（AND操作）
    pub match_all_tags: Option<bool>,
    /// 是否匹配所有分类（AND操作）
    pub match_all_categories: Option<bool>,
    /// 向量维度范围过滤
    pub vector_dimension_range: Option<VectorDimensionRange>,
    /// 最小向量质量得分
    pub min_vector_quality: Option<f64>,
    /// 语言过滤
    pub language_filter: Option<Vec<String>>,
    /// 内容长度范围过滤
    pub content_length_range: Option<ContentLengthRange>,
}

/// 混合搜索请求
#[derive(Debug, Deserialize)]
pub struct HybridSearchApiRequest {
    /// 查询向量（直接提供）
    pub query_vector: Option<Vec<f32>>,
    /// 查询文本（用于向量化和文本搜索）
    pub query_text: String,
    /// 使用的嵌入模型ID
    pub model_id: Option<String>,
    /// 向量搜索权重
    pub vector_weight: Option<f64>,
    /// 文本搜索权重
    pub text_weight: Option<f64>,
    /// 相似度阈值
    pub similarity_threshold: Option<f64>,
    /// 结果数量限制
    pub limit: Option<u32>,
    /// 知识库ID过滤
    pub knowledge_base_id: Option<String>,
}

/// 向量搜索结果项响应
#[derive(Debug, Serialize)]
pub struct VectorSearchResultApiResponse {
    /// 文档嵌入信息
    pub document_id: String,
    /// 文档chunk ID
    pub chunk_id: Option<String>,
    /// 文档信息摘要
    pub document: DocumentSummaryResponse,
    /// 搜索得分
    pub score: f64,
    /// 搜索类型标识
    pub search_type: String,
    /// 相似度计算方式
    pub similarity_method: String,
    /// 使用的模型信息
    pub model_name: String,
    /// 向量维度
    pub vector_dimension: Option<usize>,
    /// 额外元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 向量搜索结果集响应
#[derive(Debug, Serialize)]
pub struct VectorSearchApiResponse {
    /// 搜索结果列表
    pub results: Vec<VectorSearchResultApiResponse>,
    /// 总结果数量（可能大于返回的结果数量）
    pub total_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 使用的搜索策略
    pub search_strategy: String,
    /// 查询向量维度
    pub query_vector_dimension: Option<usize>,
    /// 使用的相似度阈值
    pub similarity_threshold: f64,
    /// 额外的搜索元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

impl From<wisdom_vault_core::services::VectorSearchResults> for VectorSearchApiResponse {
    fn from(results: wisdom_vault_core::services::VectorSearchResults) -> Self {
        let search_results: Vec<VectorSearchResultApiResponse> = results
            .results
            .into_iter()
            .map(|result| VectorSearchResultApiResponse {
                document_id: result.embedding.chunk_id.clone(), // 使用chunk_id作为文档标识
                chunk_id: Some(result.embedding.chunk_id.clone()),
                document: DocumentSummaryResponse {
                    id: result.embedding.chunk_id,     // 使用chunk_id作为主键
                    knowledge_base_id: next_id(),      // 临时值，需要从chunk关联查询
                    title: "向量搜索结果".to_string(), // 需要从关联的chunk/document中获取
                    summary: None,
                    file_type: "unknown".to_string(),
                    file_size: 0,
                    original_filename: None,
                    status: "indexed".to_string(),
                    uploaded_by: next_id(), // 临时值，需要从关联查询
                    created_at: result.embedding.created_at,
                    updated_at: result.embedding.created_at, // DocumentEmbedding没有updated_at字段
                },
                score: result.score,
                search_type: format!("{:?}", result.search_type),
                similarity_method: "cosine".to_string(), // 默认余弦相似度
                model_name: result.embedding.model_name,
                vector_dimension: Some(result.embedding.embedding.len()),
                metadata: result.metadata,
            })
            .collect();

        Self {
            results: search_results,
            total_count: results.total_count.unwrap_or(0),
            search_time_ms: results.search_time_ms,
            search_strategy: results.search_strategy,
            query_vector_dimension: None, // 可以从请求中获取
            similarity_threshold: 0.7,    // 默认值，实际应从请求中获取
            metadata: results.metadata,
        }
    }
}

/// 向量搜索统计信息响应
#[derive(Debug, Serialize)]
pub struct VectorSearchStatisticsApiResponse {
    /// 向量总数
    pub total_vectors: u64,
    /// 平均向量维度
    pub avg_vector_dimension: f64,
    /// 索引大小（MB）
    pub index_size_mb: f64,
    /// 最后更新时间
    pub last_updated: u64,
    /// 搜索性能统计
    pub search_performance: VectorSearchPerformanceResponse,
}

/// 向量搜索性能统计响应
#[derive(Debug, Serialize)]
pub struct VectorSearchPerformanceResponse {
    /// 总搜索次数
    pub total_searches: u64,
    /// 向量搜索次数
    pub vector_searches: u64,
    /// 混合搜索次数
    pub hybrid_searches: u64,
    /// 平均搜索时间（毫秒）
    pub avg_search_time_ms: f64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 缓存未命中次数
    pub cache_misses: u64,
    /// 缓存命中率
    pub cache_hit_rate: f64,
}

impl From<wisdom_vault_core::services::SearchStatistics> for VectorSearchPerformanceResponse {
    fn from(stats: wisdom_vault_core::services::SearchStatistics) -> Self {
        let cache_hit_rate = if stats.cache_hits + stats.cache_misses > 0 {
            stats.cache_hits as f64 / (stats.cache_hits + stats.cache_misses) as f64
        } else {
            0.0
        };

        Self {
            total_searches: stats.total_searches,
            vector_searches: stats.vector_searches,
            hybrid_searches: stats.hybrid_searches,
            avg_search_time_ms: stats.avg_search_time_ms,
            cache_hits: stats.cache_hits,
            cache_misses: stats.cache_misses,
            cache_hit_rate,
        }
    }
}

/// 语义搜索配置请求
#[derive(Debug, Deserialize)]
pub struct SemanticSearchConfigRequest {
    /// 默认相似度阈值
    pub default_similarity_threshold: Option<f64>,
    /// 默认搜索结果数量限制
    pub default_limit: Option<u32>,
    /// 最大搜索结果数量限制
    pub max_limit: Option<u32>,
    /// 混合搜索默认向量权重
    pub default_vector_weight: Option<f64>,
    /// 混合搜索默认文本权重
    pub default_text_weight: Option<f64>,
    /// 启用搜索结果缓存
    pub enable_search_cache: Option<bool>,
    /// 搜索缓存TTL（秒）
    pub search_cache_ttl: Option<u64>,
}

/// 向量索引重建请求
#[derive(Debug, Deserialize)]
pub struct VectorIndexRebuildRequest {
    /// 知识库ID（可选，为空时重建所有索引）
    pub knowledge_base_id: Option<String>,
    /// 是否强制重建
    pub force_rebuild: Option<bool>,
}

/// 向量索引重建响应
#[derive(Debug, Serialize)]
pub struct VectorIndexRebuildResponse {
    /// 任务ID
    pub task_id: String,
    /// 响应消息
    pub message: String,
    /// 预估完成时间（分钟）
    pub estimated_time_minutes: Option<u32>,
    /// 涉及的向量数量
    pub affected_vectors_count: Option<u64>,
}

/// 向量存储优化响应
#[derive(Debug, Serialize)]
pub struct VectorStorageOptimizationResponse {
    /// 优化前的存储大小（MB）
    pub before_size_mb: f64,
    /// 优化后的存储大小（MB）
    pub after_size_mb: f64,
    /// 节省的存储空间（MB）
    pub saved_size_mb: f64,
    /// 清理的向量数量
    pub cleaned_vectors: u64,
    /// 优化用时（秒）
    pub optimization_time_seconds: f64,
    /// 优化消息
    pub message: String,
}

// ===== 扩展过滤器类型 =====

/// 向量维度范围过滤
#[derive(Debug, Deserialize)]
pub struct VectorDimensionRange {
    /// 最小维度
    pub min_dimension: Option<usize>,
    /// 最大维度
    pub max_dimension: Option<usize>,
}

/// 内容长度范围过滤
#[derive(Debug, Deserialize)]
pub struct ContentLengthRange {
    /// 最小内容长度（字符数）
    pub min_length: Option<usize>,
    /// 最大内容长度（字符数）
    pub max_length: Option<usize>,
}

/// 高级向量搜索过滤器
#[derive(Debug, Deserialize)]
pub struct AdvancedVectorSearchFilters {
    /// 相似度得分范围
    pub score_range: Option<ScoreRange>,
    /// 向量稀疏性过滤
    pub sparsity_range: Option<SparsityRange>,
    /// 文档块大小过滤
    pub chunk_size_range: Option<ChunkSizeRange>,
    /// 排除已搜索的文档ID
    pub exclude_document_ids: Option<Vec<String>>,
    /// 仅包含已验证的文档
    pub verified_only: Option<bool>,
    /// 按最后修改时间过滤
    pub last_modified_after: Option<u64>,
    /// 按创建时间过滤
    pub created_after: Option<u64>,
}

/// 相似度得分范围
#[derive(Debug, Deserialize)]
pub struct ScoreRange {
    /// 最小得分
    pub min_score: Option<f64>,
    /// 最大得分
    pub max_score: Option<f64>,
}

/// 向量稀疏性范围
#[derive(Debug, Deserialize)]
pub struct SparsityRange {
    /// 最小稀疏性（0.0-1.0）
    pub min_sparsity: Option<f64>,
    /// 最大稀疏性（0.0-1.0）
    pub max_sparsity: Option<f64>,
}

/// 文档块大小范围
#[derive(Debug, Deserialize)]
pub struct ChunkSizeRange {
    /// 最小块大小（字节）
    pub min_size: Option<usize>,
    /// 最大块大小（字节）
    pub max_size: Option<usize>,
}

/// 扩展的向量搜索请求
#[derive(Debug, Deserialize)]
pub struct ExtendedVectorSearchRequest {
    /// 基础搜索请求
    #[serde(flatten)]
    pub base_request: VectorSearchApiRequest,
    /// 高级过滤器
    pub advanced_filters: Option<AdvancedVectorSearchFilters>,
    /// 搜索策略配置
    pub search_strategy: Option<SearchStrategyConfig>,
    /// 结果后处理配置
    pub post_processing: Option<PostProcessingConfig>,
}

/// 搜索策略配置
#[derive(Debug, Deserialize)]
pub struct SearchStrategyConfig {
    /// 是否启用多样性过滤
    pub enable_diversity_filtering: Option<bool>,
    /// 多样性阈值
    pub diversity_threshold: Option<f64>,
    /// 是否启用重排序
    pub enable_reranking: Option<bool>,
    /// 重排序策略
    pub reranking_strategy: Option<String>,
    /// 搜索模式
    pub search_mode: Option<SearchMode>,
}

/// 搜索模式
#[derive(Debug, Deserialize)]
pub enum SearchMode {
    /// 精确搜索（高精度，低召回率）
    #[serde(rename = "precise")]
    Precise,
    /// 平衡搜索（平衡精度和召回率）
    #[serde(rename = "balanced")]
    Balanced,
    /// 广泛搜索（低精度，高召回率）
    #[serde(rename = "broad")]
    Broad,
}

/// 结果后处理配置
#[derive(Debug, Deserialize)]
pub struct PostProcessingConfig {
    /// 是否启用得分标准化
    pub enable_score_normalization: Option<bool>,
    /// 是否添加解释信息
    pub include_explanations: Option<bool>,
    /// 是否计算额外的相似度度量
    pub compute_additional_metrics: Option<bool>,
    /// 结果聚类配置
    pub clustering_config: Option<ClusteringConfig>,
}

/// 结果聚类配置
#[derive(Debug, Deserialize)]
pub struct ClusteringConfig {
    /// 是否启用聚类
    pub enabled: Option<bool>,
    /// 聚类数量
    pub cluster_count: Option<usize>,
    /// 聚类算法
    pub algorithm: Option<String>,
}

// ===== 混合搜索 API 类型 =====

/// 混合搜索请求（扩展版本）
#[derive(Debug, Deserialize)]
pub struct HybridSearchRequest {
    /// 查询向量（直接提供）
    pub query_vector: Option<Vec<f32>>,
    /// 查询文本（用于向量化和文本搜索）
    pub query_text: String,
    /// 使用的嵌入模型ID
    pub model_id: Option<String>,
    /// 向量搜索权重
    pub vector_weight: Option<f64>,
    /// 文本搜索权重
    pub text_weight: Option<f64>,
    /// 相似度阈值
    pub similarity_threshold: Option<f64>,
    /// 结果数量限制
    pub limit: Option<u32>,
    /// 知识库ID过滤
    pub knowledge_base_id: Option<String>,
    /// 结果融合策略
    pub fusion_strategy: Option<ResultFusionStrategyRequest>,
    /// 是否启用个性化推荐
    pub enable_personalization: Option<bool>,
    /// 用户ID（用于个性化推荐）
    pub user_id: Option<String>,
    /// 搜索上下文信息
    pub search_context: Option<SearchContextRequest>,
}

/// 结果融合策略请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResultFusionStrategyRequest {
    /// 加权线性组合
    #[serde(rename = "weighted_linear_combination")]
    WeightedLinearCombination,
    /// 排序融合（RRF）
    #[serde(rename = "reciprocal_rank_fusion")]
    ReciprocalRankFusion,
    /// 分数归一化融合
    #[serde(rename = "normalized_score_fusion")]
    NormalizedScoreFusion,
    /// 混合重排序
    #[serde(rename = "hybrid_reranking")]
    HybridReranking,
    /// 动态权重调整
    #[serde(rename = "dynamic_weight_adjustment")]
    DynamicWeightAdjustment,
}

impl From<ResultFusionStrategyRequest>
    for wisdom_vault_core::services::result_fusion_algorithms::ResultFusionStrategy
{
    fn from(strategy: ResultFusionStrategyRequest) -> Self {
        match strategy {
            ResultFusionStrategyRequest::WeightedLinearCombination => {
                Self::WeightedLinearCombination
            }
            ResultFusionStrategyRequest::ReciprocalRankFusion => Self::ReciprocalRankFusion,
            ResultFusionStrategyRequest::NormalizedScoreFusion => Self::NormalizedScoreFusion,
            ResultFusionStrategyRequest::HybridReranking => Self::HybridReranking,
            ResultFusionStrategyRequest::DynamicWeightAdjustment => Self::DynamicWeightAdjustment,
        }
    }
}

/// 搜索上下文请求
#[derive(Debug, Deserialize)]
pub struct SearchContextRequest {
    /// 查询复杂度（0.0-1.0）
    pub query_complexity: Option<f64>,
    /// 查询类型
    pub query_type: Option<QueryTypeRequest>,
    /// 查询意图
    pub query_intent: Option<QueryIntentRequest>,
    /// 语言类型
    pub language: Option<String>,
    /// 用户偏好设置
    pub user_preferences: Option<UserPreferencesRequest>,
}

/// 查询类型请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QueryTypeRequest {
    /// 事实查询
    #[serde(rename = "factual")]
    Factual,
    /// 分析查询
    #[serde(rename = "analytical")]
    Analytical,
    /// 探索性查询
    #[serde(rename = "exploratory")]
    Exploratory,
    /// 导航查询
    #[serde(rename = "navigational")]
    Navigational,
    /// 交易查询
    #[serde(rename = "transactional")]
    Transactional,
}

impl From<QueryTypeRequest> for wisdom_vault_core::services::QueryType {
    fn from(query_type: QueryTypeRequest) -> Self {
        match query_type {
            QueryTypeRequest::Factual => Self::Factual,
            QueryTypeRequest::Analytical => Self::Analytical,
            QueryTypeRequest::Exploratory => Self::Exploratory,
            QueryTypeRequest::Navigational => Self::Navigational,
            QueryTypeRequest::Transactional => Self::Transactional,
        }
    }
}

/// 查询意图请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QueryIntentRequest {
    /// 查找信息
    #[serde(rename = "information")]
    Information,
    /// 比较分析
    #[serde(rename = "comparison")]
    Comparison,
    /// 操作指导
    #[serde(rename = "how_to")]
    HowTo,
    /// 定义解释
    #[serde(rename = "definition")]
    Definition,
    /// 列表枚举
    #[serde(rename = "list")]
    List,
    /// 其他
    #[serde(rename = "other")]
    Other,
}

impl From<QueryIntentRequest> for wisdom_vault_core::services::QueryIntent {
    fn from(intent: QueryIntentRequest) -> Self {
        match intent {
            QueryIntentRequest::Information => Self::Information,
            QueryIntentRequest::Comparison => Self::Comparison,
            QueryIntentRequest::HowTo => Self::HowTo,
            QueryIntentRequest::Definition => Self::Definition,
            QueryIntentRequest::List => Self::List,
            QueryIntentRequest::Other => Self::Other,
        }
    }
}

/// 用户偏好设置请求
#[derive(Debug, Deserialize)]
pub struct UserPreferencesRequest {
    /// 新鲜度偏好（0.0-1.0）
    pub freshness_preference: Option<f64>,
    /// 权威性偏好（0.0-1.0）
    pub authority_preference: Option<f64>,
    /// 详细程度偏好（0.0-1.0）
    pub detail_preference: Option<f64>,
    /// 语言偏好（0.0-1.0）
    pub language_preference: Option<f64>,
    /// 文档类型偏好
    pub document_type_preferences: Option<HashMap<String, f64>>,
}

/// 混合搜索结果响应
#[derive(Debug, Serialize)]
pub struct HybridSearchApiResponse {
    /// 搜索结果列表
    pub results: Vec<HybridSearchResultApiResponse>,
    /// 总结果数量
    pub total_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 使用的融合策略
    pub fusion_strategy: String,
    /// 关键词搜索统计
    pub keyword_stats: Option<KeywordSearchStatsResponse>,
    /// 向量搜索统计
    pub vector_stats: Option<VectorSearchStatsResponse>,
    /// 融合过程统计
    pub fusion_stats: FusionStatsResponse,
    /// 个性化推荐信息
    pub personalization_info: Option<PersonalizationInfoResponse>,
    /// 额外的搜索元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

impl From<wisdom_vault_core::services::HybridSearchResults> for HybridSearchApiResponse {
    fn from(results: wisdom_vault_core::services::HybridSearchResults) -> Self {
        Self {
            results: results.results.into_iter().map(Into::into).collect(),
            total_count: results.total_count,
            search_time_ms: results.search_time_ms,
            fusion_strategy: format!("{:?}", results.fusion_strategy),
            keyword_stats: results.keyword_stats.map(Into::into),
            vector_stats: results.vector_stats.map(Into::into),
            fusion_stats: results.fusion_stats.into(),
            personalization_info: None, // TODO: 实现个性化信息映射
            metadata: results.metadata,
        }
    }
}

/// 混合搜索结果项响应
#[derive(Debug, Serialize)]
pub struct HybridSearchResultApiResponse {
    /// 文档信息
    pub document: DocumentSummaryResponse,
    /// 融合后的综合得分
    pub final_score: f64,
    /// 关键词搜索得分
    pub keyword_score: Option<f64>,
    /// 向量搜索得分
    pub vector_score: Option<f64>,
    /// 搜索类型标识
    pub search_sources: Vec<String>,
    /// 匹配的关键词（来自关键词搜索）
    pub matched_terms: Vec<String>,
    /// 高亮片段
    pub highlights: Vec<String>,
    /// 相似度计算方式
    pub similarity_method: String,
    /// 使用的模型信息
    pub model_name: Option<String>,
    /// 排序因子详情
    pub ranking_factors: RankingFactorsResponse,
    /// 个性化调整信息
    pub personalization_adjustment: Option<PersonalizationAdjustmentResponse>,
    /// 额外元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

impl From<wisdom_vault_core::services::HybridSearchResult> for HybridSearchResultApiResponse {
    fn from(result: wisdom_vault_core::services::HybridSearchResult) -> Self {
        Self {
            document: result.document.into(),
            final_score: result.final_score,
            keyword_score: result.keyword_score,
            vector_score: result.vector_score,
            search_sources: result
                .search_sources
                .into_iter()
                .map(|s| format!("{:?}", s))
                .collect(),
            matched_terms: result.matched_terms,
            highlights: result.highlights,
            similarity_method: result.similarity_method,
            model_name: result.model_name,
            ranking_factors: result.ranking_factors.into(),
            personalization_adjustment: None, // TODO: 实现个性化调整映射
            metadata: result.metadata,
        }
    }
}

/// 排序因子详情响应
#[derive(Debug, Serialize)]
pub struct RankingFactorsResponse {
    /// 文本相关性得分
    pub text_relevance: f64,
    /// 语义相似度得分
    pub semantic_similarity: f64,
    /// 时间衰减因子
    pub time_decay: f64,
    /// 文档质量得分
    pub document_quality: f64,
    /// 用户偏好权重
    pub user_preference: f64,
    /// 流行度得分
    pub popularity_score: f64,
}

impl From<wisdom_vault_core::services::RankingFactors> for RankingFactorsResponse {
    fn from(factors: wisdom_vault_core::services::RankingFactors) -> Self {
        Self {
            text_relevance: factors.text_relevance,
            semantic_similarity: factors.semantic_similarity,
            time_decay: factors.time_decay,
            document_quality: factors.document_quality,
            user_preference: factors.user_preference,
            popularity_score: factors.popularity_score,
        }
    }
}

/// 关键词搜索统计响应
#[derive(Debug, Serialize)]
pub struct KeywordSearchStatsResponse {
    /// 结果数量
    pub result_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 匹配的查询词数量
    pub matched_terms_count: usize,
    /// 平均BM25得分
    pub avg_bm25_score: f64,
}

impl From<wisdom_vault_core::services::KeywordSearchStats> for KeywordSearchStatsResponse {
    fn from(stats: wisdom_vault_core::services::KeywordSearchStats) -> Self {
        Self {
            result_count: stats.result_count,
            search_time_ms: stats.search_time_ms,
            matched_terms_count: stats.matched_terms_count,
            avg_bm25_score: stats.avg_bm25_score,
        }
    }
}

/// 向量搜索统计响应
#[derive(Debug, Serialize)]
pub struct VectorSearchStatsResponse {
    /// 结果数量
    pub result_count: u64,
    /// 搜索用时（毫秒）
    pub search_time_ms: u64,
    /// 使用的模型名称
    pub model_name: String,
    /// 平均相似度得分
    pub avg_similarity_score: f64,
    /// 查询向量维度
    pub query_vector_dimension: usize,
}

impl From<wisdom_vault_core::services::VectorSearchStats> for VectorSearchStatsResponse {
    fn from(stats: wisdom_vault_core::services::VectorSearchStats) -> Self {
        Self {
            result_count: stats.result_count,
            search_time_ms: stats.search_time_ms,
            model_name: stats.model_name,
            avg_similarity_score: stats.avg_similarity_score,
            query_vector_dimension: stats.query_vector_dimension,
        }
    }
}

/// 融合过程统计响应
#[derive(Debug, Serialize)]
pub struct FusionStatsResponse {
    /// 关键词结果数量
    pub keyword_results_count: u64,
    /// 向量结果数量
    pub vector_results_count: u64,
    /// 重叠结果数量
    pub overlap_results_count: u64,
    /// 融合用时（毫秒）
    pub fusion_time_ms: u64,
    /// 应用的权重
    pub applied_weights: FusionWeightsResponse,
}

impl From<wisdom_vault_core::services::FusionStats> for FusionStatsResponse {
    fn from(stats: wisdom_vault_core::services::FusionStats) -> Self {
        Self {
            keyword_results_count: stats.keyword_results_count,
            vector_results_count: stats.vector_results_count,
            overlap_results_count: stats.overlap_results_count,
            fusion_time_ms: stats.fusion_time_ms,
            applied_weights: stats.applied_weights.into(),
        }
    }
}

/// 融合权重响应
#[derive(Debug, Serialize)]
pub struct FusionWeightsResponse {
    /// 关键词权重
    pub keyword_weight: f64,
    /// 向量权重
    pub vector_weight: f64,
    /// 时间权重
    pub time_weight: f64,
    /// 质量权重
    pub quality_weight: f64,
}

impl From<wisdom_vault_core::services::FusionWeights> for FusionWeightsResponse {
    fn from(weights: wisdom_vault_core::services::FusionWeights) -> Self {
        Self {
            keyword_weight: weights.keyword_weight,
            vector_weight: weights.vector_weight,
            time_weight: weights.time_weight,
            quality_weight: weights.quality_weight,
        }
    }
}

/// 个性化推荐信息响应
#[derive(Debug, Serialize)]
pub struct PersonalizationInfoResponse {
    /// 是否应用了个性化调整
    pub personalization_applied: bool,
    /// 用户画像摘要
    pub user_profile_summary: Option<UserProfileSummaryResponse>,
    /// 推荐理由
    pub recommendation_reasons: Vec<RecommendationReasonResponse>,
}

/// 用户画像摘要响应
#[derive(Debug, Serialize)]
pub struct UserProfileSummaryResponse {
    /// 主要兴趣主题
    pub top_interests: Vec<(String, f64)>,
    /// 偏好文档类型
    pub preferred_types: Vec<(String, f64)>,
    /// 搜索活跃度
    pub search_activity_level: String,
    /// 专业程度
    pub expertise_level: String,
}

/// 推荐理由响应
#[derive(Debug, Serialize)]
pub struct RecommendationReasonResponse {
    /// 文档ID
    pub document_id: String,
    /// 推荐理由类型
    pub reason_type: String,
    /// 理由描述
    pub description: String,
    /// 置信度
    pub confidence: f64,
}

/// 个性化调整信息响应
#[derive(Debug, Serialize)]
pub struct PersonalizationAdjustmentResponse {
    /// 原始得分
    pub original_score: f64,
    /// 调整后得分
    pub adjusted_score: f64,
    /// 调整因子
    pub adjustment_factors: AdjustmentFactorsResponse,
}

/// 调整因子响应
#[derive(Debug, Serialize)]
pub struct AdjustmentFactorsResponse {
    /// 兴趣匹配度
    pub interest_match: f64,
    /// 历史偏好
    pub historical_preference: f64,
    /// 时间偏好
    pub time_preference: f64,
    /// 相似用户影响
    pub similar_user_influence: f64,
}

/// 混合搜索配置请求
#[derive(Debug, Deserialize)]
pub struct HybridSearchConfigRequest {
    /// 默认关键词搜索权重
    pub default_keyword_weight: Option<f64>,
    /// 默认向量搜索权重
    pub default_vector_weight: Option<f64>,
    /// 默认相似度阈值
    pub default_similarity_threshold: Option<f64>,
    /// 默认结果数量限制
    pub default_limit: Option<u32>,
    /// 最大结果数量限制
    pub max_limit: Option<u32>,
    /// 启用结果缓存
    pub enable_result_cache: Option<bool>,
    /// 缓存TTL（秒）
    pub cache_ttl_seconds: Option<u64>,
    /// 启用个性化推荐
    pub enable_personalization: Option<bool>,
    /// 最大并发搜索数
    pub max_concurrent_searches: Option<usize>,
    /// 结果融合策略
    pub default_fusion_strategy: Option<ResultFusionStrategyRequest>,
}

/// 混合搜索统计信息响应
#[derive(Debug, Serialize)]
pub struct HybridSearchStatisticsResponse {
    /// 混合搜索性能统计
    pub performance_stats: HybridSearchPerformanceStatsResponse,
    /// 缓存统计
    pub cache_stats: Option<CacheStatisticsResponse>,
    /// 个性化统计
    pub personalization_stats: Option<PersonalizationStatisticsResponse>,
}

/// 混合搜索性能统计响应
#[derive(Debug, Serialize)]
pub struct HybridSearchPerformanceStatsResponse {
    /// 总搜索次数
    pub total_searches: u64,
    /// 成功搜索次数
    pub successful_searches: u64,
    /// 失败搜索次数
    pub failed_searches: u64,
    /// 平均搜索时间（毫秒）
    pub avg_search_time_ms: f64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 缓存未命中次数
    pub cache_misses: u64,
    /// 各融合策略使用次数
    pub fusion_strategy_usage: HashMap<String, u64>,
    /// 平均结果数量
    pub avg_result_count: f64,
}

/// 缓存统计响应
#[derive(Debug, Serialize)]
pub struct CacheStatisticsResponse {
    /// 总请求数
    pub total_requests: u64,
    /// L1缓存命中数
    pub l1_hits: u64,
    /// L2缓存命中数
    pub l2_hits: u64,
    /// 缓存未命中数
    pub cache_misses: u64,
    /// 缓存命中率
    pub hit_rate: f64,
    /// 平均响应时间（微秒）
    pub avg_response_time_us: f64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: u64,
}

/// 个性化统计响应
#[derive(Debug, Serialize)]
pub struct PersonalizationStatisticsResponse {
    /// 启用个性化的搜索次数
    pub personalized_searches: u64,
    /// 个性化调整生效的搜索次数
    pub adjustments_applied: u64,
    /// 平均个性化提升得分
    pub avg_personalization_boost: f64,
    /// 用户行为记录数量
    pub user_behavior_records: u64,
}
