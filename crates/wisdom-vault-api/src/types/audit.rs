use serde::{Deserialize, Serialize};
use utoipa::{IntoParams, ToSchema};
use validator::Validate;

/// 审计日志查询请求参数
#[derive(Debug, Deserialize, Validate, ToSchema, IntoParams)]
pub struct AuditLogQueryRequest {
    /// 页码，从1开始
    #[validate(range(min = 1))]
    pub page: Option<u32>,

    /// 每页数量，默认20，最大100
    #[validate(range(min = 1, max = 100))]
    pub page_size: Option<u32>,

    /// 开始时间
    pub start_time: Option<i64>,

    /// 结束时间
    pub end_time: Option<i64>,

    /// 用户ID过滤
    pub user_id: Option<String>,

    /// 用户名过滤
    pub username: Option<String>,

    /// 操作类型过滤（字符串枚举）
    pub action: Option<String>,

    /// 操作结果过滤（success/failure/denied）
    pub result: Option<String>,

    /// 资源类型过滤
    pub resource_type: Option<String>,

    /// 资源ID过滤
    pub resource_id: Option<String>,

    /// IP地址过滤
    pub ip_address: Option<String>,

    /// 请求路径过滤
    pub request_path: Option<String>,

    /// 搜索关键词（支持模糊搜索）
    pub search: Option<String>,

    /// 排序字段
    pub sort_by: Option<AuditSortField>,

    /// 排序顺序
    pub sort_order: Option<SortOrder>,
}

/// 审计日志排序字段
#[derive(Debug, Deserialize, Serialize, ToSchema, Clone, Copy)]
#[serde(rename_all = "snake_case")]
pub enum AuditSortField {
    Timestamp,
    Username,
    Action,
    Result,
    IpAddress,
    RequestPath,
}

/// 排序顺序
#[derive(Debug, Deserialize, Serialize, ToSchema, Clone, Copy)]
#[serde(rename_all = "snake_case")]
pub enum SortOrder {
    Asc,
    Desc,
}

/// 审计日志响应项
#[derive(Debug, Serialize, ToSchema)]
pub struct AuditLogResponse {
    /// 事件ID
    pub event_id: String,

    /// 时间戳
    pub timestamp: i64,

    /// 用户ID
    pub user_id: Option<String>,

    /// 用户名
    pub username: Option<String>,

    /// 会话ID
    pub session_id: Option<String>,

    /// 操作类型
    pub action: String,

    /// 操作结果（success/failure/denied）
    pub result: String,

    /// 资源类型
    pub resource_type: Option<String>,

    /// 资源ID
    pub resource_id: Option<String>,

    /// 客户端IP地址
    pub ip_address: Option<String>,

    /// 用户代理
    pub user_agent: Option<String>,

    /// HTTP方法
    pub http_method: Option<String>,

    /// 请求路径
    pub request_path: Option<String>,

    /// HTTP状态码
    pub status_code: Option<u16>,

    /// 错误消息
    pub error_message: Option<String>,

    /// 处理时间（毫秒）
    pub duration_ms: Option<u64>,

    /// 额外的元数据
    pub metadata: serde_json::Value,
}

/// 分页审计日志响应
#[derive(Debug, Serialize, ToSchema)]
pub struct PagedAuditLogsResponse {
    /// 审计日志列表
    pub logs: Vec<AuditLogResponse>,

    /// 分页信息
    pub pagination: PaginationInfo,
}

/// 分页信息
#[derive(Debug, Serialize, ToSchema)]
pub struct PaginationInfo {
    /// 当前页码
    pub page: u32,

    /// 每页数量
    pub page_size: u32,

    /// 总页数
    pub total_pages: u32,

    /// 总记录数
    pub total_count: u64,

    /// 是否有上一页
    pub has_previous: bool,

    /// 是否有下一页
    pub has_next: bool,
}

/// 审计统计请求参数
#[derive(Debug, Deserialize, Validate, ToSchema, IntoParams)]
pub struct AuditStatsRequest {
    /// 开始时间
    pub start_time: Option<i64>,

    /// 结束时间
    pub end_time: Option<i64>,

    /// 统计维度
    pub group_by: Option<AuditStatsGroupBy>,

    /// 时间间隔（用于时间序列统计）
    pub interval: Option<TimeInterval>,
}

/// 统计分组维度
#[derive(Debug, Deserialize, Serialize, ToSchema)]
#[serde(rename_all = "snake_case")]
pub enum AuditStatsGroupBy {
    Action,
    Result,
    User,
    IpAddress,
    ResourceType,
    Hour,
    Day,
    Week,
    Month,
}

/// 时间间隔
#[derive(Debug, Deserialize, Serialize, ToSchema)]
#[serde(rename_all = "snake_case")]
pub enum TimeInterval {
    Hour,
    Day,
    Week,
    Month,
}

/// 审计统计响应
#[derive(Debug, Serialize, ToSchema)]
pub struct AuditStatsResponse {
    /// 总操作数
    pub total_operations: u64,

    /// 成功操作数
    pub successful_operations: u64,

    /// 失败操作数
    pub failed_operations: u64,

    /// 拒绝操作数
    pub denied_operations: u64,

    /// 活跃用户数
    pub active_users: u64,

    /// 操作类型统计
    pub action_stats: Vec<ActionStat>,

    /// 用户活动统计
    pub user_stats: Vec<UserStat>,

    /// 时间序列数据
    pub time_series: Option<Vec<TimeSeriesPoint>>,
}

/// 操作统计
#[derive(Debug, Serialize, ToSchema)]
pub struct ActionStat {
    /// 操作类型（字符串枚举）
    pub action: String,

    /// 操作次数
    pub count: u64,

    /// 成功率
    pub success_rate: f64,
}

/// 用户统计
#[derive(Debug, Serialize, ToSchema)]
pub struct UserStat {
    /// 用户ID
    pub user_id: String,

    /// 用户名
    pub username: String,

    /// 操作次数
    pub operation_count: u64,

    /// 最后活动时间（毫秒时间戳）
    pub last_activity: i64,
}

/// 时间序列数据点
#[derive(Debug, Serialize, ToSchema)]
pub struct TimeSeriesPoint {
    /// 时间戳（毫秒）
    pub timestamp: i64,

    /// 操作数量
    pub count: u64,

    /// 成功数量
    pub success_count: u64,

    /// 失败数量
    pub failure_count: u64,
}

/// 用户活动查询请求
#[derive(Debug, Deserialize, Validate, ToSchema, IntoParams)]
pub struct UserActivityRequest {
    /// 用户ID
    pub user_id: String,

    /// 开始时间（毫秒时间戳）
    pub start_time: Option<i64>,

    /// 结束时间（毫秒时间戳）
    pub end_time: Option<i64>,

    /// 页码
    #[validate(range(min = 1))]
    pub page: Option<u32>,

    /// 每页数量
    #[validate(range(min = 1, max = 100))]
    pub page_size: Option<u32>,
}

/// 用户活动响应
#[derive(Debug, Serialize, ToSchema)]
pub struct UserActivityResponse {
    /// 用户基本信息
    pub user_info: UserInfo,

    /// 活动统计
    pub activity_stats: UserActivityStats,

    /// 活动列表
    pub activities: Vec<AuditLogResponse>,

    /// 分页信息
    pub pagination: PaginationInfo,
}

/// 用户基本信息
#[derive(Debug, Serialize, ToSchema)]
pub struct UserInfo {
    /// 用户ID
    pub user_id: String,

    /// 用户名
    pub username: String,

    /// 全名
    pub full_name: Option<String>,

    /// 邮箱
    pub email: String,
}

/// 用户活动统计
#[derive(Debug, Serialize, ToSchema)]
pub struct UserActivityStats {
    /// 总活动次数
    pub total_activities: u64,

    /// 成功操作数
    pub successful_operations: u64,

    /// 失败操作数
    pub failed_operations: u64,

    /// 最常用操作
    pub most_common_actions: Vec<ActionStat>,

    /// 首次活动时间（毫秒时间戳）
    pub first_activity: Option<i64>,

    /// 最后活动时间（毫秒时间戳）
    pub last_activity: Option<i64>,
}

/// 系统监控响应
#[derive(Debug, Serialize, ToSchema)]
pub struct SystemMonitorResponse {
    /// 审计系统状态
    pub audit_system_status: SystemStatus,

    /// Vector状态
    pub vector_status: SystemStatus,

    /// OpenObserve连接状态
    pub openobserve_status: SystemStatus,

    /// 最近24小时统计
    pub last_24h_stats: AuditStatsResponse,

    /// 磁盘使用情况
    pub disk_usage: DiskUsageInfo,

    /// 性能指标
    pub performance_metrics: PerformanceMetrics,
}

/// 系统状态
#[derive(Debug, Serialize, ToSchema)]
pub enum SystemStatus {
    Healthy,
    Warning,
    Critical,
    Unknown,
}

/// 磁盘使用信息
#[derive(Debug, Serialize, ToSchema)]
pub struct DiskUsageInfo {
    /// 审计日志目录路径
    pub log_directory: String,

    /// 已使用空间（字节）
    pub used_bytes: u64,

    /// 总空间（字节）
    pub total_bytes: u64,

    /// 使用率百分比
    pub usage_percentage: f64,
}

/// 性能指标
#[derive(Debug, Serialize, ToSchema)]
pub struct PerformanceMetrics {
    /// 日志写入速率（条/秒）
    pub log_write_rate: f64,

    /// 查询响应时间（毫秒）
    pub query_response_time_ms: f64,

    /// 内存使用量（MB）
    pub memory_usage_mb: f64,

    /// CPU使用率（百分比）
    pub cpu_usage_percentage: f64,
}

impl Default for AuditLogQueryRequest {
    fn default() -> Self {
        Self {
            page: Some(1),
            page_size: Some(20),
            start_time: None,
            end_time: None,
            user_id: None,
            username: None,
            action: None,
            result: None,
            resource_type: None,
            resource_id: None,
            ip_address: None,
            request_path: None,
            search: None,
            sort_by: Some(AuditSortField::Timestamp),
            sort_order: Some(SortOrder::Desc),
        }
    }
}

impl Default for AuditStatsRequest {
    fn default() -> Self {
        Self {
            start_time: None,
            end_time: None,
            group_by: None,
            interval: Some(TimeInterval::Day),
        }
    }
}
