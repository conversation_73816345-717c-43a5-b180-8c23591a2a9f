use serde::{Deserialize, Serialize};
use wisdom_vault_core::services::{
    DiffLine, VersionDiff, VersionStatistics, VersionStorageInfo, VersionSummaryDiff,
};
use wisdom_vault_database::models::DocumentVersion;

/// 创建版本的请求
#[derive(Debug, Deserialize)]
pub struct CreateVersionRequest {
    pub changes: Vec<String>,
}

/// 文档版本响应
#[derive(Debug, Serialize)]
pub struct DocumentVersionResponse {
    pub id: String,
    pub document_id: String,
    pub version_number: i32,
    pub content: String,
    pub summary: Option<String>,
    pub changes: Vec<String>,
    pub created_by: String,
    pub created_at: i64,
}

impl From<DocumentVersion> for DocumentVersionResponse {
    fn from(version: DocumentVersion) -> Self {
        Self {
            id: version.id,
            document_id: version.document_id,
            version_number: version.version_number,
            content: version.content,
            summary: version.summary,
            changes: version.changes,
            created_by: version.created_by,
            created_at: version.created_at,
        }
    }
}

/// 文档版本列表响应
#[derive(Debug, Serialize)]
pub struct DocumentVersionsResponse {
    pub document_id: String,
    pub versions: Vec<DocumentVersionResponse>,
    pub total_versions: usize,
}

/// 版本比较查询参数
#[derive(Debug, Deserialize)]
pub struct VersionCompareQuery {
    pub version1: i32,
    pub version2: i32,
}

/// 差异行响应
#[derive(Debug, Serialize)]
pub struct DiffLineResponse {
    pub line_type: String,
    pub old_line_number: Option<usize>,
    pub new_line_number: Option<usize>,
    pub content: String,
}

impl From<DiffLine> for DiffLineResponse {
    fn from(diff_line: DiffLine) -> Self {
        Self {
            line_type: format!("{:?}", diff_line.line_type),
            old_line_number: diff_line.old_line_number,
            new_line_number: diff_line.new_line_number,
            content: diff_line.content,
        }
    }
}

/// 摘要差异响应
#[derive(Debug, Serialize)]
pub struct VersionSummaryDiffResponse {
    pub old_summary: Option<String>,
    pub new_summary: Option<String>,
}

impl From<VersionSummaryDiff> for VersionSummaryDiffResponse {
    fn from(summary_diff: VersionSummaryDiff) -> Self {
        Self {
            old_summary: summary_diff.old_summary,
            new_summary: summary_diff.new_summary,
        }
    }
}

/// 版本差异响应
#[derive(Debug, Serialize)]
pub struct VersionDiffResponse {
    pub document_id: String,
    pub version1: i32,
    pub version2: i32,
    pub content_diff: Vec<DiffLineResponse>,
    pub summary_changed: bool,
    pub summary_diff: Option<VersionSummaryDiffResponse>,
    pub changes1: Vec<String>,
    pub changes2: Vec<String>,
}

impl From<VersionDiff> for VersionDiffResponse {
    fn from(diff: VersionDiff) -> Self {
        Self {
            document_id: diff.document_id,
            version1: diff.version1,
            version2: diff.version2,
            content_diff: diff
                .content_diff
                .into_iter()
                .map(DiffLineResponse::from)
                .collect(),
            summary_changed: diff.summary_changed,
            summary_diff: diff.summary_diff.map(VersionSummaryDiffResponse::from),
            changes1: diff.changes1,
            changes2: diff.changes2,
        }
    }
}

/// 版本统计响应
#[derive(Debug, Serialize)]
pub struct VersionStatisticsResponse {
    pub total_versions: i32,
    pub latest_version: i32,
    pub total_changes: i32,
    pub first_created_at: Option<i64>,
    pub last_updated_at: Option<i64>,
    pub contributors: Vec<String>,
}

impl From<VersionStatistics> for VersionStatisticsResponse {
    fn from(stats: VersionStatistics) -> Self {
        Self {
            total_versions: stats.total_versions,
            latest_version: stats.latest_version,
            total_changes: stats.total_changes,
            first_created_at: stats.first_created_at,
            last_updated_at: stats.last_updated_at,
            contributors: stats.contributors,
        }
    }
}

/// 清理版本的请求
#[derive(Debug, Deserialize)]
pub struct CleanupVersionsRequest {
    pub keep_latest: i32,
}

/// 清理版本的响应
#[derive(Debug, Serialize)]
pub struct CleanupVersionsResponse {
    pub document_id: String,
    pub deleted_count: u32,
    pub kept_versions: i32,
}

/// 版本存储信息响应
#[derive(Debug, Serialize)]
pub struct VersionStorageInfoResponse {
    pub total_versions: i32,
    pub total_content_size: u64,
    pub estimated_compressed_size: u64,
    pub average_version_size: u64,
}

impl From<VersionStorageInfo> for VersionStorageInfoResponse {
    fn from(info: VersionStorageInfo) -> Self {
        Self {
            total_versions: info.total_versions,
            total_content_size: info.total_content_size,
            estimated_compressed_size: info.estimated_compressed_size,
            average_version_size: info.average_version_size,
        }
    }
}
