use serde::{Deserialize, Serialize};
use validator::<PERSON><PERSON><PERSON>;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, <PERSON><PERSON><PERSON>)]
pub struct CreateUserRequest {
    #[validate(length(min = 3, max = 50))]
    pub username: String,

    #[validate(email)]
    pub email: String,

    #[validate(length(min = 8))]
    pub password: Option<String>, // 如果为空则自动生成

    #[validate(length(max = 100))]
    pub full_name: Option<String>,

    pub phone: Option<String>,

    pub department_id: Option<String>,

    pub is_active: Option<bool>, // 默认为 true
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Validate)]
pub struct UpdateUserRequest {
    #[validate(length(min = 3, max = 50))]
    pub username: Option<String>,

    #[validate(email)]
    pub email: Option<String>,

    #[validate(length(max = 100))]
    pub full_name: Option<String>,

    pub phone: Option<String>,

    pub department_id: Option<String>,

    pub avatar_url: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct UserResponse {
    pub id: String,
    pub username: String,
    pub email: String,
    pub full_name: Option<String>,
    pub phone: Option<String>,
    pub avatar_url: Option<String>,
    pub department_id: Option<String>,
    pub is_active: bool,
    pub last_login_at: Option<chrono::DateTime<chrono::Utc>>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub roles: Vec<String>, // 用户角色名称列表
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct UserListQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub search: Option<String>,
    pub department_id: Option<String>,
    pub is_active: Option<bool>,
    pub sort_by: Option<String>,    // username, email, created_at
    pub sort_order: Option<String>, // asc, desc
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserListResponse {
    pub users: Vec<UserResponse>,
    pub total: i64,
    pub page: u32,
    pub limit: u32,
    pub total_pages: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct UpdateUserStatusRequest {
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct AssignRoleRequest {
    pub role_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct BatchUserOperationRequest {
    pub user_ids: Vec<String>,
    pub operation: String, // activate, deactivate, delete
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchOperationResponse {
    pub success_count: i32,
    pub failed_count: i32,
    pub failed_users: Vec<String>, // 失败的用户ID
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserResponse {
    pub user: UserResponse,
    pub generated_password: Option<String>, // 如果自动生成密码则返回
}
