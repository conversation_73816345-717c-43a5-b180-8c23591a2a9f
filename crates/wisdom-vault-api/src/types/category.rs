use serde::{Deserialize, Serialize};

use crate::types::{document::DocumentSummaryResponse, knowledge_base::CategoryResponse};

/// 为文档分配分类的请求
#[derive(Debug, Deserialize)]
pub struct CategorizeDocumentRequest {
    pub confidence_score: Option<f64>,
}

/// 为文档分配分类的响应
#[derive(Debug, Serialize)]
pub struct CategorizeDocumentResponse {
    pub id: String,
    pub document_id: String,
    pub category_id: String,
    pub assigned_by: String,
    pub assigned_at: u64,
    pub confidence_score: Option<f64>,
}

/// 自动分类文档的响应
#[derive(Debug, Serialize)]
pub struct ClassifyDocumentResponse {
    pub document_id: String,
    pub categories: Vec<CategorizeDocumentResponse>,
    pub classification_method: String,
}

/// 批量分类文档的请求
#[derive(Debug, Deserialize)]
pub struct BatchClassifyDocumentsRequest {
    pub document_ids: Vec<String>,
}

/// 批量分类文档的响应
#[derive(Debug, Serialize)]
pub struct BatchClassifyDocumentsResponse {
    pub classified_count: usize,
    pub categories: Vec<CategorizeDocumentResponse>,
}

/// 分类推荐查询参数
#[derive(Debug, Deserialize)]
pub struct CategorySuggestionQuery {
    pub max_suggestions: Option<usize>,
}

/// 分类推荐响应项
#[derive(Debug, Serialize)]
pub struct CategorySuggestionResponse {
    pub category: CategoryResponse,
    pub confidence: f64,
}

/// 分类推荐响应
#[derive(Debug, Serialize)]
pub struct CategorySuggestionsResponse {
    pub document_id: String,
    pub suggestions: Vec<CategorySuggestionResponse>,
}

/// 文档分类响应
#[derive(Debug, Serialize)]
pub struct DocumentCategoriesResponse {
    pub document_id: String,
    pub categories: Vec<CategoryResponse>,
}

/// 根据分类查找文档的请求
#[derive(Debug, Deserialize)]
pub struct FindDocumentsByCategoriesRequest {
    pub category_ids: Vec<String>,
    pub match_all: Option<bool>, // true: 匹配所有分类, false: 匹配任一分类
}

/// 根据分类查找文档的响应
#[derive(Debug, Serialize)]
pub struct FindDocumentsByCategoriesResponse {
    pub documents: Vec<DocumentSummaryResponse>,
    pub total_count: i64,
    pub match_all: bool,
}

/// 分类统计项
#[derive(Debug, Serialize)]
pub struct CategoryStatistic {
    pub category: CategoryResponse,
    pub document_count: i64,
}

/// 分类统计响应
#[derive(Debug, Serialize)]
pub struct CategoryStatisticsResponse {
    pub knowledge_base_id: String,
    pub statistics: Vec<CategoryStatistic>,
    pub total_categories: usize,
}

/// 分类统计信息响应
#[derive(Debug, Serialize)]
pub struct ClassificationStatisticsResponse {
    pub knowledge_base_id: String,
    pub total_documents: i64,
    pub classified_documents: i64,
    pub unclassified_documents: i64,
    pub categories_with_documents: i64,
    pub average_classifications_per_document: f64,
}
