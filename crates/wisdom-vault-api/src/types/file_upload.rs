use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use validator::Validate;
use wisdom_vault_common::time::to_datetime;

// Upload request types
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UploadFileRequest {
    pub knowledge_base_id: Option<String>,
    pub category_id: Option<String>,
    pub tags: Vec<String>,

    #[validate(length(max = 200, message = "Title cannot exceed 200 characters"))]
    pub title: Option<String>,

    #[validate(length(max = 1000, message = "Description cannot exceed 1000 characters"))]
    pub description: Option<String>,

    pub metadata: Option<serde_json::Value>,
    pub enable_ocr: Option<bool>,
    pub auto_classify: Option<bool>,
}

impl Default for UploadFileRequest {
    fn default() -> Self {
        Self {
            knowledge_base_id: None,
            category_id: None,
            tags: Vec::new(),
            title: None,
            description: None,
            metadata: None,
            enable_ocr: Some(true),
            auto_classify: Some(true),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchUploadRequest {
    pub knowledge_base_id: String,
    pub category_id: Option<String>,
    pub tags: Option<Vec<String>>,
    pub auto_categorize: Option<bool>,
    pub auto_tag: Option<bool>,
}

// Response types
#[derive(Debug, Serialize, Deserialize)]
pub struct FileUploadResponse {
    pub file_id: String,
    pub filename: String,
    pub file_size: u64,
    pub content_type: String,
    pub upload_url: String,
    pub processing_status: String,
    pub task_ids: Vec<String>,
    pub estimated_processing_time: Option<u32>, // in seconds
    pub message: String,
}

// 保持向后兼容的构造函数
impl FileUploadResponse {
    pub fn legacy(
        file_id: String,
        original_filename: String,
        file_size: u64,
        mime_type: String,
        _upload_status: UploadStatus,
        processing_status: ProcessingStatus,
        uploaded_at: i64,
    ) -> Self {
        Self {
            file_id: file_id.clone(),
            filename: original_filename,
            file_size,
            content_type: mime_type,
            upload_url: format!("/api/v1/files/{}/download", file_id),
            processing_status: format!("{:?}", processing_status).to_lowercase(),
            task_ids: Vec::new(),
            estimated_processing_time: None,
            message: format!(
                "File uploaded at {}",
                to_datetime(uploaded_at).format("%Y-%m-%d %H:%M:%S")
            ),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchUploadResponse {
    pub batch_id: String,
    pub total_files: usize,
    pub successful_uploads: Vec<FileUploadResponse>,
    pub failed_uploads: Vec<UploadError>,
    pub processing_queue_size: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UploadError {
    pub filename: String,
    pub error_message: String,
    pub error_code: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UploadStatus {
    Pending,
    Uploading,
    Completed,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProcessingStatus {
    Queued,
    Processing,
    Completed,
    Failed,
    Cancelled,
    Skipped,
}

// File management types
#[derive(Debug, Serialize, Deserialize)]
pub struct FileInfoResponse {
    pub file_id: String,
    pub document_id: Option<String>,
    pub original_filename: String,
    pub file_size: u64,
    pub mime_type: String,
    pub checksum: String,
    pub uploaded_by: String,
    pub uploaded_at: i64,
    pub download_count: u64,
    pub last_accessed: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct FileSearchRequest {
    #[validate(length(
        min = 1,
        max = 200,
        message = "Query must be between 1 and 200 characters"
    ))]
    pub query: String,

    pub knowledge_base_id: Option<String>,
    pub file_types: Option<Vec<String>>,
    pub size_min: Option<u64>,
    pub size_max: Option<u64>,
    pub uploaded_after: Option<u64>,
    pub uploaded_before: Option<u64>,
    pub uploaded_by: Option<String>,

    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: Option<u32>,

    #[validate(range(min = 0, message = "Offset must be non-negative"))]
    pub offset: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileSearchResponse {
    pub files: Vec<FileInfoResponse>,
    pub total_count: Option<u64>,
    pub has_more: bool,
    pub search_time_ms: u64,
}

// Storage management types
#[derive(Debug, Serialize, Deserialize)]
pub struct StorageStatsResponse {
    pub total_size: u64,
    pub file_count: u64,
    pub average_file_size: f64,
    pub storage_usage_by_type: HashMap<String, StorageTypeUsage>,
    pub storage_usage_by_month: HashMap<String, u64>,
    pub largest_files: Vec<FileInfoResponse>,
    pub most_recent_files: Vec<FileInfoResponse>,
    pub storage_config: StorageConfigResponse,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StorageTypeUsage {
    pub file_count: u64,
    pub total_size: u64,
    pub percentage: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StorageConfigResponse {
    pub max_file_size: u64,
    pub allowed_extensions: Vec<String>,
    pub allowed_mime_types: Vec<String>,
    pub storage_path: String,
}

// Bulk operations
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct BulkFileOperationRequest {
    pub file_ids: Vec<String>,
    pub operation: BulkOperation,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum BulkOperation {
    Delete,
    Move { target_category_id: Option<String> },
    AddTags { tags: Vec<String> },
    RemoveTags { tags: Vec<String> },
    UpdateMetadata { metadata: serde_json::Value },
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BulkOperationResponse {
    pub operation_id: String,
    pub total_files: usize,
    pub successful_operations: usize,
    pub failed_operations: Vec<BulkOperationError>,
    pub processing_time_ms: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BulkOperationError {
    pub file_id: String,
    pub error_message: String,
    pub error_code: String,
}

// Download and streaming types
#[derive(Debug, Serialize, Deserialize)]
pub struct FileDownloadRequest {
    pub file_id: String,
    pub inline: Option<bool>, // true for inline display, false for download
    pub range: Option<FileRange>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileRange {
    pub start: u64,
    pub end: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileDownloadResponse {
    pub file_id: String,
    pub filename: String,
    pub mime_type: String,
    pub file_size: u64,
    pub download_url: String,
    pub expires_at: u64,
}

// Processing and analysis types
#[derive(Debug, Serialize, Deserialize)]
pub struct FileProcessingStatus {
    pub file_id: String,
    pub document_id: Option<String>,
    pub processing_stages: Vec<ProcessingStage>,
    pub current_stage: Option<String>,
    pub overall_progress: f32,
    pub estimated_completion: Option<u64>,
    pub error_message: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessingStage {
    pub stage_name: String,
    pub status: ProcessingStatus,
    pub progress: f32,
    pub started_at: Option<u64>,
    pub completed_at: Option<u64>,
    pub error_message: Option<String>,
}

// Duplicate detection types
#[derive(Debug, Serialize, Deserialize)]
pub struct DuplicateCheckRequest {
    pub file_id: String,
    pub check_content: Option<bool>,
    pub check_filename: Option<bool>,
    pub similarity_threshold: Option<f32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DuplicateCheckResponse {
    pub file_id: String,
    pub potential_duplicates: Vec<DuplicateMatch>,
    pub check_performed_at: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DuplicateMatch {
    pub matching_file_id: String,
    pub similarity_score: f32,
    pub match_type: DuplicateMatchType,
    pub confidence: f32,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum DuplicateMatchType {
    ExactMatch,
    ContentSimilar,
    FilenameSimilar,
    MetadataSimilar,
}

// Temporary file handling
#[derive(Debug, Serialize, Deserialize)]
pub struct TempFileInfo {
    pub temp_id: String,
    pub original_filename: String,
    pub file_size: u64,
    pub mime_type: String,
    pub created_at: u64,
    pub expires_at: u64,
}

impl From<wisdom_vault_core::FileMetadata> for FileInfoResponse {
    fn from(metadata: wisdom_vault_core::FileMetadata) -> Self {
        Self {
            file_id: metadata.file_id,
            document_id: None, // Will be set separately
            original_filename: metadata.original_filename,
            file_size: metadata.file_size,
            mime_type: metadata.mime_type,
            checksum: metadata.checksum,
            uploaded_by: metadata.uploaded_by,
            uploaded_at: metadata.uploaded_at,
            download_count: 0,   // Will be tracked separately
            last_accessed: None, // Will be tracked separately
        }
    }
}

// Helper functions for file type detection
pub fn detect_file_category(mime_type: &str, filename: &str) -> String {
    match mime_type {
        mime if mime.starts_with("image/") => "image".to_string(),
        "application/pdf" => "pdf".to_string(),
        mime if mime.contains("word") || mime.contains("document") => "document".to_string(),
        mime if mime.contains("spreadsheet") || mime.contains("excel") => "spreadsheet".to_string(),
        mime if mime.contains("presentation") || mime.contains("powerpoint") => {
            "presentation".to_string()
        }
        mime if mime.starts_with("text/") => "text".to_string(),
        mime if mime.starts_with("audio/") => "audio".to_string(),
        mime if mime.starts_with("video/") => "video".to_string(),
        _ => {
            // Fallback to file extension
            if let Some(ext) = std::path::Path::new(filename)
                .extension()
                .and_then(|e| e.to_str())
            {
                match ext.to_lowercase().as_str() {
                    "pdf" => "pdf".to_string(),
                    "doc" | "docx" | "odt" | "rtf" => "document".to_string(),
                    "xls" | "xlsx" | "ods" => "spreadsheet".to_string(),
                    "ppt" | "pptx" | "odp" => "presentation".to_string(),
                    "txt" | "md" | "html" | "htm" => "text".to_string(),
                    "jpg" | "jpeg" | "png" | "gif" | "bmp" | "svg" => "image".to_string(),
                    "mp3" | "wav" | "flac" | "aac" => "audio".to_string(),
                    "mp4" | "avi" | "mkv" | "mov" => "video".to_string(),
                    _ => "other".to_string(),
                }
            } else {
                "other".to_string()
            }
        }
    }
}

pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size_f = size as f64;
    let mut unit_index = 0;

    while size_f >= 1024.0 && unit_index < UNITS.len() - 1 {
        size_f /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size_f, UNITS[unit_index])
    }
}
