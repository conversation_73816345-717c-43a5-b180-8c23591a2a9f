use serde::{Deserialize, Serialize};
use wisdom_vault_database::models::{
    Document, DocumentMetadata, DocumentProcessingMetadata, DocumentStatus,
};

// ============================================================================
// 文档请求类型 (Document Request Types)
// ============================================================================

#[derive(Debug, Deserialize)]
pub struct CreateDocumentRequest {
    pub knowledge_base_id: String,
    pub title: String,
    pub content: String,
    pub summary: Option<String>,
    pub file_type: String,
    pub mime_type: String,
    pub original_filename: Option<String>,
    pub language: Option<String>,
    pub metadata: Option<CreateDocumentMetadata>,
}

#[derive(Debug, Deserialize)]
pub struct CreateDocumentMetadata {
    pub author: Option<String>,
    pub subject: Option<String>,
    pub keywords: Vec<String>,
    pub source_url: Option<String>,
    pub custom_fields: serde_json::Value,
}

#[derive(Debug, Deserialize)]
pub struct UpdateDocumentRequest {
    pub title: Option<String>,
    pub content: Option<String>,
    pub summary: Option<String>,
    pub language: Option<String>,
    pub metadata: Option<UpdateDocumentMetadata>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateDocumentMetadata {
    pub author: Option<String>,
    pub subject: Option<String>,
    pub keywords: Option<Vec<String>>,
    pub source_url: Option<String>,
    pub custom_fields: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct DocumentQuery {
    pub knowledge_base_id: Option<String>,
    pub status: Option<String>,
    pub file_type: Option<String>,
    pub author: Option<String>,
    pub limit: Option<u32>,
    pub offset: Option<u32>,
}

#[derive(Debug, Deserialize)]
pub struct DocumentSearchRequest {
    pub query: String,
    pub knowledge_base_id: Option<String>,
    pub file_types: Option<Vec<String>>,
    pub authors: Option<Vec<String>>,
    pub date_range: Option<DateRangeFilter>,
    pub limit: Option<u32>,
    pub offset: Option<u32>,
}

#[derive(Debug, Deserialize)]
pub struct DateRangeFilter {
    pub start: u64,
    pub end: u64,
}

#[derive(Debug, Deserialize)]
pub struct BatchUpdateStatusRequest {
    pub document_ids: Vec<String>,
    pub status: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateDocumentStatusRequest {
    pub status: String,
}

// ============================================================================
// 文档响应类型 (Document Response Types)
// ============================================================================

/// 文档删除响应
#[derive(Debug, Serialize)]
pub struct DocumentDeleteResponse {
    pub deleted: bool,
    pub id: String,
}

/// 文档状态更新响应
#[derive(Debug, Serialize)]
pub struct DocumentStatusUpdateResponse {
    pub id: String,
    pub status: String,
    pub updated_at: u64,
}

#[derive(Debug, Serialize)]
pub struct DocumentResponse {
    pub id: String,
    pub knowledge_base_id: String,
    pub title: String,
    pub content: String,
    pub summary: Option<String>,
    pub file_type: String,
    pub file_size: i64,
    pub file_path: Option<String>,
    pub original_filename: Option<String>,
    pub mime_type: String,
    pub language: Option<String>,
    pub metadata: DocumentMetadataResponse,
    pub processing_metadata: DocumentProcessingMetadataResponse,
    pub status: String,
    pub uploaded_by: String,
    pub indexed_at: Option<i64>,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Serialize)]
pub struct DocumentSummaryResponse {
    pub id: String,
    pub knowledge_base_id: String,
    pub title: String,
    pub summary: Option<String>,
    pub file_type: String,
    pub file_size: i64,
    pub original_filename: Option<String>,
    pub status: String,
    pub uploaded_by: String,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Serialize)]
pub struct DocumentMetadataResponse {
    pub author: Option<String>,
    pub subject: Option<String>,
    pub creator: Option<String>,
    pub producer: Option<String>,
    pub keywords: Vec<String>,
    pub source_url: Option<String>,
    pub page_count: Option<i32>,
    pub word_count: Option<i32>,
    pub character_count: Option<i32>,
    pub creation_date: Option<u64>,
    pub modification_date: Option<u64>,
    pub content_type: String,
    pub content_encoding: Option<String>,
    pub content_language: Option<String>,
    pub custom_fields: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct DocumentProcessingMetadataResponse {
    pub extraction_method: String,
    pub extraction_quality: f32,
    pub processing_time_ms: u64,
    pub parsing_errors: Vec<String>,
    pub parsing_warnings: Vec<String>,
    pub file_checksum: String,
    pub structured_content: Option<String>,
    pub processing_attempts: u32,
    pub last_processing_attempt: Option<i64>,
}

#[derive(Debug, Serialize)]
pub struct DocumentListResponse {
    pub documents: Vec<DocumentSummaryResponse>,
    pub total_count: i64,
    pub has_more: bool,
}

#[derive(Debug, Serialize)]
pub struct DocumentSearchResponse {
    pub documents: Vec<DocumentSearchResult>,
    pub total_count: i64,
    pub query: String,
    pub search_time_ms: u64,
}

#[derive(Debug, Serialize)]
pub struct DocumentSearchResult {
    pub document: DocumentSummaryResponse,
    pub relevance_score: f64,
    pub highlight_snippets: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct DocumentStatisticsResponse {
    pub total_count: i64,
    pub indexed_count: i64,
    pub processing_count: i64,
    pub failed_count: i64,
    pub archived_count: i64,
    pub ready_count: i64,
    pub success_rate: f64,
}

#[derive(Debug, Serialize)]
pub struct CreateDocumentResponse {
    pub id: String,
    pub title: String,
    pub status: String,
    pub created_at: u64,
}

#[derive(Debug, Serialize)]
pub struct UpdateDocumentResponse {
    pub id: String,
    pub title: String,
    pub status: String,
    pub updated_at: u64,
}

#[derive(Debug, Serialize)]
pub struct BatchUpdateResponse {
    pub updated_count: u64,
    pub failed_ids: Vec<String>,
}

// ============================================================================
// 通用响应类型 (Generic Response Types)
// ============================================================================

// ============================================================================
// 转换函数 (Conversion Functions)
// ============================================================================

impl From<Document> for DocumentResponse {
    fn from(doc: Document) -> Self {
        Self {
            id: doc.id,
            knowledge_base_id: doc.knowledge_base_id,
            title: doc.title,
            content: doc.content,
            summary: doc.summary,
            file_type: doc.file_type,
            file_size: doc.file_size,
            file_path: doc.file_path,
            original_filename: doc.original_filename,
            mime_type: doc.mime_type,
            language: doc.language,
            metadata: doc.metadata.into(),
            processing_metadata: doc.processing_metadata.into(),
            status: format!("{:?}", doc.status),
            uploaded_by: doc.uploaded_by,
            indexed_at: doc.indexed_at,
            created_at: doc.created_at,
            updated_at: doc.updated_at,
        }
    }
}

impl From<Document> for DocumentSummaryResponse {
    fn from(doc: Document) -> Self {
        Self {
            id: doc.id,
            knowledge_base_id: doc.knowledge_base_id,
            title: doc.title,
            summary: doc.summary,
            file_type: doc.file_type,
            file_size: doc.file_size,
            original_filename: doc.original_filename,
            status: format!("{:?}", doc.status),
            uploaded_by: doc.uploaded_by,
            created_at: doc.created_at,
            updated_at: doc.updated_at,
        }
    }
}

impl From<DocumentMetadata> for DocumentMetadataResponse {
    fn from(metadata: DocumentMetadata) -> Self {
        Self {
            author: metadata.author,
            subject: metadata.subject,
            creator: metadata.creator,
            producer: metadata.producer,
            keywords: metadata.keywords,
            source_url: metadata.source_url,
            page_count: metadata.page_count,
            word_count: metadata.word_count,
            character_count: metadata.character_count,
            creation_date: metadata.creation_date,
            modification_date: metadata.modification_date,
            content_type: metadata.content_type,
            content_encoding: metadata.content_encoding,
            content_language: metadata.content_language,
            custom_fields: metadata.custom_fields,
        }
    }
}

impl From<DocumentProcessingMetadata> for DocumentProcessingMetadataResponse {
    fn from(metadata: DocumentProcessingMetadata) -> Self {
        Self {
            extraction_method: metadata.extraction_method,
            extraction_quality: metadata.extraction_quality,
            processing_time_ms: metadata.processing_time_ms,
            parsing_errors: metadata.parsing_errors,
            parsing_warnings: metadata.parsing_warnings,
            file_checksum: metadata.file_checksum,
            structured_content: metadata.structured_content,
            processing_attempts: metadata.processing_attempts,
            last_processing_attempt: metadata.last_processing_attempt,
        }
    }
}

/// 解析文档状态字符串
pub fn parse_document_status(status_str: &str) -> Result<DocumentStatus, String> {
    match status_str.to_lowercase().as_str() {
        "uploading" => Ok(DocumentStatus::Uploading),
        "uploaded" => Ok(DocumentStatus::Uploaded),
        "processing" => Ok(DocumentStatus::Processing),
        "parsed" => Ok(DocumentStatus::Parsed),
        "indexing" => Ok(DocumentStatus::Indexing),
        "indexed" => Ok(DocumentStatus::Indexed),
        "failed" => Ok(DocumentStatus::Failed),
        "archived" => Ok(DocumentStatus::Archived),
        "deleted" => Ok(DocumentStatus::Deleted),
        _ => Err(format!("Invalid status: {}", status_str)),
    }
}

/// 创建默认文档元数据
pub fn create_default_metadata() -> DocumentMetadata {
    DocumentMetadata {
        author: None,
        subject: None,
        creator: None,
        producer: None,
        keywords: Vec::new(),
        source_url: None,
        page_count: None,
        word_count: None,
        character_count: None,
        creation_date: None,
        modification_date: None,
        content_type: "text/plain".to_string(),
        content_encoding: None,
        content_language: None,
        custom_fields: serde_json::Value::Object(serde_json::Map::new()),
    }
}

/// 创建默认处理元数据
pub fn create_default_processing_metadata() -> DocumentProcessingMetadata {
    DocumentProcessingMetadata {
        extraction_method: "manual".to_string(),
        extraction_quality: 1.0,
        processing_time_ms: 0,
        parsing_errors: Vec::new(),
        parsing_warnings: Vec::new(),
        file_checksum: String::new(),
        structured_content: None,
        processing_attempts: 0,
        last_processing_attempt: None,
    }
}
