use serde::{Deserialize, Serialize};
use wisdom_vault_database::models::{TaskPriority, TaskStatus, TaskType};

/// 任务信息响应
#[derive(Debug, Serialize)]
pub struct TaskResponse {
    pub id: String,
    pub task_type: TaskType,
    pub status: TaskStatus,
    pub resource_id: String,
    pub priority: TaskPriority,
    pub progress: f64,
    pub retry_count: u32,
    pub max_retries: u32,
    pub error_message: Option<String>,
    pub created_at: u64,
    pub started_at: Option<u64>,
    pub completed_at: Option<u64>,
    pub next_retry_at: Option<u64>,
    pub estimated_completion: Option<u64>,
}

/// 任务列表查询参数
#[derive(Debug, Deserialize)]
pub struct TaskListQuery {
    pub resource_id: Option<String>,
    pub status: Option<TaskStatus>,
    pub task_type: Option<TaskType>,
    pub limit: Option<u32>,
    pub offset: Option<u32>,
}

/// 任务统计信息响应
#[derive(Debug, Serialize)]
pub struct TaskStatisticsResponse {
    pub total_tasks: i64,
    pub queued_tasks: i64,
    pub running_tasks: i64,
    pub completed_tasks: i64,
    pub failed_tasks: i64,
    pub cancelled_tasks: i64,
    pub retrying_tasks: i64,
    pub average_processing_time: Option<f64>, // 平均处理时间(秒)
    pub success_rate: f64,                    // 成功率
}

/// 文档处理进度响应
#[derive(Debug, Serialize)]
pub struct DocumentProcessingProgressResponse {
    pub document_id: String,
    pub overall_progress: f64,
    pub status: String,
    pub tasks_completed: usize,
    pub tasks_total: usize,
    pub tasks_failed: usize,
    pub estimated_completion: Option<u64>,
    pub current_task: Option<String>,
    pub tasks: Vec<TaskResponse>,
}

/// 批量任务操作请求
#[derive(Debug, Deserialize)]
pub struct BatchTaskOperationRequest {
    pub task_ids: Vec<String>,
    pub operation: TaskOperation,
}

/// 任务操作类型
#[derive(Debug, Deserialize)]
pub enum TaskOperation {
    Cancel,
    Retry,
    Reset,
}

/// 任务重试请求
#[derive(Debug, Deserialize)]
pub struct RetryTaskRequest {
    pub force: Option<bool>, // 是否强制重试（忽略重试限制）
}

/// 实时任务监控响应
#[derive(Debug, Serialize)]
pub struct TaskMonitorResponse {
    pub timestamp: u64,
    pub statistics: TaskStatisticsResponse,
    pub active_tasks: Vec<TaskResponse>,
    pub recent_failures: Vec<TaskResponse>,
    pub processing_queue_size: usize,
}

/// 任务创建请求（用于手动创建任务）
#[derive(Debug, Deserialize)]
pub struct CreateTaskRequest {
    pub task_type: TaskType,
    pub resource_id: String,
    pub priority: Option<TaskPriority>,
    pub file_path: Option<String>,
    pub original_filename: Option<String>,
    pub mime_type: Option<String>,
}
