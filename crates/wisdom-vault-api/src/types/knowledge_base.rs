use serde::{Deserialize, Serialize};
use validator::Validate;
use wisdom_vault_database::{
    Category, KnowledgeBase, KnowledgeBaseSettings, KnowledgeBaseStatistics,
    KnowledgeBaseVisibility, Tag,
};

// Request types for knowledge base operations
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateKnowledgeBaseRequest {
    #[validate(length(
        min = 1,
        max = 100,
        message = "Name must be between 1 and 100 characters"
    ))]
    pub name: String,

    #[validate(length(max = 500, message = "Description cannot exceed 500 characters"))]
    pub description: Option<String>,

    pub organization_id: String,
    pub visibility: KnowledgeBaseVisibility,
    pub settings: Option<KnowledgeBaseSettings>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UpdateKnowledgeBaseRequest {
    #[validate(length(
        min = 1,
        max = 100,
        message = "Name must be between 1 and 100 characters"
    ))]
    pub name: Option<String>,

    #[validate(length(max = 500, message = "Description cannot exceed 500 characters"))]
    pub description: Option<String>,

    pub visibility: Option<KnowledgeBaseVisibility>,
    pub settings: Option<KnowledgeBaseSettings>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct ListKnowledgeBasesRequest {
    pub user_id: Option<String>,
    pub organization_id: Option<String>,
    pub visibility_filter: Option<Vec<KnowledgeBaseVisibility>>,

    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: Option<u32>,

    #[validate(range(min = 0, message = "Offset must be non-negative"))]
    pub offset: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct SearchKnowledgeBasesRequest {
    #[validate(length(
        min = 1,
        max = 200,
        message = "Query must be between 1 and 200 characters"
    ))]
    pub query: String,

    pub user_id: Option<String>,

    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: Option<u32>,

    #[validate(range(min = 0, message = "Offset must be non-negative"))]
    pub offset: Option<u32>,
}

// Response types
#[derive(Debug, Serialize, Deserialize)]
pub struct KnowledgeBaseResponse {
    pub id: String,
    pub organization_id: String,
    pub name: String,
    pub description: Option<String>,
    pub owner_id: String,
    pub visibility: KnowledgeBaseVisibility,
    pub settings: KnowledgeBaseSettings,
    pub statistics: KnowledgeBaseStatistics,
    pub created_at: i64,
    pub updated_at: i64,
}

impl From<KnowledgeBase> for KnowledgeBaseResponse {
    fn from(kb: KnowledgeBase) -> Self {
        Self {
            id: kb.id,
            organization_id: kb.organization_id,
            name: kb.name,
            description: kb.description,
            owner_id: kb.owner_id,
            visibility: kb.visibility,
            settings: kb.settings,
            statistics: kb.statistics,
            created_at: kb.created_at,
            updated_at: kb.updated_at,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ListKnowledgeBasesResponse {
    pub knowledge_bases: Vec<KnowledgeBaseResponse>,
    pub total: Option<i64>,
    pub limit: u32,
    pub offset: u32,
}

// Category-related types
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateCategoryRequest {
    #[validate(length(
        min = 1,
        max = 50,
        message = "Name must be between 1 and 50 characters"
    ))]
    pub name: String,

    #[validate(length(max = 200, message = "Description cannot exceed 200 characters"))]
    pub description: Option<String>,

    pub parent_id: Option<String>,
    pub icon: Option<String>,
    pub sort_order: i32,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UpdateCategoryRequest {
    #[validate(length(
        min = 1,
        max = 50,
        message = "Name must be between 1 and 50 characters"
    ))]
    pub name: Option<String>,

    #[validate(length(max = 200, message = "Description cannot exceed 200 characters"))]
    pub description: Option<String>,

    pub icon: Option<String>,
    pub sort_order: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CategoryResponse {
    pub id: String,
    pub knowledge_base_id: String,
    pub parent_id: Option<String>,
    pub name: String,
    pub description: Option<String>,
    pub icon: Option<String>,
    pub sort_order: i32,
    pub document_count: i64,
    pub created_by: String,
    pub created_at: i64,
    pub updated_at: i64,
}

impl From<Category> for CategoryResponse {
    fn from(category: Category) -> Self {
        Self {
            id: category.id,
            knowledge_base_id: category.knowledge_base_id,
            parent_id: category.parent_id,
            name: category.name,
            description: category.description,
            icon: category.icon,
            sort_order: category.sort_order,
            document_count: category.document_count,
            created_by: category.created_by,
            created_at: category.created_at,
            updated_at: category.updated_at,
        }
    }
}

// Tag-related types
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateTagRequest {
    #[validate(length(
        min = 1,
        max = 30,
        message = "Name must be between 1 and 30 characters"
    ))]
    pub name: String,

    #[validate(length(
        min = 1,
        max = 50,
        message = "Display name must be between 1 and 50 characters"
    ))]
    pub display_name: String,

    pub color: Option<String>,

    #[validate(length(max = 200, message = "Description cannot exceed 200 characters"))]
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UpdateTagRequest {
    #[validate(length(
        min = 1,
        max = 50,
        message = "Display name must be between 1 and 50 characters"
    ))]
    pub display_name: Option<String>,

    pub color: Option<String>,

    #[validate(length(max = 200, message = "Description cannot exceed 200 characters"))]
    pub description: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct SearchTagsRequest {
    #[validate(length(
        min = 1,
        max = 100,
        message = "Query must be between 1 and 100 characters"
    ))]
    pub query: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TagResponse {
    pub id: String,
    pub knowledge_base_id: String,
    pub name: String,
    pub display_name: String,
    pub color: Option<String>,
    pub description: Option<String>,
    pub usage_count: i64,
    pub created_by: String,
    pub created_at: i64,
    pub updated_at: i64,
}

impl From<Tag> for TagResponse {
    fn from(tag: Tag) -> Self {
        Self {
            id: tag.id,
            knowledge_base_id: tag.knowledge_base_id,
            name: tag.name,
            display_name: tag.display_name,
            color: tag.color,
            description: tag.description,
            usage_count: tag.usage_count,
            created_by: tag.created_by,
            created_at: tag.created_at,
            updated_at: tag.updated_at,
        }
    }
}
