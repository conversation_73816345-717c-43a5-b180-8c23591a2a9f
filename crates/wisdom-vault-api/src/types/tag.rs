use serde::{Deserialize, Serialize};
use wisdom_vault_database::models::Tag;

use crate::types::document::DocumentSummaryResponse;

/// 标签响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TagResponse {
    pub id: String,
    pub knowledge_base_id: String,
    pub name: String,
    pub display_name: String,
    pub color: Option<String>,
    pub description: Option<String>,
    pub usage_count: i64,
    pub created_by: String,
    pub created_at: i64,
    pub updated_at: i64,
}

impl From<Tag> for TagResponse {
    fn from(tag: Tag) -> Self {
        Self {
            id: tag.id,
            knowledge_base_id: tag.knowledge_base_id,
            name: tag.name,
            display_name: tag.display_name,
            color: tag.color,
            description: tag.description,
            usage_count: tag.usage_count,
            created_by: tag.created_by,
            created_at: tag.created_at,
            updated_at: tag.updated_at,
        }
    }
}

/// 为文档添加标签的响应
#[derive(Debug, Serialize, Deserialize)]
pub struct TagDocumentResponse {
    pub id: String,
    pub document_id: String,
    pub tag_id: String,
    pub tagged_by: String,
    pub tagged_at: u64,
}

/// 批量为文档添加标签的请求
#[derive(Debug, Deserialize)]
pub struct BatchTagDocumentsRequest {
    pub document_ids: Vec<String>,
    pub tag_id: String,
}

/// 批量为文档添加标签的响应
#[derive(Debug, Serialize)]
pub struct BatchTagDocumentsResponse {
    pub tagged_count: usize,
    pub document_tags: Vec<TagDocumentResponse>,
}

/// 批量从文档移除标签的请求
#[derive(Debug, Deserialize)]
pub struct BatchUntagDocumentsRequest {
    pub document_ids: Vec<String>,
    pub tag_id: String,
}

/// 批量从文档移除标签的响应
#[derive(Debug, Serialize)]
pub struct BatchUntagDocumentsResponse {
    pub removed_count: u64,
    pub tag_id: String,
}

/// 标签推荐查询参数
#[derive(Debug, Deserialize)]
pub struct TagSuggestionQuery {
    pub max_suggestions: Option<usize>,
    pub detailed: Option<bool>,
}

/// 简单标签推荐响应
#[derive(Debug, Serialize)]
pub struct SimpleTagSuggestionResponse {
    pub tag: TagResponse,
    pub confidence: f64,
}

/// 详细标签推荐响应
#[derive(Debug, Serialize)]
pub struct DetailedTagSuggestionResponse {
    pub tag: TagResponse,
    pub confidence: f64,
    pub suggestion_type: String,
    pub explanation: String,
}

/// 标签推荐响应（支持简单和详细两种模式）
#[derive(Debug, Serialize)]
#[serde(untagged)]
pub enum TagSuggestionsResponse {
    Simple(Vec<SimpleTagSuggestionResponse>),
    Detailed(Vec<DetailedTagSuggestionResponse>),
}

/// 文档标签响应
#[derive(Debug, Serialize)]
pub struct DocumentTagsResponse {
    pub document_id: String,
    pub tags: Vec<TagResponse>,
}

/// 根据标签查找文档的请求
#[derive(Debug, Deserialize)]
pub struct FindDocumentsByTagsRequest {
    pub tag_ids: Vec<String>,
    pub match_all: Option<bool>, // true: 匹配所有标签, false: 匹配任一标签
}

/// 根据标签查找文档的响应
#[derive(Debug, Serialize)]
pub struct FindDocumentsByTagsResponse {
    pub documents: Vec<DocumentSummaryResponse>,
    pub total_count: i64,
    pub match_all: bool,
}

/// 智能标签请求
#[derive(Debug, Deserialize)]
pub struct SmartTagDocumentRequest {
    pub confidence_threshold: Option<f64>,
}

/// 智能标签响应
#[derive(Debug, Serialize)]
pub struct SmartTagDocumentResponse {
    pub document_id: String,
    pub applied_tags: Vec<TagDocumentResponse>,
    pub confidence_threshold: f64,
}

/// 标签使用统计
#[derive(Debug, Serialize)]
pub struct TagUsageStatistic {
    pub tag: TagResponse,
    pub usage_count: i64,
}

/// 标签使用统计响应
#[derive(Debug, Serialize)]
pub struct TagUsageStatisticsResponse {
    pub knowledge_base_id: String,
    pub statistics: Vec<TagUsageStatistic>,
    pub total_tags: usize,
}
