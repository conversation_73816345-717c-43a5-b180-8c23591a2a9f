use anyhow::{Result, anyhow};
use chrono::{DateTime, TimeDelta, Utc};
use reqwest::{Client, Url};
use serde::Deserialize;
use serde_json::Value;

#[derive(Clone, Debug)]
pub struct OpenObserveConfig {
    pub base_url: String,
    pub org_id: String,
    pub stream: String,
    pub email: String,
    pub token: String,
    pub timeout_seconds: u64,
}

#[derive(Clone)]
pub struct OpenObserveClient {
    http: Client,
    base: Url,
    org_id: String,
    stream: String,
    email: String,
    token: String,
}

impl OpenObserveClient {
    pub fn new(cfg: OpenObserveConfig) -> Result<Self> {
        let timeout = std::time::Duration::from_secs(cfg.timeout_seconds.max(5));
        let http = Client::builder().timeout(timeout).build()?;
        let mut base = Url::parse(&cfg.base_url)
            .map_err(|e| anyhow!("Invalid OpenObserve base_url: {}", e))?;
        if !base.path().ends_with('/') {
            base.set_path(&format!("{}/", base.path()));
        }
        Ok(Self {
            http,
            base,
            org_id: cfg.org_id,
            stream: cfg.stream,
            email: cfg.email,
            token: cfg.token,
        })
    }

    fn search_url(&self) -> Result<Url> {
        let mut url = self.base.clone();
        url.set_path(&format!("api/{}/_search", self.org_id));
        Ok(url)
    }

    pub async fn search(
        &self,
        sql: String,
        start_us: i64,
        end_us: i64,
        from: i64,
        size: i64,
    ) -> Result<SearchResponse> {
        let body = serde_json::json!({
            "query": {
                "sql": sql,
                "start_time": start_us,
                "end_time": end_us,
                "from": from,
                "size": size
            },
            "search_type": "ui",
            "timeout": 0
        });

        let resp = self
            .http
            .post(self.search_url()?)
            .basic_auth(self.email.clone(), Some(self.token.clone()))
            .json(&body)
            .send()
            .await?;

        if !resp.status().is_success() {
            let status = resp.status();
            let text = resp.text().await.unwrap_or_default();
            return Err(anyhow!("OpenObserve search failed: {} - {}", status, text));
        }

        let sr: SearchResponse = resp.json().await?;
        Ok(sr)
    }

    pub fn default_time_range_us(&self, start_ms: Option<i64>, end_ms: Option<i64>) -> (i64, i64) {
        let now = Utc::now();
        let end: i64 = end_ms
            .map(|v| v * 1000)
            .unwrap_or_else(|| now.timestamp_micros());
        let start: i64 = start_ms
            .map(|v| v * 1000)
            .unwrap_or_else(|| (now - TimeDelta::days(1)).timestamp_micros());
        (start, end)
    }

    pub fn stream_name(&self) -> &str {
        &self.stream
    }
}

#[derive(Debug, Deserialize)]
pub struct SearchResponse {
    pub took: Option<i64>,
    pub hits: Option<Vec<Value>>, // each hit is a flattened JSON object
    pub total: Option<i64>,
    pub from: Option<i64>,
    pub size: Option<i64>,
}

pub fn parse_rfc3339_to_millis(s: &str) -> Option<i64> {
    DateTime::parse_from_rfc3339(s)
        .ok()
        .map(|dt| dt.timestamp_millis())
}

pub fn micros_to_millis(us: i64) -> i64 {
    us / 1000
}
