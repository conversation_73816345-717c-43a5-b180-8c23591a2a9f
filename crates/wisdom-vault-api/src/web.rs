pub mod validate_error {
    use std::sync::LazyLock;

    use regex::Regex;
    use serde_json::{Value, json};
    use validator::ValidationErrors;
    use wisdom_vault_common::t;

    static RE: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"\{([^}]+)\}").unwrap());

    pub fn to_value(locale: &str, err: &ValidationErrors) -> Value {
        let mut errors = json!({});
        for (field, err) in err.field_errors() {
            let mut error_messages = vec![];
            for e in err {
                if let Some(ref key) = e.message {
                    let tpl: String = t!(key.as_ref(), locale = locale).into();
                    let message = RE
                        .replace_all(&tpl, |caps: &regex::Captures| {
                            let key = &caps[1];
                            e.params
                                .get(key)
                                .map(|v| match v {
                                    Value::String(s) => s.clone(),
                                    _ => v.to_string(),
                                })
                                .unwrap_or_default()
                        })
                        .into_owned();
                    error_messages.push(message);
                }
            }

            errors[field.to_string()] = json!(error_messages);
        }

        errors
    }
}
