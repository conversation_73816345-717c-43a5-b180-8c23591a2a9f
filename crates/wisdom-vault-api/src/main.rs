use actix_session::{SessionMiddleware, config::PersistentSession, storage::RedisSessionStore};
use actix_web::{
    App, HttpServer,
    cookie::{Key, time::Duration},
};
use std::fmt::Write as FmtWrite;
use std::{fs, io, path::Path};
use tracing::field::{Field, Visit};
use tracing::info;
use tracing_appender::{non_blocking::WorkerGuard, rolling};
use tracing_subscriber::{
    Layer,
    filter::EnvFilter,
    fmt::{self, FmtContext, FormatEvent, FormatFields},
    layer::SubscriberExt,
    registry::LookupSpan,
    util::SubscriberInitExt,
};
use utoipa::OpenApi;
use utoipa_actix_web::AppExt;
use utoipa_swagger_ui::SwaggerUi;
use wisdom_vault_api::{
    app_state::{AppState, configure_app_state},
    config::AppConfig,
    middleware, routes,
};

use crate::api_docs::ApiDoc;

mod api_docs;

// A custom formatter that writes only the `event` (or fallback `message`) field as a single line
struct AuditJsonlFormatter;

struct FieldExtractor {
    event_json: Option<String>,
    message: Option<String>,
}

impl Visit for FieldExtractor {
    fn record_str(&mut self, field: &Field, value: &str) {
        match field.name() {
            "event" => self.event_json = Some(value.to_string()),
            "message" => self.message = Some(value.to_string()),
            _ => {}
        }
    }
    fn record_debug(&mut self, field: &Field, value: &dyn std::fmt::Debug) {
        // Many fields recorded with % (Display) are passed via Debug wrapper; try to dequote JSON strings
        let raw = format!("{:?}", value);
        let maybe_unquoted = serde_json::from_str::<String>(&raw).unwrap_or(raw);
        match field.name() {
            "event" => self.event_json = Some(maybe_unquoted),
            "message" => self.message = Some(maybe_unquoted),
            _ => {}
        }
    }
}

impl<S, N> FormatEvent<S, N> for AuditJsonlFormatter
where
    S: tracing::Subscriber + for<'a> LookupSpan<'a>,
    N: for<'writer> FormatFields<'writer> + 'static,
{
    fn format_event(
        &self,
        _ctx: &FmtContext<'_, S, N>,
        mut writer: fmt::format::Writer<'_>,
        event: &tracing::Event<'_>,
    ) -> std::fmt::Result {
        let mut extractor = FieldExtractor {
            event_json: None,
            message: None,
        };
        event.record(&mut extractor);
        let line = extractor
            .event_json
            .or(extractor.message)
            .unwrap_or_else(|| "{}".to_string());
        writer.write_str(&line)?;
        writer.write_char('\n')
    }
}

fn init_logging(config: &AppConfig) -> Vec<WorkerGuard> {
    use tracing_subscriber::filter::FilterFn;

    let mut guards = Vec::new();

    let filter =
        EnvFilter::try_from_default_env().unwrap_or_else(|_| EnvFilter::new(&config.logging.level));

    // Optional console layer
    let console_layer = if config.logging.console_output {
        Some(
            fmt::Layer::new()
                .with_writer(io::stdout)
                .with_ansi(true)
                .with_target(true)
                .with_thread_ids(false)
                .with_thread_names(false)
                .with_file(false)
                .with_line_number(false),
        )
    } else {
        None
    };

    // Optional general app file layer
    let file_layer = if let Some(file_path) = &config.logging.file_path {
        if let Some(parent) = Path::new(file_path).parent() {
            if let Err(e) = fs::create_dir_all(parent) {
                eprintln!("Failed to create log directory: {}", e);
            }
        }
        let file_appender = rolling::daily(
            Path::new(file_path).parent().unwrap_or(Path::new(".")),
            Path::new(file_path).file_name().unwrap().to_str().unwrap(),
        );
        let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);
        guards.push(guard);

        Some(
            fmt::Layer::new()
                .with_writer(non_blocking)
                .with_ansi(false)
                .with_target(true)
                .with_thread_ids(false)
                .with_file(true)
                .with_line_number(true),
        )
    } else {
        None
    };

    // Optional audit file layer (JSON lines) filtered by target = "audit"
    let audit_layer = if let Some(audit_path) = &config.logging.audit_file_path {
        if let Some(parent) = Path::new(audit_path).parent() {
            if let Err(e) = fs::create_dir_all(parent) {
                eprintln!("Failed to create audit log directory: {}", e);
            }
        }
        let audit_appender = rolling::daily(
            Path::new(audit_path).parent().unwrap_or(Path::new(".")),
            Path::new(audit_path).file_name().unwrap().to_str().unwrap(),
        );
        let (audit_nb, audit_guard) = tracing_appender::non_blocking(audit_appender);
        guards.push(audit_guard);

        Some(
            fmt::Layer::new()
                .event_format(AuditJsonlFormatter)
                .with_writer(audit_nb)
                .with_ansi(false)
                .with_filter(FilterFn::new(|metadata| metadata.target() == "audit")),
        )
    } else {
        None
    };

    tracing_subscriber::registry()
        .with(filter)
        .with(console_layer)
        .with(file_layer)
        .with(audit_layer)
        .init();

    guards
}

fn init_file_and_console_logging(
    file_path: &str,
    filter: EnvFilter,
    guards: &mut Vec<WorkerGuard>,
) {
    if let Some(parent) = Path::new(file_path).parent() {
        if let Err(e) = fs::create_dir_all(parent) {
            eprintln!("Failed to create log directory: {}", e);
            tracing_subscriber::fmt::init();
            return;
        }
    }

    let file_appender = rolling::daily(
        Path::new(file_path).parent().unwrap_or(Path::new(".")),
        Path::new(file_path).file_name().unwrap().to_str().unwrap(),
    );

    let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);
    guards.push(guard);

    tracing_subscriber::registry()
        .with(filter)
        .with(
            fmt::Layer::new()
                .with_writer(io::stdout)
                .with_ansi(true)
                .with_target(true)
                .with_thread_ids(false)
                .with_thread_names(false)
                .with_file(true)
                .with_line_number(true),
        )
        .with(
            fmt::Layer::new()
                .with_writer(non_blocking)
                .with_ansi(false)
                .with_target(true)
                .with_thread_ids(false)
                .with_file(true)
                .with_line_number(true),
        )
        .init();
}

fn init_file_only_logging(file_path: &str, filter: EnvFilter, guards: &mut Vec<WorkerGuard>) {
    if let Some(parent) = Path::new(file_path).parent() {
        if let Err(e) = fs::create_dir_all(parent) {
            eprintln!("Failed to create log directory: {}", e);
            tracing_subscriber::fmt::init();
            return;
        }
    }

    let file_appender = rolling::daily(
        Path::new(file_path).parent().unwrap_or(Path::new(".")),
        Path::new(file_path).file_name().unwrap().to_str().unwrap(),
    );

    let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);
    guards.push(guard);

    tracing_subscriber::registry()
        .with(filter)
        .with(
            fmt::Layer::new()
                .with_writer(non_blocking)
                .with_ansi(false)
                .with_target(true)
                .with_thread_ids(true)
                .with_file(true)
                .with_line_number(true),
        )
        .init();
}

#[actix_web::main]
async fn main() -> anyhow::Result<()> {
    // Load configuration first
    let config = AppConfig::load().expect("Failed to load configuration");

    // Initialize logging with configuration
    let _guards = init_logging(&config);

    info!("Starting Wisdom Vault API server...");

    let bind_address = format!("{}:{}", config.host, config.port);

    info!("Server will bind to: {}", bind_address);

    // Initialize application state with cache integration
    let app_state = AppState::new(config.clone())
        .await
        .expect("Failed to initialize application state");

    info!("Application state initialized successfully");
    info!("Redis cache: connected at {}", config.redis.url);
    info!("Database: connected at {}", config.mongodb.url);
    info!("Qdrant: connected at {}", config.qdrant.url);

    let redis_store = RedisSessionStore::new(&config.redis.url).await?;

    // Start HTTP server
    HttpServer::new(move || {
        App::new()
            .wrap(middleware::cors_from_config(&config.cors))
            .wrap(
                SessionMiddleware::builder(
                    redis_store.clone(),
                    Key::from(&hex::decode(&config.auth.secret).unwrap()),
                )
                .cookie_secure(false)
                .cookie_name(config.auth.cookie_name.clone())
                .session_lifecycle(
                    PersistentSession::default()
                        .session_ttl(Duration::seconds(config.auth.session_expire_seconds)),
                )
                .build(),
            )
            .wrap(middleware::AuditMiddleware::new(
                app_state.audit_logger.clone(),
            ))
            .configure(|cfg: &mut actix_web::web::ServiceConfig| {
                configure_app_state(cfg, app_state.clone())
            })
            .configure(routes::configure)
            .into_utoipa_app()
            .openapi(ApiDoc::openapi())
            .openapi_service(|api| {
                SwaggerUi::new("/swagger-ui/{_:.*}").url("/api-docs/openapi.json", api)
            })
            .into_app()
    })
    .bind(&bind_address)?
    .run()
    .await?;

    Ok(())
}
