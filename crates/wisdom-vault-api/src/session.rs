use actix_session::{SessionExt, SessionGetError};
use actix_web::{
    body::BoxBody,
    dev::Payload,
    http::{header::USER_AGENT, StatusCode},
    FromRequest, HttpRequest, HttpResponse, ResponseError,
};
use derive_more::Display;
use futures_util::future::{ready, FutureExt, LocalBoxFuture, Ready};
use serde::{Deserialize, Serialize};
use tracing::error;
use wisdom_vault_common::{t, time::current_millis};

use crate::types::ApiResponse;

pub const SESSION_NAME: &str = "user";

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSession {
    pub user_id: String,
    pub username: String,
    pub timestamp: i64, // Unix时间戳（秒）
    pub ip_address: String,
    pub user_agent: Option<String>,
}

impl UserSession {
    /// 创建新的用户会话
    pub fn new(
        user_id: String,
        username: String,
        ip_address: String,
        user_agent: Option<String>,
    ) -> Self {
        Self {
            user_id,
            username,
            timestamp: current_millis(),
            ip_address,
            user_agent,
        }
    }

    /// 检查会话是否有效（未过期）
    pub fn is_valid(&self, max_age_minutes: i64) -> bool {
        let now = current_millis();
        let max_age_seconds = (max_age_minutes * 60) as i64;
        now.saturating_sub(self.timestamp) < max_age_seconds
    }

    /// 刷新会话时间戳
    pub fn refresh(&mut self) {
        self.timestamp = current_millis();
    }

    /// 检查IP地址是否匹配（防止会话劫持）
    pub fn validate_ip(&self, current_ip: &str) -> bool {
        self.ip_address == current_ip
    }

    /// 获取会话创建时间的DateTime<Utc>
    pub fn created_at(&self) -> i64 {
        self.timestamp
    }
}

pub struct OptionalUserSession(pub Option<UserSession>);

impl OptionalUserSession {
    pub fn into_inner(self) -> Option<UserSession> {
        self.0
    }
}

impl FromRequest for OptionalUserSession {
    type Future = Ready<Result<Self, Self::Error>>;
    type Error = SessionGetError;

    fn from_request(
        req: &actix_web::HttpRequest,
        _payload: &mut actix_web::dev::Payload,
    ) -> Self::Future {
        let session = req.get_session();

        ready(
            session
                .get::<UserSession>(SESSION_NAME)
                .map(|res| OptionalUserSession(res)),
        )
    }
}

#[derive(Debug, Display)]
pub enum Error {
    #[display("Unauthorized")]
    Unauthorized { locale: String },
    #[display("Internal Server Error: {err}")]
    InternalServerError { err: anyhow::Error, locale: String },
}

impl ResponseError for Error {
    fn status_code(&self) -> StatusCode {
        match self {
            Error::Unauthorized { .. } => StatusCode::UNAUTHORIZED,
            Error::InternalServerError { .. } => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }

    fn error_response(&self) -> HttpResponse<BoxBody> {
        let mut res = HttpResponse::build(self.status_code());

        match self {
            Error::Unauthorized { locale } => res.json(ApiResponse::unauthorized(Some(
                t!("session.expired", locale = locale.as_str()).into(),
            ))),
            Error::InternalServerError { locale, err } => {
                error!("Internal Server Error: {err:?}");
                res.json(ApiResponse::internal_error(Some(
                    t!("system.busy", locale = locale.as_str()).into(),
                )))
            }
        }
    }
}

/// 强制要求用户会话的提取器
pub struct RequiredUserSession(pub UserSession);

impl RequiredUserSession {
    pub fn into_inner(self) -> UserSession {
        self.0
    }
}

impl FromRequest for RequiredUserSession {
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self, Self::Error>>;

    fn from_request(req: &HttpRequest, _payload: &mut Payload) -> Self::Future {
        let session = req.get_session();
        let locale = get_locale(req);

        async move {
            match session.get::<UserSession>(SESSION_NAME) {
                Ok(Some(user_session)) => {
                    // TODO 验证用户状态
                    Ok(RequiredUserSession(user_session))
                }
                Ok(None) => Err(Error::Unauthorized { locale }),
                Err(e) => Err(Error::InternalServerError {
                    err: e.into(),
                    locale,
                }),
            }
        }
        .boxed_local()
    }
}

pub fn get_locale(req: &HttpRequest) -> String {
    req.headers()
        .get("lang")
        .and_then(|header| header.to_str().ok())
        .and_then(|header| header.split(',').next())
        .map(|locale| locale.to_string())
        .unwrap_or_else(|| "zh".to_string())
}

/// 获取客户端IP地址的工具函数
pub fn get_client_ip(req: &HttpRequest) -> String {
    // 首先尝试从Forwarded/X-Forwarded-For头获取
    if let Some(real_ip) = req.connection_info().realip_remote_addr() {
        return real_ip.to_string();
    }

    // 然后尝试从X-Real-IP头获取
    if let Some(real_ip) = req.headers().get("X-Real-IP") {
        if let Ok(real_ip_str) = real_ip.to_str() {
            return real_ip_str.to_string();
        }
    }

    // 最后使用连接信息
    req.connection_info()
        .peer_addr()
        .unwrap_or("unknown")
        .to_string()
}

/// 获取User-Agent的工具函数
pub fn get_user_agent(req: &HttpRequest) -> Option<String> {
    req.headers()
        .get(USER_AGENT)?
        .to_str()
        .ok()
        .map(|s| s.to_string())
}
