use actix_web::web;
use anyhow::Result;
use std::sync::Arc;
use tokio::sync::Mutex;
// use utoipa_actix_web::service_config::ServiceConfig;

use wisdom_vault_core::{
    AuditConfig, AuditLogger, AuthService, BatchVectorizationConfig, DocumentClassificationService,
    DocumentRelationService,
    services::{
        BatchDocumentVectorization,
        DocumentChunkingService,
        DocumentParserService,
        DocumentProcessingPipeline,
        DocumentService,
        EmbeddingModelManager,
        HybridSearchService,
        IncrementalUpdateConfig,
        IncrementalVectorUpdate,
        KeywordSearchService,
        // PersonalizedSearchService,  // 暂时禁用
        ProcessingTaskService,
        TaskProcessor,
        TaskSchedulerConfig,
        TextVectorizationConfig,
        TextVectorizationPipeline,
        TikaConfig,
        VectorQualityAssessment,
        VectorQualityConfig,
        VectorSearchConfig,
        VectorSearchService,
        VectorizationConfig,
        VectorizationService,
        VectorizationTaskScheduler,
    },
};
use wisdom_vault_database::{
    CacheInvalidationSystem, CacheService, CachedUserRepository, DataCache, DatabaseManager,
    MongoCategoryRepository, MongoDocumentCategoryRepository, MongoDocumentRelationRepository,
    QdrantManager, QdrantVectorRepository, RedisCacheEventHandler, VectorRepository,
    establish_cache_connection,
    repositories::{
        MongoConversationRepository, MongoDocumentChunkRepository, MongoDocumentRepository,
        MongoKnowledgeBaseRepository, MongoMessageRepository, MongoPermissionRepository,
        MongoProcessingTaskRepository, MongoRolePermissionRepository, MongoRoleRepository,
        MongoTagRepository, MongoUserRepository, MongoUserRoleRepository,
    },
};

use crate::{
    config::AppConfig,
    openobserve_client::{OpenObserveClient, OpenObserveConfig},
};

/// Shared application state containing database connections and services
pub struct AppState {
    pub db_manager: Arc<DatabaseManager>,
    pub qdrant_manager: Arc<QdrantManager>,
    pub auth_service: AuthService,
    pub audit_logger: AuditLogger,
    pub cache_service: CacheService,
    pub data_cache: DataCache,
    pub user_repository: Arc<Mutex<CachedUserRepository<MongoUserRepository>>>,
    pub cache_invalidation: Arc<CacheInvalidationSystem>,
    pub processing_task_service: Arc<ProcessingTaskService>,
    pub document_processing_pipeline: Arc<DocumentProcessingPipeline>,
    pub document_service: Arc<DocumentService>,
    pub document_chunking_service: Arc<DocumentChunkingService>,
    pub document_chunk_repository: Arc<MongoDocumentChunkRepository>,
    pub vector_repository: Arc<QdrantVectorRepository>,
    pub task_processor: Arc<TaskProcessor>,
    // 文件存储服务
    pub file_storage_service: Arc<dyn wisdom_vault_core::FileStorageServiceTrait>,
    // 向量化相关服务
    pub vectorization_service: Arc<VectorizationService>,
    pub incremental_update_service: Arc<IncrementalVectorUpdate>,
    pub batch_vectorization_service: Arc<BatchDocumentVectorization>,
    pub task_scheduler: Arc<VectorizationTaskScheduler>,
    pub model_manager: Arc<EmbeddingModelManager>,
    pub quality_assessment_service: Arc<VectorQualityAssessment>,
    pub text_vectorization_pipeline: Arc<TextVectorizationPipeline>,
    pub vector_search_service: Arc<VectorSearchService>,
    // 搜索相关服务
    pub keyword_search_service: Arc<KeywordSearchService>,
    pub hybrid_search_service: Arc<HybridSearchService>,
    // Repository services
    pub conversation_repository: Arc<MongoConversationRepository>,
    pub message_repository: Arc<MongoMessageRepository>,
    pub knowledge_base_repository: Arc<MongoKnowledgeBaseRepository>,
    pub tag_repository: Arc<MongoTagRepository>,
    pub config: AppConfig,
    pub openobserve: Option<OpenObserveClient>,
}

impl Clone for AppState {
    fn clone(&self) -> Self {
        Self {
            db_manager: self.db_manager.clone(),
            qdrant_manager: self.qdrant_manager.clone(),
            auth_service: self.auth_service.clone(),
            audit_logger: self.audit_logger.clone(),
            cache_service: self.cache_service.clone(),
            data_cache: self.data_cache.clone(),
            user_repository: self.user_repository.clone(),
            cache_invalidation: self.cache_invalidation.clone(),
            processing_task_service: self.processing_task_service.clone(),
            document_processing_pipeline: self.document_processing_pipeline.clone(),
            document_service: self.document_service.clone(),
            document_chunking_service: self.document_chunking_service.clone(),
            document_chunk_repository: self.document_chunk_repository.clone(),
            vector_repository: self.vector_repository.clone(),
            task_processor: self.task_processor.clone(),
            // 文件存储服务
            file_storage_service: self.file_storage_service.clone(),
            // 向量化相关服务
            vectorization_service: self.vectorization_service.clone(),
            incremental_update_service: self.incremental_update_service.clone(),
            batch_vectorization_service: self.batch_vectorization_service.clone(),
            task_scheduler: self.task_scheduler.clone(),
            model_manager: self.model_manager.clone(),
            quality_assessment_service: self.quality_assessment_service.clone(),
            text_vectorization_pipeline: self.text_vectorization_pipeline.clone(),
            vector_search_service: self.vector_search_service.clone(),
            // 搜索相关服务
            keyword_search_service: self.keyword_search_service.clone(),
            hybrid_search_service: self.hybrid_search_service.clone(),
            // Repository services
            conversation_repository: self.conversation_repository.clone(),
            message_repository: self.message_repository.clone(),
            knowledge_base_repository: self.knowledge_base_repository.clone(),
            tag_repository: self.tag_repository.clone(),
            config: self.config.clone(),
            openobserve: self.openobserve.clone(),
        }
    }
}

impl AppState {
    pub async fn new(config: AppConfig) -> Result<Self> {
        // 初始化MongoDB数据库连接
        let db_config = wisdom_vault_database::MongoDBConfig {
            url: config.mongodb.url.clone(),
            max_pool_size: Some(config.mongodb.max_pool_size),
            min_pool_size: Some(config.mongodb.min_pool_size),
            max_idle_time_ms: Some(config.mongodb.max_idle_time_ms),
            connect_timeout_ms: Some(config.mongodb.connect_timeout_ms),
            server_selection_timeout_ms: Some(config.mongodb.server_selection_timeout_ms),
        };
        let db_manager = Arc::new(DatabaseManager::new(db_config));
        db_manager.initialize().await?;

        // 初始化Qdrant向量数据库连接
        let qdrant_config = wisdom_vault_database::QdrantConfig {
            url: config.qdrant.url.clone(),
            api_key: if config.qdrant.api_key.is_empty() {
                None
            } else {
                Some(config.qdrant.api_key.clone())
            },
            connect_timeout_ms: Some(config.qdrant.connect_timeout_ms),
            request_timeout_ms: Some(config.qdrant.request_timeout_ms),
            enable_tls: config.qdrant.enable_tls,
        };
        let qdrant_manager = Arc::new(QdrantManager::new(qdrant_config));
        qdrant_manager.initialize().await?;

        // 建立缓存连接
        let cache_connection = establish_cache_connection(&config.redis.url).await?;
        let cache_service = CacheService::new(cache_connection);
        let data_cache = DataCache::new(cache_service.clone());

        // 获取数据库连接
        let db_connection = db_manager.get_connection().await?;
        let qdrant_connection = qdrant_manager.get_connection().await?;

        // 初始化数据库schema和索引
        wisdom_vault_database::initialize_schema(&db_connection).await?;

        // 创建Repository实例用于数据初始化
        let user_repository = Arc::new(MongoUserRepository::new(&db_connection));
        let role_repository = Arc::new(MongoRoleRepository::new(&db_connection));
        let permission_repository = Arc::new(MongoPermissionRepository::new(&db_connection));
        let user_role_repository = Arc::new(MongoUserRoleRepository::new(&db_connection));
        let role_permission_repository =
            Arc::new(MongoRolePermissionRepository::new(&db_connection));

        // 执行系统数据初始化
        let data_init_service = wisdom_vault_core::DataInitializationService::new(
            user_repository.clone(),
            role_repository.clone(),
            permission_repository.clone(),
            user_role_repository.clone(),
            role_permission_repository.clone(),
        );

        let admin_config = wisdom_vault_core::InitializationConfig {
            admin_username: config.admin.username.clone(),
            admin_email: config.admin.email.clone(),
            admin_password: config.admin.password.clone(),
            admin_full_name: config.admin.full_name.clone(),
            force_reinit: config.admin.force_reinit,
        };

        if let Err(e) = data_init_service.initialize_system(admin_config).await {
            tracing::warn!("系统数据初始化失败: {}", e);
        }

        let document_repository = Arc::new(MongoDocumentRepository::new(&db_connection));
        let document_relation_repository =
            Arc::new(MongoDocumentRelationRepository::new(&db_connection));
        let document_category_repository =
            Arc::new(MongoDocumentCategoryRepository::new(&db_connection));
        let category_repository = Arc::new(MongoCategoryRepository::new(&db_connection));
        let document_chunk_repository = Arc::new(MongoDocumentChunkRepository::new(&db_connection));
        let processing_task_repository =
            Arc::new(MongoProcessingTaskRepository::new(&db_connection));
        let conversation_repository = Arc::new(MongoConversationRepository::new(&db_connection));
        let message_repository = Arc::new(MongoMessageRepository::new(&db_connection));
        let knowledge_base_repository = Arc::new(MongoKnowledgeBaseRepository::new(&db_connection));
        let tag_repository = Arc::new(MongoTagRepository::new(&db_connection));

        // 创建向量Repository
        let vector_repository = Arc::new(QdrantVectorRepository::new(qdrant_connection));
        // 初始化向量集合
        vector_repository.initialize_collections().await?;

        // 创建Auth服务
        let auth_service = AuthService::new(user_repository.clone());

        // 创建审计日志服务
        let audit_config = AuditConfig {
            enabled: config.audit.enabled,
            log_level: config.audit.log_level.clone(),
            log_success: config.audit.log_success,
            log_failure: config.audit.log_failure,
            log_sensitive: config.audit.log_sensitive,
            exclude_paths: config.audit.exclude_paths.clone(),
            include_actions: config.audit.include_actions.clone(),
        };
        let audit_logger = AuditLogger::new(audit_config);

        let cached_user_repo = CachedUserRepository::new(user_repository, data_cache.clone());
        let user_repository = Arc::new(Mutex::new(cached_user_repo));

        // 设置缓存失效系统
        let cache_invalidation = Arc::new(CacheInvalidationSystem::new());
        let cache_handler = RedisCacheEventHandler::new(cache_service.clone());
        cache_invalidation
            .add_handler(Box::new(cache_handler))
            .await;
        cache_invalidation.start_event_loop().await;

        // 创建处理任务服务
        let processing_task_service =
            Arc::new(ProcessingTaskService::new(processing_task_repository));

        // 创建文档服务
        let document_service = Arc::new(DocumentService::new(document_repository.clone()));

        // 创建文档分块服务
        let document_chunking_service = Arc::new(DocumentChunkingService::new(
            document_chunk_repository.clone(),
        )?);

        // 创建文档解析服务
        let tika_config = TikaConfig::from_app_config_tika(
            &config.tika.server_url,
            config.tika.timeout_seconds,
            config.tika.max_retries,
            &config.tika.supported_mime_types,
        );
        let document_parser_service = Arc::new(DocumentParserService::new(tika_config)?);

        // 创建文件存储服务
        let file_storage_service = wisdom_vault_core::FileStorageServiceFactory::create(
            &wisdom_vault_core::FileStorageConfig {
                max_file_size: config.file_storage.max_file_size,
                allowed_extensions: config.file_storage.allowed_extensions.clone(),
                cleanup_interval_hours: config.file_storage.cleanup_interval_hours,
                gridfs_bucket: config.file_storage.gridfs_bucket.clone(),
                gridfs_chunk_size_kb: config.file_storage.gridfs_chunk_size_kb,
            },
            db_connection.database(),
        )
        .await?;

        // 创建文本向量化管道
        let text_vectorization_config = TextVectorizationConfig::default();
        let text_vectorization_pipeline =
            Arc::new(TextVectorizationPipeline::new(text_vectorization_config)?);

        // 创建嵌入模型管理器
        let model_config = wisdom_vault_core::services::ModelManagerConfig::default();
        let model_manager = Arc::new(EmbeddingModelManager::new(model_config)?);

        // 创建向量质量评估服务
        let quality_config = VectorQualityConfig {
            quality_threshold: 0.8,
            anomaly_threshold: 0.95,
            min_magnitude: 0.1,
            max_sparsity_ratio: 0.9,
            similarity_sample_size: 100,
        };
        let quality_assessment_service = Arc::new(VectorQualityAssessment::new(quality_config));

        // 创建向量化服务（需要适配新的向量存储）
        let vectorization_config = VectorizationConfig::default();
        let vectorization_service = Arc::new(VectorizationService::new(
            model_manager.clone(),
            document_repository.clone(),
            document_chunk_repository.clone(),
            vector_repository.clone(), // 使用Qdrant向量存储
            text_vectorization_pipeline.clone(),
            vectorization_config,
        ));

        let document_classification_service = Arc::new(DocumentClassificationService::new(
            document_repository.clone(),
            category_repository.clone(),
            document_category_repository.clone(),
        ));

        let document_relation_service = Arc::new(DocumentRelationService::new(
            document_repository.clone(),
            document_relation_repository.clone(),
        ));

        // 创建文档处理管道
        let document_processing_pipeline = Arc::new(DocumentProcessingPipeline::new(
            document_service.clone(),
            processing_task_service.clone(),
            document_parser_service,
            file_storage_service.clone(),
            vectorization_service.clone(),
            document_classification_service.clone(),
            document_relation_service.clone(),
        ));

        // 创建任务处理器
        let task_processor = Arc::new(TaskProcessor::new(
            processing_task_service.clone(),
            document_processing_pipeline.clone(),
        ));

        // 创建增量向量更新服务
        let incremental_config = IncrementalUpdateConfig::default();
        let incremental_update_service = Arc::new(IncrementalVectorUpdate::new(
            vectorization_service.clone(),
            document_repository.clone(),
            document_chunk_repository.clone(),
            document_chunking_service.clone(),
            incremental_config,
        ));

        // 创建批量文档向量化服务
        let batch_config = BatchVectorizationConfig::default();
        let batch_vectorization_service = Arc::new(BatchDocumentVectorization::new(
            vectorization_service.clone(),
            quality_assessment_service.clone(),
            document_repository.clone(),
            document_chunk_repository.clone(),
            batch_config,
        ));

        // 创建任务调度器
        let scheduler_config = TaskSchedulerConfig::default();
        let task_scheduler = Arc::new(VectorizationTaskScheduler::new(
            scheduler_config,
            vectorization_service.clone(),
            incremental_update_service.clone(),
            batch_vectorization_service.clone(),
            model_manager.clone(),
        ));

        // 启动任务调度器
        if let Err(e) = task_scheduler.start().await {
            tracing::warn!("启动向量化任务调度器失败: {}", e);
        }

        // 创建向量搜索服务
        let vector_search_config = VectorSearchConfig::default();
        let vector_search_service = Arc::new(VectorSearchService::new(
            vector_repository.clone(), // 使用Qdrant向量存储
            model_manager.clone(),
            text_vectorization_pipeline.clone(),
            vector_search_config,
        ));

        // 创建关键词搜索服务
        let keyword_search_service = Arc::new(KeywordSearchService::new_with_defaults(
            document_repository.clone(),
        ));

        // 初始化 OpenObserve 客户端（如果配置存在且凭据有效）
        let openobserve = config.openobserve.as_ref().and_then(|oo| {
            if oo.email.is_empty() || oo.token.is_empty() {
                tracing::info!("OpenObserve config present but email/token empty; client disabled");
                None
            } else {
                let cfg = OpenObserveConfig {
                    base_url: oo.base_url.clone(),
                    org_id: oo.org_id.clone(),
                    stream: oo.stream.clone(),
                    email: oo.email.clone(),
                    token: oo.token.clone(),
                    timeout_seconds: oo.timeout_seconds.unwrap_or(30),
                };
                match OpenObserveClient::new(cfg) {
                    Ok(c) => Some(c),
                    Err(e) => {
                        tracing::warn!("Failed to init OpenObserve client: {}", e);
                        None
                    }
                }
            }
        });

        // 创建混合搜索服务
        let hybrid_search_service = Arc::new(HybridSearchService::new_with_defaults(
            keyword_search_service.clone(),
            vector_search_service.clone(),
        ));

        Ok(Self {
            db_manager,
            qdrant_manager,
            auth_service,
            audit_logger,
            cache_service,
            data_cache,
            user_repository,
            cache_invalidation,
            processing_task_service,
            document_processing_pipeline,
            document_service,
            document_chunking_service,
            document_chunk_repository,
            vector_repository,
            task_processor,
            // 文件存储服务
            file_storage_service,
            // 向量化相关服务
            vectorization_service,
            incremental_update_service,
            batch_vectorization_service,
            task_scheduler,
            model_manager,
            quality_assessment_service,
            text_vectorization_pipeline,
            vector_search_service,
            // 搜索相关服务
            keyword_search_service,
            hybrid_search_service,
            // Repository服务
            conversation_repository,
            message_repository,
            knowledge_base_repository,
            tag_repository,
            config,
            openobserve,
        })
    }

    /// Get a clone of the cache invalidation system for emitting events
    pub fn cache_events(&self) -> Arc<CacheInvalidationSystem> {
        self.cache_invalidation.clone()
    }

    /// Get cache metrics for monitoring
    pub async fn get_cache_metrics(&self) -> wisdom_vault_database::CacheMetrics {
        let repo = self.user_repository.lock().await;
        repo.get_cache_metrics().clone()
    }

    /// Health check that includes cache connectivity
    pub async fn health_check(&mut self) -> Result<HealthStatus> {
        let mut status = HealthStatus {
            database: false,
            cache: false,
            overall: false,
        };

        // Check database
        match self.db_manager.health_check().await {
            Ok(_) => status.database = true,
            Err(e) => tracing::warn!("Database health check failed: {}", e),
        }

        // Check cache
        match self.cache_service.set("health_check", "ok", Some(10)).await {
            Ok(_) => match self.cache_service.get::<&str, String>("health_check").await {
                Ok(Some(_)) => status.cache = true,
                Ok(None) => tracing::warn!("Cache health check: key not found"),
                Err(e) => tracing::warn!("Cache health check read failed: {}", e),
            },
            Err(e) => tracing::warn!("Cache health check write failed: {}", e),
        }

        status.overall = status.database && status.cache;
        Ok(status)
    }
}

#[derive(Debug, serde::Serialize)]
pub struct HealthStatus {
    pub database: bool,
    pub cache: bool,
    pub overall: bool,
}

/// Configure the application with the shared state
pub fn configure_app_state(cfg: &mut actix_web::web::ServiceConfig, app_state: AppState) {
    cfg.app_data(web::Data::new(app_state));
}
