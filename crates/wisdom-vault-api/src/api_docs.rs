use utoipa::OpenApi;
use wisdom_vault_api::handlers;

#[derive(OpenApi)]
#[openapi(
        paths(handlers::health::health_check,
             handlers::auth::login),
        tags(
            (name = "Wisdom Vault API", description = "企业智能知识库与问答系统")
        ),
        components(
            schemas(handlers::auth::LoginRequest),
        )
        // modifiers(&SecurityAddon)
    )]
pub struct ApiDoc;
