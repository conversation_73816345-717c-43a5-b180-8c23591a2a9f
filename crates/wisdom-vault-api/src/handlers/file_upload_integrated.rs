use actix_multipart::Multipart;
use actix_web::{HttpResponse, Result, web};
use futures_util::TryStreamExt;
use tracing::{error, info, warn};
use uuid::Uuid;
use validator::Validate;

use crate::{
    app_state::AppState,
    types::{ApiResponse, FileUploadResponse, UploadFileRequest},
};
use wisdom_vault_auth::Claims;
use wisdom_vault_database::models::{TaskContext, TaskPriority, TaskType};

const MAX_FILE_SIZE: usize = 100 * 1024 * 1024; // 100MB

/// 带任务集成的文件上传处理器
pub async fn upload_file_with_tasks(
    mut payload: Multipart,
    app_state: web::Data<AppState>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let mut file_data: Option<Vec<u8>> = None;
    let mut filename: Option<String> = None;
    let mut content_type: Option<String> = None;
    let mut upload_request: Option<UploadFileRequest> = None;

    // Process multipart form data
    while let Some(mut field) = payload.try_next().await? {
        let field_name = field.name().unwrap_or("").to_string();

        match field_name.as_str() {
            "file" => {
                // Extract file metadata
                if let Some(content_disposition) = field.content_disposition() {
                    filename = content_disposition.get_filename().map(|f| f.to_string());
                }

                content_type = field.content_type().map(|ct| ct.to_string());

                // Read file data
                let mut bytes = Vec::new();
                while let Some(chunk) = field.try_next().await? {
                    bytes.extend_from_slice(&chunk);

                    if bytes.len() > MAX_FILE_SIZE {
                        return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
                            "File size exceeds maximum allowed size".to_string(),
                        )));
                    }
                }
                file_data = Some(bytes);
            }
            "metadata" => {
                // Parse upload metadata
                let mut metadata_bytes = Vec::new();
                while let Some(chunk) = field.try_next().await? {
                    metadata_bytes.extend_from_slice(&chunk);
                }

                if let Ok(metadata_str) = String::from_utf8(metadata_bytes) {
                    match serde_json::from_str::<UploadFileRequest>(&metadata_str) {
                        Ok(req) => {
                            if let Err(validation_errors) = req.validate() {
                                return Ok(HttpResponse::BadRequest().json(
                                    ApiResponse::<()>::error(format!(
                                        "Validation failed: {:?}",
                                        validation_errors
                                    )),
                                ));
                            }
                            upload_request = Some(req);
                        }
                        Err(e) => {
                            warn!("Failed to parse upload metadata: {}", e);
                        }
                    }
                }
            }
            _ => {
                // Skip unknown fields
                while let Some(_chunk) = field.try_next().await? {}
            }
        }
    }

    // Validate required fields
    let file_data =
        file_data.ok_or_else(|| actix_web::error::ErrorBadRequest("No file provided"))?;

    let filename =
        filename.ok_or_else(|| actix_web::error::ErrorBadRequest("No filename provided"))?;

    let content_type = content_type.unwrap_or_else(|| "application/octet-stream".to_string());

    let upload_request = upload_request
        .ok_or_else(|| {
            warn!("No upload metadata provided, using defaults");
            UploadFileRequest {
                knowledge_base_id: None,
                category_id: None,
                tags: Vec::new(),
                title: None,
                description: None,
                metadata: None,
                enable_ocr: Some(true),
                auto_classify: Some(true),
            }
        })
        .unwrap_or_default();

    info!(
        "Processing file upload: {} ({} bytes)",
        filename,
        file_data.len()
    );

    // Step 1: Store the file using file storage service
    let file_metadata = match app_state
        .document_processing_pipeline
        .file_storage_service()
        .store_file(file_data, &filename, &content_type, claims.user_id)
        .await
    {
        Ok(metadata) => metadata,
        Err(e) => {
            error!("Failed to store file: {}", e);
            return Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("Failed to store file".to_string())));
        }
    };

    // Step 2: Create processing tasks
    let mut task_ids = Vec::new();
    let resource_id = file_metadata.file_id;

    // Create task context
    let task_context = TaskContext {
        file_path: Some(file_metadata.file_path.to_string_lossy().to_string()),
        original_filename: Some(filename.clone()),
        mime_type: Some(content_type.clone()),
        file_size: Some(file_metadata.file_size as i64),
        knowledge_base_id: upload_request.knowledge_base_id,
        user_id: Some(claims.user_id),
        additional_params: serde_json::json!({
            "auto_classify": upload_request.auto_classify.unwrap_or(false),
            "enable_ocr": upload_request.enable_ocr.unwrap_or(false),
            "categories": upload_request.category_id,
            "tags": upload_request.tags
        }),
    };

    // Create document parsing task
    match app_state
        .processing_task_service
        .create_task(
            TaskType::DocumentParsing,
            resource_id,
            TaskPriority::Normal,
            task_context.clone(),
        )
        .await
    {
        Ok(task) => {
            info!("Created document parsing task: {}", task.id);
            task_ids.push(task.id);
        }
        Err(e) => {
            error!("Failed to create document parsing task: {}", e);
        }
    }

    // Create metadata extraction task
    match app_state
        .processing_task_service
        .create_task(
            TaskType::MetadataExtraction,
            resource_id,
            TaskPriority::Normal,
            task_context.clone(),
        )
        .await
    {
        Ok(task) => {
            info!("Created metadata extraction task: {}", task.id);
            task_ids.push(task.id);
        }
        Err(e) => {
            error!("Failed to create metadata extraction task: {}", e);
        }
    }

    // Create document indexing task (lower priority, run after parsing)
    match app_state
        .processing_task_service
        .create_task(
            TaskType::DocumentIndexing,
            resource_id,
            TaskPriority::Low,
            task_context,
        )
        .await
    {
        Ok(task) => {
            info!("Created document indexing task: {}", task.id);
            task_ids.push(task.id);
        }
        Err(e) => {
            error!("Failed to create document indexing task: {}", e);
        }
    }

    // Step 3: Return response with task information
    let response = FileUploadResponse {
        file_id: file_metadata.file_id,
        filename: file_metadata.original_filename,
        file_size: file_metadata.file_size,
        content_type: content_type.clone(),
        upload_url: format!("/api/v1/files/{}/download", file_metadata.file_id),
        processing_status: "queued".to_string(),
        task_ids: task_ids.clone(),
        estimated_processing_time: Some(estimate_processing_time(
            &content_type,
            file_metadata.file_size,
        )),
        message: format!(
            "File uploaded successfully. {} processing tasks created.",
            task_ids.len()
        ),
    };

    info!(
        "File upload completed successfully: {} with {} tasks",
        file_metadata.file_id,
        task_ids.len()
    );

    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

/// 根据文件类型和大小估算处理时间
fn estimate_processing_time(content_type: &str, file_size: u64) -> u32 {
    let base_time = match content_type {
        ct if ct.contains("pdf") => 30,        // PDF文件基础处理时间30秒
        ct if ct.contains("word") => 20,       // Word文档20秒
        ct if ct.contains("text") => 10,       // 文本文件10秒
        ct if ct.contains("excel") => 25,      // Excel文件25秒
        ct if ct.contains("powerpoint") => 35, // PPT文件35秒
        _ => 15,                               // 其他文件类型15秒
    };

    // 根据文件大小调整时间 (每MB增加2秒)
    let size_factor = (file_size / (1024 * 1024)) as u32 * 2;

    base_time + size_factor
}

/// 批量文件上传处理器（集成任务系统）
pub async fn batch_upload_files_with_tasks(
    mut payload: Multipart,
    app_state: web::Data<AppState>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let mut files: Vec<(Vec<u8>, String, String)> = Vec::new();
    let mut upload_request: Option<UploadFileRequest> = None;

    // Process multipart form data
    while let Some(mut field) = payload.try_next().await? {
        let field_name = field.name().unwrap_or("").to_string();

        if field_name.starts_with("files") {
            // Handle multiple file fields
            let mut filename = "unknown".to_string();
            let mut content_type = "application/octet-stream".to_string();

            if let Some(content_disposition) = field.content_disposition() {
                if let Some(name) = content_disposition.get_filename() {
                    filename = name.to_string();
                }
            }

            if let Some(ct) = field.content_type() {
                content_type = ct.to_string();
            }

            // Read file data
            let mut bytes = Vec::new();
            while let Some(chunk) = field.try_next().await? {
                bytes.extend_from_slice(&chunk);

                if bytes.len() > MAX_FILE_SIZE {
                    return Ok(
                        HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                            "File {} exceeds maximum allowed size",
                            filename
                        ))),
                    );
                }
            }

            files.push((bytes, filename, content_type));

            if files.len() > 10 {
                // 最多10个文件
                return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
                    "Too many files in batch upload".to_string(),
                )));
            }
        } else if field_name == "metadata" {
            // Parse upload metadata
            let mut metadata_bytes = Vec::new();
            while let Some(chunk) = field.try_next().await? {
                metadata_bytes.extend_from_slice(&chunk);
            }

            if let Ok(metadata_str) = String::from_utf8(metadata_bytes) {
                if let Ok(req) = serde_json::from_str::<UploadFileRequest>(&metadata_str) {
                    if req.validate().is_ok() {
                        upload_request = Some(req);
                    }
                }
            }
        } else {
            // Skip unknown fields
            while let Some(_chunk) = field.try_next().await? {}
        }
    }

    if files.is_empty() {
        return Ok(HttpResponse::BadRequest()
            .json(ApiResponse::<()>::error("No files provided".to_string())));
    }

    let upload_request = upload_request.unwrap_or_default();
    let mut results = Vec::new();
    let mut total_tasks = 0;

    info!("Processing batch upload of {} files", files.len());

    // Process each file
    for (file_data, filename, content_type) in files {
        // Store file
        match app_state
            .document_processing_pipeline
            .file_storage_service()
            .store_file(file_data, &filename, &content_type, claims.user_id)
            .await
        {
            Ok(file_metadata) => {
                let resource_id = file_metadata.file_id;
                let mut task_ids = Vec::new();

                // Create tasks for this file
                let task_context = TaskContext {
                    file_path: Some(file_metadata.file_path.to_string_lossy().to_string()),
                    original_filename: Some(filename.clone()),
                    mime_type: Some(content_type.clone()),
                    file_size: Some(file_metadata.file_size as i64),
                    knowledge_base_id: upload_request.knowledge_base_id,
                    user_id: Some(claims.user_id),
                    additional_params: serde_json::json!({
                        "auto_classify": upload_request.auto_classify.unwrap_or(false),
                        "enable_ocr": upload_request.enable_ocr.unwrap_or(false),
                        "batch_upload": true
                    }),
                };

                // Create processing tasks
                for task_type in &[
                    TaskType::DocumentParsing,
                    TaskType::MetadataExtraction,
                    TaskType::DocumentIndexing,
                ] {
                    match app_state
                        .processing_task_service
                        .create_task(
                            task_type.clone(),
                            resource_id,
                            TaskPriority::Normal,
                            task_context.clone(),
                        )
                        .await
                    {
                        Ok(task) => task_ids.push(task.id),
                        Err(e) => error!(
                            "Failed to create task {:?} for file {}: {}",
                            task_type, filename, e
                        ),
                    }
                }

                total_tasks += task_ids.len();

                results.push(FileUploadResponse {
                    file_id: file_metadata.file_id,
                    filename: file_metadata.original_filename,
                    file_size: file_metadata.file_size,
                    content_type: content_type.clone(),
                    upload_url: format!("/api/v1/files/{}/download", file_metadata.file_id),
                    processing_status: "queued".to_string(),
                    task_ids,
                    estimated_processing_time: Some(estimate_processing_time(
                        &content_type,
                        file_metadata.file_size,
                    )),
                    message: "File uploaded and queued for processing".to_string(),
                });
            }
            Err(e) => {
                error!("Failed to store file {}: {}", filename, e);
                results.push(FileUploadResponse {
                    file_id: Uuid::new_v4(), // Placeholder
                    filename: filename.clone(),
                    file_size: 0,
                    content_type,
                    upload_url: String::new(),
                    processing_status: "failed".to_string(),
                    task_ids: Vec::new(),
                    estimated_processing_time: None,
                    message: format!("Failed to upload file: {}", e),
                });
            }
        }
    }

    info!(
        "Batch upload completed: {} files, {} total tasks created",
        results.len(),
        total_tasks
    );

    Ok(HttpResponse::Ok().json(ApiResponse::success(results)))
}
