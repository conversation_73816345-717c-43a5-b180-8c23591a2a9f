use actix_web::{HttpResponse, Result as ActixResult, web};
use std::sync::Arc;
use uuid::Uuid;
use wisdom_vault_auth::Claims;
use wisdom_vault_core::services::DocumentTaggingService;
use wisdom_vault_database::{
    connection::DatabaseConnection,
    repositories::{SurrealDocumentRepository, SurrealDocumentTagRepository, SurrealTagRepository},
};

use crate::types::{ApiResponse, tag::*};

/// 为文档添加标签
pub async fn tag_document(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<(Uuid, Uuid)>, // (document_id, tag_id)
) -> ActixResult<HttpResponse> {
    let (document_id, tag_id) = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    match service
        .tag_document(document_id, tag_id, claims.user_id)
        .await
    {
        Ok(document_tag) => {
            let response = TagDocumentResponse {
                id: document_tag.id,
                document_id: document_tag.document_id,
                tag_id: document_tag.tag_id,
                tagged_by: document_tag.tagged_by,
                tagged_at: document_tag.tagged_at,
            };
            Ok(HttpResponse::Created().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to tag document: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                    "Failed to tag document: {}",
                    e
                ))),
            )
        }
    }
}

/// 从文档移除标签
pub async fn untag_document(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<(Uuid, Uuid)>, // (document_id, tag_id)
) -> ActixResult<HttpResponse> {
    let (document_id, tag_id) = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    match service.untag_document(document_id, tag_id).await {
        Ok(true) => Ok(
            HttpResponse::Ok().json(ApiResponse::success(serde_json::json!({
                "removed": true,
                "document_id": document_id,
                "tag_id": tag_id
            }))),
        ),
        Ok(false) => Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error(
            "Tag relationship not found".to_string(),
        ))),
        Err(e) => {
            tracing::error!("Failed to untag document: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 批量为文档添加标签
pub async fn batch_tag_documents(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    request: web::Json<BatchTagDocumentsRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    match service
        .batch_tag_documents(request.document_ids, request.tag_id, claims.user_id)
        .await
    {
        Ok(document_tags) => {
            let responses: Vec<TagDocumentResponse> = document_tags
                .into_iter()
                .map(|dt| TagDocumentResponse {
                    id: dt.id,
                    document_id: dt.document_id,
                    tag_id: dt.tag_id,
                    tagged_by: dt.tagged_by,
                    tagged_at: dt.tagged_at,
                })
                .collect();

            let response = BatchTagDocumentsResponse {
                tagged_count: responses.len(),
                document_tags: responses,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to batch tag documents: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                    "Failed to batch tag documents: {}",
                    e
                ))),
            )
        }
    }
}

/// 批量从文档移除标签
pub async fn batch_untag_documents(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    request: web::Json<BatchUntagDocumentsRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    match service
        .batch_untag_documents(request.document_ids, request.tag_id)
        .await
    {
        Ok(removed_count) => {
            let response = BatchUntagDocumentsResponse {
                removed_count,
                tag_id: request.tag_id,
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to batch untag documents: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 获取文档的标签推荐
pub async fn suggest_tags_for_document(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
    query: web::Query<TagSuggestionQuery>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();
    let query = query.into_inner();
    let max_suggestions = query.max_suggestions.unwrap_or(10).min(50);

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    // 获取详细推荐或简单推荐
    let result = if query.detailed.unwrap_or(false) {
        match service
            .get_detailed_tag_suggestions(document_id, max_suggestions)
            .await
        {
            Ok(suggestions) => {
                let detailed_suggestions: Vec<DetailedTagSuggestionResponse> = suggestions
                    .into_iter()
                    .map(|s| DetailedTagSuggestionResponse {
                        tag: TagResponse::from(s.tag),
                        confidence: s.confidence,
                        suggestion_type: format!("{:?}", s.suggestion_type),
                        explanation: s.explanation,
                    })
                    .collect();

                Ok(TagSuggestionsResponse::Detailed(detailed_suggestions))
            }
            Err(e) => Err(e),
        }
    } else {
        match service
            .suggest_tags_for_document(document_id, max_suggestions)
            .await
        {
            Ok(suggestions) => {
                let simple_suggestions: Vec<SimpleTagSuggestionResponse> = suggestions
                    .into_iter()
                    .map(|(tag, confidence)| SimpleTagSuggestionResponse {
                        tag: TagResponse::from(tag),
                        confidence,
                    })
                    .collect();

                Ok(TagSuggestionsResponse::Simple(simple_suggestions))
            }
            Err(e) => Err(e),
        }
    };

    match result {
        Ok(response) => Ok(HttpResponse::Ok().json(ApiResponse::success(response))),
        Err(e) => {
            tracing::error!("Failed to get tag suggestions: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 获取文档的所有标签
pub async fn get_document_tags(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    match service.get_document_tags(document_id).await {
        Ok(tags) => {
            let tag_responses: Vec<TagResponse> = tags.into_iter().map(TagResponse::from).collect();

            let response = DocumentTagsResponse {
                document_id,
                tags: tag_responses,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get document tags: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 根据标签查找文档
pub async fn find_documents_by_tags(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    request: web::Json<FindDocumentsByTagsRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    match service
        .find_documents_by_tags(request.tag_ids, request.match_all.unwrap_or(false))
        .await
    {
        Ok(documents) => {
            use crate::types::document::DocumentSummaryResponse;

            let document_summaries: Vec<DocumentSummaryResponse> = documents
                .into_iter()
                .map(DocumentSummaryResponse::from)
                .collect();

            let total_count = document_summaries.len() as i64;

            let response = FindDocumentsByTagsResponse {
                documents: document_summaries,
                total_count,
                match_all: request.match_all.unwrap_or(false),
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to find documents by tags: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 智能为文档添加标签
pub async fn smart_tag_document(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
    request: web::Json<SmartTagDocumentRequest>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();
    let request = request.into_inner();
    let confidence_threshold = request.confidence_threshold.unwrap_or(0.3);

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    match service
        .smart_tag_document(document_id, claims.user_id, confidence_threshold)
        .await
    {
        Ok(document_tags) => {
            let responses: Vec<TagDocumentResponse> = document_tags
                .into_iter()
                .map(|dt| TagDocumentResponse {
                    id: dt.id,
                    document_id: dt.document_id,
                    tag_id: dt.tag_id,
                    tagged_by: dt.tagged_by,
                    tagged_at: dt.tagged_at,
                })
                .collect();

            let response = SmartTagDocumentResponse {
                document_id,
                applied_tags: responses,
                confidence_threshold,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to smart tag document: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                    "Failed to smart tag document: {}",
                    e
                ))),
            )
        }
    }
}

/// 获取标签使用统计
pub async fn get_tag_usage_statistics(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // knowledge_base_id
) -> ActixResult<HttpResponse> {
    let kb_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));

    let service = DocumentTaggingService::new(document_repo, tag_repo, document_tag_repo);

    match service.get_tag_usage_statistics(kb_id).await {
        Ok(stats) => {
            let statistics: Vec<TagUsageStatistic> = stats
                .into_iter()
                .map(|(tag, count)| TagUsageStatistic {
                    tag: TagResponse::from(tag),
                    usage_count: count,
                })
                .collect();

            let total_tags = statistics.len();

            let response = TagUsageStatisticsResponse {
                knowledge_base_id: kb_id,
                statistics,
                total_tags,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get tag usage statistics: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}
