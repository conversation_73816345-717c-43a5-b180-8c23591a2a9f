use actix_web::{HttpRequest, HttpResponse, Result, web};
use chrono::{Duration, Utc};
use serde_json::json;

use crate::{
    app_state::AppState,
    session::RequiredUserSession,
    types::{audit::*, common::ApiResponse},
};

/// GET /api/v1/audit/logs - 分页查询审计日志
#[utoipa::path(
    get,
    path = "/api/v1/audit/logs",
    tag = "审计API",
    params(AuditLogQueryRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResponse<PagedAuditLogsResponse>),
        (status = 400, description = "参数错误"),
        (status = 401, description = "未授权"),
        (status = 403, description = "无权限"),
        (status = 500, description = "服务器错误"),
    ),
    security(
        ("session" = [])
    )
)]
pub async fn query_audit_logs(
    req: HttpRequest,
    query: web::Query<AuditLogQueryRequest>,
    app_state: web::Data<AppState>,
    _session: RequiredUserSession,
) -> Result<HttpResponse> {
    tracing::info!("查询审计日志: {:?}", query);

    // TODO: 检查用户权限 - 只有管理员和审计员可以查看审计日志

    let query_params = query.into_inner();

    // 设置默认值
    let page = query_params.page.unwrap_or(1);
    let page_size = query_params.page_size.unwrap_or(20).min(100);
    let sort_by = query_params.sort_by.unwrap_or(AuditSortField::Timestamp);
    let sort_order = query_params.sort_order.clone().unwrap_or(SortOrder::Desc);

    if let Some(oo) = &app_state.openobserve {
        // OpenObserve 查询
        let (start_us, end_us) =
            oo.default_time_range_us(query_params.start_time, query_params.end_time);
        let offset = ((page.saturating_sub(1)) as i64) * page_size as i64;
        let size = page_size as i64;

        let where_clause = build_where_clause(&query_params);
        let order_clause = build_order_clause(sort_by, sort_order);
        let sql = format!(
            "SELECT * FROM {} {} {}",
            oo.stream_name(),
            where_clause,
            order_clause
        );

        match oo.search(sql, start_us, end_us, offset, size).await {
            Ok(sr) => {
                let total_count = sr.total.unwrap_or(0).max(0) as u64;
                let logs: Vec<AuditLogResponse> = sr
                    .hits
                    .unwrap_or_default()
                    .into_iter()
                    .filter_map(map_hit_to_audit_log)
                    .collect();
                let total_pages = ((total_count as f64) / (page_size as f64)).ceil() as u32;
                let response = PagedAuditLogsResponse {
                    logs,
                    pagination: PaginationInfo {
                        page,
                        page_size,
                        total_pages,
                        total_count,
                        has_previous: page > 1,
                        has_next: page < total_pages,
                    },
                };
                return Ok(HttpResponse::Ok().json(ApiResponse::success(response)));
            }
            Err(e) => {
                tracing::warn!("OpenObserve 查询失败，回退到模拟数据: {}", e);
            }
        }
    }

    // 回退：模拟数据
    let mock_logs = generate_mock_audit_logs(&query_params, page, page_size);
    let total_count = 150u64; // 模拟总数
    let total_pages = (total_count as f64 / page_size as f64).ceil() as u32;

    let response = PagedAuditLogsResponse {
        logs: mock_logs,
        pagination: PaginationInfo {
            page,
            page_size,
            total_pages,
            total_count,
            has_previous: page > 1,
            has_next: page < total_pages,
        },
    };

    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

fn build_where_clause(q: &AuditLogQueryRequest) -> String {
    let mut conds: Vec<String> = Vec::new();
    if let Some(user_id) = &q.user_id {
        conds.push(format!("user_id='{}'", escape_sql(user_id)));
    }
    if let Some(username) = &q.username {
        conds.push(format!("username='{}'", escape_sql(username)));
    }
    if let Some(action) = &q.action {
        conds.push(format!("action='{}'", escape_sql(action)));
    }
    if let Some(result) = &q.result {
        conds.push(format!("result='{}'", escape_sql(result)));
    }
    if let Some(rt) = &q.resource_type {
        conds.push(format!("resource_type='{}'", escape_sql(rt)));
    }
    if let Some(rid) = &q.resource_id {
        conds.push(format!("resource_id='{}'", escape_sql(rid)));
    }
    if let Some(ip) = &q.ip_address {
        conds.push(format!("ip_address='{}'", escape_sql(ip)));
    }
    if let Some(path) = &q.request_path {
        conds.push(format!("request_path='{}'", escape_sql(path)));
    }
    if let Some(keyword) = &q.search {
        conds.push(format!("match_all('{}')", escape_sql(keyword)));
    }
    if conds.is_empty() {
        String::new()
    } else {
        format!("WHERE {}", conds.join(" AND "))
    }
}

fn build_order_clause(field: AuditSortField, order: SortOrder) -> String {
    let col = match field {
        AuditSortField::Timestamp => "_timestamp",
        AuditSortField::Username => "username",
        AuditSortField::Action => "action",
        AuditSortField::Result => "result",
        AuditSortField::IpAddress => "ip_address",
        AuditSortField::RequestPath => "request_path",
    };
    let dir = match order {
        SortOrder::Asc => "ASC",
        SortOrder::Desc => "DESC",
    };
    format!("ORDER BY {} {}", col, dir)
}

fn escape_sql(s: &str) -> String {
    s.replace("'", "''")
}

fn map_hit_to_audit_log(v: serde_json::Value) -> Option<AuditLogResponse> {
    let obj = v.as_object()?;
    // We expect event JSONL fields flattened as top-level keys in OpenObserve
    let event_id = obj.get("event_id")?.as_str()?.to_string();
    let ts = obj
        .get("_timestamp")
        .and_then(|x| x.as_i64())
        .map(|us| us / 1000) // convert to ms
        .or_else(|| obj.get("timestamp").and_then(|x| x.as_i64()))
        .unwrap_or(0);

    Some(AuditLogResponse {
        event_id,
        timestamp: ts,
        user_id: obj
            .get("user_id")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        username: obj
            .get("username")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        session_id: obj
            .get("session_id")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        action: obj
            .get("action")
            .and_then(|x| x.as_str())
            .unwrap_or("")
            .to_string(),
        result: obj
            .get("result")
            .and_then(|x| x.as_str())
            .unwrap_or("")
            .to_string(),
        resource_type: obj
            .get("resource_type")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        resource_id: obj
            .get("resource_id")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        ip_address: obj
            .get("ip_address")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        user_agent: obj
            .get("user_agent")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        http_method: obj
            .get("http_method")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        request_path: obj
            .get("request_path")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        status_code: obj
            .get("status_code")
            .and_then(|x| x.as_u64())
            .map(|v| v as u16),
        error_message: obj
            .get("error_message")
            .and_then(|x| x.as_str())
            .map(|s| s.to_string()),
        duration_ms: obj.get("duration_ms").and_then(|x| x.as_u64()),
        metadata: serde_json::Value::Object(obj.clone()),
    })
}

/// GET /api/v1/audit/logs/{event_id} - 获取单条审计日志详情
#[utoipa::path(
    get,
    path = "/api/v1/audit/logs/{event_id}",
    tag = "审计API",
    params(
        ("event_id" = String, Path, description = "审计事件ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResponse<AuditLogResponse>),
        (status = 404, description = "审计日志不存在"),
        (status = 401, description = "未授权"),
        (status = 403, description = "无权限"),
        (status = 500, description = "服务器错误"),
    ),
    security(
        ("session" = [])
    )
)]
pub async fn get_audit_log_detail(
    path: web::Path<String>,
    app_state: web::Data<AppState>,
    _session: RequiredUserSession,
) -> Result<HttpResponse> {
    let event_id = path.into_inner();
    tracing::info!("获取审计日志详情: {}", event_id);

    // 优先从 OpenObserve 查询
    if let Some(oo) = &app_state.openobserve {
        let (start_us, end_us) = oo.default_time_range_us(None, None);
        let sql = format!(
            "SELECT * FROM {} WHERE event_id='{}' LIMIT 1",
            oo.stream_name(),
            event_id
        );
        match oo.search(sql, start_us, end_us, 0, 1).await {
            Ok(sr) => {
                if let Some(mut hits) = sr.hits {
                    if let Some(hit) = hits.pop() {
                        if let Some(audit_log) = map_hit_to_audit_log(hit) {
                            return Ok(HttpResponse::Ok().json(ApiResponse::success(audit_log)));
                        }
                    }
                }
            }
            Err(e) => tracing::warn!("OpenObserve 查询详情失败: {}", e),
        }
    }

    // 回退到模拟数据
    let log = generate_mock_audit_log(&event_id);
    match log {
        Some(audit_log) => Ok(HttpResponse::Ok().json(ApiResponse::success(audit_log))),
        None => Ok(HttpResponse::NotFound()
            .json(ApiResponse::<()>::error("AUDIT_LOG_NOT_FOUND".to_string()))),
    }
}

/// GET /api/v1/audit/stats - 获取审计统计信息
#[utoipa::path(
    get,
    path = "/api/v1/audit/stats",
    tag = "审计API",
    params(AuditStatsRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResponse<AuditStatsResponse>),
        (status = 400, description = "参数错误"),
        (status = 401, description = "未授权"),
        (status = 403, description = "无权限"),
        (status = 500, description = "服务器错误"),
    ),
    security(
        ("session" = [])
    )
)]
pub async fn get_audit_stats(
    query: web::Query<AuditStatsRequest>,
    app_state: web::Data<AppState>,
    _session: RequiredUserSession,
) -> Result<HttpResponse> {
    let stats_params = query.into_inner();
    tracing::info!("获取审计统计: {:?}", stats_params);

    if let Some(oo) = &app_state.openobserve {
        let (start_us, end_us) =
            oo.default_time_range_us(stats_params.start_time, stats_params.end_time);
        // 1) 总量
        let total = oo
            .search(
                format!("SELECT count(*) AS cnt FROM {}", oo.stream_name()),
                start_us,
                end_us,
                0,
                1,
            )
            .await
            .ok()
            .and_then(|r| first_count(&r, "cnt"));
        let success = oo
            .search(
                format!(
                    "SELECT count(*) AS cnt FROM {} WHERE result='success'",
                    oo.stream_name()
                ),
                start_us,
                end_us,
                0,
                1,
            )
            .await
            .ok()
            .and_then(|r| first_count(&r, "cnt"));
        let failure = oo
            .search(
                format!(
                    "SELECT count(*) AS cnt FROM {} WHERE result='failure'",
                    oo.stream_name()
                ),
                start_us,
                end_us,
                0,
                1,
            )
            .await
            .ok()
            .and_then(|r| first_count(&r, "cnt"));
        let denied = oo
            .search(
                format!(
                    "SELECT count(*) AS cnt FROM {} WHERE result='denied'",
                    oo.stream_name()
                ),
                start_us,
                end_us,
                0,
                1,
            )
            .await
            .ok()
            .and_then(|r| first_count(&r, "cnt"));

        // 2) 每个 action 的次数
        let action_rows = oo.search(format!("SELECT action, count(*) AS cnt FROM {} GROUP BY action ORDER BY cnt DESC LIMIT 50", oo.stream_name()), start_us, end_us, 0, 50).await
            .ok().and_then(|r| r.hits).unwrap_or_default();
        // 2b) 每个 action 的成功次数
        let action_success_rows = oo.search(format!("SELECT action, count(*) AS cnt FROM {} WHERE result='success' GROUP BY action ORDER BY cnt DESC LIMIT 50", oo.stream_name()), start_us, end_us, 0, 50).await
            .ok().and_then(|r| r.hits).unwrap_or_default();
        let mut success_map: std::collections::HashMap<String, u64> =
            std::collections::HashMap::new();
        for row in action_success_rows {
            if let (Some(a), Some(c)) = (
                row.get("action").and_then(|x| x.as_str()),
                row.get("cnt").and_then(|x| x.as_i64()),
            ) {
                success_map.insert(a.to_string(), c as u64);
            }
        }
        let mut action_stats: Vec<ActionStat> = Vec::new();
        for row in action_rows {
            if let (Some(a), Some(c)) = (
                row.get("action").and_then(|x| x.as_str()),
                row.get("cnt").and_then(|x| x.as_i64()),
            ) {
                let succ = success_map.get(a).copied().unwrap_or(0);
                let rate = if c > 0 { succ as f64 / c as f64 } else { 0.0 };
                action_stats.push(ActionStat {
                    action: a.to_string(),
                    count: c as u64,
                    success_rate: rate,
                });
            }
        }

        // 3) 用户排行及最近活动
        let user_rows = oo.search(format!("SELECT username, user_id, count(*) AS cnt, max(_timestamp) AS last_ts FROM {} GROUP BY username, user_id ORDER BY cnt DESC LIMIT 20", oo.stream_name()), start_us, end_us, 0, 20).await
            .ok().and_then(|r| r.hits).unwrap_or_default();
        let mut user_stats: Vec<UserStat> = Vec::new();
        for row in user_rows {
            let username = row
                .get("username")
                .and_then(|x| x.as_str())
                .unwrap_or("")
                .to_string();
            let user_id = row
                .get("user_id")
                .and_then(|x| x.as_str())
                .unwrap_or("")
                .to_string();
            let cnt = row.get("cnt").and_then(|x| x.as_i64()).unwrap_or(0) as u64;
            let last_ts_ms = row
                .get("last_ts")
                .and_then(|x| x.as_i64())
                .map(|us| us / 1000)
                .unwrap_or(0);
            if !username.is_empty() || !user_id.is_empty() {
                user_stats.push(UserStat {
                    user_id,
                    username,
                    operation_count: cnt,
                    last_activity: last_ts_ms,
                });
            }
        }
        // active_users 粗略取 distinct username 计数
        let distinct_rows = oo
            .search(
                format!(
                    "SELECT username FROM {} GROUP BY username",
                    oo.stream_name()
                ),
                start_us,
                end_us,
                0,
                10000,
            )
            .await
            .ok()
            .and_then(|r| r.hits)
            .unwrap_or_default();
        let active_users = distinct_rows.len() as u64;

        let stats = AuditStatsResponse {
            total_operations: total.unwrap_or(0),
            successful_operations: success.unwrap_or(0),
            failed_operations: failure.unwrap_or(0),
            denied_operations: denied.unwrap_or(0),
            active_users,
            action_stats,
            user_stats,
            time_series: None,
        };
        return Ok(HttpResponse::Ok().json(ApiResponse::success(stats)));
    }

    // 回退：模拟
    let stats = generate_mock_audit_stats(&stats_params);
    Ok(HttpResponse::Ok().json(ApiResponse::success(stats)))
}

/// GET /api/v1/audit/users/{user_id}/activities - 获取用户活动记录
#[utoipa::path(
    get,
    path = "/api/v1/audit/users/{user_id}/activities",
    tag = "审计API",
    params(
        ("user_id" = String, Path, description = "用户ID"),
        UserActivityRequest
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResponse<UserActivityResponse>),
        (status = 404, description = "用户不存在"),
        (status = 401, description = "未授权"),
        (status = 403, description = "无权限"),
        (status = 500, description = "服务器错误"),
    ),
    security(
        ("session" = [])
    )
)]
pub async fn get_user_activities(
    path: web::Path<String>,
    query: web::Query<UserActivityRequest>,
    app_state: web::Data<AppState>,
    session: RequiredUserSession,
) -> Result<HttpResponse> {
    let user_id = path.into_inner();
    let mut activity_params = query.into_inner();
    activity_params.user_id = user_id.clone();

    tracing::info!("获取用户活动记录: {:?}", activity_params);

    // TODO: 检查权限 - 用户只能查看自己的活动记录，管理员可以查看所有用户
    let current_user = session.into_inner();
    if current_user.user_id != user_id {
        // TODO: 检查是否为管理员
    }

    // 如有 OpenObserve，优先按用户查询活动
    if let Some(oo) = &app_state.openobserve {
        let (start_us, end_us) =
            oo.default_time_range_us(activity_params.start_time, activity_params.end_time);
        let page = activity_params.page.unwrap_or(1);
        let page_size = activity_params.page_size.unwrap_or(20).min(100);
        let offset = ((page.saturating_sub(1)) as i64) * page_size as i64;

        let sql = format!(
            "SELECT * FROM {} WHERE user_id='{}' ORDER BY _timestamp DESC",
            oo.stream_name(),
            escape_sql(&activity_params.user_id)
        );
        match oo
            .search(sql, start_us, end_us, offset, page_size as i64)
            .await
        {
            Ok(sr) => {
                let logs: Vec<AuditLogResponse> = sr
                    .hits
                    .unwrap_or_default()
                    .into_iter()
                    .filter_map(map_hit_to_audit_log)
                    .collect();
                let total_count = sr.total.unwrap_or(0).max(0) as u64;
                let total_pages = ((total_count as f64) / (page_size as f64)).ceil() as u32;

                // 统计
                let total = oo
                    .search(
                        format!(
                            "SELECT count(*) AS cnt FROM {} WHERE user_id='{}'",
                            oo.stream_name(),
                            escape_sql(&activity_params.user_id)
                        ),
                        start_us,
                        end_us,
                        0,
                        1,
                    )
                    .await
                    .ok()
                    .and_then(|r| first_count(&r, "cnt"))
                    .unwrap_or(0);
                let succ = oo.search(format!("SELECT count(*) AS cnt FROM {} WHERE user_id='{}' AND result='success'", oo.stream_name(), escape_sql(&activity_params.user_id)), start_us, end_us, 0, 1).await
                    .ok().and_then(|r| first_count(&r, "cnt")).unwrap_or(0);
                let fail = oo.search(format!("SELECT count(*) AS cnt FROM {} WHERE user_id='{}' AND result='failure'", oo.stream_name(), escape_sql(&activity_params.user_id)), start_us, end_us, 0, 1).await
                    .ok().and_then(|r| first_count(&r, "cnt")).unwrap_or(0);
                let first_ts = oo
                    .search(
                        format!(
                            "SELECT min(_timestamp) AS ts FROM {} WHERE user_id='{}'",
                            oo.stream_name(),
                            escape_sql(&activity_params.user_id)
                        ),
                        start_us,
                        end_us,
                        0,
                        1,
                    )
                    .await
                    .ok()
                    .and_then(|r| first_i64(&r, "ts"))
                    .map(|us| us / 1000);
                let last_ts = oo
                    .search(
                        format!(
                            "SELECT max(_timestamp) AS ts FROM {} WHERE user_id='{}'",
                            oo.stream_name(),
                            escape_sql(&activity_params.user_id)
                        ),
                        start_us,
                        end_us,
                        0,
                        1,
                    )
                    .await
                    .ok()
                    .and_then(|r| first_i64(&r, "ts"))
                    .map(|us| us / 1000);

                // 常见 action
                let action_rows = oo.search(format!("SELECT action, count(*) AS cnt FROM {} WHERE user_id='{}' GROUP BY action ORDER BY cnt DESC LIMIT 5", oo.stream_name(), escape_sql(&activity_params.user_id)), start_us, end_us, 0, 5).await
                    .ok().and_then(|r| r.hits).unwrap_or_default();
                let mut most_common_actions = Vec::new();
                for row in action_rows {
                    if let (Some(a), Some(c)) = (
                        row.get("action").and_then(|x| x.as_str()),
                        row.get("cnt").and_then(|x| x.as_i64()),
                    ) {
                        // action 的成功率
                        let a_succ = oo.search(format!("SELECT count(*) AS cnt FROM {} WHERE user_id='{}' AND action='{}' AND result='success'", oo.stream_name(), escape_sql(&activity_params.user_id), escape_sql(a)), start_us, end_us, 0, 1).await
                            .ok().and_then(|r| first_count(&r, "cnt")).unwrap_or(0);
                        let rate = if c > 0 { a_succ as f64 / c as f64 } else { 0.0 };
                        most_common_actions.push(ActionStat {
                            action: a.to_string(),
                            count: c as u64,
                            success_rate: rate,
                        });
                    }
                }

                // 用户信息（尽力从第一条记录填充）
                let (username, email) = logs
                    .get(0)
                    .map(|l| {
                        (
                            l.username.clone().unwrap_or_else(|| "".to_string()),
                            String::new(),
                        )
                    })
                    .unwrap_or_else(|| (String::new(), String::new()));

                let response = UserActivityResponse {
                    user_info: UserInfo {
                        user_id: activity_params.user_id.clone(),
                        username,
                        full_name: None,
                        email,
                    },
                    activity_stats: UserActivityStats {
                        total_activities: total,
                        successful_operations: succ,
                        failed_operations: fail,
                        most_common_actions,
                        first_activity: first_ts,
                        last_activity: last_ts,
                    },
                    activities: logs,
                    pagination: PaginationInfo {
                        page,
                        page_size,
                        total_pages,
                        total_count,
                        has_previous: page > 1,
                        has_next: page < total_pages,
                    },
                };
                return Ok(HttpResponse::Ok().json(ApiResponse::success(response)));
            }
            Err(e) => tracing::warn!("OpenObserve 查询用户活动失败：{}", e),
        }
    }

    // 回退：模拟
    let response = generate_mock_user_activities(&activity_params);
    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

/// GET /api/v1/audit/monitor - 获取系统监控信息
#[utoipa::path(
    get,
    path = "/api/v1/audit/monitor",
    tag = "审计API",
    responses(
        (status = 200, description = "查询成功", body = ApiResponse<SystemMonitorResponse>),
        (status = 401, description = "未授权"),
        (status = 403, description = "无权限"),
        (status = 500, description = "服务器错误"),
    ),
    security(
        ("session" = [])
    )
)]
fn first_count(r: &crate::openobserve_client::SearchResponse, key: &str) -> Option<u64> {
    let v = r.hits.as_ref()?.get(0)?;
    v.get(key).and_then(|x| x.as_i64()).map(|n| n as u64)
}

fn first_i64(r: &crate::openobserve_client::SearchResponse, key: &str) -> Option<i64> {
    let v = r.hits.as_ref()?.get(0)?;
    v.get(key).and_then(|x| x.as_i64())
}

pub async fn get_system_monitor(
    app_state: web::Data<AppState>,
    _session: RequiredUserSession,
) -> Result<HttpResponse> {
    tracing::info!("获取系统监控信息");

    // TODO: 只有管理员可以查看系统监控信息

    // TODO: 实际检查各个系统的状态
    let monitor_response = generate_mock_system_monitor();

    Ok(HttpResponse::Ok().json(ApiResponse::success(monitor_response)))
}

// 以下是模拟数据生成函数，实际实现时需要替换为真实的数据查询逻辑

fn generate_mock_audit_logs(
    query: &AuditLogQueryRequest,
    page: u32,
    page_size: u32,
) -> Vec<AuditLogResponse> {
    let mut logs = Vec::new();
    let base_time = Utc::now() - Duration::hours(24);

    for i in 0..page_size.min(10) {
        let log = AuditLogResponse {
            event_id: format!("evt_{:08}", (page - 1) * page_size + i),
            timestamp: (base_time + Duration::minutes(i as i64 * 30)).timestamp_millis(),
            user_id: Some(format!("user_{}", i % 3 + 1)),
            username: Some(format!("testuser{}", i % 3 + 1)),
            session_id: Some(format!("session_{:08}", i)),
            action: match i % 4 {
                0 => "login".to_string(),
                1 => "document_create".to_string(),
                2 => "document_view".to_string(),
                _ => "search".to_string(),
            },
            result: if i % 5 == 0 {
                "failure".to_string()
            } else {
                "success".to_string()
            },
            resource_type: Some("document".to_string()),
            resource_id: Some(format!("doc_{}", i)),
            ip_address: Some(format!("192.168.1.{}", i % 255 + 1)),
            user_agent: Some("Mozilla/5.0 (Windows NT 10.0; Win64; x64)".to_string()),
            http_method: Some("POST".to_string()),
            request_path: Some(format!("/api/v1/documents/{}", i)),
            status_code: Some(if i % 5 == 0 { 400 } else { 200 }),
            error_message: if i % 5 == 0 {
                Some("操作失败".to_string())
            } else {
                None
            },
            duration_ms: Some(50 + i as u64 * 10),
            metadata: json!({
                "additional_info": format!("测试数据 {}", i),
                "client_version": "1.0.0"
            }),
        };
        logs.push(log);
    }

    logs
}

fn generate_mock_audit_log(event_id: &str) -> Option<AuditLogResponse> {
    if event_id.starts_with("evt_") {
        Some(AuditLogResponse {
            event_id: event_id.to_string(),
            timestamp: (Utc::now() - Duration::hours(2)).timestamp_millis(),
            user_id: Some("user_1".to_string()),
            username: Some("testuser1".to_string()),
            session_id: Some("session_00000001".to_string()),
            action: "document_create".to_string(),
            result: "success".to_string(),
            resource_type: Some("document".to_string()),
            resource_id: Some("doc_123".to_string()),
            ip_address: Some("*************".to_string()),
            user_agent: Some("Mozilla/5.0 (Windows NT 10.0; Win64; x64)".to_string()),
            http_method: Some("POST".to_string()),
            request_path: Some("/api/v1/documents".to_string()),
            status_code: Some(201),
            error_message: None,
            duration_ms: Some(125),
            metadata: json!({
                "document_title": "测试文档",
                "file_size": 1024,
                "mime_type": "application/pdf"
            }),
        })
    } else {
        None
    }
}

fn generate_mock_audit_stats(params: &AuditStatsRequest) -> AuditStatsResponse {
    AuditStatsResponse {
        total_operations: 1250,
        successful_operations: 1100,
        failed_operations: 120,
        denied_operations: 30,
        active_users: 15,
        action_stats: vec![
            ActionStat {
                action: "login".to_string(),
                count: 200,
                success_rate: 0.95,
            },
            ActionStat {
                action: "document_create".to_string(),
                count: 300,
                success_rate: 0.92,
            },
            ActionStat {
                action: "document_view".to_string(),
                count: 500,
                success_rate: 0.99,
            },
            ActionStat {
                action: "search".to_string(),
                count: 250,
                success_rate: 0.97,
            },
        ],
        user_stats: vec![
            UserStat {
                user_id: "user_1".to_string(),
                username: "admin".to_string(),
                operation_count: 450,
                last_activity: (Utc::now() - Duration::minutes(15)).timestamp_millis(),
            },
            UserStat {
                user_id: "user_2".to_string(),
                username: "testuser1".to_string(),
                operation_count: 300,
                last_activity: (Utc::now() - Duration::hours(2)).timestamp_millis(),
            },
        ],
        time_series: Some(generate_mock_time_series()),
    }
}

fn generate_mock_time_series() -> Vec<TimeSeriesPoint> {
    let mut points = Vec::new();
    let base_time = Utc::now() - Duration::days(7);

    for i in 0..7 {
        points.push(TimeSeriesPoint {
            timestamp: (base_time + Duration::days(i)).timestamp_millis(),
            count: 150 + (i * 20) as u64,
            success_count: 140 + (i * 18) as u64,
            failure_count: 10 + (i * 2) as u64,
        });
    }

    points
}

fn generate_mock_user_activities(params: &UserActivityRequest) -> UserActivityResponse {
    let page = params.page.unwrap_or(1);
    let page_size = params.page_size.unwrap_or(20);

    UserActivityResponse {
        user_info: UserInfo {
            user_id: params.user_id.clone(),
            username: "testuser1".to_string(),
            full_name: Some("测试用户1".to_string()),
            email: "<EMAIL>".to_string(),
        },
        activity_stats: UserActivityStats {
            total_activities: 85,
            successful_operations: 78,
            failed_operations: 7,
            most_common_actions: vec![
                ActionStat {
                    action: "document_view".to_string(),
                    count: 35,
                    success_rate: 1.0,
                },
                ActionStat {
                    action: "search".to_string(),
                    count: 25,
                    success_rate: 0.96,
                },
            ],
            first_activity: Some((Utc::now() - Duration::days(30)).timestamp_millis()),
            last_activity: Some((Utc::now() - Duration::minutes(30)).timestamp_millis()),
        },
        activities: generate_mock_audit_logs(
            &AuditLogQueryRequest {
                user_id: Some(params.user_id.clone()),
                page: Some(page),
                page_size: Some(page_size),
                ..Default::default()
            },
            page,
            page_size,
        ),
        pagination: PaginationInfo {
            page,
            page_size,
            total_pages: 5,
            total_count: 85,
            has_previous: page > 1,
            has_next: page < 5,
        },
    }
}

fn generate_mock_system_monitor() -> SystemMonitorResponse {
    SystemMonitorResponse {
        audit_system_status: SystemStatus::Healthy,
        vector_status: SystemStatus::Healthy,
        openobserve_status: SystemStatus::Healthy,
        last_24h_stats: generate_mock_audit_stats(&AuditStatsRequest::default()),
        disk_usage: DiskUsageInfo {
            log_directory: "/var/log/wisdom-vault/audit".to_string(),
            used_bytes: 512 * 1024 * 1024,        // 512MB
            total_bytes: 10 * 1024 * 1024 * 1024, // 10GB
            usage_percentage: 5.12,
        },
        performance_metrics: PerformanceMetrics {
            log_write_rate: 125.5,
            query_response_time_ms: 45.2,
            memory_usage_mb: 128.5,
            cpu_usage_percentage: 12.3,
        },
    }
}
