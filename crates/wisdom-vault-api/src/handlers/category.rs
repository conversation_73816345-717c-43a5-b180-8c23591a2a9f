use actix_web::{HttpResponse, Result as ActixResult, web};
use std::sync::Arc;
use wisdom_vault_auth::RequiredUserSession;
use wisdom_vault_core::services::DocumentClassificationService;
use wisdom_vault_database::{
    connection::DatabaseConnection,
    repositories::{
        SurrealCategoryRepository, SurrealDocumentCategoryRepository, SurrealDocumentRepository,
    },
};

use crate::types::{ApiResponse, category::*};

/// 为文档分配分类
pub async fn categorize_document(
    db: web::Data<DatabaseConnection>,
    user: RequiredUserSession,
    path: web::Path<(String, String)>, // (document_id, category_id)
    request: web::Json<CategorizeDocumentRequest>,
) -> ActixResult<HttpResponse> {
    let user = user.into_inner();

    let (document_id, category_id) = path.into_inner();
    let request = request.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    // 手动分配分类
    match service
        .manually_categorize_document(
            &document_id,
            &category_id,
            &user.user_id,
            request.confidence_score,
        )
        .await
    {
        Ok(document_category) => {
            let response = CategorizeDocumentResponse {
                id: document_category.id,
                document_id: document_category.document_id,
                category_id: document_category.category_id,
                assigned_by: document_category.assigned_by,
                assigned_at: document_category.assigned_at,
                confidence_score: document_category.confidence_score,
            };
            Ok(HttpResponse::Created().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to categorize document: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                    "Failed to categorize document: {}",
                    e
                ))),
            )
        }
    }
}

/// 从文档移除分类
pub async fn uncategorize_document(
    db: web::Data<DatabaseConnection>,
    path: web::Path<(String, String)>, // (document_id, category_id)
) -> ActixResult<HttpResponse> {
    let (document_id, category_id) = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service
        .remove_document_classification(&document_id, &category_id)
        .await
    {
        Ok(true) => Ok(HttpResponse::Ok().json(ApiResponse::success_empty())),
        Ok(false) => Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error(
            "Category relationship not found".to_string(),
        ))),
        Err(e) => {
            tracing::error!("Failed to uncategorize document: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 自动分类文档
pub async fn classify_document(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service.classify_document(document_id, claims.user_id).await {
        Ok(document_categories) => {
            let responses: Vec<CategorizeDocumentResponse> = document_categories
                .into_iter()
                .map(|dc| CategorizeDocumentResponse {
                    id: dc.id,
                    document_id: dc.document_id,
                    category_id: dc.category_id,
                    assigned_by: dc.assigned_by,
                    assigned_at: dc.assigned_at,
                    confidence_score: dc.confidence_score,
                })
                .collect();

            let response = ClassifyDocumentResponse {
                document_id,
                categories: responses,
                classification_method: "content_based".to_string(),
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to classify document: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                    "Failed to classify document: {}",
                    e
                ))),
            )
        }
    }
}

/// 批量分类文档
pub async fn batch_classify_documents(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    request: web::Json<BatchClassifyDocumentsRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service
        .batch_classify_documents(request.document_ids, claims.user_id)
        .await
    {
        Ok(document_categories) => {
            let responses: Vec<CategorizeDocumentResponse> = document_categories
                .into_iter()
                .map(|dc| CategorizeDocumentResponse {
                    id: dc.id,
                    document_id: dc.document_id,
                    category_id: dc.category_id,
                    assigned_by: dc.assigned_by,
                    assigned_at: dc.assigned_at,
                    confidence_score: dc.confidence_score,
                })
                .collect();

            let response = BatchClassifyDocumentsResponse {
                classified_count: responses.len(),
                categories: responses,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to batch classify documents: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                    "Failed to batch classify documents: {}",
                    e
                ))),
            )
        }
    }
}

/// 获取分类推荐
pub async fn suggest_categories_for_document(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
    query: web::Query<CategorySuggestionQuery>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();
    let query = query.into_inner();
    let max_suggestions = query.max_suggestions.unwrap_or(10).min(50);

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service
        .suggest_categories(document_id, max_suggestions)
        .await
    {
        Ok(suggestions) => {
            use crate::types::knowledge_base::CategoryResponse;

            let category_suggestions: Vec<CategorySuggestionResponse> = suggestions
                .into_iter()
                .map(|(category, confidence)| CategorySuggestionResponse {
                    category: CategoryResponse::from(category),
                    confidence,
                })
                .collect();

            let response = CategorySuggestionsResponse {
                document_id,
                suggestions: category_suggestions,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get category suggestions: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 获取文档的所有分类
pub async fn get_document_categories(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service.get_document_categories(document_id).await {
        Ok(categories) => {
            use crate::types::knowledge_base::CategoryResponse;

            let category_responses: Vec<CategoryResponse> =
                categories.into_iter().map(CategoryResponse::from).collect();

            let response = DocumentCategoriesResponse {
                document_id,
                categories: category_responses,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get document categories: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 根据分类查找文档
pub async fn find_documents_by_categories(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    request: web::Json<FindDocumentsByCategoriesRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service
        .find_documents_by_categories(request.category_ids, request.match_all.unwrap_or(false))
        .await
    {
        Ok(documents) => {
            use crate::types::document::DocumentSummaryResponse;

            let document_summaries: Vec<DocumentSummaryResponse> = documents
                .into_iter()
                .map(DocumentSummaryResponse::from)
                .collect();

            let total_count = document_summaries.len() as i64;

            let response = FindDocumentsByCategoriesResponse {
                documents: document_summaries,
                total_count,
                match_all: request.match_all.unwrap_or(false),
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to find documents by categories: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 基于相似文档的分类
pub async fn classify_by_similar_documents(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service
        .classify_by_similar_documents(document_id, claims.user_id)
        .await
    {
        Ok(document_categories) => {
            let responses: Vec<CategorizeDocumentResponse> = document_categories
                .into_iter()
                .map(|dc| CategorizeDocumentResponse {
                    id: dc.id,
                    document_id: dc.document_id,
                    category_id: dc.category_id,
                    assigned_by: dc.assigned_by,
                    assigned_at: dc.assigned_at,
                    confidence_score: dc.confidence_score,
                })
                .collect();

            let response = ClassifyDocumentResponse {
                document_id,
                categories: responses,
                classification_method: "similar_documents".to_string(),
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to classify by similar documents: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                    "Failed to classify by similar documents: {}",
                    e
                ))),
            )
        }
    }
}

/// 获取分类统计
pub async fn get_category_statistics(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // knowledge_base_id
) -> ActixResult<HttpResponse> {
    let kb_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service.get_category_statistics(kb_id).await {
        Ok(stats) => {
            use crate::types::knowledge_base::CategoryResponse;

            let statistics: Vec<CategoryStatistic> = stats
                .into_iter()
                .map(|(category, count)| CategoryStatistic {
                    category: CategoryResponse::from(category),
                    document_count: count,
                })
                .collect();

            let total_categories = statistics.len();

            let response = CategoryStatisticsResponse {
                knowledge_base_id: kb_id,
                statistics,
                total_categories,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get category statistics: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 获取分类统计信息
pub async fn get_classification_statistics(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // knowledge_base_id
) -> ActixResult<HttpResponse> {
    let kb_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service =
        DocumentClassificationService::new(document_repo, category_repo, document_category_repo);

    match service.get_classification_statistics(kb_id).await {
        Ok(stats) => {
            let response = ClassificationStatisticsResponse {
                knowledge_base_id: kb_id,
                total_documents: stats.total_documents,
                classified_documents: stats.classified_documents,
                unclassified_documents: stats.unclassified_documents,
                categories_with_documents: stats.categories_with_documents,
                average_classifications_per_document: stats.average_classifications_per_document,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get classification statistics: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}
