use crate::{app_state::AppState, types::ApiResponse};
use actix_web::{HttpResponse, Result, web};
use wisdom_vault_auth::Claims;

/// 启动任务处理器
pub async fn start_task_processor(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    if app_state.task_processor.is_running().await {
        return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
            "Task processor is already running".to_string(),
        )));
    }

    // 在后台启动任务处理器
    let processor = app_state.task_processor.clone();
    tokio::spawn(async move {
        if let Err(e) = processor.start().await {
            tracing::error!("Task processor error: {}", e);
        }
    });

    Ok(HttpResponse::Ok().json(ApiResponse::success("Task processor started successfully")))
}

/// 停止任务处理器
pub async fn stop_task_processor(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    app_state.task_processor.stop().await;

    Ok(HttpResponse::Ok().json(ApiResponse::success("Task processor stop signal sent")))
}

/// 获取任务处理器状态
pub async fn get_task_processor_status(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let status = app_state.task_processor.get_status().await;

    Ok(HttpResponse::Ok().json(ApiResponse::success(status)))
}
