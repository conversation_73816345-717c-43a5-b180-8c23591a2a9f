use actix_web::{HttpResponse, Result as ActixResult, web};
use chrono::Utc;
use std::{sync::Arc, time::Instant};
use uuid::Uuid;
use wisdom_vault_auth::Claims;
use wisdom_vault_core::services::DocumentService;
use wisdom_vault_database::{
    connection::DatabaseConnection,
    models::{Document, DocumentMetadata, DocumentStatus},
    repositories::SurrealDocumentRepository,
};

use crate::types::{
    ApiResponse,
    document::{
        BatchUpdateResponse, BatchUpdateStatusRequest, CreateDocumentRequest,
        CreateDocumentResponse, DocumentDeleteResponse, DocumentListResponse, 
        DocumentQuery, DocumentResponse, DocumentSearchRequest, DocumentSearchResponse, 
        DocumentSearchResult, DocumentStatisticsResponse, DocumentStatusUpdateResponse,
        DocumentSummaryResponse, UpdateDocumentRequest, UpdateDocumentResponse, 
        UpdateDocumentStatusRequest, create_default_metadata,
        create_default_processing_metadata, parse_document_status,
    },
};

/// 创建文档
pub async fn create_document(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    request: web::Json<CreateDocumentRequest>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let request = request.into_inner();

    // 创建文档实例
    let document = Document {
        id: Uuid::new_v4(),
        knowledge_base_id: request.knowledge_base_id,
        title: request.title.clone(),
        content: request.content,
        summary: request.summary,
        file_type: request.file_type,
        file_size: 0, // 将在后续处理中更新
        file_path: None,
        original_filename: request.original_filename,
        mime_type: request.mime_type,
        language: request.language,
        metadata: request
            .metadata
            .map(|m| DocumentMetadata {
                author: m.author,
                subject: m.subject,
                creator: None,
                producer: None,
                keywords: m.keywords,
                source_url: m.source_url,
                page_count: None,
                word_count: None,
                character_count: None,
                creation_date: None,
                modification_date: None,
                content_type: "text/plain".to_string(),
                content_encoding: None,
                content_language: None,
                custom_fields: m.custom_fields,
            })
            .unwrap_or_else(create_default_metadata),
        processing_metadata: create_default_processing_metadata(),
        status: DocumentStatus::Uploaded,
        uploaded_by: claims.user_id,
        indexed_at: None,
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    match service.create_document(document).await {
        Ok(created_doc) => {
            let response = CreateDocumentResponse {
                id: created_doc.id,
                title: created_doc.title,
                status: format!("{:?}", created_doc.status),
                created_at: created_doc.created_at,
            };
            Ok(HttpResponse::Created().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to create document: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                    "Failed to create document: {}",
                    e
                ))),
            )
        }
    }
}

/// 获取文档详情
pub async fn get_document(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<Uuid>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let document_id = path.into_inner();

    match service.get_document(document_id).await {
        Ok(Some(document)) => {
            // 简单的权限检查 - 实际实现中需要更完善的权限控制
            if document.uploaded_by != claims.user_id {
                return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                    "No permission to access this document".to_string(),
                )));
            }

            let response = DocumentResponse::from(document);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Ok(None) => Ok(HttpResponse::NotFound()
            .json(ApiResponse::<()>::error("Document not found".to_string()))),
        Err(e) => {
            tracing::error!("Failed to get document {}: {}", document_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 更新文档
pub async fn update_document(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<Uuid>,
    request: web::Json<UpdateDocumentRequest>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let document_id = path.into_inner();
    let request = request.into_inner();

    // 首先获取现有文档
    match service.get_document(document_id).await {
        Ok(Some(mut document)) => {
            // 权限检查
            if document.uploaded_by != claims.user_id {
                return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                    "No permission to update this document".to_string(),
                )));
            }

            // 更新字段
            if let Some(title) = request.title {
                document.title = title;
            }
            if let Some(content) = request.content {
                document.content = content;
            }
            if let Some(summary) = request.summary {
                document.summary = Some(summary);
            }
            if let Some(language) = request.language {
                document.language = Some(language);
            }

            // 更新元数据
            if let Some(metadata_update) = request.metadata {
                if let Some(author) = metadata_update.author {
                    document.metadata.author = Some(author);
                }
                if let Some(subject) = metadata_update.subject {
                    document.metadata.subject = Some(subject);
                }
                if let Some(keywords) = metadata_update.keywords {
                    document.metadata.keywords = keywords;
                }
                if let Some(source_url) = metadata_update.source_url {
                    document.metadata.source_url = Some(source_url);
                }
                if let Some(custom_fields) = metadata_update.custom_fields {
                    document.metadata.custom_fields = custom_fields;
                }
            }

            // 保存更新
            match service.update_document(document).await {
                Ok(updated_doc) => {
                    let response = UpdateDocumentResponse {
                        id: updated_doc.id,
                        title: updated_doc.title,
                        status: format!("{:?}", updated_doc.status),
                        updated_at: updated_doc.updated_at,
                    };
                    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
                }
                Err(e) => {
                    tracing::error!("Failed to update document {}: {}", document_id, e);
                    Ok(
                        HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                            "Failed to update document: {}",
                            e
                        ))),
                    )
                }
            }
        }
        Ok(None) => Ok(HttpResponse::NotFound()
            .json(ApiResponse::<()>::error("Document not found".to_string()))),
        Err(e) => {
            tracing::error!("Failed to get document {}: {}", document_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 删除文档
pub async fn delete_document(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<Uuid>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let document_id = path.into_inner();

    // 首先检查文档是否存在和权限
    match service.get_document(document_id).await {
        Ok(Some(document)) => {
            if document.uploaded_by != claims.user_id {
                return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                    "No permission to delete this document".to_string(),
                )));
            }

            match service.delete_document(document_id).await {
                Ok(true) => Ok(HttpResponse::Ok().json(ApiResponse::success(
                    DocumentDeleteResponse {
                        deleted: true,
                        id: document_id,
                    },
                ))),
                Ok(false) => Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error(
                    "Document not found or already deleted".to_string(),
                ))),
                Err(e) => {
                    tracing::error!("Failed to delete document {}: {}", document_id, e);
                    Ok(
                        HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                            "Internal server error".to_string(),
                        )),
                    )
                }
            }
        }
        Ok(None) => Ok(HttpResponse::NotFound()
            .json(ApiResponse::<()>::error("Document not found".to_string()))),
        Err(e) => {
            tracing::error!("Failed to get document {}: {}", document_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 列出文档（支持分页和过滤）
pub async fn list_documents(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    query: web::Query<DocumentQuery>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let query = query.into_inner();
    let limit = query.limit.unwrap_or(20).min(100);
    let offset = query.offset.unwrap_or(0);

    // 根据查询参数过滤文档
    let documents = if let Some(status_str) = &query.status {
        match parse_document_status(status_str) {
            Ok(status) => {
                service
                    .get_documents_by_status(status, query.knowledge_base_id)
                    .await
            }
            Err(_) => {
                return Ok(
                    HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                        "Invalid status: {}",
                        status_str
                    ))),
                );
            }
        }
    } else if let Some(file_type) = &query.file_type {
        service
            .get_documents_by_file_type(file_type, query.knowledge_base_id)
            .await
    } else if let Some(author) = &query.author {
        service
            .get_documents_by_author(author, query.knowledge_base_id)
            .await
    } else if let Some(kb_id) = query.knowledge_base_id {
        service.get_documents_by_knowledge_base(kb_id).await
    } else {
        service
            .list_documents_with_pagination(query.knowledge_base_id, limit, offset)
            .await
    };

    match documents {
        Ok(docs) => {
            let total_count = service
                .count_documents(query.knowledge_base_id)
                .await
                .unwrap_or(0);
            let document_summaries: Vec<DocumentSummaryResponse> = docs
                .into_iter()
                .map(DocumentSummaryResponse::from)
                .collect();

            let response = DocumentListResponse {
                documents: document_summaries,
                total_count,
                has_more: (offset + limit) < total_count as u32,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to list documents: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 搜索文档
pub async fn search_documents(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    request: web::Json<DocumentSearchRequest>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let request = request.into_inner();
    let start_time = Instant::now();

    match service
        .search_documents(&request.query, request.knowledge_base_id)
        .await
    {
        Ok(documents) => {
            let search_time_ms = start_time.elapsed().as_millis() as u64;
            let total_count = documents.len() as i64;

            let search_results: Vec<DocumentSearchResult> = documents
                .into_iter()
                .map(|doc| DocumentSearchResult {
                    document: DocumentSummaryResponse::from(doc),
                    relevance_score: 1.0, // 简单实现，实际需要计算相关性分数
                    highlight_snippets: vec![], // 简单实现，实际需要提取高亮片段
                })
                .collect();

            let response = DocumentSearchResponse {
                documents: search_results,
                total_count,
                query: request.query,
                search_time_ms,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to search documents: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 更新文档状态
pub async fn update_document_status(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<Uuid>,
    request: web::Json<UpdateDocumentStatusRequest>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let document_id = path.into_inner();
    let request = request.into_inner();

    // 解析状态
    let status = match parse_document_status(&request.status) {
        Ok(s) => s,
        Err(e) => return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(e))),
    };

    // 权限检查
    match service
        .validate_document_access(document_id, claims.user_id, Uuid::nil())
        .await
    {
        Ok(true) => match service.update_document_status(document_id, status).await {
            Ok(true) => Ok(
                HttpResponse::Ok().json(ApiResponse::success(DocumentStatusUpdateResponse {
                    id: document_id,
                    status: request.status.clone(),
                    updated_at: Utc::now(),
                })),
            ),
            Ok(false) => Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Document not found".to_string()))),
            Err(e) => {
                tracing::error!("Failed to update document status: {}", e);
                Ok(
                    HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                        "Internal server error".to_string(),
                    )),
                )
            }
        },
        Ok(false) => Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
            "No permission to update this document".to_string(),
        ))),
        Err(e) => {
            tracing::error!("Failed to validate document access: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 批量更新文档状态
pub async fn batch_update_status(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    request: web::Json<BatchUpdateStatusRequest>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let request = request.into_inner();

    // 解析状态
    let status = match parse_document_status(&request.status) {
        Ok(s) => s,
        Err(e) => return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(e))),
    };

    match service
        .batch_update_status(request.document_ids, status)
        .await
    {
        Ok(updated_count) => {
            let response = BatchUpdateResponse {
                updated_count,
                failed_ids: vec![], // 简单实现，实际需要跟踪失败的ID
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to batch update status: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 获取文档统计信息
pub async fn get_document_statistics(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    query: web::Query<DocumentQuery>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let query = query.into_inner();

    match service
        .get_document_statistics(query.knowledge_base_id)
        .await
    {
        Ok(stats) => {
            let response = DocumentStatisticsResponse {
                total_count: stats.total_count,
                indexed_count: stats.indexed_count,
                processing_count: stats.processing_count,
                failed_count: stats.failed_count,
                archived_count: stats.archived_count,
                ready_count: stats.ready_count(),
                success_rate: stats.success_rate(),
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get document statistics: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}

/// 获取失败的文档
pub async fn get_failed_documents(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    query: web::Query<DocumentQuery>,
) -> ActixResult<HttpResponse> {
    let repository: Arc<dyn wisdom_vault_database::repositories::DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(repository);

    let query = query.into_inner();

    match service.get_failed_documents(query.knowledge_base_id).await {
        Ok(documents) => {
            let total_count = documents.len() as i64;
            let document_summaries: Vec<DocumentSummaryResponse> = documents
                .into_iter()
                .map(DocumentSummaryResponse::from)
                .collect();

            let response = DocumentListResponse {
                documents: document_summaries,
                total_count,
                has_more: false,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get failed documents: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            )
        }
    }
}
