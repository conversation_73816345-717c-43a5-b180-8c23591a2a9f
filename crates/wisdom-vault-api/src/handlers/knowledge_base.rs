use actix_web::{HttpResponse, Result, web};
use tracing::{error, info};
use uuid::Uuid;
use validator::Validate;

use wisdom_vault_auth::Claims;
use wisdom_vault_core::services::KnowledgeBaseService;
use wisdom_vault_database::{
    connection::DatabaseConnection,
    repositories::{
        SurrealCategoryRepository, SurrealKnowledgeBaseRepository, SurrealTagRepository,
    },
};

use crate::types::{
    ApiResponse, PaginatedResponse,
    knowledge_base::{
        CategoryResponse, CreateCategoryRequest, CreateKnowledgeBaseRequest, CreateTagRequest,
        KnowledgeBaseResponse, ListKnowledgeBasesRequest, SearchKnowledgeBasesRequest,
        SearchTagsRequest, TagResponse, UpdateKnowledgeBaseRequest,
    },
};

// Knowledge Base Management Handlers

pub async fn create_knowledge_base(
    request: web::Json<CreateKnowledgeBaseRequest>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    // Validate request
    if let Err(validation_errors) = request.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Create knowledge base
    match service
        .create_knowledge_base(
            request.name.clone(),
            request.description.clone(),
            request.organization_id,
            claims.user_id,
            request.visibility.clone(),
            request.settings.clone(),
        )
        .await
    {
        Ok(kb) => {
            info!(
                "Knowledge base created: {} by user {}",
                kb.id, claims.user_id
            );
            Ok(HttpResponse::Created().json(ApiResponse::success(KnowledgeBaseResponse::from(kb))))
        }
        Err(e) => {
            error!("Failed to create knowledge base: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to create knowledge base".to_string(),
                )),
            )
        }
    }
}

pub async fn get_knowledge_base(
    path: web::Path<Uuid>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Check read permission first
    match service
        .check_read_permission(kb_id, Some(claims.user_id), claims.organization_id)
        .await
    {
        Ok(false) => {
            return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                "Insufficient permissions to access this knowledge base".to_string(),
            )));
        }
        Err(e) => {
            error!("Failed to check permissions: {:?}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Permission check failed".to_string(),
                )),
            );
        }
        Ok(true) => {}
    }

    // Get knowledge base
    match service.get_knowledge_base(kb_id).await {
        Ok(Some(kb)) => {
            // Increment view count
            let _ = service.increment_view_count(kb_id).await;

            Ok(HttpResponse::Ok().json(ApiResponse::success(KnowledgeBaseResponse::from(kb))))
        }
        Ok(None) => Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error(
            "Knowledge base not found".to_string(),
        ))),
        Err(e) => {
            error!("Failed to get knowledge base: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to retrieve knowledge base".to_string(),
                )),
            )
        }
    }
}

pub async fn update_knowledge_base(
    path: web::Path<Uuid>,
    request: web::Json<UpdateKnowledgeBaseRequest>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Validate request
    if let Err(validation_errors) = request.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Update knowledge base
    match service
        .update_knowledge_base(
            kb_id,
            request.name.clone(),
            request.description.clone(),
            request.visibility.clone(),
            request.settings.clone(),
            claims.user_id,
        )
        .await
    {
        Ok(kb) => {
            info!(
                "Knowledge base updated: {} by user {}",
                kb.id, claims.user_id
            );
            Ok(HttpResponse::Ok().json(ApiResponse::success(KnowledgeBaseResponse::from(kb))))
        }
        Err(e) => {
            error!("Failed to update knowledge base: {:?}", e);
            if e.to_string().contains("Permission denied") {
                Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(e.to_string())))
            } else if e.to_string().contains("not found") {
                Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error(e.to_string())))
            } else {
                Ok(
                    HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                        "Failed to update knowledge base".to_string(),
                    )),
                )
            }
        }
    }
}

pub async fn delete_knowledge_base(
    path: web::Path<Uuid>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Delete knowledge base
    match service.delete_knowledge_base(kb_id, claims.user_id).await {
        Ok(true) => {
            info!(
                "Knowledge base deleted: {} by user {}",
                kb_id, claims.user_id
            );
            Ok(HttpResponse::NoContent().finish())
        }
        Ok(false) => Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error(
            "Knowledge base not found".to_string(),
        ))),
        Err(e) => {
            error!("Failed to delete knowledge base: {:?}", e);
            if e.to_string().contains("Permission denied") {
                Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(e.to_string())))
            } else {
                Ok(
                    HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                        "Failed to delete knowledge base".to_string(),
                    )),
                )
            }
        }
    }
}

pub async fn list_knowledge_bases(
    query: web::Query<ListKnowledgeBasesRequest>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    // Validate query parameters
    if let Err(validation_errors) = query.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Use current user's info if not specified
    let user_id = query.user_id.unwrap_or(claims.user_id);
    let organization_id = query.organization_id.or(claims.organization_id);

    // List knowledge bases
    match service
        .list_knowledge_bases(
            Some(user_id),
            organization_id,
            query.visibility_filter.clone(),
            query.limit,
            query.offset,
        )
        .await
    {
        Ok(knowledge_bases) => {
            let responses: Vec<KnowledgeBaseResponse> = knowledge_bases
                .into_iter()
                .map(KnowledgeBaseResponse::from)
                .collect();

            let paginated = PaginatedResponse::new(
                responses,
                None, // TODO: Get total count if needed
                query.limit.unwrap_or(20),
                query.offset.unwrap_or(0),
            );

            Ok(HttpResponse::Ok().json(ApiResponse::success(paginated)))
        }
        Err(e) => {
            error!("Failed to list knowledge bases: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to retrieve knowledge bases".to_string(),
                )),
            )
        }
    }
}

pub async fn search_knowledge_bases(
    query: web::Query<SearchKnowledgeBasesRequest>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    // Validate query parameters
    if let Err(validation_errors) = query.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Search knowledge bases
    let user_id = query.user_id.unwrap_or(claims.user_id);

    match service
        .search_knowledge_bases(&query.query, Some(user_id), query.limit, query.offset)
        .await
    {
        Ok(knowledge_bases) => {
            let responses: Vec<KnowledgeBaseResponse> = knowledge_bases
                .into_iter()
                .map(KnowledgeBaseResponse::from)
                .collect();

            let paginated = PaginatedResponse::new(
                responses,
                None, // TODO: Get total count if needed
                query.limit.unwrap_or(20),
                query.offset.unwrap_or(0),
            );

            Ok(HttpResponse::Ok().json(ApiResponse::success(paginated)))
        }
        Err(e) => {
            error!("Failed to search knowledge bases: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to search knowledge bases".to_string(),
                )),
            )
        }
    }
}

// Category Management Handlers

pub async fn create_category(
    path: web::Path<Uuid>,
    request: web::Json<CreateCategoryRequest>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Validate request
    if let Err(validation_errors) = request.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Check write permission
    match service.check_write_permission(kb_id, claims.user_id).await {
        Ok(false) => {
            return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                "Insufficient permissions to create categories in this knowledge base".to_string(),
            )));
        }
        Err(e) => {
            error!("Failed to check permissions: {:?}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Permission check failed".to_string(),
                )),
            );
        }
        Ok(true) => {}
    }

    // Create category
    match service
        .create_category(
            kb_id,
            request.name.clone(),
            request.description.clone(),
            request.parent_id,
            request.icon.clone(),
            request.sort_order,
            claims.user_id,
        )
        .await
    {
        Ok(category) => {
            info!(
                "Category created: {} in KB {} by user {}",
                category.id, kb_id, claims.user_id
            );
            Ok(
                HttpResponse::Created()
                    .json(ApiResponse::success(CategoryResponse::from(category))),
            )
        }
        Err(e) => {
            error!("Failed to create category: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to create category".to_string(),
                )),
            )
        }
    }
}

pub async fn list_categories(
    path: web::Path<Uuid>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Check read permission
    match service
        .check_read_permission(kb_id, Some(claims.user_id), claims.organization_id)
        .await
    {
        Ok(false) => {
            return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                "Insufficient permissions to access categories in this knowledge base".to_string(),
            )));
        }
        Err(e) => {
            error!("Failed to check permissions: {:?}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Permission check failed".to_string(),
                )),
            );
        }
        Ok(true) => {}
    }

    // Get categories
    match service.get_categories_by_knowledge_base(kb_id).await {
        Ok(categories) => {
            let responses: Vec<CategoryResponse> =
                categories.into_iter().map(CategoryResponse::from).collect();

            Ok(HttpResponse::Ok().json(ApiResponse::success(responses)))
        }
        Err(e) => {
            error!("Failed to list categories: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to retrieve categories".to_string(),
                )),
            )
        }
    }
}

pub async fn get_category_hierarchy(
    path: web::Path<Uuid>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Check read permission
    match service
        .check_read_permission(kb_id, Some(claims.user_id), claims.organization_id)
        .await
    {
        Ok(false) => {
            return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                "Insufficient permissions to access categories in this knowledge base".to_string(),
            )));
        }
        Err(e) => {
            error!("Failed to check permissions: {:?}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Permission check failed".to_string(),
                )),
            );
        }
        Ok(true) => {}
    }

    // Get category hierarchy
    match service.get_category_hierarchy(kb_id).await {
        Ok(categories) => {
            let responses: Vec<CategoryResponse> =
                categories.into_iter().map(CategoryResponse::from).collect();

            Ok(HttpResponse::Ok().json(ApiResponse::success(responses)))
        }
        Err(e) => {
            error!("Failed to get category hierarchy: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to retrieve category hierarchy".to_string(),
                )),
            )
        }
    }
}

// Tag Management Handlers

pub async fn create_tag(
    path: web::Path<Uuid>,
    request: web::Json<CreateTagRequest>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Validate request
    if let Err(validation_errors) = request.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Check write permission
    match service.check_write_permission(kb_id, claims.user_id).await {
        Ok(false) => {
            return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                "Insufficient permissions to create tags in this knowledge base".to_string(),
            )));
        }
        Err(e) => {
            error!("Failed to check permissions: {:?}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Permission check failed".to_string(),
                )),
            );
        }
        Ok(true) => {}
    }

    // Create tag
    match service
        .create_tag(
            kb_id,
            request.name.clone(),
            request.display_name.clone(),
            request.color.clone(),
            request.description.clone(),
            claims.user_id,
        )
        .await
    {
        Ok(tag) => {
            info!(
                "Tag created: {} in KB {} by user {}",
                tag.id, kb_id, claims.user_id
            );
            Ok(HttpResponse::Created().json(ApiResponse::success(TagResponse::from(tag))))
        }
        Err(e) => {
            error!("Failed to create tag: {:?}", e);
            if e.to_string().contains("already exists") {
                Ok(HttpResponse::Conflict().json(ApiResponse::<()>::error(e.to_string())))
            } else {
                Ok(HttpResponse::InternalServerError()
                    .json(ApiResponse::<()>::error("Failed to create tag".to_string())))
            }
        }
    }
}

pub async fn list_tags(
    path: web::Path<Uuid>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Check read permission
    match service
        .check_read_permission(kb_id, Some(claims.user_id), claims.organization_id)
        .await
    {
        Ok(false) => {
            return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                "Insufficient permissions to access tags in this knowledge base".to_string(),
            )));
        }
        Err(e) => {
            error!("Failed to check permissions: {:?}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Permission check failed".to_string(),
                )),
            );
        }
        Ok(true) => {}
    }

    // Get tags
    match service.get_tags_by_knowledge_base(kb_id).await {
        Ok(tags) => {
            let responses: Vec<TagResponse> = tags.into_iter().map(TagResponse::from).collect();

            Ok(HttpResponse::Ok().json(ApiResponse::success(responses)))
        }
        Err(e) => {
            error!("Failed to list tags: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to retrieve tags".to_string(),
                )),
            )
        }
    }
}

pub async fn search_tags(
    path: web::Path<Uuid>,
    query: web::Query<SearchTagsRequest>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();

    // Validate query parameters
    if let Err(validation_errors) = query.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Check read permission
    match service
        .check_read_permission(kb_id, Some(claims.user_id), claims.organization_id)
        .await
    {
        Ok(false) => {
            return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                "Insufficient permissions to search tags in this knowledge base".to_string(),
            )));
        }
        Err(e) => {
            error!("Failed to check permissions: {:?}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Permission check failed".to_string(),
                )),
            );
        }
        Ok(true) => {}
    }

    // Search tags
    match service.search_tags(kb_id, &query.query).await {
        Ok(tags) => {
            let responses: Vec<TagResponse> = tags.into_iter().map(TagResponse::from).collect();

            Ok(HttpResponse::Ok().json(ApiResponse::success(responses)))
        }
        Err(e) => {
            error!("Failed to search tags: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to search tags".to_string(),
                )),
            )
        }
    }
}

pub async fn get_popular_tags(
    path: web::Path<Uuid>,
    query: web::Query<std::collections::HashMap<String, String>>,
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let kb_id = path.into_inner();
    let limit = query
        .get("limit")
        .and_then(|l| l.parse::<u32>().ok())
        .unwrap_or(10)
        .min(50); // Cap at 50

    // Create repositories and service
    let kb_repo = SurrealKnowledgeBaseRepository::new(db.get_ref().clone());
    let category_repo = SurrealCategoryRepository::new(db.get_ref().clone());
    let tag_repo = SurrealTagRepository::new(db.get_ref().clone());
    let service = KnowledgeBaseService::new(kb_repo, category_repo, tag_repo);

    // Check read permission
    match service
        .check_read_permission(kb_id, Some(claims.user_id), claims.organization_id)
        .await
    {
        Ok(false) => {
            return Ok(HttpResponse::Forbidden().json(ApiResponse::<()>::error(
                "Insufficient permissions to access tags in this knowledge base".to_string(),
            )));
        }
        Err(e) => {
            error!("Failed to check permissions: {:?}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Permission check failed".to_string(),
                )),
            );
        }
        Ok(true) => {}
    }

    // Get popular tags
    match service.get_popular_tags(kb_id, limit).await {
        Ok(tags) => {
            let responses: Vec<TagResponse> = tags.into_iter().map(TagResponse::from).collect();

            Ok(HttpResponse::Ok().json(ApiResponse::success(responses)))
        }
        Err(e) => {
            error!("Failed to get popular tags: {:?}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to retrieve popular tags".to_string(),
                )),
            )
        }
    }
}
