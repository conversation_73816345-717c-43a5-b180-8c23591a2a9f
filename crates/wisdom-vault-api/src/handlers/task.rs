use crate::{
    app_state::AppState,
    types::{
        ApiResponse, BatchTaskOperationRequest, CreateTaskRequest,
        DocumentProcessingProgressResponse, RetryTaskRequest, TaskListQuery, TaskMonitorResponse,
        TaskResponse, TaskStatisticsResponse,
    },
};
use actix_web::{HttpResponse, Result, web};
use chrono::Utc;
use uuid::Uuid;
use wisdom_vault_auth::Claims;
use wisdom_vault_common::time::current_millis;
use wisdom_vault_database::models::{ProcessingTask, TaskContext, TaskStatus};

/// 转换 ProcessingTask 到 TaskResponse
fn processing_task_to_response(task: ProcessingTask) -> TaskResponse {
    TaskResponse {
        id: task.id,
        task_type: task.task_type,
        status: task.status,
        resource_id: task.resource_id,
        priority: task.priority,
        progress: task.progress,
        retry_count: task.retry_count,
        max_retries: task.max_retries,
        error_message: task.error_message,
        created_at: task.created_at,
        started_at: Some(task.started_at),
        completed_at: task.completed_at,
        next_retry_at: task.next_retry_at,
        estimated_completion: if task.status == "processing" && task.started_at.is_some() {
            // 简单的预估：基于任务类型的平均处理时间
            let estimated_minutes = match task.task_type.as_str() {
                "document_parsing" => 5,
                "vectorization" => 10,
                "batch_processing" => 30,
                _ => 15,
            };
            task.started_at.map(|start| start + chrono::Duration::minutes(estimated_minutes))
        } else {
            None
        },
    }
}

/// 获取任务列表
pub async fn get_tasks(
    app_state: web::Data<AppState>,
    query: web::Query<TaskListQuery>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let query = query.into_inner();

    let tasks = if let Some(resource_id) = query.resource_id {
        app_state
            .processing_task_service
            .get_tasks_by_resource(resource_id)
            .await
    } else if let Some(status) = query.status {
        app_state
            .processing_task_service
            .get_tasks_by_status(status)
            .await
    } else {
        // 默认获取排队中的任务
        app_state
            .processing_task_service
            .get_queued_tasks(query.limit)
            .await
    };

    match tasks {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> =
                tasks.into_iter().map(processing_task_to_response).collect();
            Ok(HttpResponse::Ok().json(ApiResponse::success(task_responses)))
        }
        Err(e) => {
            tracing::error!("Failed to get tasks: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("Failed to get tasks".to_string())))
        }
    }
}

/// 获取单个任务详情
pub async fn get_task(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();

    match app_state.processing_task_service.get_task(task_id).await {
        Ok(Some(task)) => {
            let task_response = processing_task_to_response(task);
            Ok(HttpResponse::Ok().json(ApiResponse::success(task_response)))
        }
        Ok(None) => {
            Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Task not found".to_string())))
        }
        Err(e) => {
            tracing::error!("Failed to get task {}: {}", task_id, e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("Failed to get task".to_string())))
        }
    }
}

/// 获取任务统计信息
pub async fn get_task_statistics(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    match app_state.processing_task_service.get_statistics().await {
        Ok(stats) => {
            let statistics = TaskStatisticsResponse {
                total_tasks: stats.total_tasks,
                queued_tasks: stats.queued_tasks,
                running_tasks: stats.running_tasks,
                completed_tasks: stats.completed_tasks,
                failed_tasks: stats.failed_tasks,
                cancelled_tasks: stats.cancelled_tasks,
                retrying_tasks: stats.retrying_tasks,
                average_processing_time: if stats.completed_tasks > 0 {
                    // 简单的平均值估计，这里应该从任务服务获取实际数据
                    Some(5000) // 平均5秒，应该从实际统计中获取
                } else {
                    None
                },
                success_rate: if stats.total_tasks > 0 {
                    stats.completed_tasks as f64 / stats.total_tasks as f64 * 100.0
                } else {
                    0.0
                },
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(statistics)))
        }
        Err(e) => {
            tracing::error!("Failed to get task statistics: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to get task statistics".to_string(),
                )),
            )
        }
    }
}

/// 获取文档处理进度
pub async fn get_document_processing_progress(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let document_id = path.into_inner();

    match app_state
        .document_processing_pipeline
        .get_document_progress(document_id)
        .await
    {
        Ok(progress) => {
            // 获取与该文档相关的任务列表
            let tasks = app_state
                .processing_task_service
                .get_tasks_by_resource(document_id)
                .await
                .unwrap_or_default();

            let progress_response = DocumentProcessingProgressResponse {
                document_id: progress.document_id,
                overall_progress: progress.overall_progress,
                status: progress.status,
                tasks_completed: progress.tasks_completed,
                tasks_total: progress.tasks_total,
                tasks_failed: progress.tasks_failed,
                estimated_completion: progress.estimated_completion,
                current_task: progress.current_task,
                tasks: tasks.into_iter().map(processing_task_to_response).collect(),
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(progress_response)))
        }
        Err(e) => {
            tracing::error!(
                "Failed to get document processing progress for {}: {}",
                document_id,
                e
            );
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to get document processing progress".to_string(),
                )),
            )
        }
    }
}

/// 重试任务
pub async fn retry_task(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    request: web::Json<RetryTaskRequest>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let retry_request = request.into_inner();

    let success = if retry_request.force.unwrap_or(false) {
        app_state
            .processing_task_service
            .force_requeue_task(task_id)
            .await
    } else {
        app_state.processing_task_service.retry_task(task_id).await
    };

    match success {
        Ok(true) => Ok(HttpResponse::Ok().json(ApiResponse::success("Task retry initiated"))),
        Ok(false) => Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
            "Task cannot be retried or not found".to_string(),
        ))),
        Err(e) => {
            tracing::error!("Failed to retry task {}: {}", task_id, e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("Failed to retry task".to_string())))
        }
    }
}

/// 取消任务
pub async fn cancel_task(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();

    match app_state.processing_task_service.cancel_task(task_id).await {
        Ok(true) => Ok(HttpResponse::Ok().json(ApiResponse::success("Task cancelled"))),
        Ok(false) => Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
            "Task cannot be cancelled or not found".to_string(),
        ))),
        Err(e) => {
            tracing::error!("Failed to cancel task {}: {}", task_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to cancel task".to_string(),
                )),
            )
        }
    }
}

/// 批量任务操作
pub async fn batch_task_operation(
    app_state: web::Data<AppState>,
    request: web::Json<BatchTaskOperationRequest>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let batch_request = request.into_inner();
    let mut affected_count = 0;

    for task_id in batch_request.task_ids {
        let result = match batch_request.operation {
            crate::types::TaskOperation::Cancel => {
                app_state.processing_task_service.cancel_task(task_id).await
            }
            crate::types::TaskOperation::Retry => {
                app_state.processing_task_service.retry_task(task_id).await
            }
            crate::types::TaskOperation::Reset => {
                app_state
                    .processing_task_service
                    .force_requeue_task(task_id)
                    .await
            }
        };

        if let Ok(true) = result {
            affected_count += 1;
        }
    }

    Ok(HttpResponse::Ok().json(ApiResponse::success(format!(
        "{} tasks processed",
        affected_count
    ))))
}

/// 获取实时任务监控数据
pub async fn get_task_monitor(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    // 获取统计信息
    let stats_result = app_state.processing_task_service.get_statistics().await;
    let active_tasks_result = app_state
        .processing_task_service
        .get_tasks_by_status(TaskStatus::Running)
        .await;
    let failed_tasks_result = app_state
        .processing_task_service
        .get_failed_tasks(false)
        .await;
    let queued_tasks_result = app_state
        .processing_task_service
        .get_queued_tasks(Some(10))
        .await;

    match (
        stats_result,
        active_tasks_result,
        failed_tasks_result,
        queued_tasks_result,
    ) {
        (Ok(stats), Ok(active_tasks), Ok(failed_tasks), Ok(queued_tasks)) => {
            let monitor_data = TaskMonitorResponse {
                timestamp: current_millis(),
                statistics: TaskStatisticsResponse {
                    total_tasks: stats.total_tasks,
                    queued_tasks: stats.queued_tasks,
                    running_tasks: stats.running_tasks,
                    completed_tasks: stats.completed_tasks,
                    failed_tasks: stats.failed_tasks,
                    cancelled_tasks: stats.cancelled_tasks,
                    retrying_tasks: stats.retrying_tasks,
                    average_processing_time: None,
                    success_rate: if stats.total_tasks > 0 {
                        stats.completed_tasks as f64 / stats.total_tasks as f64 * 100.0
                    } else {
                        0.0
                    },
                },
                active_tasks: active_tasks
                    .into_iter()
                    .map(processing_task_to_response)
                    .collect(),
                recent_failures: failed_tasks
                    .into_iter()
                    .take(5)
                    .map(processing_task_to_response)
                    .collect(),
                processing_queue_size: queued_tasks.len(),
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(monitor_data)))
        }
        _ => {
            tracing::error!("Failed to get task monitor data");
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to get task monitor data".to_string(),
                )),
            )
        }
    }
}

/// 创建新任务（手动创建）
pub async fn create_task(
    app_state: web::Data<AppState>,
    request: web::Json<CreateTaskRequest>,
    claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let create_request = request.into_inner();

    let task_context = TaskContext {
        file_path: create_request.file_path,
        original_filename: create_request.original_filename,
        mime_type: create_request.mime_type,
        file_size: None,
        knowledge_base_id: None,
        user_id: Some(claims.user_id),
        additional_params: serde_json::json!({}),
    };

    match app_state
        .processing_task_service
        .create_task(
            create_request.task_type,
            create_request.resource_id,
            create_request
                .priority
                .unwrap_or(wisdom_vault_database::models::TaskPriority::Normal),
            task_context,
        )
        .await
    {
        Ok(task) => {
            let task_response = processing_task_to_response(task);
            Ok(HttpResponse::Created().json(ApiResponse::success(task_response)))
        }
        Err(e) => {
            tracing::error!("Failed to create task: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to create task".to_string(),
                )),
            )
        }
    }
}

/// 清理已完成的旧任务
pub async fn cleanup_completed_tasks(
    app_state: web::Data<AppState>,
    query: web::Query<serde_json::Value>,
    _claims: web::ReqData<Claims>,
) -> Result<HttpResponse> {
    let days_old = query.get("days_old").and_then(|v| v.as_i64()).unwrap_or(7);

    match app_state
        .processing_task_service
        .cleanup_old_tasks(days_old)
        .await
    {
        Ok(cleaned_count) => Ok(HttpResponse::Ok().json(ApiResponse::success(format!(
            "Cleaned {} completed tasks older than {} days",
            cleaned_count, days_old
        )))),
        Err(e) => {
            tracing::error!("Failed to cleanup completed tasks: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to cleanup tasks".to_string(),
                )),
            )
        }
    }
}
