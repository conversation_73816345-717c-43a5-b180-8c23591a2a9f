use actix_session::{Session, SessionExt};
use actix_web::{
    HttpRequest, HttpResponse, ResponseError, Result,
    body::BoxBody,
    http::StatusCode,
    web::{Data, Json},
};
use derive_more::Display;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use validator::Validate;
use wisdom_vault_common::{t, time::to_datetime};
use wisdom_vault_core::auth;

use crate::{
    app_state::AppState,
    session::{
        OptionalUserSession, RequiredUserSession, SESSION_NAME, UserSession, get_client_ip,
        get_locale, get_user_agent,
    },
    types::common::ApiResponse,
    web::validate_error,
};

/// 登录请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct LoginRequest {
    /// 用户名
    #[validate(length(min = 1, message = "用户名不能为空"))]
    pub username: String,
    /// 密码
    #[validate(length(min = 1, message = "密码不能为空"))]
    pub password: String,
    /// 是否记住我
    pub remember_me: Option<bool>,
}

/// 用户信息
#[derive(Debug, Serialize)]
pub struct UserInfo {
    pub id: String,
    pub username: String,
    pub email: String,
    pub full_name: Option<String>,
}

/// 当前用户信息响应
#[derive(Debug, Serialize)]
pub struct MeResponse {
    pub user: UserInfo,
    pub session_info: SessionInfo,
}

/// 会话信息
#[derive(Debug, Serialize)]
pub struct SessionInfo {
    pub ip_address: String,
    pub user_agent: Option<String>,
    pub login_time: String,
}

#[derive(Debug, Display)]
pub enum AuthError {
    /// 验证错误
    #[display("Validation error: {err}")]
    ValidationError {
        locale: String,
        err: validator::ValidationErrors,
    },

    /// 用户名或密码错误
    #[display("Username or password incorrect")]
    UsernameOrPasswordIncorrect { locale: String },

    /// 账户已禁用
    #[display("Account disabled")]
    AccountDisabled { locale: String },

    /// 系统错误
    #[display("System error: {err}")]
    SystemError { locale: String, err: anyhow::Error },
}

impl From<(auth::Error, String)> for AuthError {
    fn from((err, locale): (auth::Error, String)) -> Self {
        match err {
            auth::Error::UsernameOrPasswordIncorrect => {
                AuthError::UsernameOrPasswordIncorrect { locale }
            }
            auth::Error::AccountDisabled => AuthError::AccountDisabled { locale },
            auth::Error::Unknown(err) => AuthError::SystemError { locale, err },
        }
    }
}

impl ResponseError for AuthError {
    fn status_code(&self) -> StatusCode {
        match self {
            AuthError::SystemError { .. } => StatusCode::INTERNAL_SERVER_ERROR,
            _ => StatusCode::BAD_REQUEST,
        }
    }

    fn error_response(&self) -> HttpResponse<BoxBody> {
        let mut res = HttpResponse::build(self.status_code());

        match self {
            AuthError::ValidationError { locale, err } => res.json(
                ApiResponse::<()>::validation_error(validate_error::to_value(locale, err)),
            ),
            AuthError::UsernameOrPasswordIncorrect { locale } => {
                res.json(ApiResponse::bad_request(
                    t!("auth.username_password_incorrect", locale = locale).into(),
                ))
            }
            AuthError::AccountDisabled { locale } => res.json(ApiResponse::<()>::unauthorized(
                Some(t!("session.disabled", locale = locale).into()),
            )),
            AuthError::SystemError { locale, err } => {
                tracing::error!("System error: {:?}", err);
                res.json(ApiResponse::<()>::internal_error(Some(
                    t!("system.busy", locale = locale).into(),
                )))
            }
        }
    }
}

/// 用户登录
#[utoipa::path(
    tag = "认证API",
    post,
    path = "/api/v1/auth/login",
    request_body = LoginRequest,
    responses(
        (status = 200, description = "登录成功", body = ApiResponse<u8>),
        (status = 400, description = "请求参数错误"),
        (status = 500, description = "系统繁忙"),
    )
)]
pub async fn login(
    req: HttpRequest,
    request: Json<LoginRequest>,
    app_state: Data<AppState>,
) -> Result<ApiResponse<()>, AuthError> {
    let locale = get_locale(&req);

    // 验证输入
    request
        .validate()
        .map_err(|err| AuthError::ValidationError {
            locale: locale.clone(),
            err,
        })?;

    // 获取客户端信息
    let ip_address = get_client_ip(&req);
    let user_agent = get_user_agent(&req);

    // 执行登录验证
    let login_req = auth::LoginRequest {
        principal: request.username.clone(),
        password: request.password.clone(),
    };
    match app_state.auth_service.authenticate(login_req).await {
        Ok(auth_response) => {
            // 创建会话
            let session = req.get_session();
            let user_session = UserSession::new(
                auth_response.id.clone(),
                auth_response.username.clone(),
                ip_address,
                user_agent,
            );

            // 存储会话到session中
            _ = session.insert(SESSION_NAME, &user_session);

            Ok(ApiResponse::success_empty())
        }
        Err(err) => Err((err, locale).into()),
    }
}

/// POST /api/v1/auth/logout - 用户注销
pub async fn logout(session: Session, _user: RequiredUserSession) -> HttpResponse {
    // 清除会话
    session.purge();

    HttpResponse::Ok().json(ApiResponse::success_empty())
}

/// GET /api/v1/auth/current - 获取当前用户信息
pub async fn current(session: RequiredUserSession) -> Result<HttpResponse> {
    let user_session = session.into_inner();

    let response = MeResponse {
        user: UserInfo {
            id: user_session.user_id.clone(),
            username: user_session.username.clone(),
            email: "".to_string(), // 从会话中无法获取，需要从数据库查询
            full_name: None,
        },
        session_info: SessionInfo {
            ip_address: user_session.ip_address.clone(),
            user_agent: user_session.user_agent.clone(),
            login_time: to_datetime(user_session.created_at())
                .format("%Y-%m-%d %H:%M:%S")
                .to_string(),
        },
    };

    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

/// POST /api/v1/auth/refresh - 刷新会话
pub async fn refresh_session(
    session: RequiredUserSession,
    req: HttpRequest,
) -> Result<HttpResponse> {
    let mut user_session = session.into_inner();
    let session = req.get_session();

    // 刷新会话时间戳
    user_session.refresh();

    // 更新会话
    if let Err(e) = session.insert("user_session", &user_session) {
        tracing::error!("会话刷新失败: {:?}", e);
        return Ok(
            HttpResponse::InternalServerError().json(ApiResponse::<()>::internal_error(Some(
                "会话刷新失败".to_string(),
            ))),
        );
    }

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        (),
        "会话刷新成功".to_string(),
    )))
}

/// GET /api/v1/auth/is_login - 检查认证状态
pub async fn auth_status(session: OptionalUserSession) -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(ApiResponse::success(session.into_inner().is_some())))
}
