use actix_web::{HttpResponse, Result as ActixResult, web};
use std::{collections::HashMap, sync::Arc};
use uuid::Uuid;
use wisdom_vault_auth::Claims;
use wisdom_vault_core::services::{
    AdvancedSearchParams,
    AdvancedSearchResult,
    AdvancedVectorSearchRequest,
    BM25Config,
    DateRangeFilter,
    DocumentService,
    KeywordIndexManager,
    KeywordSearchRequest,
    KeywordSearchService,
    SearchPerformanceConfig,
    SearchPerformanceMonitor,
    SearchSortBy,
    SearchSortOrder,
    // 向量搜索相关服务
    VectorSearchRequest,
    hybrid_search::HybridSearchRequest,
};
use wisdom_vault_database::{
    connection::DatabaseConnection,
    models::{DocumentStatus, EmbeddingType},
    repositories::{
        DocumentRepository, SurrealCategoryRepository, SurrealDocumentCategoryRepository,
        SurrealDocumentRepository, SurrealDocumentTagRepository, SurrealTagRepository,
    },
};

use crate::{
    app_state::AppState,
    types::{ApiResponse, search::*},
};

/// 高级搜索文档
pub async fn advanced_search_documents(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    request: web::Json<AdvancedSearchRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();

    // 创建仓储实例
    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    // 创建增强的文档服务
    let service = DocumentService::with_enhanced_search(
        document_repo,
        tag_repo,
        category_repo,
        document_tag_repo,
        document_category_repo,
    );

    // 构建搜索参数
    let search_params = AdvancedSearchParams {
        query: request.query,
        knowledge_base_id: request.knowledge_base_id,
        tag_ids: request.tag_ids,
        category_ids: request.category_ids,
        file_types: request.file_types,
        authors: request.authors,
        statuses: request
            .statuses
            .map(|statuses| statuses.into_iter().map(|s| s.into()).collect()),
        date_range: request.date_range.map(|dr| DateRangeFilter {
            start: dr.start,
            end: dr.end,
            date_field: dr.date_field.unwrap_or_else(|| "created_at".to_string()),
        }),
        match_all_tags: request.match_all_tags.unwrap_or(false),
        match_all_categories: request.match_all_categories.unwrap_or(false),
        sort_by: request
            .sort_by
            .map(|s| s.into())
            .unwrap_or(SearchSortBy::Relevance),
        sort_order: request
            .sort_order
            .map(|s| s.into())
            .unwrap_or(SearchSortOrder::Desc),
        limit: request.limit,
        offset: request.offset,
    };

    match service.advanced_search(search_params).await {
        Ok(result) => {
            let response = AdvancedSearchResponse::from(result);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Advanced search failed: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("Search failed".to_string())))
        }
    }
}

/// 搜索建议（自动完成）
pub async fn search_suggestions(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    query: web::Query<SearchSuggestionsQuery>,
) -> ActixResult<HttpResponse> {
    let query = query.into_inner();

    if query.query.trim().is_empty() {
        return Ok(
            HttpResponse::Ok().json(ApiResponse::success(SearchSuggestionsResponse {
                suggestions: Vec::new(),
            })),
        );
    }

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(document_repo);

    // 简单的搜索建议实现：基于现有文档标题
    match service
        .search_documents(&query.query, query.knowledge_base_id)
        .await
    {
        Ok(documents) => {
            let suggestions: Vec<String> = documents
                .into_iter()
                .take(query.limit.unwrap_or(10) as usize)
                .map(|doc| doc.title)
                .collect();

            let response = SearchSuggestionsResponse { suggestions };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Search suggestions failed: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Search suggestions failed".to_string(),
                )),
            )
        }
    }
}

/// 获取搜索统计
pub async fn get_search_statistics(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Option<Uuid>>, // knowledge_base_id
) -> ActixResult<HttpResponse> {
    let kb_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentService::new(document_repo);

    match service.get_document_statistics(kb_id).await {
        Ok(stats) => {
            let response = SearchStatisticsResponse {
                knowledge_base_id: kb_id,
                total_documents: stats.total_count,
                indexed_documents: stats.indexed_count,
                processing_documents: stats.processing_count,
                failed_documents: stats.failed_count,
                archived_documents: stats.archived_count,
                ready_documents: stats.ready_count(),
                success_rate: stats.success_rate(),
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get search statistics: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to get search statistics".to_string(),
                )),
            )
        }
    }
}

/// 导出搜索结果
pub async fn export_search_results(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    request: web::Json<ExportSearchRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();

    // 创建增强的文档服务
    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let tag_repo = Arc::new(SurrealTagRepository::new(db.get_ref().clone()));
    let category_repo = Arc::new(SurrealCategoryRepository::new(db.get_ref().clone()));
    let document_tag_repo = Arc::new(SurrealDocumentTagRepository::new(db.get_ref().clone()));
    let document_category_repo =
        Arc::new(SurrealDocumentCategoryRepository::new(db.get_ref().clone()));

    let service = DocumentService::with_enhanced_search(
        document_repo,
        tag_repo,
        category_repo,
        document_tag_repo,
        document_category_repo,
    );

    // 构建搜索参数，移除分页限制以获取所有结果
    let search_params = AdvancedSearchParams {
        query: request.search_params.query,
        knowledge_base_id: request.search_params.knowledge_base_id,
        tag_ids: request.search_params.tag_ids,
        category_ids: request.search_params.category_ids,
        file_types: request.search_params.file_types,
        authors: request.search_params.authors,
        statuses: request
            .search_params
            .statuses
            .map(|statuses| statuses.into_iter().map(|s| s.into()).collect()),
        date_range: request.search_params.date_range.map(|dr| DateRangeFilter {
            start: dr.start,
            end: dr.end,
            date_field: dr.date_field.unwrap_or_else(|| "created_at".to_string()),
        }),
        match_all_tags: request.search_params.match_all_tags.unwrap_or(false),
        match_all_categories: request.search_params.match_all_categories.unwrap_or(false),
        sort_by: request
            .search_params
            .sort_by
            .map(|s| s.into())
            .unwrap_or(SearchSortBy::Relevance),
        sort_order: request
            .search_params
            .sort_order
            .map(|s| s.into())
            .unwrap_or(SearchSortOrder::Desc),
        limit: Some(10000), // 大限制以获取所有结果
        offset: Some(0),
    };

    match service.advanced_search(search_params).await {
        Ok(result) => {
            let response = ExportSearchResponse {
                format: request.format.clone(),
                total_exported: result.documents.len() as i64,
                download_url: format!("/api/v1/search/download/{}", uuid::Uuid::new_v4()),
                expires_at: chrono::Utc::now() + chrono::Duration::hours(24),
            };

            // TODO: 实际的导出逻辑应该生成文件并存储，这里只是返回响应
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Export search results failed: {}", e);
            Ok(HttpResponse::BadRequest()
                .json(ApiResponse::<()>::error("Export failed".to_string())))
        }
    }
}

/// BM25关键词搜索
pub async fn keyword_search(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    request: web::Json<KeywordSearchApiRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();
    let start_time = std::time::Instant::now();

    // 创建文档仓储
    let document_repo: Arc<dyn DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));

    // 创建关键词搜索服务
    let keyword_service = Arc::new(KeywordSearchService::new_with_defaults(document_repo));

    // 创建性能监控器
    let performance_monitor = Arc::new(SearchPerformanceMonitor::new_with_defaults(Arc::clone(
        &keyword_service,
    )));

    // 启动性能监控（如果还未启动）
    let _ = performance_monitor.start_monitoring().await;

    // 构建搜索请求
    let search_request = KeywordSearchRequest {
        query: request.query.clone(),
        knowledge_base_id: request.knowledge_base_id,
        limit: request.limit,
        offset: request.offset,
        min_score: request.min_score,
        status_filter: request
            .status_filter
            .map(|statuses| statuses.into_iter().map(|s| s.into()).collect()),
        enable_query_expansion: request.enable_query_expansion,
    };

    match keyword_service.search(search_request.clone()).await {
        Ok(results) => {
            let response_time_ms = start_time.elapsed().as_millis() as u64;

            // 记录搜索查询到性能监控器
            let _ = performance_monitor
                .record_search_query(
                    &search_request,
                    response_time_ms,
                    results.total_count,
                    Some(results.query_stats.clone()),
                    None, // TODO: 从 Claims 中提取用户ID
                    None, // TODO: 从请求中提取客户端IP
                )
                .await;

            let response = KeywordSearchApiResponse::from(results);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("关键词搜索失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("关键词搜索失败".to_string())))
        }
    }
}

/// 重建关键词索引
pub async fn rebuild_keyword_index(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    query: web::Query<RebuildIndexQuery>,
) -> ActixResult<HttpResponse> {
    let query = query.into_inner();

    // 创建文档仓储
    let document_repo: Arc<dyn DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));

    // 创建关键词搜索服务
    let keyword_service = Arc::new(KeywordSearchService::new_with_defaults(Arc::clone(
        &document_repo,
    )));

    // 创建索引管理器
    let index_manager = Arc::new(KeywordIndexManager::new_with_defaults(
        Arc::clone(&keyword_service),
        document_repo,
    ));

    // 启动索引管理器
    if let Err(e) = index_manager.start().await {
        tracing::error!("启动索引管理器失败: {}", e);
        return Ok(HttpResponse::InternalServerError()
            .json(ApiResponse::<()>::error("启动索引管理器失败".to_string())));
    }

    // 提交重建任务
    match index_manager
        .submit_full_rebuild_task(query.knowledge_base_id)
        .await
    {
        Ok(task_id) => {
            let response = IndexRebuildResponse {
                task_id,
                message: "索引重建任务已提交".to_string(),
                estimated_time_minutes: Some(5), // 估算时间
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("提交索引重建任务失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("提交索引重建任务失败".to_string())))
        }
    }
}

/// 获取索引统计信息
pub async fn get_keyword_index_statistics(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    // 创建文档仓储
    let document_repo: Arc<dyn DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));

    // 创建关键词搜索服务
    let keyword_service = Arc::new(KeywordSearchService::new_with_defaults(document_repo));

    match keyword_service.get_index_statistics().await {
        stats => {
            let response = KeywordIndexStatisticsResponse::from(stats);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
    }
}

/// 获取索引任务状态
pub async fn get_index_task_status(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // task_id
) -> ActixResult<HttpResponse> {
    let task_id = path.into_inner();

    // 创建文档仓储
    let document_repo: Arc<dyn DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));

    // 创建关键词搜索服务
    let keyword_service = Arc::new(KeywordSearchService::new_with_defaults(Arc::clone(
        &document_repo,
    )));

    // 创建索引管理器
    let index_manager = Arc::new(KeywordIndexManager::new_with_defaults(
        keyword_service,
        document_repo,
    ));

    if let Some(task) = index_manager.get_task_status(task_id).await {
        let response = IndexTaskStatusResponse::from(task);
        Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
    } else {
        Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error("索引任务未找到".to_string())))
    }
}

/// 获取搜索性能指标
pub async fn get_search_performance_metrics(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    // 创建文档仓储
    let document_repo: Arc<dyn DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));

    // 创建关键词搜索服务
    let keyword_service = Arc::new(KeywordSearchService::new_with_defaults(document_repo));

    // 创建性能监控器
    let performance_monitor =
        Arc::new(SearchPerformanceMonitor::new_with_defaults(keyword_service));

    match performance_monitor.get_performance_metrics().await {
        Ok(metrics) => {
            let response = SearchPerformanceMetricsResponse::from(metrics);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("获取搜索性能指标失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("获取搜索性能指标失败".to_string())))
        }
    }
}

/// 获取热门查询
pub async fn get_popular_queries(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    query: web::Query<HashMap<String, String>>,
) -> ActixResult<HttpResponse> {
    let limit = query
        .get("limit")
        .and_then(|s| s.parse::<usize>().ok())
        .or(Some(10));

    // 创建文档仓储
    let document_repo: Arc<dyn DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));

    // 创建关键词搜索服务
    let keyword_service = Arc::new(KeywordSearchService::new_with_defaults(document_repo));

    // 创建性能监控器
    let performance_monitor =
        Arc::new(SearchPerformanceMonitor::new_with_defaults(keyword_service));

    let popular_queries = performance_monitor.get_popular_queries(limit).await;
    let responses: Vec<PopularQueryResponse> =
        popular_queries.into_iter().map(Into::into).collect();

    let response = PopularQueriesResponse { queries: responses };

    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

/// 获取搜索性能异常
pub async fn get_search_anomalies(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    query: web::Query<HashMap<String, String>>,
) -> ActixResult<HttpResponse> {
    let limit = query
        .get("limit")
        .and_then(|s| s.parse::<usize>().ok())
        .or(Some(20));

    // 创建文档仓储
    let document_repo: Arc<dyn DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));

    // 创建关键词搜索服务
    let keyword_service = Arc::new(KeywordSearchService::new_with_defaults(document_repo));

    // 创建性能监控器
    let performance_monitor =
        Arc::new(SearchPerformanceMonitor::new_with_defaults(keyword_service));

    let anomalies = performance_monitor.get_performance_anomalies(limit).await;
    let responses: Vec<SearchAnomalyResponse> = anomalies.into_iter().map(Into::into).collect();

    let response = SearchAnomaliesResponse {
        anomalies: responses,
    };

    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

/// 获取调优建议
pub async fn get_tuning_recommendations(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    query: web::Query<HashMap<String, String>>,
) -> ActixResult<HttpResponse> {
    let limit = query
        .get("limit")
        .and_then(|s| s.parse::<usize>().ok())
        .or(Some(10));

    // 创建文档仓储
    let document_repo: Arc<dyn DocumentRepository + Send + Sync> =
        Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));

    // 创建关键词搜索服务
    let keyword_service = Arc::new(KeywordSearchService::new_with_defaults(document_repo));

    // 创建性能监控器
    let performance_monitor =
        Arc::new(SearchPerformanceMonitor::new_with_defaults(keyword_service));

    let recommendations = performance_monitor.get_tuning_recommendations(limit).await;
    let responses: Vec<TuningRecommendationResponse> =
        recommendations.into_iter().map(Into::into).collect();

    let response = TuningRecommendationsResponse {
        recommendations: responses,
    };

    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

// ===== 语义向量搜索处理器 =====

/// 向量相似度搜索
pub async fn vector_similarity_search(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
    request: web::Json<VectorSearchApiRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();
    let start_time = std::time::Instant::now();

    // 转换请求到服务层类型
    let search_request = VectorSearchRequest {
        query_vector: request.query_vector,
        query_text: request.query_text,
        model_id: request.model_id,
        similarity_threshold: request.similarity_threshold,
        limit: request.limit,
        knowledge_base_id: request.knowledge_base_id,
        model_name_filter: request.model_name_filter,
        embedding_type_filter: request.embedding_type_filter.map(|s| match s.as_str() {
            "dense" => EmbeddingType::Dense,
            "sparse" => EmbeddingType::Sparse,
            "hybrid" => EmbeddingType::Hybrid,
            _ => EmbeddingType::Dense,
        }),
    };

    match app_state
        .vector_search_service
        .vector_similarity_search(search_request)
        .await
    {
        Ok(mut results) => {
            let response_time_ms = start_time.elapsed().as_millis() as u64;
            results.search_time_ms = response_time_ms;

            let response = VectorSearchApiResponse::from(results);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("向量相似度搜索失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("向量相似度搜索失败".to_string())))
        }
    }
}

/// 带过滤器的向量搜索
pub async fn vector_search_with_filters(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
    request: web::Json<VectorSearchApiRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();
    let start_time = std::time::Instant::now();

    // 转换请求到服务层类型
    let search_request = VectorSearchRequest {
        query_vector: request.query_vector,
        query_text: request.query_text,
        model_id: request.model_id,
        similarity_threshold: request.similarity_threshold,
        limit: request.limit,
        knowledge_base_id: request.knowledge_base_id,
        model_name_filter: request.model_name_filter,
        embedding_type_filter: request.embedding_type_filter.map(|s| match s.as_str() {
            "dense" => EmbeddingType::Dense,
            "sparse" => EmbeddingType::Sparse,
            "hybrid" => EmbeddingType::Hybrid,
            _ => EmbeddingType::Dense,
        }),
    };

    match app_state
        .vector_search_service
        .vector_search_with_filters(search_request)
        .await
    {
        Ok(mut results) => {
            let response_time_ms = start_time.elapsed().as_millis() as u64;
            results.search_time_ms = response_time_ms;

            let response = VectorSearchApiResponse::from(results);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("带过滤器的向量搜索失败: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "带过滤器的向量搜索失败".to_string(),
                )),
            )
        }
    }
}

/// 混合搜索（向量 + 文本）
pub async fn hybrid_search(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
    request: web::Json<HybridSearchApiRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();
    let start_time = std::time::Instant::now();

    // 转换请求到服务层类型
    let search_request = wisdom_vault_core::services::vector_search::HybridSearchRequest {
        query_vector: request.query_vector,
        query_text: request.query_text,
        model_id: request.model_id,
        vector_weight: request.vector_weight,
        text_weight: request.text_weight,
        similarity_threshold: request.similarity_threshold,
        limit: request.limit,
        knowledge_base_id: request.knowledge_base_id,
    };

    match app_state
        .vector_search_service
        .hybrid_search(search_request)
        .await
    {
        Ok(mut results) => {
            let response_time_ms = start_time.elapsed().as_millis() as u64;
            results.search_time_ms = response_time_ms;

            let response = VectorSearchApiResponse::from(results);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("混合搜索失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("混合搜索失败".to_string())))
        }
    }
}

/// 获取向量搜索统计信息
pub async fn get_vector_search_statistics(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    match app_state
        .vector_search_service
        .get_vector_statistics()
        .await
    {
        Ok(vector_stats) => {
            let search_stats = app_state
                .vector_search_service
                .get_search_statistics()
                .await;

            let response = VectorSearchStatisticsApiResponse {
                total_vectors: vector_stats.total_vectors as u64,
                avg_vector_dimension: vector_stats.average_dimension,
                index_size_mb: vector_stats.storage_size_mb,
                last_updated: chrono::Utc::now(), /* VectorStatistics没有last_updated字段，
                                                   * 使用当前时间 */
                search_performance: VectorSearchPerformanceResponse::from(search_stats),
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("获取向量搜索统计信息失败: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "获取向量搜索统计信息失败".to_string(),
                )),
            )
        }
    }
}

/// 重建向量索引
pub async fn rebuild_vector_index(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
    request: web::Json<VectorIndexRebuildRequest>,
) -> ActixResult<HttpResponse> {
    let _request = request.into_inner();

    match app_state.vector_search_service.rebuild_vector_index().await {
        Ok(_) => {
            let task_id = Uuid::new_v4();
            let response = VectorIndexRebuildResponse {
                task_id,
                message: "向量索引重建任务已启动".to_string(),
                estimated_time_minutes: Some(10), // 估算时间
                affected_vectors_count: None,     // 可以从统计信息中获取
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("重建向量索引失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("重建向量索引失败".to_string())))
        }
    }
}

/// 高级向量搜索（带完整过滤器支持）
pub async fn advanced_vector_search(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
    request: web::Json<ExtendedVectorSearchRequest>,
) -> ActixResult<HttpResponse> {
    let request = request.into_inner();
    let start_time = std::time::Instant::now();

    // 转换基础搜索请求
    let base_request = VectorSearchRequest {
        query_vector: request.base_request.query_vector,
        query_text: request.base_request.query_text,
        model_id: request.base_request.model_id,
        similarity_threshold: request.base_request.similarity_threshold,
        limit: request.base_request.limit,
        knowledge_base_id: request.base_request.knowledge_base_id,
        model_name_filter: request.base_request.model_name_filter,
        embedding_type_filter: request.base_request.embedding_type_filter.map(|s| {
            match s.as_str() {
                "dense" => EmbeddingType::Dense,
                "sparse" => EmbeddingType::Sparse,
                "hybrid" => EmbeddingType::Hybrid,
                _ => EmbeddingType::Dense,
            }
        }),
    };

    // 转换高级过滤器
    let advanced_filters = request.advanced_filters.map(|api_filters| {
        wisdom_vault_core::services::AdvancedVectorSearchFilters {
            score_range: api_filters
                .score_range
                .map(|r| wisdom_vault_core::services::ScoreRange {
                    min_score: r.min_score,
                    max_score: r.max_score,
                }),
            sparsity_range: api_filters.sparsity_range.map(|r| {
                wisdom_vault_core::services::SparsityRange {
                    min_sparsity: r.min_sparsity,
                    max_sparsity: r.max_sparsity,
                }
            }),
            chunk_size_range: api_filters.chunk_size_range.map(|r| {
                wisdom_vault_core::services::ChunkSizeRange {
                    min_size: r.min_size,
                    max_size: r.max_size,
                }
            }),
            exclude_document_ids: api_filters.exclude_document_ids,
            verified_only: api_filters.verified_only,
            last_modified_after: api_filters.last_modified_after,
            created_after: api_filters.created_after,
        }
    });

    // 构建高级搜索请求
    let advanced_request = AdvancedVectorSearchRequest {
        base_request,
        advanced_filters,
    };

    match app_state
        .vector_search_service
        .advanced_vector_search(advanced_request)
        .await
    {
        Ok(mut results) => {
            let response_time_ms = start_time.elapsed().as_millis() as u64;
            results.search_time_ms = response_time_ms;

            let response = VectorSearchApiResponse::from(results);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("高级向量搜索失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("高级向量搜索失败".to_string())))
        }
    }
}

/// 优化向量存储
pub async fn optimize_vector_storage(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let start_time = std::time::Instant::now();

    // 获取优化前的统计信息
    let before_stats = match app_state
        .vector_search_service
        .get_vector_statistics()
        .await
    {
        Ok(stats) => stats,
        Err(e) => {
            tracing::error!("获取向量统计信息失败: {}", e);
            return Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("获取向量统计信息失败".to_string())));
        }
    };

    match app_state
        .vector_search_service
        .optimize_vector_storage()
        .await
    {
        Ok(cleaned_count) => {
            let optimization_time = start_time.elapsed().as_secs_f64();

            // 获取优化后的统计信息
            let after_stats = app_state
                .vector_search_service
                .get_vector_statistics()
                .await
                .unwrap_or(before_stats.clone());

            let saved_size_mb = before_stats.storage_size_mb - after_stats.storage_size_mb;

            let response = VectorStorageOptimizationResponse {
                before_size_mb: before_stats.storage_size_mb,
                after_size_mb: after_stats.storage_size_mb,
                saved_size_mb,
                cleaned_vectors: cleaned_count,
                optimization_time_seconds: optimization_time,
                message: format!(
                    "向量存储优化完成，清理了 {} 个向量，节省了 {:.2} MB 存储空间",
                    cleaned_count, saved_size_mb
                ),
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("优化向量存储失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("优化向量存储失败".to_string())))
        }
    }
}
