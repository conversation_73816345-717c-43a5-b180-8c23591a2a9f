use actix_web::{HttpResponse, Result as ActixResult, web};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use wisdom_vault_auth::Claims;
use wisdom_vault_database::models::{EmbeddingTaskStatus, TaskPriority};

use crate::{app_state::AppState, types::ApiResponse};

// ============================================================================
// 请求和响应类型定义
// ============================================================================

/// 文档向量化请求
#[derive(Debug, Deserialize)]
pub struct VectorizeDocumentRequest {
    pub document_id: Uuid,
    pub model_id: Option<Uuid>,
    pub force_recompute: Option<bool>,
}

/// 批量文档向量化请求
#[derive(Debug, Deserialize)]
pub struct BatchVectorizeRequest {
    pub document_ids: Vec<Uuid>,
    pub model_id: Option<Uuid>,
    pub force_recompute: Option<bool>,
    pub priority: Option<TaskPriority>,
    pub batch_config: Option<BatchVectorizationConfigRequest>,
}

/// 批量向量化配置请求
#[derive(Debug, Deserialize)]
pub struct BatchVectorizationConfigRequest {
    pub batch_size: Option<usize>,
    pub max_parallel_tasks: Option<usize>,
    pub retry_failed: Option<bool>,
    pub quality_threshold: Option<f64>,
    pub auto_recompute: Option<bool>,
}

/// 增量更新请求
#[derive(Debug, Deserialize)]
pub struct IncrementalUpdateRequest {
    pub knowledge_base_id: Option<Uuid>,
    pub force_update: Option<bool>,
}

/// 批量向量化提交响应
#[derive(Debug, Serialize)]
pub struct BatchVectorizationSubmitResponse {
    pub batch_id: Uuid,
    pub message: String,
    pub document_count: usize,
}

/// 模型加载响应
#[derive(Debug, Serialize)]
pub struct ModelLoadResponse {
    pub message: String,
    pub model_id: Uuid,
}

/// 模型卸载响应
#[derive(Debug, Serialize)]
pub struct ModelUnloadResponse {
    pub message: String,
    pub model_id: Uuid,
}

/// 批量任务取消响应
#[derive(Debug, Serialize)]
pub struct BatchCancelResponse {
    pub message: String,
    pub batch_id: Uuid,
}

/// 缓存清理响应
#[derive(Debug, Serialize)]
pub struct CacheClearResponse {
    pub message: String,
    pub cleared_entries: u64,
}

/// 无变更响应
#[derive(Debug, Serialize)]
pub struct NoChangesResponse {
    pub message: String,
    pub changes_count: usize,
}

/// 向量化结果响应
#[derive(Debug, Serialize)]
pub struct VectorizationResultResponse {
    pub task_id: Option<Uuid>,
    pub embeddings_count: usize,
    pub processing_time_ms: i64,
    pub quality_score: f64,
    pub success_count: usize,
    pub failed_count: usize,
}

/// 批量向量化结果响应
#[derive(Debug, Serialize)]
pub struct BatchVectorizationResultResponse {
    pub batch_id: Uuid,
    pub total_processed: usize,
    pub successful: usize,
    pub failed: usize,
    pub overall_quality_score: f64,
    pub total_processing_time_ms: i64,
    pub failed_document_ids: Vec<Uuid>,
}

/// 增量更新结果响应
#[derive(Debug, Serialize)]
pub struct IncrementalUpdateResultResponse {
    pub updated_documents: Vec<Uuid>,
    pub updated_chunks: Vec<Uuid>,
    pub vectors_updated: u64,
    pub failed_updates: u64,
    pub total_processing_time_ms: i64,
}

/// 任务状态响应
#[derive(Debug, Serialize)]
pub struct TaskStatusResponse {
    pub task_id: Uuid,
    pub status: EmbeddingTaskStatus,
    pub progress: f64,
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
}

/// 活跃任务响应
#[derive(Debug, Serialize)]
pub struct ActiveTaskResponse {
    pub task_id: Uuid,
    pub executor_type: String,
    pub started_at: DateTime<Utc>,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub progress: f64,
}

/// 调度器统计响应
#[derive(Debug, Serialize)]
pub struct SchedulerStatisticsResponse {
    pub total_tasks_scheduled: u64,
    pub total_tasks_completed: u64,
    pub total_tasks_failed: u64,
    pub current_queue_size: usize,
    pub active_tasks_count: usize,
    pub avg_execution_time_ms: f64,
}

/// 向量质量评估响应
#[derive(Debug, Serialize)]
pub struct VectorQualityResponse {
    pub overall_quality_score: f64,
    pub anomaly_count: usize,
    pub dimension_consistency: bool,
    pub avg_magnitude: f64,
    pub avg_sparsity_ratio: f64,
    pub quality_distribution: QualityDistributionResponse,
}

/// 质量分布响应
#[derive(Debug, Serialize)]
pub struct QualityDistributionResponse {
    pub excellent_count: usize,
    pub good_count: usize,
    pub fair_count: usize,
    pub poor_count: usize,
}

// ============================================================================
// API 处理器函数
// ============================================================================

/// 向量化单个文档
pub async fn vectorize_document(
    app_state: web::Data<AppState>,
    request: web::Json<VectorizeDocumentRequest>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let vectorization_service = &app_state.vectorization_service;

    match vectorization_service
        .vectorize_document(
            request.document_id,
            request.model_id,
            request.force_recompute.unwrap_or(false),
        )
        .await
    {
        Ok(result) => {
            let response = VectorizationResultResponse {
                task_id: None,
                embeddings_count: result.embeddings.len(),
                processing_time_ms: result.processing_time_ms,
                quality_score: result.quality_metrics.quality_score,
                success_count: result.success_count,
                failed_count: result.failed_count,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("文档向量化失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

/// 批量向量化文档
pub async fn batch_vectorize_documents(
    app_state: web::Data<AppState>,
    request: web::Json<BatchVectorizeRequest>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let batch_service = &app_state.batch_vectorization_service;

    // 提交批量任务
    match batch_service
        .submit_batch_task(
            request.document_ids.clone(),
            request.model_id.unwrap_or_else(|| Uuid::new_v4()), // 应该从默认模型获取
            request.priority.clone().unwrap_or(TaskPriority::Normal),
            request.force_recompute.unwrap_or(false),
        )
        .await
    {
        Ok(batch_id) => {
            let response = BatchVectorizationSubmitResponse {
                batch_id,
                message: "批量向量化任务已提交".to_string(),
                document_count: request.document_ids.len(),
            };

            Ok(HttpResponse::Accepted().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("批量向量化提交失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

/// 获取批量向量化进度
pub async fn get_batch_progress(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let batch_id = path.into_inner();
    let batch_service = &app_state.batch_vectorization_service;

    match batch_service.get_batch_progress(batch_id).await {
        Some(progress) => Ok(HttpResponse::Ok().json(ApiResponse::success(progress))),
        None => {
            Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error("批次未找到".to_string())))
        }
    }
}

/// 执行增量向量更新
pub async fn incremental_update(
    app_state: web::Data<AppState>,
    request: web::Json<IncrementalUpdateRequest>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let incremental_service = &app_state.incremental_update_service;

    // 检测变更
    match incremental_service
        .detect_changes(request.knowledge_base_id)
        .await
    {
        Ok(changes) => {
            if changes.is_empty() {
                let response = NoChangesResponse {
                    message: "未检测到需要更新的变更".to_string(),
                    changes_count: 0,
                };
                return Ok(HttpResponse::Ok().json(ApiResponse::success(response)));
            }

            // 执行增量更新
            match incremental_service
                .perform_incremental_update(changes)
                .await
            {
                Ok(result) => {
                    let response = IncrementalUpdateResultResponse {
                        updated_documents: result.updated_documents,
                        updated_chunks: result.updated_chunks,
                        vectors_updated: result.vectors_updated,
                        failed_updates: result.failed_updates,
                        total_processing_time_ms: result.total_processing_time_ms,
                    };

                    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
                }
                Err(e) => {
                    tracing::error!("增量更新执行失败: {}", e);
                    Ok(HttpResponse::InternalServerError()
                        .json(ApiResponse::<()>::error(e.to_string())))
                }
            }
        }
        Err(e) => {
            tracing::error!("变更检测失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

/// 获取调度器统计信息
pub async fn get_scheduler_statistics(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let scheduler = &app_state.task_scheduler;

    let stats = scheduler.get_statistics().await;
    let response = SchedulerStatisticsResponse {
        total_tasks_scheduled: stats.total_tasks_scheduled,
        total_tasks_completed: stats.total_tasks_completed,
        total_tasks_failed: stats.total_tasks_failed,
        current_queue_size: stats.current_queue_size,
        active_tasks_count: stats.active_tasks_count,
        avg_execution_time_ms: stats.avg_execution_time_ms,
    };

    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

/// 获取活跃任务列表
pub async fn get_active_tasks(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let scheduler = &app_state.task_scheduler;
    let active_tasks = scheduler.get_active_tasks().await;

    // 转换为可序列化的响应类型
    let response_tasks: Vec<ActiveTaskResponse> = active_tasks
        .into_iter()
        .map(|task| ActiveTaskResponse {
            task_id: task.task_id,
            executor_type: format!("{:?}", task.executor_type),
            started_at: task.started_at,
            estimated_completion: task.estimated_completion,
            progress: task.progress,
        })
        .collect();

    Ok(HttpResponse::Ok().json(ApiResponse::success(response_tasks)))
}

/// 评估向量质量
pub async fn assess_vector_quality(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let _document_id = path.into_inner();

    // 这里需要从数据库获取文档的向量数据
    // 由于我们没有完整的向量存储实现，这里提供一个简化版本
    let _quality_service = &app_state.quality_assessment_service;

    // 模拟质量评估结果
    let response = VectorQualityResponse {
        overall_quality_score: 0.85,
        anomaly_count: 2,
        dimension_consistency: true,
        avg_magnitude: 1.0,
        avg_sparsity_ratio: 0.15,
        quality_distribution: QualityDistributionResponse {
            excellent_count: 45,
            good_count: 32,
            fair_count: 8,
            poor_count: 2,
        },
    };

    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

/// 获取嵌入模型列表
pub async fn list_embedding_models(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let model_manager = &app_state.model_manager;

    let models = model_manager.list_loaded_models().await;
    Ok(HttpResponse::Ok().json(ApiResponse::success(models)))
}

/// 加载嵌入模型
pub async fn load_embedding_model(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let model_id = path.into_inner();
    let model_manager = &app_state.model_manager;

    match model_manager.load_model(model_id).await {
        Ok(_) => {
            let response = ModelLoadResponse {
                message: "模型加载成功".to_string(),
                model_id,
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("模型加载失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

/// 卸载嵌入模型
pub async fn unload_embedding_model(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let model_id = path.into_inner();
    let model_manager = &app_state.model_manager;

    match model_manager.unload_model(model_id).await {
        Ok(_) => {
            let response = ModelUnloadResponse {
                message: "模型卸载成功".to_string(),
                model_id,
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("模型卸载失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

/// 取消批量任务
pub async fn cancel_batch_task(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let batch_id = path.into_inner();
    let batch_service = &app_state.batch_vectorization_service;

    match batch_service.cancel_batch(batch_id).await {
        Ok(_) => {
            let response = BatchCancelResponse {
                message: "批量任务已取消".to_string(),
                batch_id,
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("取消批量任务失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(ApiResponse::<()>::error(e.to_string())))
        }
    }
}

// ============================================================================
// 路由配置
// ============================================================================

pub fn configure_vectorization_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/vectorization")
            // 文档向量化
            .route("/documents", web::post().to(vectorize_document))
            .route(
                "/documents/batch",
                web::post().to(batch_vectorize_documents),
            )
            .route(
                "/batch/{batch_id}/progress",
                web::get().to(get_batch_progress),
            )
            .route(
                "/batch/{batch_id}/cancel",
                web::post().to(cancel_batch_task),
            )
            // 增量更新
            .route("/incremental", web::post().to(incremental_update))
            // 任务调度和监控
            .route(
                "/scheduler/statistics",
                web::get().to(get_scheduler_statistics),
            )
            .route("/scheduler/tasks/active", web::get().to(get_active_tasks))
            // 质量评估
            .route(
                "/quality/{document_id}",
                web::get().to(assess_vector_quality),
            )
            // 模型管理
            .route("/models", web::get().to(list_embedding_models))
            .route(
                "/models/{model_id}/load",
                web::post().to(load_embedding_model),
            )
            .route(
                "/models/{model_id}/unload",
                web::post().to(unload_embedding_model),
            ),
    );
}
