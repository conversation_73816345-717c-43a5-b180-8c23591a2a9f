use actix_web::{HttpResponse, web::Data};

use crate::app_state::AppState;

#[utoipa::path(
    get,
    path = "/health",
    responses(
        (status = 200, description = "健康检查")
    )
)]
/// Health check endpoint
pub async fn health_check(app_state: Data<AppState>) -> HttpResponse {
    let mut state = app_state.as_ref().clone();

    match state.health_check().await {
        Ok(health_status) => {
            let cache_metrics = state.get_cache_metrics().await;

            HttpResponse::Ok().json(serde_json::json!({
                "status": if health_status.overall { "healthy" } else { "unhealthy" },
                "service": "wisdom-vault-api",
                "version": env!("CARGO_PKG_VERSION"),
                "next_id()": chrono::Local::now(),
                "components": {
                    "database": health_status.database,
                    "cache": health_status.cache
                },
                "cache_metrics": {
                    "hits": cache_metrics.hits,
                    "misses": cache_metrics.misses,
                    "errors": cache_metrics.errors,
                    "hit_rate": cache_metrics.hit_rate()
                }
            }))
        }
        Err(e) => HttpResponse::ServiceUnavailable().json(serde_json::json!({
            "status": "unhealthy",
            "service": "wisdom-vault-api",
            "version": env!("CARGO_PKG_VERSION"),
            "next_id()": chrono::Local::now(),
            "error": e.to_string()
        })),
    }
}
