use actix_multipart::Multipart;
use actix_web::{HttpResponse, Result, web};
use futures_util::TryStreamExt;
use std::collections::HashMap;
use tracing::{error, info, warn};
use uuid::Uuid;
use validator::Validate;

use wisdom_vault_core::{DocumentParserService, FileStorageService, StorageConfig, TikaConfig};
use wisdom_vault_database::connection::DatabaseConnection;

use crate::{
    session::RequiredUserSession,
    types::{
        ApiResponse, BatchUploadResponse, BulkFileOperationRequest, FileSearchRequest,
        FileUploadResponse, ProcessingStatus, StorageStatsResponse, UploadFileRequest,
    }
};

const MAX_FILE_SIZE: usize = 100 * 1024 * 1024; // 100MB
const MAX_FILES_PER_BATCH: usize = 10;

pub async fn upload_single_file(
    mut payload: Multipart,
    _db: web::Data<DatabaseConnection>,
    user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    let mut file_data: Option<Vec<u8>> = None;
    let mut filename: Option<String> = None;
    let mut content_type: Option<String> = None;
    let mut upload_request: Option<UploadFileRequest> = None;

    // Process multipart form data
    while let Some(mut field) = payload.try_next().await? {
        let field_name = field.name().unwrap_or("").to_string();

        match field_name.as_str() {
            "file" => {
                // Extract file metadata
                if let Some(content_disposition) = field.content_disposition() {
                    filename = content_disposition.get_filename().map(|f| f.to_string());
                }

                content_type = field.content_type().map(|ct| ct.to_string());

                // Read file data
                let mut bytes = Vec::new();
                while let Some(chunk) = field.try_next().await? {
                    bytes.extend_from_slice(&chunk);

                    if bytes.len() > MAX_FILE_SIZE {
                        return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
                            "File size exceeds maximum allowed size".to_string(),
                        )));
                    }
                }
                file_data = Some(bytes);
            }
            "metadata" => {
                // Parse upload metadata
                let mut metadata_bytes = Vec::new();
                while let Some(chunk) = field.try_next().await? {
                    metadata_bytes.extend_from_slice(&chunk);
                }

                if let Ok(metadata_str) = String::from_utf8(metadata_bytes) {
                    match serde_json::from_str::<UploadFileRequest>(&metadata_str) {
                        Ok(req) => {
                            if let Err(validation_errors) = req.validate() {
                                return Ok(HttpResponse::BadRequest().json(
                                    ApiResponse::<()>::error(format!(
                                        "Validation failed: {:?}",
                                        validation_errors
                                    )),
                                ));
                            }
                            upload_request = Some(req);
                        }
                        Err(e) => {
                            warn!("Failed to parse upload metadata: {}", e);
                        }
                    }
                }
            }
            _ => {
                // Skip unknown fields
                while let Some(_chunk) = field.try_next().await? {}
            }
        }
    }

    // Validate required fields
    let file_data =
        file_data.ok_or_else(|| actix_web::error::ErrorBadRequest("No file provided"))?;

    let filename =
        filename.ok_or_else(|| actix_web::error::ErrorBadRequest("Filename not provided"))?;

    let content_type = content_type.unwrap_or_else(|| {
        mime_guess::from_path(&filename)
            .first_or_octet_stream()
            .to_string()
    });

    let _upload_request = upload_request
        .ok_or_else(|| actix_web::error::ErrorBadRequest("Upload metadata not provided"))?;

    // Create storage service
    let storage_config = StorageConfig::default();
    let storage_service = match FileStorageService::new(storage_config).await {
        Ok(service) => service,
        Err(e) => {
            error!("Failed to initialize storage service: {}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Storage service initialization failed".to_string(),
                )),
            );
        }
    };

    // Validate file type
    if let Err(e) = storage_service.validate_file_type(&filename, &content_type) {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "File validation failed: {}",
                e
            ))),
        );
    }

    // Store file
    let user_info = user_session.into_inner();
    match storage_service
        .store_file(file_data.clone(), &filename, &content_type, user_info.user_id.clone())
        .await
    {
        Ok(file_metadata) => {
            // Initialize document parser service
            let tika_config = TikaConfig::default();
            let parser_service = match DocumentParserService::new(tika_config) {
                Ok(service) => service,
                Err(e) => {
                    warn!("Failed to initialize document parser: {}", e);
                    // Continue without parsing - file is still stored
                    let response = FileUploadResponse {
                        file_id: file_metadata.file_id,
                        filename: file_metadata.original_filename,
                        file_size: file_metadata.file_size,
                        content_type: file_metadata.mime_type,
                        upload_url: format!("/api/v1/files/{}/download", file_metadata.file_id),
                        processing_status: "failed".to_string(), // Parsing failed
                        task_ids: Vec::new(),
                        estimated_processing_time: None,
                        message: format!(
                            "File uploaded but parsing failed at {}",
                            file_metadata.uploaded_at.format("%Y-%m-%d %H:%M:%S")
                        ),
                    };

                    info!(
                        "File uploaded successfully but parsing failed: {} ({} bytes) by user {}",
                        filename, file_metadata.file_size, user_info.user_id
                    );

                    return Ok(HttpResponse::Created().json(ApiResponse::success(response)));
                }
            };

            // Attempt document parsing
            let processing_status = if parser_service.is_supported_mime_type(&content_type) {
                match parser_service
                    .parse_document(file_data, &filename, &content_type)
                    .await
                {
                    Ok(parsed_doc) => {
                        info!(
                            "Document parsed successfully: {} ({}ms processing time)",
                            filename, parsed_doc.processing_time_ms
                        );

                        if !parsed_doc.parsing_errors.is_empty() {
                            warn!(
                                "Parsing completed with errors: {:?}",
                                parsed_doc.parsing_errors
                            );
                        }

                        // TODO: Store parsed document in database
                        // This would involve creating a document record with:
                        // - parsed_doc.metadata
                        // - parsed_doc.content
                        // - linking to knowledge base from upload_request

                        ProcessingStatus::Completed
                    }
                    Err(e) => {
                        error!("Document parsing failed: {}", e);
                        ProcessingStatus::Failed
                    }
                }
            } else {
                warn!("Document type not supported for parsing: {}", content_type);
                ProcessingStatus::Skipped
            };

            let response = FileUploadResponse {
                file_id: file_metadata.file_id,
                filename: file_metadata.original_filename,
                file_size: file_metadata.file_size,
                content_type: file_metadata.mime_type,
                upload_url: format!("/api/v1/files/{}/download", file_metadata.file_id),
                processing_status: format!("{:?}", processing_status).to_lowercase(),
                task_ids: Vec::new(),
                estimated_processing_time: None,
                message: format!(
                    "File uploaded successfully at {}",
                    file_metadata.uploaded_at.format("%Y-%m-%d %H:%M:%S")
                ),
            };

            info!(
                "File uploaded and processed: {} ({} bytes) by user {}",
                filename, file_metadata.file_size, user_info.user_id
            );

            Ok(HttpResponse::Created().json(ApiResponse::success(response)))
        }
        Err(e) => {
            error!("Failed to store file: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("File storage failed".to_string())))
        }
    }
}

pub async fn upload_batch_files(
    mut payload: Multipart,
    _db: web::Data<DatabaseConnection>,
    user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    let mut files: Vec<(String, String, Vec<u8>)> = Vec::new(); // (filename, content_type, data)
    let mut batch_request: Option<crate::types::BatchUploadRequest> = None;
    
    // Get user info early for reuse
    let user_info = user_session.into_inner();

    // Process multipart form data
    while let Some(mut field) = payload.try_next().await? {
        let field_name = field.name().unwrap_or("").to_string();

        if field_name.starts_with("files[") {
            // Handle file fields
            let mut filename = String::new();
            if let Some(content_disposition) = field.content_disposition() {
                filename = content_disposition
                    .get_filename()
                    .unwrap_or("unknown")
                    .to_string();
            }

            let content_type = field
                .content_type()
                .map(|ct| ct.to_string())
                .unwrap_or_else(|| {
                    mime_guess::from_path(&filename)
                        .first_or_octet_stream()
                        .to_string()
                });

            // Read file data
            let mut bytes = Vec::new();
            while let Some(chunk) = field.try_next().await? {
                bytes.extend_from_slice(&chunk);

                if bytes.len() > MAX_FILE_SIZE {
                    return Ok(
                        HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                            "File {} exceeds maximum allowed size",
                            filename
                        ))),
                    );
                }
            }

            if files.len() >= MAX_FILES_PER_BATCH {
                return Ok(
                    HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                        "Batch upload limited to {} files",
                        MAX_FILES_PER_BATCH
                    ))),
                );
            }

            files.push((filename, content_type, bytes));
        } else if field_name == "metadata" {
            // Parse batch metadata
            let mut metadata_bytes = Vec::new();
            while let Some(chunk) = field.try_next().await? {
                metadata_bytes.extend_from_slice(&chunk);
            }

            if let Ok(metadata_str) = String::from_utf8(metadata_bytes) {
                if let Ok(req) = serde_json::from_str(&metadata_str) {
                    batch_request = Some(req);
                }
            }
        } else {
            // Skip unknown fields
            while let Some(_chunk) = field.try_next().await? {}
        }
    }

    if files.is_empty() {
        return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
            "No files provided for batch upload".to_string(),
        )));
    }

    let _batch_request = batch_request
        .ok_or_else(|| actix_web::error::ErrorBadRequest("Batch metadata not provided"))?;

    // Create storage service
    let storage_config = StorageConfig::default();
    let storage_service = match FileStorageService::new(storage_config).await {
        Ok(service) => service,
        Err(e) => {
            error!("Failed to initialize storage service: {}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Storage service initialization failed".to_string(),
                )),
            );
        }
    };

    let batch_id = Uuid::new_v4();
    let mut successful_uploads = Vec::new();
    let mut failed_uploads = Vec::new();

    // Initialize document parser service once for the batch
    let parser_service = match DocumentParserService::new(TikaConfig::default()) {
        Ok(service) => Some(service),
        Err(e) => {
            warn!("Failed to initialize document parser for batch: {}", e);
            None
        }
    };

    // Process each file
    for (filename, content_type, file_data) in files {
        // Validate file type
        if let Err(e) = storage_service.validate_file_type(&filename, &content_type) {
            failed_uploads.push(crate::types::UploadError {
                filename: filename.clone(),
                error_message: format!("File validation failed: {}", e),
                error_code: "INVALID_FILE_TYPE".to_string(),
            });
            continue;
        }

        // Store file
        match storage_service
            .store_file(file_data.clone(), &filename, &content_type, user_info.user_id.clone())
            .await
        {
            Ok(file_metadata) => {
                // Attempt document parsing if parser is available
                let processing_status = if let Some(ref parser) = parser_service {
                    if parser.is_supported_mime_type(&content_type) {
                        match parser
                            .parse_document(file_data, &filename, &content_type)
                            .await
                        {
                            Ok(parsed_doc) => {
                                info!(
                                    "Batch file parsed successfully: {} ({}ms)",
                                    filename, parsed_doc.processing_time_ms
                                );
                                ProcessingStatus::Completed
                            }
                            Err(e) => {
                                warn!("Batch file parsing failed for {}: {}", filename, e);
                                ProcessingStatus::Failed
                            }
                        }
                    } else {
                        ProcessingStatus::Skipped
                    }
                } else {
                    ProcessingStatus::Failed
                };

                let response = FileUploadResponse {
                    file_id: file_metadata.file_id,
                    filename: file_metadata.original_filename,
                    file_size: file_metadata.file_size,
                    content_type: file_metadata.mime_type,
                    upload_url: format!("/api/v1/files/{}/download", file_metadata.file_id),
                    processing_status: format!("{:?}", processing_status).to_lowercase(),
                    task_ids: Vec::new(),
                    estimated_processing_time: None,
                    message: format!(
                        "File uploaded successfully at {}",
                        file_metadata.uploaded_at.format("%Y-%m-%d %H:%M:%S")
                    ),
                };
                successful_uploads.push(response);
            }
            Err(e) => {
                failed_uploads.push(crate::types::UploadError {
                    filename: filename.clone(),
                    error_message: format!("Storage failed: {}", e),
                    error_code: "STORAGE_ERROR".to_string(),
                });
            }
        }
    }

    let response = BatchUploadResponse {
        batch_id,
        total_files: successful_uploads.len() + failed_uploads.len(),
        successful_uploads,
        failed_uploads,
        processing_queue_size: app_state.processing_task_service
            .get_statistics()
            .await
            .map(|stats| stats.queued_tasks as u32)
            .unwrap_or(0),
    };

    info!(
        "Batch upload completed: {} successful, {} failed by user {}",
        response.successful_uploads.len(),
        response.failed_uploads.len(),
        user_info.user_id
    );

    Ok(HttpResponse::Created().json(ApiResponse::success(response)))
}

pub async fn get_file_info(
    path: web::Path<Uuid>,
    _db: web::Data<DatabaseConnection>,
    _user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    let _file_id = path.into_inner();

    // TODO: Implement file info retrieval from database
    // For now, return a placeholder response

    Ok(HttpResponse::Ok().json(ApiResponse::<()>::error(
        "File info retrieval not yet implemented".to_string(),
    )))
}

pub async fn download_file(
    path: web::Path<Uuid>,
    query: web::Query<HashMap<String, String>>,
    _db: web::Data<DatabaseConnection>,
    _user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    let _file_id = path.into_inner();
    let _inline = query
        .get("inline")
        .and_then(|v| v.parse::<bool>().ok())
        .unwrap_or(false);

    // TODO: Implement file download
    // 1. Get file metadata from database
    // 2. Check permissions
    // 3. Retrieve file from storage
    // 4. Stream file with appropriate headers

    Ok(HttpResponse::Ok().json(ApiResponse::<()>::error(
        "File download not yet implemented".to_string(),
    )))
}

pub async fn delete_file(
    path: web::Path<Uuid>,
    _db: web::Data<DatabaseConnection>,
    _user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    let _file_id = path.into_inner();

    // TODO: Implement file deletion
    // 1. Check permissions
    // 2. Delete from database
    // 3. Delete from storage
    // 4. Update statistics

    Ok(HttpResponse::Ok().json(ApiResponse::<()>::error(
        "File deletion not yet implemented".to_string(),
    )))
}

pub async fn search_files(
    query: web::Query<FileSearchRequest>,
    _db: web::Data<DatabaseConnection>,
    _user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    // Validate query parameters
    if let Err(validation_errors) = query.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    // TODO: Implement file search
    // 1. Build search query with filters
    // 2. Execute search in database
    // 3. Apply permissions filtering
    // 4. Return paginated results

    Ok(HttpResponse::Ok().json(ApiResponse::<()>::error(
        "File search not yet implemented".to_string(),
    )))
}

pub async fn get_storage_stats(
    _db: web::Data<DatabaseConnection>,
    user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    // 实现管理员权限检查
    let user_info = user_session.into_inner();
    
    // 简化的权限检查：基于用户名或用户ID判断是否为管理员
    // 在实际应用中，这应该从数据库中检查用户角色
    let is_admin = user_info.username == "admin" || 
                   user_info.user_id.contains("admin") ||
                   user_info.username.starts_with("admin_");
    
    if !is_admin {
        return Ok(HttpResponse::Forbidden().json(
            ApiResponse::<()>::error(
                "permission_denied".into(),
                "Admin permissions required to access storage statistics".to_string()
            )
        ));
    }

    let storage_config = StorageConfig::default();
    match FileStorageService::new(storage_config).await {
        Ok(storage_service) => {
            match storage_service.get_storage_usage().await {
                Ok(usage) => {
                    let stats = StorageStatsResponse {
                        total_size: usage.total_size,
                        file_count: usage.file_count,
                        average_file_size: if usage.file_count > 0 {
                            usage.total_size as f64 / usage.file_count as f64
                        } else {
                            0.0
                        },
                        storage_usage_by_type: usage
                            .extension_counts
                            .into_iter()
                            .map(|(ext, count)| {
                                let percentage = if usage.file_count > 0 {
                                    (count as f64 / usage.file_count as f64) * 100.0
                                } else {
                                    0.0
                                };
                                (
                                    ext,
                                    crate::types::StorageTypeUsage {
                                        file_count: count,
                                        total_size: 0, // TODO: Calculate actual size per type
                                        percentage,
                                    },
                                )
                            })
                            .collect(),
                        storage_usage_by_month: HashMap::new(), /* TODO: Implement monthly
                                                                 * statistics */
                        largest_files: Vec::new(), // TODO: Get largest files
                        most_recent_files: Vec::new(), // TODO: Get most recent files
                        storage_config: crate::types::StorageConfigResponse {
                            max_file_size: storage_service.get_storage_stats().max_file_size,
                            allowed_extensions: storage_service
                                .get_storage_stats()
                                .allowed_extensions,
                            allowed_mime_types: storage_service
                                .get_storage_stats()
                                .allowed_mime_types,
                            storage_path: storage_service
                                .get_storage_stats()
                                .base_path
                                .to_string_lossy()
                                .to_string(),
                        },
                    };

                    Ok(HttpResponse::Ok().json(ApiResponse::success(stats)))
                }
                Err(e) => {
                    error!("Failed to get storage usage: {}", e);
                    Ok(
                        HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                            "Failed to retrieve storage statistics".to_string(),
                        )),
                    )
                }
            }
        }
        Err(e) => {
            error!("Failed to initialize storage service: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Storage service initialization failed".to_string(),
                )),
            )
        }
    }
}

pub async fn get_processing_status(
    path: web::Path<Uuid>,
    _db: web::Data<DatabaseConnection>,
    _user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    let _file_id = path.into_inner();

    // TODO: Implement processing status retrieval
    // 1. Get processing status from database/queue
    // 2. Return detailed status information

    Ok(HttpResponse::Ok().json(ApiResponse::<()>::error(
        "Processing status retrieval not yet implemented".to_string(),
    )))
}

pub async fn bulk_file_operation(
    request: web::Json<BulkFileOperationRequest>,
    _db: web::Data<DatabaseConnection>,
    _user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    // Validate request
    if let Err(validation_errors) = request.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                validation_errors
            ))),
        );
    }

    if request.file_ids.is_empty() {
        return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
            "No files specified for bulk operation".to_string(),
        )));
    }

    if request.file_ids.len() > 100 {
        return Ok(HttpResponse::BadRequest().json(ApiResponse::<()>::error(
            "Bulk operations limited to 100 files at a time".to_string(),
        )));
    }

    // TODO: Implement bulk operations
    // 1. Check permissions for all files
    // 2. Execute operation based on type
    // 3. Track success/failure for each file
    // 4. Update database records

    Ok(HttpResponse::Ok().json(ApiResponse::<()>::error(
        "Bulk file operations not yet implemented".to_string(),
    )))
}
