use actix_web::{HttpResponse, Result, web};
use chrono::Utc;
use serde::{Deserialize, Serialize};
use tracing::{debug, info, warn};
use uuid::Uuid;
use validator::Validate;

use crate::{
    app_state::AppState,
    types::common::{ApiResponse, PaginationQuery},
};
use wisdom_vault_core::services::{
    ConversationContext as RAGConversationContext, LLMParams, MessageRole, RAGRequest,
    RetrievalParams,
};
use wisdom_vault_database::models::{
    Conversation, ConversationContext, ConversationSettings, ConversationStatistics,
    ConversationStatus, ConversationType, MessageStatus,
};

// ============================================================================
// 请求/响应数据结构
// ============================================================================

/// 创建对话请求
#[derive(Debug, Deserialize, Validate)]
pub struct CreateConversationRequest {
    #[validate(length(min = 1, max = 200, message = "标题长度必须在1-200字符之间"))]
    pub title: Option<String>,
    pub knowledge_base_id: Option<Uuid>,
    pub conversation_type: Option<ConversationType>,
    pub tags: Option<Vec<String>>,
}

/// 对话响应
#[derive(Debug, Serialize)]
pub struct ConversationResponse {
    pub id: Uuid,
    pub title: String,
    pub status: ConversationStatus,
    pub knowledge_base_id: Option<Uuid>,
    pub summary: Option<String>,
    pub message_count: i32,
    pub created_at: chrono::DateTime<Utc>,
    pub updated_at: chrono::DateTime<Utc>,
    pub last_message_at: Option<chrono::DateTime<Utc>>,
    pub statistics: ConversationStatistics,
}

/// 聊天请求
#[derive(Debug, Deserialize, Validate)]
pub struct ChatRequest {
    pub conversation_id: Uuid,
    #[validate(length(min = 1, max = 10000, message = "消息内容长度必须在1-10000字符之间"))]
    pub message: String,
    pub knowledge_base_id: Option<Uuid>,
    pub retrieval_params: Option<ChatRetrievalParams>,
    pub llm_params: Option<ChatLLMParams>,
    pub include_sources: Option<bool>,
}

/// 聊天检索参数
#[derive(Debug, Deserialize)]
pub struct ChatRetrievalParams {
    pub count: Option<u32>,
    pub relevance_threshold: Option<f64>,
    pub keyword_weight: Option<f64>,
    pub vector_weight: Option<f64>,
}

/// 聊天 LLM 参数
#[derive(Debug, Deserialize)]
pub struct ChatLLMParams {
    pub model: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
}

/// 聊天响应
#[derive(Debug, Serialize)]
pub struct ChatResponse {
    pub message_id: Uuid,
    pub conversation_id: Uuid,
    pub answer: String,
    pub confidence: f64,
    pub sources: Vec<ChatSourceResponse>,
    pub model: String,
    pub token_usage: Option<TokenUsageResponse>,
    pub response_time_ms: u64,
    pub created_at: chrono::DateTime<Utc>,
}

/// 聊天来源响应
#[derive(Debug, Serialize)]
pub struct ChatSourceResponse {
    pub document_id: String,
    pub title: String,
    pub content_snippet: String,
    pub relevance_score: f64,
    pub source_type: String,
}

/// Token 使用响应
#[derive(Debug, Serialize)]
pub struct TokenUsageResponse {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// 消息响应
#[derive(Debug, Serialize)]
pub struct MessageResponse {
    pub id: Uuid,
    pub conversation_id: Uuid,
    pub role: MessageRole,
    pub content: String,
    pub status: MessageStatus,
    pub sequence_number: u32,
    pub created_at: chrono::DateTime<Utc>,
    pub updated_at: chrono::DateTime<Utc>,
    pub metadata: Option<MessageMetadataResponse>,
}

/// 消息元数据响应
#[derive(Debug, Serialize)]
pub struct MessageMetadataResponse {
    pub token_usage: Option<TokenUsageResponse>,
    pub response_time_ms: Option<u64>,
    pub confidence: Option<f64>,
    pub sources: Vec<ChatSourceResponse>,
    pub model: Option<String>,
}

/// 对话查询参数
#[derive(Debug, Deserialize)]
pub struct ConversationQueryParams {
    pub knowledge_base_id: Option<Uuid>,
    pub status: Option<ConversationStatus>,
    pub conversation_type: Option<ConversationType>,
    pub limit: Option<u32>,
    pub offset: Option<u32>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

/// 搜索对话请求
#[derive(Debug, Deserialize, Validate)]
pub struct SearchConversationsRequest {
    #[validate(length(min = 1, max = 100, message = "搜索查询长度必须在1-100字符之间"))]
    pub query: String,
    pub limit: Option<u32>,
}

/// 更新消息状态请求
#[derive(Debug, Deserialize)]
pub struct UpdateMessageStatusRequest {
    pub status: MessageStatus,
}

/// 用户反馈请求
#[derive(Debug, Deserialize, Validate)]
pub struct UserFeedbackRequest {
    pub rating: Option<u8>,
    pub helpful: Option<bool>,
    #[validate(length(max = 500, message = "反馈内容不能超过500字符"))]
    pub comment: Option<String>,
}

/// 对话统计响应
#[derive(Debug, Serialize)]
pub struct ConversationStatsResponse {
    pub total_conversations: u32,
    pub active_conversations: u32,
    pub total_messages: u32,
    pub avg_messages_per_conversation: f64,
    pub conversations_last_24h: u32,
    pub conversations_last_7d: u32,
}

// ============================================================================
// API 处理器函数
// ============================================================================

/// 创建新对话
pub async fn create_conversation(
    _app_state: web::Data<AppState>,
    req: web::Json<CreateConversationRequest>,
    user_id: web::ReqData<Uuid>, // 从认证中间件获取
) -> Result<HttpResponse, actix_web::Error> {
    let user_id = user_id.into_inner();
    info!("Creating new conversation for user: {}", user_id);

    if let Err(e) = req.validate() {
        warn!("Invalid conversation creation request: {:?}", e);
        return Ok(HttpResponse::BadRequest().json(
            ApiResponse::<()>::validation_error(serde_json::to_value(e).unwrap())
        ));
    }

    let conversation = Conversation {
        id: Uuid::new_v4(),
        user_id,
        knowledge_base_id: req.knowledge_base_id,
        title: req.title.clone().unwrap_or_else(|| "新对话".to_string()),
        summary: None,
        status: ConversationStatus::Active,
        context: ConversationContext {
            session_id: Uuid::new_v4().to_string(),
            previous_queries: Vec::new(),
            topic_focus: None,
            mentioned_documents: Vec::new(),
            context_window_size: 8000,
        },
        settings: ConversationSettings::default(),
        statistics: ConversationStatistics::default(),
        created_at: Utc::now(),
        updated_at: Utc::now(),
        last_message_at: None,
    };

    // 这里需要创建 ConversationService 实例
    // 暂时返回成功响应，实际集成需要完善
    let response = ConversationResponse {
        id: conversation.id,
        title: conversation.title.clone(),
        status: conversation.status.clone(),
        knowledge_base_id: conversation.knowledge_base_id,
        summary: conversation.summary.clone(),
        message_count: conversation.statistics.message_count,
        created_at: conversation.created_at,
        updated_at: conversation.updated_at,
        last_message_at: conversation.last_message_at,
        statistics: conversation.statistics.clone(),
    };

    debug!("Created conversation: {}", conversation.id);

    Ok(HttpResponse::Created().json(ApiResponse::success_with_message(
        response,
        "对话创建成功".to_string()
    )))
}

/// 发送聊天消息
pub async fn send_chat_message(
    _app_state: web::Data<AppState>,
    req: web::Json<ChatRequest>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let user_id = user_id.into_inner();
    info!(
        "Processing chat message for conversation: {} from user: {}",
        req.conversation_id, user_id
    );

    if let Err(e) = req.validate() {
        warn!("Invalid chat request: {:?}", e);
        return Ok(HttpResponse::BadRequest().json(
            ApiResponse::<()>::validation_error(serde_json::to_value(e).unwrap())
        ));
    }

    // 构建 RAG 请求
    let _rag_request = RAGRequest {
        question: req.message.clone(),
        knowledge_base_id: req.knowledge_base_id.unwrap_or_else(|| Uuid::new_v4()), /* 默认创建一个新的UUID */
        conversation_context: Some(RAGConversationContext {
            conversation_id: req.conversation_id.to_string(),
            history: Vec::new(), // 这里需要从数据库获取历史消息
            user_preferences: None,
        }),
        retrieval_params: req.retrieval_params.as_ref().map(|p| RetrievalParams {
            count: p.count,
            relevance_threshold: p.relevance_threshold,
            keyword_weight: p.keyword_weight,
            vector_weight: p.vector_weight,
        }),
        llm_params: req.llm_params.as_ref().map(|p| LLMParams {
            model: p.model.clone(),
            max_tokens: p.max_tokens,
            temperature: p.temperature,
        }),
    };

    // 模拟 RAG 响应 (实际需要集成 RAG 服务)
    let message_id = Uuid::new_v4();
    let mock_response = ChatResponse {
        message_id,
        conversation_id: req.conversation_id,
        answer: "这是一个模拟的回答。实际需要集成 RAG 服务来生成真实的答案。".to_string(),
        confidence: 0.85,
        sources: vec![ChatSourceResponse {
            document_id: "doc-123".to_string(),
            title: "示例文档".to_string(),
            content_snippet: "这是相关的文档片段...".to_string(),
            relevance_score: 0.92,
            source_type: "document".to_string(),
        }],
        model: "gpt-4o-mini".to_string(),
        token_usage: Some(TokenUsageResponse {
            prompt_tokens: 150,
            completion_tokens: 50,
            total_tokens: 200,
        }),
        response_time_ms: 1200,
        created_at: Utc::now(),
    };

    debug!(
        "Generated chat response for message: {} in conversation: {}",
        message_id, req.conversation_id
    );

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        mock_response,
        "消息发送成功".to_string()
    )))
}

/// 获取对话列表
pub async fn list_conversations(
    _app_state: web::Data<AppState>,
    query: web::Query<ConversationQueryParams>,
    _pagination: web::Query<PaginationQuery>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let user_id = user_id.into_inner();
    info!("Listing conversations for user: {}", user_id);

    // 模拟对话列表 (实际需要从数据库查询)
    let conversations = vec![
        ConversationResponse {
            id: Uuid::new_v4(),
            title: "示例对话 1".to_string(),
            status: ConversationStatus::Active,
            knowledge_base_id: None,
            summary: None,
            message_count: 5,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            last_message_at: Some(Utc::now()),
            statistics: ConversationStatistics::default(),
        },
        ConversationResponse {
            id: Uuid::new_v4(),
            title: "示例对话 2".to_string(),
            status: ConversationStatus::Active,
            knowledge_base_id: query.knowledge_base_id,
            summary: Some("多轮对话示例".to_string()),
            message_count: 12,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            last_message_at: Some(Utc::now()),
            statistics: ConversationStatistics::default(),
        },
    ];

    debug!("Retrieved {} conversations", conversations.len());

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        conversations,
        "获取对话列表成功".to_string()
    )))
}

/// 获取对话详情
pub async fn get_conversation(
    _app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let conversation_id = path.into_inner();
    let user_id = user_id.into_inner();
    info!(
        "Getting conversation: {} for user: {}",
        conversation_id, user_id
    );

    // 模拟获取对话详情 (实际需要从数据库查询)
    let conversation = ConversationResponse {
        id: conversation_id,
        title: "示例对话".to_string(),
        status: ConversationStatus::Active,
        knowledge_base_id: None,
        summary: None,
        message_count: 3,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        last_message_at: Some(Utc::now()),
        statistics: ConversationStatistics::default(),
    };

    debug!("Retrieved conversation: {}", conversation_id);

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        conversation,
        "获取对话详情成功".to_string()
    )))
}

/// 获取对话消息列表
pub async fn get_conversation_messages(
    _app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    _pagination: web::Query<PaginationQuery>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let conversation_id = path.into_inner();
    let user_id = user_id.into_inner();
    info!(
        "Getting messages for conversation: {} by user: {}",
        conversation_id, user_id
    );

    // 模拟消息列表 (实际需要从数据库查询)
    let messages = vec![
        MessageResponse {
            id: Uuid::new_v4(),
            conversation_id,
            role: MessageRole::User,
            content: "你好，这是一个测试消息".to_string(),
            status: MessageStatus::Sent,
            sequence_number: 1,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            metadata: None,
        },
        MessageResponse {
            id: Uuid::new_v4(),
            conversation_id,
            role: MessageRole::Assistant,
            content: "你好！我是智能助手，很高兴为您服务。".to_string(),
            status: MessageStatus::Sent,
            sequence_number: 2,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            metadata: Some(MessageMetadataResponse {
                token_usage: Some(TokenUsageResponse {
                    prompt_tokens: 20,
                    completion_tokens: 15,
                    total_tokens: 35,
                }),
                response_time_ms: Some(800),
                confidence: Some(0.95),
                sources: vec![],
                model: Some("gpt-4o-mini".to_string()),
            }),
        },
    ];

    debug!(
        "Retrieved {} messages for conversation: {}",
        messages.len(),
        conversation_id
    );

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        messages,
        "获取消息列表成功".to_string()
    )))
}

/// 删除对话
pub async fn delete_conversation(
    _app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let conversation_id = path.into_inner();
    let user_id = user_id.into_inner();
    info!(
        "Deleting conversation: {} by user: {}",
        conversation_id, user_id
    );

    // 实际需要验证用户权限并删除对话
    debug!("Deleted conversation: {}", conversation_id);

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        (),
        "对话删除成功".to_string()
    )))
}

/// 更新消息状态
pub async fn update_message_status(
    _app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    req: web::Json<UpdateMessageStatusRequest>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let message_id = path.into_inner();
    let user_id = user_id.into_inner();
    info!(
        "Updating message status: {} to {:?} by user: {}",
        message_id, req.status, user_id
    );

    // 实际需要验证用户权限并更新消息状态
    debug!("Updated message status: {}", message_id);

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        (),
        "消息状态更新成功".to_string()
    )))
}

/// 搜索对话
pub async fn search_conversations(
    _app_state: web::Data<AppState>,
    req: web::Json<SearchConversationsRequest>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let user_id = user_id.into_inner();
    info!(
        "Searching conversations with query: '{}' for user: {}",
        req.query, user_id
    );

    if let Err(e) = req.validate() {
        warn!("Invalid search request: {:?}", e);
        return Ok(HttpResponse::BadRequest().json(
            ApiResponse::<()>::validation_error(serde_json::to_value(e).unwrap())
        ));
    }

    // 模拟搜索结果 (实际需要从数据库搜索)
    let conversations = vec![ConversationResponse {
        id: Uuid::new_v4(),
        title: format!("包含 '{}' 的对话", req.query),
        status: ConversationStatus::Active,
        knowledge_base_id: None,
        summary: None,
        message_count: 8,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        last_message_at: Some(Utc::now()),
        statistics: ConversationStatistics::default(),
    }];

    debug!(
        "Found {} conversations matching query: '{}'",
        conversations.len(),
        req.query
    );

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        conversations,
        "搜索对话成功".to_string()
    )))
}

/// 获取对话统计
pub async fn get_conversation_stats(
    _app_state: web::Data<AppState>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let user_id = user_id.into_inner();
    info!("Getting conversation stats for user: {}", user_id);

    // 模拟统计数据 (实际需要从数据库查询)
    let stats = ConversationStatsResponse {
        total_conversations: 15,
        active_conversations: 8,
        total_messages: 120,
        avg_messages_per_conversation: 8.0,
        conversations_last_24h: 3,
        conversations_last_7d: 12,
    };

    debug!("Retrieved conversation stats for user: {}", user_id);

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        stats,
        "获取对话统计成功".to_string()
    )))
}

/// 提交用户反馈
pub async fn submit_user_feedback(
    _app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    req: web::Json<UserFeedbackRequest>,
    user_id: web::ReqData<Uuid>,
) -> Result<HttpResponse, actix_web::Error> {
    let message_id = path.into_inner();
    let user_id = user_id.into_inner();
    info!(
        "Submitting feedback for message: {} by user: {}",
        message_id, user_id
    );

    if let Err(e) = req.validate() {
        warn!("Invalid feedback request: {:?}", e);
        return Ok(HttpResponse::BadRequest().json(
            ApiResponse::<()>::validation_error(serde_json::to_value(e).unwrap())
        ));
    }

    // 实际需要保存用户反馈到数据库
    debug!("Saved feedback for message: {}", message_id);

    Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
        (),
        "反馈提交成功".to_string()
    )))
}

// ============================================================================
// 路由配置
// ============================================================================

pub fn configure_chat_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/api/v1/chat")
            .route("/conversations", web::post().to(create_conversation))
            .route("/conversations", web::get().to(list_conversations))
            .route(
                "/conversations/search",
                web::post().to(search_conversations),
            )
            .route(
                "/conversations/stats",
                web::get().to(get_conversation_stats),
            )
            .route("/conversations/{id}", web::get().to(get_conversation))
            .route("/conversations/{id}", web::delete().to(delete_conversation))
            .route(
                "/conversations/{id}/messages",
                web::get().to(get_conversation_messages),
            )
            .route("/messages", web::post().to(send_chat_message))
            .route(
                "/messages/{id}/status",
                web::put().to(update_message_status),
            )
            .route(
                "/messages/{id}/feedback",
                web::post().to(submit_user_feedback),
            ),
    );
}
