use actix_web::{HttpResponse, Result as ActixResult, web};
use std::sync::Arc;
use wisdom_vault_core::services::DocumentVersionService;
use wisdom_vault_database::{
    connection::DatabaseConnection, repositories::SurrealDocumentRepository,
};

use crate::types::{ApiResponse, version::*};

/// 创建文档版本
pub async fn create_document_version(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<String>, // document_id
    request: web::Json<CreateVersionRequest>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();
    let request = request.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentVersionService::new(document_repo);

    match service
        .create_version(document_id, request.changes, claims.user_id)
        .await
    {
        Ok(version) => {
            let response = DocumentVersionResponse::from(version);
            Ok(HttpResponse::Created().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to create document version: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::bad_request(format!(
                    "创建文档版本失败: {}",
                    e
                ))),
            )
        }
    }
}

/// 获取文档的所有版本
pub async fn get_document_versions(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentVersionService::new(document_repo);

    match service.get_document_versions(document_id).await {
        Ok(versions) => {
            let responses: Vec<DocumentVersionResponse> = versions
                .into_iter()
                .map(DocumentVersionResponse::from)
                .collect();

            let total_versions = responses.len();

            let response = DocumentVersionsResponse {
                document_id,
                versions: responses,
                total_versions,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get document versions: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::internal_error(Some(
                    "服务器内部错误".to_string(),
                ))),
            )
        }
    }
}

/// 获取特定版本的文档
pub async fn get_document_version(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<(Uuid, i32)>, // (document_id, version_number)
) -> ActixResult<HttpResponse> {
    let (document_id, version_number) = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentVersionService::new(document_repo);

    match service
        .get_document_version(document_id, version_number)
        .await
    {
        Ok(Some(version)) => {
            let response = DocumentVersionResponse::from(version);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Ok(None) => Ok(HttpResponse::NotFound().json(ApiResponse::<()>::not_found("文档版本"))),
        Err(e) => {
            tracing::error!("Failed to get document version: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::internal_error(Some(
                    "服务器内部错误".to_string(),
                ))),
            )
        }
    }
}

/// 恢复文档到指定版本
pub async fn restore_document_to_version(
    db: web::Data<DatabaseConnection>,
    claims: web::ReqData<Claims>,
    path: web::Path<(Uuid, i32)>, // (document_id, version_number)
) -> ActixResult<HttpResponse> {
    let (document_id, version_number) = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentVersionService::new(document_repo);

    match service
        .restore_to_version(document_id, version_number, claims.user_id)
        .await
    {
        Ok(document) => {
            use crate::types::document::DocumentResponse;
            let response = DocumentResponse::from(document);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to restore document to version: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::bad_request(format!(
                    "恢复文档版本失败: {}",
                    e
                ))),
            )
        }
    }
}

/// 比较两个版本的差异
pub async fn compare_document_versions(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
    query: web::Query<VersionCompareQuery>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();
    let query = query.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentVersionService::new(document_repo);

    match service
        .compare_versions(document_id, query.version1, query.version2)
        .await
    {
        Ok(diff) => {
            let response = VersionDiffResponse::from(diff);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to compare document versions: {}", e);
            Ok(
                HttpResponse::BadRequest().json(ApiResponse::<()>::bad_request(format!(
                    "比较文档版本失败: {}",
                    e
                ))),
            )
        }
    }
}

/// 获取文档版本统计
pub async fn get_document_version_statistics(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentVersionService::new(document_repo);

    match service.get_version_statistics(document_id).await {
        Ok(stats) => {
            let response = VersionStatisticsResponse::from(stats);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get document version statistics: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::internal_error(Some(
                    "服务器内部错误".to_string(),
                ))),
            )
        }
    }
}

/// 清理文档的旧版本
pub async fn cleanup_document_versions(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
    request: web::Json<CleanupVersionsRequest>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();
    let request = request.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentVersionService::new(document_repo);

    match service
        .cleanup_old_versions(document_id, request.keep_latest)
        .await
    {
        Ok(deleted_count) => {
            let response = CleanupVersionsResponse {
                document_id,
                deleted_count,
                kept_versions: request.keep_latest,
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to cleanup document versions: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::internal_error(Some(
                    "服务器内部错误".to_string(),
                ))),
            )
        }
    }
}

/// 获取版本存储信息
pub async fn get_version_storage_info(
    db: web::Data<DatabaseConnection>,
    _claims: web::ReqData<Claims>,
    path: web::Path<Uuid>, // document_id
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    let document_repo = Arc::new(SurrealDocumentRepository::new(db.get_ref().clone()));
    let service = DocumentVersionService::new(document_repo);

    match service.estimate_version_storage_size(document_id).await {
        Ok(storage_info) => {
            let response = VersionStorageInfoResponse::from(storage_info);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get version storage info: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::internal_error(Some(
                    "服务器内部错误".to_string(),
                ))),
            )
        }
    }
}
