use actix_web::{HttpResponse, Result as ActixResult, web};
use std::collections::HashMap;

use wisdom_vault_core::services::{
    HybridSearchService, QueryContext, QueryIntent, QueryType,
    hybrid_search::HybridSearchRequest as CoreHybridSearchRequest,
};

use crate::{
    app_state::AppState,
    types::{
        common::ApiResponse,
        search::{
            HybridSearchApiResponse, HybridSearchConfigRequest,
            HybridSearchPerformanceStatsResponse, HybridSearchRequest,
            HybridSearchStatisticsResponse, ResultFusionStrategyRequest,
        },
    },
};
use wisdom_vault_auth::Claims;

/// 执行混合搜索
pub async fn hybrid_search(
    app_state: web::Data<AppState>,
    req: web::Json<HybridSearchRequest>,
    claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    tracing::info!(
        "混合搜索请求: query='{}', user_id={}, fusion_strategy={:?}",
        req.query_text,
        claims.user_id,
        req.fusion_strategy
    );

    // 构建核心服务请求
    let core_request = build_core_hybrid_search_request(&req, &claims).await?;

    // 根据配置选择或创建服务实例
    let hybrid_service = get_or_create_hybrid_service(&app_state, &req).await?;

    // 执行混合搜索
    match hybrid_service.hybrid_search(core_request).await {
        Ok(results) => {
            tracing::info!(
                "混合搜索成功: 返回{}个结果, 耗时{}ms",
                results.results.len(),
                results.search_time_ms
            );

            // 记录用户搜索行为（如果启用个性化）
            if req.enable_personalization.unwrap_or(false) {
                if let Err(e) = record_search_behavior(&app_state, &req, &claims, &results).await {
                    tracing::warn!("记录用户搜索行为失败: {}", e);
                }
            }

            let response = HybridSearchApiResponse::from(results);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("混合搜索失败: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::internal_error(Some(format!("搜索失败: {}", e)))))
        }
    }
}

/// 获取混合搜索配置
pub async fn get_hybrid_search_config(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    // 创建默认的混合搜索服务来获取配置
    let keyword_service = app_state.keyword_search_service.clone();
    let vector_service = app_state.vector_search_service.clone();

    let _config = wisdom_vault_core::services::HybridSearchConfig::default();
    let hybrid_service = HybridSearchService::new_with_defaults(keyword_service, vector_service);

    let current_config = hybrid_service.get_config();

    let config_response = serde_json::json!({
        "default_keyword_weight": current_config.default_keyword_weight,
        "default_vector_weight": current_config.default_vector_weight,
        "default_similarity_threshold": current_config.default_similarity_threshold,
        "default_limit": current_config.default_limit,
        "max_limit": current_config.max_limit,
        "enable_result_cache": current_config.enable_result_cache,
        "cache_ttl_seconds": current_config.cache_ttl_seconds,
        "enable_personalization": current_config.enable_personalization,
        "max_concurrent_searches": current_config.max_concurrent_searches,
        "default_fusion_strategy": format!("{:?}", current_config.default_fusion_strategy),
    });

    Ok(HttpResponse::Ok().json(ApiResponse::success(config_response)))
}

/// 更新混合搜索配置
pub async fn update_hybrid_search_config(
    _app_state: web::Data<AppState>,
    req: web::Json<HybridSearchConfigRequest>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    tracing::info!("更新混合搜索配置请求");

    // 在实际实现中，这里应该更新全局配置
    // 目前返回成功响应
    let updated_config = serde_json::json!({
        "message": "混合搜索配置已更新",
        "updated_fields": extract_updated_fields(&req),
    });

    Ok(HttpResponse::Ok().json(ApiResponse::success(updated_config)))
}

/// 获取混合搜索统计信息
pub async fn get_hybrid_search_statistics(
    app_state: web::Data<AppState>,
    query: web::Query<HashMap<String, String>>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let time_window = query
        .get("time_window")
        .unwrap_or(&"24h".to_string())
        .clone();

    tracing::info!("获取混合搜索统计信息: time_window={}", time_window);

    // 创建混合搜索服务实例来获取统计信息
    let keyword_service = app_state.keyword_search_service.clone();
    let vector_service = app_state.vector_search_service.clone();
    let hybrid_service = HybridSearchService::new_with_defaults(keyword_service, vector_service);

    let stats = hybrid_service.get_performance_stats().await;
    let performance_stats = HybridSearchPerformanceStatsResponse {
        total_searches: stats.total_searches,
        successful_searches: stats.successful_searches,
        failed_searches: stats.failed_searches,
        avg_search_time_ms: stats.avg_search_time_ms,
        cache_hits: stats.cache_hits,
        cache_misses: stats.cache_misses,
        fusion_strategy_usage: stats.fusion_strategy_usage,
        avg_result_count: stats.avg_result_count,
    };

    let statistics_response = HybridSearchStatisticsResponse {
        performance_stats,
        cache_stats: None,           // TODO: 实现缓存统计
        personalization_stats: None, // TODO: 实现个性化统计
    };

    Ok(HttpResponse::Ok().json(ApiResponse::success(statistics_response)))
}

/// 清理混合搜索缓存
pub async fn clear_hybrid_search_cache(
    app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    tracing::info!("清理混合搜索缓存请求");

    // 创建混合搜索服务实例并清理缓存
    let keyword_service = app_state.keyword_search_service.clone();
    let vector_service = app_state.vector_search_service.clone();
    let hybrid_service = HybridSearchService::new_with_defaults(keyword_service, vector_service);

    // 清理混合搜索缓存
    hybrid_service.clear_cache().await;

    let response = serde_json::json!({
        "message": "混合搜索缓存已清理",
        "cleared_at": chrono::Utc::now(),
    });
    Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
}

/// 获取可用的融合策略
pub async fn get_fusion_strategies(
    _app_state: web::Data<AppState>,
    _claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    let strategies = vec![
        serde_json::json!({
            "name": "weighted_linear_combination",
            "display_name": "加权线性组合",
            "description": "基于设定权重对关键词和向量搜索结果进行线性组合",
            "parameters": ["keyword_weight", "vector_weight"],
            "suitable_for": ["平衡搜索", "快速搜索"],
        }),
        serde_json::json!({
            "name": "reciprocal_rank_fusion",
            "display_name": "排序融合(RRF)",
            "description": "基于倒数排名融合，适用于不同搜索引擎结果的合并",
            "parameters": ["k_parameter"],
            "suitable_for": ["多样性搜索", "排名融合"],
        }),
        serde_json::json!({
            "name": "normalized_score_fusion",
            "display_name": "分数归一化融合",
            "description": "对搜索得分进行归一化处理后融合",
            "parameters": ["normalization_method", "keyword_weight", "vector_weight"],
            "suitable_for": ["精确搜索", "得分标准化"],
        }),
        serde_json::json!({
            "name": "hybrid_reranking",
            "display_name": "混合重排序",
            "description": "结合多种因素对搜索结果进行重新排序",
            "parameters": ["diversity_threshold", "intent_boost", "freshness_weight"],
            "suitable_for": ["高质量搜索", "个性化搜索"],
        }),
        serde_json::json!({
            "name": "dynamic_weight_adjustment",
            "display_name": "动态权重调整",
            "description": "根据查询特征动态调整搜索权重",
            "parameters": ["query_analysis", "context_awareness"],
            "suitable_for": ["智能搜索", "自适应搜索"],
        }),
    ];

    Ok(HttpResponse::Ok().json(ApiResponse::success(strategies)))
}

/// 测试混合搜索配置
pub async fn test_hybrid_search_config(
    app_state: web::Data<AppState>,
    req: web::Json<serde_json::Value>,
    claims: web::ReqData<Claims>,
) -> ActixResult<HttpResponse> {
    tracing::info!("测试混合搜索配置: user_id={}", claims.user_id);

    // 从请求中提取测试查询和配置
    let test_query = req
        .get("test_query")
        .and_then(|v| v.as_str())
        .unwrap_or("测试查询");

    let config = req.get("config").cloned().unwrap_or_default();

    // 创建测试请求
    let test_request = HybridSearchRequest {
        query_vector: None,
        query_text: test_query.to_string(),
        model_id: None,
        vector_weight: config.get("vector_weight").and_then(|v| v.as_f64()),
        text_weight: config.get("text_weight").and_then(|v| v.as_f64()),
        similarity_threshold: config.get("similarity_threshold").and_then(|v| v.as_f64()),
        limit: Some(5), // 限制测试结果数量
        knowledge_base_id: None,
        fusion_strategy: config
            .get("fusion_strategy")
            .and_then(|v| v.as_str())
            .and_then(|s| parse_fusion_strategy(s)),
        enable_personalization: Some(false), // 测试时禁用个性化
        user_id: Some(claims.user_id),
        search_context: None,
    };

    // 构建核心搜索请求
    let core_request = build_core_hybrid_search_request(&test_request, &claims).await?;

    // 获取混合搜索服务
    let hybrid_service = get_or_create_hybrid_service(&app_state, &test_request).await?;

    // 执行测试搜索
    match hybrid_service.hybrid_search(core_request).await {
        Ok(results) => {
            let test_results = serde_json::json!({
                "test_query": test_query,
                "results_count": results.results.len(),
                "search_time_ms": results.search_time_ms,
                "fusion_strategy": format!("{:?}", results.fusion_strategy),
                "performance_metrics": {
                    "keyword_results": results.keyword_stats.as_ref().map(|s| s.result_count).unwrap_or(0),
                    "vector_results": results.vector_stats.as_ref().map(|s| s.result_count).unwrap_or(0),
                    "overlap_count": results.fusion_stats.overlap_results_count,
                },
                "sample_results": results.results.into_iter().take(3).map(|r| {
                    serde_json::json!({
                        "title": r.document.title,
                        "final_score": r.final_score,
                        "keyword_score": r.keyword_score,
                        "vector_score": r.vector_score,
                    })
                }).collect::<Vec<_>>(),
            });

            Ok(HttpResponse::Ok().json(ApiResponse::success(test_results)))
        }
        Err(e) => {
            tracing::error!("测试混合搜索配置失败: {}", e);
            Ok(HttpResponse::BadRequest()
                .json(ApiResponse::<()>::bad_request(format!("配置测试失败: {}", e))))
        }
    }
}

// ===== 辅助函数 =====

/// 构建核心混合搜索请求
async fn build_core_hybrid_search_request(
    req: &HybridSearchRequest,
    _claims: &Claims,
) -> Result<CoreHybridSearchRequest, actix_web::Error> {
    Ok(CoreHybridSearchRequest {
        query_vector: req.query_vector.clone(),
        query_text: req.query_text.clone(),
        model_id: req.model_id,
        vector_weight: req.vector_weight,
        keyword_weight: req.text_weight, // 使用 text_weight 作为 keyword_weight
        text_weight: req.text_weight,
        similarity_threshold: req.similarity_threshold,
        limit: req.limit,
        knowledge_base_id: req.knowledge_base_id,
    })
}

/// 获取或创建混合搜索服务
async fn get_or_create_hybrid_service(
    app_state: &AppState,
    _req: &HybridSearchRequest,
) -> Result<HybridSearchService, actix_web::Error> {
    // 在实际实现中，这里可以根据请求参数创建不同配置的服务
    // 目前使用默认配置
    let keyword_service = app_state.keyword_search_service.clone();
    let vector_service = app_state.vector_search_service.clone();

    Ok(HybridSearchService::new_with_defaults(
        keyword_service,
        vector_service,
    ))
}

/// 记录用户搜索行为
async fn record_search_behavior(
    _app_state: &AppState,
    req: &HybridSearchRequest,
    claims: &Claims,
    results: &wisdom_vault_core::services::HybridSearchResults,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // 构建搜索行为记录
    // 暂时禁用 SearchBehavior，因为类型定义缺失
    // let search_behavior = wisdom_vault_core::services::SearchBehavior {
    //     query_text: req.query_text.clone(),
    //     query_time: chrono::Utc::now(),
    //     query_context: build_query_context(req).await,
    //     result_clicks: Vec::new(), // 后续可以通过其他API更新点击记录
    //     satisfaction_score: None, // 需要用户反馈
    // };

    // 这里应该调用个性化搜索服务记录行为
    // 目前只是记录日志
    tracing::info!(
        "记录用户搜索行为: user_id={}, query='{}', results_count={}",
        claims.user_id,
        req.query_text,
        results.results.len()
    );

    Ok(())
}

/// 构建查询上下文
async fn build_query_context(req: &HybridSearchRequest) -> QueryContext {
    let default_context = QueryContext {
        query_length: req.query_text.len(),
        query_complexity: calculate_query_complexity(&req.query_text),
        query_type: QueryType::Factual,         // 默认值
        query_intent: QueryIntent::Information, // 默认值
        language: "zh".to_string(),             // 默认中文
    };

    // 如果有搜索上下文，使用提供的值
    if let Some(context) = &req.search_context {
        QueryContext {
            query_length: req.query_text.len(),
            query_complexity: context
                .query_complexity
                .unwrap_or(default_context.query_complexity),
            query_type: context
                .query_type
                .as_ref()
                .map(|t| wisdom_vault_core::services::QueryType::from(t.clone()))
                .unwrap_or(default_context.query_type),
            query_intent: context
                .query_intent
                .as_ref()
                .map(|i| wisdom_vault_core::services::QueryIntent::from(i.clone()))
                .unwrap_or(default_context.query_intent),
            language: context.language.clone().unwrap_or(default_context.language),
        }
    } else {
        default_context
    }
}

/// 计算查询复杂度
fn calculate_query_complexity(query: &str) -> f64 {
    let word_count = query.split_whitespace().count() as f64;
    let char_count = query.len() as f64;
    let unique_words = query
        .split_whitespace()
        .collect::<std::collections::HashSet<_>>()
        .len() as f64;

    // 综合考虑词数、字符数和词汇多样性
    let complexity = (word_count / 20.0).min(1.0) * 0.4
        + (char_count / 200.0).min(1.0) * 0.3
        + (unique_words / word_count.max(1.0)) * 0.3;

    complexity.clamp(0.0, 1.0)
}

/// 提取更新的配置字段
fn extract_updated_fields(req: &HybridSearchConfigRequest) -> Vec<String> {
    let mut fields = Vec::new();

    if req.default_keyword_weight.is_some() {
        fields.push("default_keyword_weight".to_string());
    }
    if req.default_vector_weight.is_some() {
        fields.push("default_vector_weight".to_string());
    }
    if req.default_similarity_threshold.is_some() {
        fields.push("default_similarity_threshold".to_string());
    }
    if req.enable_result_cache.is_some() {
        fields.push("enable_result_cache".to_string());
    }
    if req.enable_personalization.is_some() {
        fields.push("enable_personalization".to_string());
    }

    fields
}

/// 解析融合策略字符串
fn parse_fusion_strategy(strategy_str: &str) -> Option<ResultFusionStrategyRequest> {
    match strategy_str {
        "weighted_linear_combination" => {
            Some(ResultFusionStrategyRequest::WeightedLinearCombination)
        }
        "reciprocal_rank_fusion" => Some(ResultFusionStrategyRequest::ReciprocalRankFusion),
        "normalized_score_fusion" => Some(ResultFusionStrategyRequest::NormalizedScoreFusion),
        "hybrid_reranking" => Some(ResultFusionStrategyRequest::HybridReranking),
        "dynamic_weight_adjustment" => Some(ResultFusionStrategyRequest::DynamicWeightAdjustment),
        _ => None,
    }
}

/// 配置混合搜索路由
pub fn configure_hybrid_search_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/hybrid-search")
            .route("", web::post().to(hybrid_search))
            .route("/config", web::get().to(get_hybrid_search_config))
            .route("/config", web::put().to(update_hybrid_search_config))
            .route("/statistics", web::get().to(get_hybrid_search_statistics))
            .route("/cache/clear", web::delete().to(clear_hybrid_search_cache))
            .route("/strategies", web::get().to(get_fusion_strategies))
            .route("/test", web::post().to(test_hybrid_search_config)),
    );
}
