use actix_web::{HttpRequest, HttpResponse, Result, web};
use uuid::Uuid;
use validator::Validate;
use wisdom_vault_core::UserManagementService;
use wisdom_vault_database::{
    connection::DatabaseConnection,
    repositories::{SurrealRoleRepository, SurrealUserRepository, SurrealUserRoleRepository},
};

use crate::{
    session::RequiredUserSession,
    types::{
        AssignRoleRequest, BatchOperationResponse, BatchUserOperationRequest, CreateUserRequest,
        CreateUserResponse, UpdateUserRequest, UserListQuery, UserListResponse, UserResponse,
        common::ApiResponse,
    }
};

// Helper function to convert User model to UserResponse
fn user_to_response(user: wisdom_vault_database::models::User, roles: Vec<String>) -> UserResponse {
    UserResponse {
        id: user.id.to_string(),
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        phone: user.phone,
        avatar_url: user.avatar_url,
        department_id: user.department_id.map(|id| id.to_string()),
        is_active: user.is_active,
        last_login_at: user.last_login_at,
        created_at: user.created_at,
        updated_at: user.updated_at,
        roles,
    }
}

// POST /api/v1/users - 创建新用户
pub async fn create_user(
    request: web::Json<CreateUserRequest>,
    db: web::Data<DatabaseConnection>,
    user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    // 验证输入
    if let Err(validation_errors) = request.validate() {
        return Ok(HttpResponse::BadRequest().json(
            ApiResponse::<()>::validation_error(serde_json::to_value(validation_errors).unwrap())
        ));
    }

    // 创建服务实例
    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    // 从Session中获取创建者ID（字符串格式）
    let created_by_str = user_session.into_inner().user_id;

    match service
        .create_user(
            request.username.clone(),
            request.email.clone(),
            request.password.clone(),
            request.full_name.clone(),
            request.phone.clone(),
            request.department_id,
            request.is_active,
            created_by_str,
        )
        .await
    {
        Ok((user, generated_password)) => {
            let response = CreateUserResponse {
                user: user_to_response(user, vec!["user".to_string()]),
                generated_password,
            };
            Ok(HttpResponse::Created().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to create user: {:?}", e);
            Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request(e.to_string())
            ))
        }
    }
}

// GET /api/v1/users - 获取用户列表
pub async fn list_users(
    query: web::Query<UserListQuery>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    let page = query.page.unwrap_or(1);
    let limit = query.limit.unwrap_or(20);

    match service
        .list_users(
            Some(page),
            Some(limit),
            query.search.clone(),
            query.department_id,
            query.is_active,
        )
        .await
    {
        Ok((users, total)) => {
            let mut user_responses = Vec::new();

            for user in users {
                // 获取用户角色
                let roles = match service.get_user_roles(user.id).await {
                    Ok(roles) => roles.into_iter().map(|r| r.name).collect(),
                    Err(_) => vec![],
                };

                user_responses.push(user_to_response(user, roles));
            }

            let total_pages = ((total as f64) / (limit as f64)).ceil() as u32;

            let response = UserListResponse {
                users: user_responses,
                total,
                page,
                limit,
                total_pages,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to list users: {:?}", e);
            Ok(HttpResponse::InternalServerError().json(
                ApiResponse::<()>::internal_error(Some("获取用户列表失败".to_string()))
            ))
        }
    }
}

// GET /api/v1/users/{id} - 获取单个用户
pub async fn get_user(
    path: web::Path<String>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    let user_id = match Uuid::parse_str(&path) {
        Ok(id) => id,
        Err(_) => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request("无效的用户ID格式".to_string())
            ));
        }
    };

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    match service.get_user_by_id(user_id).await {
        Ok(Some(user)) => {
            // 获取用户角色
            let roles = match service.get_user_roles(user.id).await {
                Ok(roles) => roles.into_iter().map(|r| r.name).collect(),
                Err(_) => vec![],
            };

            let response = user_to_response(user, roles);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Ok(None) => Ok(HttpResponse::NotFound().json(
            ApiResponse::<()>::not_found("用户")
        )),
        Err(e) => {
            tracing::error!("Failed to get user: {:?}", e);
            Ok(HttpResponse::InternalServerError().json(
                ApiResponse::<()>::internal_error(Some("获取用户失败".to_string()))
            ))
        }
    }
}

// PUT /api/v1/users/{id} - 更新用户信息
pub async fn update_user(
    path: web::Path<String>,
    request: web::Json<UpdateUserRequest>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    // 验证输入
    if let Err(validation_errors) = request.validate() {
        return Ok(HttpResponse::BadRequest().json(
            ApiResponse::<()>::validation_error(serde_json::to_value(validation_errors).unwrap())
        ));
    }

    let user_id = match Uuid::parse_str(&path) {
        Ok(id) => id,
        Err(_) => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request("无效的用户ID格式".to_string())
            ));
        }
    };

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    match service
        .update_user(
            user_id,
            request.username.clone(),
            request.email.clone(),
            request.full_name.clone(),
            request.phone.clone(),
            request.department_id,
            request.avatar_url.clone(),
        )
        .await
    {
        Ok(user) => {
            // 获取用户角色
            let roles = match service.get_user_roles(user.id).await {
                Ok(roles) => roles.into_iter().map(|r| r.name).collect(),
                Err(_) => vec![],
            };

            let response = user_to_response(user, roles);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to update user: {:?}", e);
            Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request(e.to_string())
            ))
        }
    }
}

// POST /api/v1/users/{id}/activate - 激活用户
pub async fn activate_user(
    path: web::Path<String>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    let user_id = match Uuid::parse_str(&path) {
        Ok(id) => id,
        Err(_) => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request("无效的用户ID格式".to_string())
            ));
        }
    };

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    match service.set_user_status(user_id, true).await {
        Ok(true) => Ok(HttpResponse::Ok().json(
            ApiResponse::success_with_message((), "用户激活成功".to_string())
        )),
        Ok(false) => Ok(HttpResponse::NotFound().json(
            ApiResponse::<()>::not_found("用户")
        )),
        Err(e) => {
            tracing::error!("Failed to activate user: {:?}", e);
            Ok(HttpResponse::InternalServerError().json(
                ApiResponse::<()>::internal_error(Some("激活用户失败".to_string()))
            ))
        }
    }
}

// POST /api/v1/users/{id}/deactivate - 禁用用户
pub async fn deactivate_user(
    path: web::Path<String>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    let user_id = match Uuid::parse_str(&path) {
        Ok(id) => id,
        Err(_) => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request("无效的用户ID格式".to_string())
            ));
        }
    };

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    match service.set_user_status(user_id, false).await {
        Ok(true) => Ok(HttpResponse::Ok().json(
            ApiResponse::success_with_message((), "用户禁用成功".to_string())
        )),
        Ok(false) => Ok(HttpResponse::NotFound().json(
            ApiResponse::<()>::not_found("用户")
        )),
        Err(e) => {
            tracing::error!("Failed to deactivate user: {:?}", e);
            Ok(HttpResponse::InternalServerError().json(
                ApiResponse::<()>::internal_error(Some("禁用用户失败".to_string()))
            ))
        }
    }
}

// DELETE /api/v1/users/{id} - 删除用户
pub async fn delete_user(
    path: web::Path<String>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    let user_id = match Uuid::parse_str(&path) {
        Ok(id) => id,
        Err(_) => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request("无效的用户ID格式".to_string())
            ));
        }
    };

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    match service.delete_user(user_id).await {
        Ok(true) => Ok(HttpResponse::Ok().json(
            ApiResponse::success_with_message((), "用户删除成功".to_string())
        )),
        Ok(false) => Ok(HttpResponse::NotFound().json(
            ApiResponse::<()>::not_found("用户")
        )),
        Err(e) => {
            tracing::error!("Failed to delete user: {:?}", e);
            Ok(HttpResponse::InternalServerError().json(
                ApiResponse::<()>::internal_error(Some("删除用户失败".to_string()))
            ))
        }
    }
}

// POST /api/v1/users/{id}/roles - 分配角色给用户
pub async fn assign_role_to_user(
    path: web::Path<String>,
    request: web::Json<AssignRoleRequest>,
    db: web::Data<DatabaseConnection>,
    user_session: RequiredUserSession,
) -> Result<HttpResponse> {
    if let Err(validation_errors) = request.validate() {
        return Ok(HttpResponse::BadRequest().json(
            ApiResponse::<()>::validation_error(serde_json::to_value(validation_errors).unwrap())
        ));
    }

    let user_id = path.into_inner();

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    // 从Session中获取分配者ID（字符串格式）
    let assigned_by_str = user_session.into_inner().user_id;

    match service
        .assign_role_to_user(user_id, request.role_id, assigned_by_str)
        .await
    {
        Ok(_) => Ok(HttpResponse::Ok().json(
            ApiResponse::success_with_message((), "角色分配成功".to_string())
        )),
        Err(e) => {
            tracing::error!("Failed to assign role: {:?}", e);
            Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request(e.to_string())
            ))
        }
    }
}

// DELETE /api/v1/users/{user_id}/roles/{role_id} - 移除用户角色
pub async fn remove_role_from_user(
    path: web::Path<(String, String)>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    let (user_id_str, role_id_str) = path.into_inner();

    let user_id = match Uuid::parse_str(&user_id_str) {
        Ok(id) => id,
        Err(_) => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request("无效的用户ID格式".to_string())
            ));
        }
    };

    let role_id = match Uuid::parse_str(&role_id_str) {
        Ok(id) => id,
        Err(_) => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request("无效的角色ID格式".to_string())
            ));
        }
    };

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    match service.remove_role_from_user(user_id, role_id).await {
        Ok(true) => Ok(HttpResponse::Ok().json(
            ApiResponse::success_with_message((), "角色移除成功".to_string())
        )),
        Ok(false) => Ok(HttpResponse::NotFound().json(
            ApiResponse::<()>::not_found("角色分配")
        )),
        Err(e) => {
            tracing::error!("Failed to remove role: {:?}", e);
            Ok(HttpResponse::InternalServerError().json(
                ApiResponse::<()>::internal_error(Some("移除角色失败".to_string()))
            ))
        }
    }
}

// GET /api/v1/users/{id}/roles - 获取用户角色列表
pub async fn get_user_roles(
    path: web::Path<String>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    let user_id = match Uuid::parse_str(&path) {
        Ok(id) => id,
        Err(_) => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request("无效的用户ID格式".to_string())
            ));
        }
    };

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    match service.get_user_roles(user_id).await {
        Ok(roles) => Ok(HttpResponse::Ok().json(ApiResponse::success(roles))),
        Err(e) => {
            tracing::error!("Failed to get user roles: {:?}", e);
            Ok(HttpResponse::InternalServerError().json(
                ApiResponse::<()>::internal_error(Some("获取用户角色失败".to_string()))
            ))
        }
    }
}

// POST /api/v1/users/batch - 批量用户操作
pub async fn batch_user_operation(
    request: web::Json<BatchUserOperationRequest>,
    db: web::Data<DatabaseConnection>,
) -> Result<HttpResponse> {
    if let Err(validation_errors) = request.validate() {
        return Ok(HttpResponse::BadRequest().json(
            ApiResponse::<()>::validation_error(serde_json::to_value(validation_errors).unwrap())
        ));
    }

    let user_repo = SurrealUserRepository::new(db.get_ref().clone());
    let role_repo = SurrealRoleRepository::new(db.get_ref().clone());
    let user_role_repo = SurrealUserRoleRepository::new(db.get_ref().clone());
    let service = UserManagementService::new(user_repo, role_repo, user_role_repo);

    let result = match request.operation.as_str() {
        "activate" => {
            service
                .batch_update_user_status(request.user_ids.clone(), true)
                .await
        }
        "deactivate" => {
            service
                .batch_update_user_status(request.user_ids.clone(), false)
                .await
        }
        "delete" => service.batch_delete_users(request.user_ids.clone()).await,
        _ => {
            return Ok(HttpResponse::BadRequest().json(
                ApiResponse::<()>::bad_request(
                    "无效的操作。支持的操作: activate, deactivate, delete".to_string()
                )
            ));
        }
    };

    match result {
        Ok((success_count, failed_count, failed_users)) => {
            let response = BatchOperationResponse {
                success_count,
                failed_count,
                failed_users,
            };
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Batch operation failed: {:?}", e);
            Ok(HttpResponse::InternalServerError().json(
                ApiResponse::<()>::internal_error(Some("批量操作失败".to_string()))
            ))
        }
    }
}
