use crate::{
    app_state::AppState,
    types::common::{ApiResponse, PaginatedResponse},
};
use actix_web::{HttpResponse, Result as ActixResult, web};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;
use wisdom_vault_core::services::{ChunkingConfig, ChunkingStrategy};
use wisdom_vault_database::{
    models::{ChunkType, DocumentChunk},
    repositories::DocumentChunkRepository,
};

/// 分页参数
#[derive(Debug, Deserialize)]
pub struct PaginationParams {
    pub limit: Option<u32>,
    pub offset: Option<u32>,
}

/// 分块配置请求
#[derive(Debug, Deserialize, Validate)]
pub struct ChunkDocumentRequest {
    /// 分块策略
    pub strategy: ChunkingStrategyRequest,
    /// 是否保留格式
    #[serde(default)]
    pub preserve_formatting: bool,
    /// 是否包含元数据
    #[serde(default = "default_true")]
    pub include_metadata: bool,
    /// 质量阈值 (0.0-1.0)
    #[validate(range(min = 0.0, max = 1.0))]
    #[serde(default = "default_quality_threshold")]
    pub quality_threshold: f64,
}

fn default_true() -> bool {
    true
}

fn default_quality_threshold() -> f64 {
    0.5
}

/// 分块策略请求
#[derive(Debug, Clone, Deserialize)]
#[serde(tag = "type", rename_all = "snake_case")]
pub enum ChunkingStrategyRequest {
    FixedLength {
        max_length: usize,
        #[serde(default)]
        overlap: usize,
    },
    Paragraph,
    Sentence {
        max_sentences: usize,
    },
    SemanticBoundary {
        max_length: usize,
        min_length: usize,
    },
    Structured,
}

impl From<ChunkingStrategyRequest> for ChunkingStrategy {
    fn from(req: ChunkingStrategyRequest) -> Self {
        match req {
            ChunkingStrategyRequest::FixedLength {
                max_length,
                overlap,
            } => ChunkingStrategy::FixedLength {
                max_length,
                overlap,
            },
            ChunkingStrategyRequest::Paragraph => ChunkingStrategy::Paragraph,
            ChunkingStrategyRequest::Sentence { max_sentences } => {
                ChunkingStrategy::Sentence { max_sentences }
            }
            ChunkingStrategyRequest::SemanticBoundary {
                max_length,
                min_length,
            } => ChunkingStrategy::SemanticBoundary {
                max_length,
                min_length,
            },
            ChunkingStrategyRequest::Structured => ChunkingStrategy::Structured,
        }
    }
}

/// 分块结果响应
#[derive(Debug, Serialize)]
pub struct ChunkingResultResponse {
    pub chunks: Vec<DocumentChunkResponse>,
    pub statistics: ChunkingStatisticsResponse,
    pub warnings: Vec<String>,
}

/// 分块响应
#[derive(Debug, Serialize)]
pub struct DocumentChunkResponse {
    pub id: Uuid,
    pub document_id: Uuid,
    pub content: String,
    pub chunk_index: i32,
    pub chunk_type: ChunkType,
    pub token_count: i32,
    pub char_count: i32,
    pub start_offset: i32,
    pub end_offset: i32,
    pub metadata: ChunkMetadataResponse,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

impl From<DocumentChunk> for DocumentChunkResponse {
    fn from(chunk: DocumentChunk) -> Self {
        Self {
            id: chunk.id,
            document_id: chunk.document_id,
            content: chunk.content,
            chunk_index: chunk.chunk_index,
            chunk_type: chunk.chunk_type,
            token_count: chunk.token_count,
            char_count: chunk.char_count,
            start_offset: chunk.start_offset,
            end_offset: chunk.end_offset,
            metadata: chunk.metadata.into(),
            created_at: chunk.created_at,
        }
    }
}

/// 分块元数据响应
#[derive(Debug, Serialize)]
pub struct ChunkMetadataResponse {
    pub heading: Option<String>,
    pub section_level: Option<i32>,
    pub language: Option<String>,
    pub quality_score: Option<f64>,
    pub extraction_confidence: Option<f64>,
}

impl From<wisdom_vault_database::models::ChunkMetadata> for ChunkMetadataResponse {
    fn from(metadata: wisdom_vault_database::models::ChunkMetadata) -> Self {
        Self {
            heading: metadata.heading,
            section_level: metadata.section_level,
            language: metadata.language,
            quality_score: metadata.quality_score,
            extraction_confidence: metadata.extraction_confidence,
        }
    }
}

/// 分块统计响应
#[derive(Debug, Serialize)]
pub struct ChunkingStatisticsResponse {
    pub total_chunks: usize,
    pub average_chunk_size: f64,
    pub min_chunk_size: usize,
    pub max_chunk_size: usize,
    pub quality_score: f64,
    pub processing_time_ms: u64,
}

impl From<wisdom_vault_core::services::ChunkingStatistics> for ChunkingStatisticsResponse {
    fn from(stats: wisdom_vault_core::services::ChunkingStatistics) -> Self {
        Self {
            total_chunks: stats.total_chunks,
            average_chunk_size: stats.average_chunk_size,
            min_chunk_size: stats.min_chunk_size,
            max_chunk_size: stats.max_chunk_size,
            quality_score: stats.quality_score,
            processing_time_ms: stats.processing_time_ms,
        }
    }
}

/// 更新分块请求
#[derive(Debug, Deserialize, Validate)]
pub struct UpdateChunkRequest {
    #[validate(length(min = 1, max = 10000))]
    pub content: String,
    pub metadata: Option<UpdateChunkMetadataRequest>,
}

/// 更新分块元数据请求
#[derive(Debug, Deserialize)]
pub struct UpdateChunkMetadataRequest {
    pub heading: Option<String>,
    pub section_level: Option<i32>,
    pub language: Option<String>,
}

/// 对文档进行分块
/// POST /api/v1/documents/{document_id}/chunks
pub async fn chunk_document(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    request: web::Json<ChunkDocumentRequest>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    // 验证请求参数
    if let Err(errors) = request.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                errors
            ))),
        );
    }

    // 获取文档
    let document_service = &app_state.document_service;
    let document = match document_service.get_document(document_id).await {
        Ok(Some(doc)) => doc,
        Ok(None) => {
            return Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Document not found".to_string())));
        }
        Err(e) => {
            tracing::error!("Failed to get document: {}", e);
            return Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Internal server error".to_string(),
                )),
            );
        }
    };

    // 配置分块参数
    let config = ChunkingConfig {
        strategy: request.strategy.clone().into(),
        preserve_formatting: request.preserve_formatting,
        include_metadata: request.include_metadata,
        quality_threshold: request.quality_threshold,
    };

    // 执行分块
    let chunking_service = &app_state.document_chunking_service;
    match chunking_service.rechunk_document(&document, &config).await {
        Ok(result) => {
            let response = ChunkingResultResponse {
                chunks: result
                    .chunks
                    .into_iter()
                    .map(DocumentChunkResponse::from)
                    .collect(),
                statistics: result.statistics.into(),
                warnings: result.warnings,
            };

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to chunk document: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to chunk document".to_string(),
                )),
            )
        }
    }
}

/// 获取文档的分块列表
/// GET /api/v1/documents/{document_id}/chunks
pub async fn get_document_chunks(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    query: web::Query<PaginationParams>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    // 验证分页参数
    let limit = query.limit.unwrap_or(50).min(100);
    let offset = query.offset.unwrap_or(0);

    let chunking_service = &app_state.document_chunking_service;

    // 获取分块列表
    match chunking_service.get_document_chunks(document_id).await {
        Ok(chunks) => {
            // 手动分页（如果需要更高效的分页，应该在repository层实现）
            let total_count = chunks.len() as i64;
            let paginated_chunks: Vec<_> = chunks
                .into_iter()
                .skip(offset as usize)
                .take(limit as usize)
                .map(DocumentChunkResponse::from)
                .collect();

            let response = PaginatedResponse::new(
                paginated_chunks,
                Some(total_count),
                (offset / limit) + 1,
                limit,
            );

            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to get document chunks: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to get document chunks".to_string(),
                )),
            )
        }
    }
}

/// 获取单个分块
/// GET /api/v1/chunks/{chunk_id}
pub async fn get_chunk(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
) -> ActixResult<HttpResponse> {
    let chunk_id = path.into_inner();

    let chunk_repository = &app_state.document_chunk_repository;

    match chunk_repository.find_by_id(chunk_id).await {
        Ok(Some(chunk)) => {
            let response = DocumentChunkResponse::from(chunk);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Ok(None) => {
            Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Chunk not found".to_string())))
        }
        Err(e) => {
            tracing::error!("Failed to get chunk: {}", e);
            Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("Failed to get chunk".to_string())))
        }
    }
}

/// 更新分块
/// PUT /api/v1/chunks/{chunk_id}
pub async fn update_chunk(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
    request: web::Json<UpdateChunkRequest>,
) -> ActixResult<HttpResponse> {
    let chunk_id = path.into_inner();

    // 验证请求参数
    if let Err(errors) = request.validate() {
        return Ok(
            HttpResponse::BadRequest().json(ApiResponse::<()>::error(format!(
                "Validation failed: {:?}",
                errors
            ))),
        );
    }

    let chunk_repository = &app_state.document_chunk_repository;

    // 获取现有分块
    let mut chunk = match chunk_repository.find_by_id(chunk_id).await {
        Ok(Some(chunk)) => chunk,
        Ok(None) => {
            return Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Chunk not found".to_string())));
        }
        Err(e) => {
            tracing::error!("Failed to get chunk: {}", e);
            return Ok(HttpResponse::InternalServerError()
                .json(ApiResponse::<()>::error("Failed to get chunk".to_string())));
        }
    };

    // 更新分块内容
    chunk.content = request.content.clone();
    chunk.char_count = request.content.len() as i32;
    chunk.token_count = (request.content.len() / 4) as i32; // 简单估算

    // 更新元数据
    if let Some(ref metadata_update) = request.metadata {
        if let Some(ref heading) = metadata_update.heading {
            chunk.metadata.heading = Some(heading.clone());
        }
        if let Some(section_level) = metadata_update.section_level {
            chunk.metadata.section_level = Some(section_level);
        }
        if let Some(ref language) = metadata_update.language {
            chunk.metadata.language = Some(language.clone());
        }
    }

    // 保存更新
    match chunk_repository.update(&chunk).await {
        Ok(updated_chunk) => {
            let response = DocumentChunkResponse::from(updated_chunk);
            Ok(HttpResponse::Ok().json(ApiResponse::success(response)))
        }
        Err(e) => {
            tracing::error!("Failed to update chunk: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to update chunk".to_string(),
                )),
            )
        }
    }
}

/// 删除分块
/// DELETE /api/v1/chunks/{chunk_id}
pub async fn delete_chunk(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
) -> ActixResult<HttpResponse> {
    let chunk_id = path.into_inner();

    let chunk_repository = &app_state.document_chunk_repository;

    match chunk_repository.delete(chunk_id).await {
        Ok(true) => Ok(HttpResponse::Ok().json(ApiResponse::success("Chunk deleted successfully"))),
        Ok(false) => {
            Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Chunk not found".to_string())))
        }
        Err(e) => {
            tracing::error!("Failed to delete chunk: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to delete chunk".to_string(),
                )),
            )
        }
    }
}

/// 删除文档的所有分块
/// DELETE /api/v1/documents/{document_id}/chunks
pub async fn delete_document_chunks(
    app_state: web::Data<AppState>,
    path: web::Path<Uuid>,
) -> ActixResult<HttpResponse> {
    let document_id = path.into_inner();

    let chunking_service = &app_state.document_chunking_service;

    match chunking_service.delete_document_chunks(document_id).await {
        Ok(true) => {
            Ok(HttpResponse::Ok()
                .json(ApiResponse::success("Document chunks deleted successfully")))
        }
        Ok(false) => Ok(HttpResponse::NotFound().json(ApiResponse::<()>::error(
            "No chunks found for document".to_string(),
        ))),
        Err(e) => {
            tracing::error!("Failed to delete document chunks: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(
                    "Failed to delete document chunks".to_string(),
                )),
            )
        }
    }
}

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/api/v1")
            .route(
                "/documents/{document_id}/chunks",
                web::post().to(chunk_document),
            )
            .route(
                "/documents/{document_id}/chunks",
                web::get().to(get_document_chunks),
            )
            .route(
                "/documents/{document_id}/chunks",
                web::delete().to(delete_document_chunks),
            )
            .route("/chunks/{chunk_id}", web::get().to(get_chunk))
            .route("/chunks/{chunk_id}", web::put().to(update_chunk))
            .route("/chunks/{chunk_id}", web::delete().to(delete_chunk)),
    );
}
