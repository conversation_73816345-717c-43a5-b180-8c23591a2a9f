use config::{Config, ConfigError, File};
use serde::Deserialize;

#[derive(Debug, Deserialize, Clone)]
pub struct AppConfig {
    pub host: String,
    pub port: u16,
    pub mongodb: MongoDBConfig,
    pub qdrant: QdrantConfig,
    pub redis: RedisConfig,
    pub auth: AuthConfig,
    pub audit: AuditConfig,
    pub admin: AdminConfig,
    pub ai: AIConfig,
    pub cors: CorsConfig,
    pub file_storage: FileStorageConfig,
    pub tika: TikaConfig,
    pub vectorization: VectorizationConfig,
    pub task_processing: TaskProcessingConfig,
    pub search: SearchConfig,
    pub logging: LoggingConfig,
    pub openobserve: Option<OpenObserveSettings>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct MongoDBConfig {
    pub url: String,
    pub max_pool_size: u32,
    pub min_pool_size: u32,
    pub max_idle_time_ms: u64,
    pub connect_timeout_ms: u64,
    pub server_selection_timeout_ms: u64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct QdrantConfig {
    pub url: String,
    pub api_key: String,
    pub connect_timeout_ms: u64,
    pub request_timeout_ms: u64,
    pub enable_tls: bool,
}

#[derive(Debug, Deserialize, Clone)]
pub struct RedisConfig {
    pub url: String,
    pub pool_size: Option<u32>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct AuthConfig {
    pub secret: String,
    pub cookie_name: String,
    pub session_expire_seconds: i64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct AuditConfig {
    pub enabled: bool,
    pub log_level: String,
    pub log_success: bool,
    pub log_failure: bool,
    pub log_sensitive: bool,
    pub exclude_paths: Vec<String>,
    pub include_actions: Vec<String>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct AdminConfig {
    pub username: String,
    pub email: String,
    pub password: String,
    pub full_name: String,
    pub force_reinit: bool,
}

#[derive(Debug, Deserialize, Clone)]
pub struct AIConfig {
    pub openai_api_key: Option<String>,
    pub openai_base_url: Option<String>,
    pub default_model: String,
    pub max_tokens: u32,
    pub temperature: f32,
    pub timeout_seconds: u64,
    pub max_retries: u32,
}

#[derive(Debug, Deserialize, Clone)]
pub struct CorsConfig {
    pub allowed_origins: Vec<String>,
    pub allowed_methods: Vec<String>,
    pub allowed_headers: Vec<String>,
    pub supports_credentials: bool,
    pub max_age: u32,
}

#[derive(Debug, Deserialize, Clone)]
pub struct FileStorageConfig {
    pub gridfs_bucket: String,             // GridFS bucket名称
    pub gridfs_chunk_size_kb: Option<u32>, // GridFS chunk大小(KB)
    pub max_file_size: u64,                // in bytes
    pub max_batch_files: u32,
    pub allowed_extensions: Vec<String>,
    pub cleanup_interval_hours: u64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct TikaConfig {
    pub server_url: String,
    pub timeout_seconds: u64,
    pub max_retries: u32,
    pub supported_mime_types: Vec<String>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct VectorizationConfig {
    pub embedding_model: String,
    pub batch_size: u32,
    pub max_concurrent_tasks: u32,
    pub quality_threshold: f32,
    pub memory_limit_mb: u64,
    pub max_retries: u32,
    pub timeout_seconds: u64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct TaskProcessingConfig {
    pub max_concurrent_tasks: u32,
    pub retry_interval_seconds: u64,
    pub max_processing_time_minutes: u64,
    pub queue_size: u32,
    pub cleanup_interval_hours: u64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct SearchConfig {
    pub max_results: u32,
    pub cache_ttl_minutes: u64,
    pub keyword_index_path: String,
    pub vector_similarity_threshold: f32,
    pub bm25_k1: f32,
    pub bm25_b: f32,
}

#[derive(Debug, Deserialize, Clone)]
pub struct OpenObserveSettings {
    pub base_url: String,
    pub org_id: String,
    pub stream: String,
    pub email: String,
    pub token: String,
    pub timeout_seconds: Option<u64>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct LoggingConfig {
    pub level: String,
    pub file_path: Option<String>,
    pub audit_file_path: Option<String>,
    pub max_file_size_mb: u64,
    pub max_files: u32,
    pub console_output: bool,
}

impl AppConfig {
    pub fn load() -> Result<Self, ConfigError> {
        let config = Config::builder()
            .add_source(File::with_name("config/default").required(false))
            .add_source(File::with_name("config/local").required(false))
            .add_source(config::Environment::with_prefix("WISDOM_VAULT"))
            // Server config
            .set_default("host", "127.0.0.1")?
            .set_default("port", 5141)?
            // MongoDB config
            .set_default("mongodb.url", "mongodb://127.0.0.1:27017/wisdom_vault")?
            .set_default("mongodb.max_pool_size", 100)?
            .set_default("mongodb.min_pool_size", 5)?
            .set_default("mongodb.max_idle_time_ms", 300000)?
            .set_default("mongodb.connect_timeout_ms", 10000)?
            .set_default("mongodb.server_selection_timeout_ms", 30000)?
            // Qdrant config
            .set_default("qdrant.url", "http://127.0.0.1:6334")?
            .set_default("qdrant.api_key", "")?
            .set_default("qdrant.connect_timeout_ms", 10000)?
            .set_default("qdrant.request_timeout_ms", 30000)?
            .set_default("qdrant.enable_tls", false)?
            // Redis config
            .set_default("redis.url", "redis://127.0.0.1:6379")?
            .set_default("redis.pool_size", 10)?
            // Auth config
            .set_default("auth.secret", "a2b006566e47ff428ddf1cf91d8283ed3de980459f50b0719b3a3db66c0b19a5f348eae3c2da7dca3ecae60659fb31188d8301d38834097762be7bd1fa3a9997")?
            .set_default("auth.cookie_name", "WV_SID")?
            .set_default("auth.session_expire_seconds", 1800)?
            // Audit config
            .set_default("audit.enabled", true)?
            .set_default("audit.log_level", "info")?
            .set_default("audit.log_success", true)?
            .set_default("audit.log_failure", true)?
            .set_default("audit.log_sensitive", true)?
            .set_default("audit.exclude_paths", vec!["/api/v1/health", "/metrics", "/swagger"])?
            .set_default("audit.include_actions", Vec::<String>::new())?
            // Admin config
            .set_default("admin.username", "admin")?
            .set_default("admin.email", "<EMAIL>")?
            .set_default("admin.password", "admin123")?
            .set_default("admin.full_name", "系统管理员")?
            .set_default("admin.force_reinit", false)?
            // AI config
            .set_default("ai.openai_base_url", "https://api.openai.com/v1")?
            .set_default("ai.default_model", "gpt-4o-mini")?
            .set_default("ai.max_tokens", 4000)?
            .set_default("ai.temperature", 0.7)?
            .set_default("ai.timeout_seconds", 30)?
            .set_default("ai.max_retries", 3)?
            // CORS config
            .set_default("cors.allowed_origins", vec!["http://localhost:3000", "http://localhost:8080"])?
            .set_default("cors.allowed_methods", vec!["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"])?
            .set_default("cors.allowed_headers", vec!["Authorization", "Accept", "Content-Type"])?
            .set_default("cors.supports_credentials", true)?
            .set_default("cors.max_age", 3600)?
            // File storage config
            .set_default("file_storage.gridfs_bucket", "file_storage")?
            .set_default("file_storage.gridfs_chunk_size_kb", 255)?
            .set_default("file_storage.max_file_size", 104857600)? // 100MB
            .set_default("file_storage.max_batch_files", 10)?
            .set_default("file_storage.allowed_extensions", vec!["pdf", "doc", "docx", "txt", "md", "html", "ppt", "pptx", "xls", "xlsx"])?
            .set_default("file_storage.cleanup_interval_hours", 24)?
            // Tika config
            .set_default("tika.server_url", "http://localhost:9998")?
            .set_default("tika.timeout_seconds", 300)?
            .set_default("tika.max_retries", 3)?
            .set_default("tika.supported_mime_types", vec!["application/pdf", "application/msword", "text/plain", "text/html"])?
            // Vectorization config
            .set_default("vectorization.embedding_model", "paraphrase-multilingual-MiniLM-L12-v2")?
            .set_default("vectorization.batch_size", 32)?
            .set_default("vectorization.max_concurrent_tasks", 4)?
            .set_default("vectorization.quality_threshold", 0.8)?
            .set_default("vectorization.memory_limit_mb", 2048)?
            .set_default("vectorization.max_retries", 3)?
            .set_default("vectorization.timeout_seconds", 600)?
            // Task processing config
            .set_default("task_processing.max_concurrent_tasks", 5)?
            .set_default("task_processing.retry_interval_seconds", 60)?
            .set_default("task_processing.max_processing_time_minutes", 30)?
            .set_default("task_processing.queue_size", 1000)?
            .set_default("task_processing.cleanup_interval_hours", 6)?
            // Search config
            .set_default("search.max_results", 100)?
            .set_default("search.cache_ttl_minutes", 30)?
            .set_default("search.keyword_index_path", "./data/keyword_index")?
            .set_default("search.vector_similarity_threshold", 0.7)?
            .set_default("search.bm25_k1", 1.5)?
            .set_default("search.bm25_b", 0.75)?
            // Logging config
            .set_default("logging.level", "info")?
            .set_default("logging.max_file_size_mb", 100)?
            .set_default("logging.max_files", 5)?
            .set_default("logging.console_output", true)?
            .set_default("logging.file_path", "./logs/wisdom-vault.log")?
            .set_default("logging.audit_file_path", "./logs/audit/audit.log")?
            // OpenObserve defaults (disabled unless provided in config/local.toml)
            .set_default("openobserve.base_url", "http://localhost:5080")?
            .set_default("openobserve.org_id", "wisdom-vault")?
            .set_default("openobserve.stream", "audit_logs")?
            .set_default("openobserve.email", "")?
            .set_default("openobserve.token", "")?
            .set_default("openobserve.timeout_seconds", 30)?
            .build()?;

        config.try_deserialize()
    }
}
