use actix_web::web::{self, ServiceConfig, get, scope};
use actix_web_httpauth::middleware::HttpAuthentication;

use crate::{
    handlers::{audit, auth, health::health_check},
    middleware,
};

pub fn configure(cfg: &mut ServiceConfig) {
    let auth_middleware = HttpAuthentication::with_fn(middleware::validator);

    cfg.route("/health", get().to(health_check))
        .service(
            scope("/api/v1/auth")
                .route("/login", web::post().to(auth::login))
                .route("/logout", web::post().to(auth::logout)),
        )
        .service(
            scope("/api/v1")
                .wrap(auth_middleware)
                .route("/auth/current", web::get().to(auth::current))
                .configure(audit_routes), // .configure(configure_chat_routes)
                                          // .configure(knowledge_base_routes)
                                          // .configure(file_upload_routes)
                                          // .configure(document_routes)
                                          // .configure(document_chunking_routes)
                                          // .configure(search_routes)
                                          // .configure(configure_hybrid_search_routes)
                                          // .configure(category_routes)
                                          // .configure(tag_routes)
                                          // .configure(version_routes)
                                          // .configure(task_routes)
                                          // .configure(task_processor_routes)
                                          // .configure(user_routes)
                                          // .configure(configure_vectorization_routes),
        );
}

// pub fn knowledge_base_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/knowledge-bases")
//             // Knowledge base management
//             .route("", web::get().to(list_knowledge_bases))
//             .route("", web::post().to(create_knowledge_base))
//             .route("/search", web::get().to(search_knowledge_bases))
//             .route("/{id}", web::get().to(get_knowledge_base))
//             .route("/{id}", web::put().to(update_knowledge_base))
//             .route("/{id}", web::delete().to(delete_knowledge_base))
//             // Category management
//             .route("/{id}/categories", web::get().to(list_categories))
//             .route("/{id}/categories", web::post().to(create_category))
//             .route(
//                 "/{id}/categories/hierarchy",
//                 web::get().to(get_category_hierarchy),
//             )
//             // Tag management
//             .route("/{id}/tags", web::get().to(list_tags))
//             .route("/{id}/tags", web::post().to(create_tag))
//             .route("/{id}/tags/search", web::get().to(search_tags))
//             .route("/{id}/tags/popular", web::get().to(get_popular_tags)),
//     );
// }

// pub fn file_upload_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/files")
//             // File upload endpoints
//             .route("/upload", web::post().to(upload_single_file))
//             .route("/upload/batch", web::post().to(upload_batch_files))
//             .route("/upload-with-tasks", web::post().to(upload_file_with_tasks))
//             .route(
//                 "/upload-batch-with-tasks",
//                 web::post().to(batch_upload_files_with_tasks),
//             )
//             // File management endpoints
//             .route("/{id}", web::get().to(get_file_info))
//             .route("/{id}/download", web::get().to(download_file))
//             .route("/{id}", web::delete().to(delete_file))
//             // File search and listing
//             .route("/search", web::get().to(search_files))
//             // Storage management (admin endpoints)
//             .route("/storage/stats", web::get().to(get_storage_stats))
//             // Processing status
//             .route("/{id}/processing", web::get().to(get_processing_status))
//             // Bulk operations
//             .route("/bulk", web::post().to(bulk_file_operation)),
//     );
// }

// pub fn document_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/documents")
//             // Basic CRUD operations
//             .route("", web::post().to(create_document))
//             .route("", web::get().to(list_documents))
//             .route("/search", web::post().to(search_documents))
//             .route("/{id}", web::get().to(get_document))
//             .route("/{id}", web::put().to(update_document))
//             .route("/{id}", web::delete().to(delete_document))
//             // Status management
//             .route("/{id}/status", web::put().to(update_document_status))
//             .route("/batch/status", web::put().to(batch_update_status))
//             // Statistics and monitoring
//             .route("/statistics", web::get().to(get_document_statistics))
//             .route("/failed", web::get().to(get_failed_documents)),
//     );
// }

// pub fn document_chunking_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("")
//             // Document chunking routes
//             .route(
//                 "/documents/{document_id}/chunks",
//                 web::post().to(chunk_document),
//             )
//             .route(
//                 "/documents/{document_id}/chunks",
//                 web::get().to(get_document_chunks),
//             )
//             .route(
//                 "/documents/{document_id}/chunks",
//                 web::delete().to(delete_document_chunks),
//             )
//             // Individual chunk management
//             .route("/chunks/{chunk_id}", web::get().to(get_chunk))
//             .route("/chunks/{chunk_id}", web::put().to(update_chunk))
//             .route("/chunks/{chunk_id}", web::delete().to(delete_chunk)),
//     );
// }

// pub fn task_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/tasks")
//             // 任务查询和管理
//             .route("", web::get().to(get_tasks))
//             .route("", web::post().to(create_task))
//             .route("/{id}", web::get().to(get_task))
//             .route("/{id}/retry", web::post().to(retry_task))
//             .route("/{id}/cancel", web::post().to(cancel_task))
//             // 批量操作
//             .route("/batch", web::post().to(batch_task_operation))
//             // 统计和监控
//             .route("/statistics", web::get().to(get_task_statistics))
//             .route("/monitor", web::get().to(get_task_monitor))
//             // 文档处理进度
//             .route(
//                 "/documents/{id}/progress",
//                 web::get().to(get_document_processing_progress),
//             )
//             // 清理操作
//             .route("/cleanup", web::post().to(cleanup_completed_tasks)),
//     );
// }

// pub fn user_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/users")
//             // 基础用户管理
//             .route("", web::post().to(create_user))
//             .route("", web::get().to(list_users))
//             .route("/{id}", web::get().to(get_user))
//             .route("/{id}", web::put().to(update_user))
//             .route("/{id}", web::delete().to(delete_user))
//             // 用户状态管理
//             .route("/{id}/activate", web::post().to(activate_user))
//             .route("/{id}/deactivate", web::post().to(deactivate_user))
//             // 角色管理
//             .route("/{id}/roles", web::get().to(get_user_roles))
//             .route("/{id}/roles", web::post().to(assign_role_to_user))
//             .route(
//                 "/{user_id}/roles/{role_id}",
//                 web::delete().to(remove_role_from_user),
//             )
//             // 批量操作
//             .route("/batch", web::post().to(batch_user_operation))
//             // 当前用户信息 (保留原有的)
//             .route("/me", web::get().to(get_current_user))
//             .route("/me", web::put().to(update_current_user)),
//     );
// }

// pub fn task_processor_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/task-processor")
//             .route("/start", web::post().to(start_task_processor))
//             .route("/stop", web::post().to(stop_task_processor))
//             .route("/status", web::get().to(get_task_processor_status)),
//     );
// }

// // 搜索相关路由
// pub fn search_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/search")
//             // 高级搜索
//             .route("/advanced", web::post().to(advanced_search_documents))
//             // 关键词搜索 (BM25)
//             .route("/keyword", web::post().to(keyword_search))
//             // 搜索建议
//             .route("/suggestions", web::get().to(search_suggestions))
//             // 统计信息
//             .route("/statistics", web::get().to(get_search_statistics))
//             .route("/statistics/{kb_id}", web::get().to(get_search_statistics))
//             // 导出搜索结果
//             .route("/export", web::post().to(export_search_results))
//             // 关键词索引管理
//             .route("/index/rebuild", web::post().to(rebuild_keyword_index))
//             .route(
//                 "/index/statistics",
//                 web::get().to(get_keyword_index_statistics),
//             )
//             .route(
//                 "/index/tasks/{task_id}",
//                 web::get().to(get_index_task_status),
//             )
//             // 性能监控
//             .route(
//                 "/performance/metrics",
//                 web::get().to(get_search_performance_metrics),
//             )
//             .route(
//                 "/performance/popular-queries",
//                 web::get().to(get_popular_queries),
//             )
//             .route(
//                 "/performance/anomalies",
//                 web::get().to(get_search_anomalies),
//             )
//             .route(
//                 "/performance/recommendations",
//                 web::get().to(get_tuning_recommendations),
//             )
//             // 向量搜索相关路由
//             .route("/vector", web::post().to(vector_similarity_search))
//             .route(
//                 "/vector/filtered",
//                 web::post().to(vector_search_with_filters),
//             )
//             .route("/vector/advanced", web::post().to(advanced_vector_search))
//             .route(
//                 "/hybrid",
//                 web::post().to(crate::handlers::hybrid_search::hybrid_search),
//             )
//             .route(
//                 "/vector/statistics",
//                 web::get().to(get_vector_search_statistics),
//             )
//             .route(
//                 "/vector/index/rebuild",
//                 web::post().to(rebuild_vector_index),
//             )
//             .route(
//                 "/vector/storage/optimize",
//                 web::post().to(optimize_vector_storage),
//             ),
//     );
// }

// // 分类管理路由
// pub fn category_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/categories")
//             // 文档分类操作
//             .route("/categorize", web::post().to(categorize_document))
//             .route("/uncategorize", web::post().to(uncategorize_document))
//             // 智能分类
//             .route("/classify", web::post().to(classify_document))
//             .route("/classify/batch", web::post().to(batch_classify_documents))
//             .route(
//                 "/classify/similar",
//                 web::post().to(classify_by_similar_documents),
//             )
//             // 分类建议
//             .route("/suggest", web::get().to(suggest_categories_for_document))
//             // 文档查询
//             .route("/documents", web::get().to(get_document_categories))
//             .route(
//                 "/documents/find",
//                 web::post().to(find_documents_by_categories),
//             )
//             // 统计信息
//             .route("/statistics", web::get().to(get_category_statistics))
//             .route(
//                 "/statistics/classification",
//                 web::get().to(get_classification_statistics),
//             ),
//     );
// }

// // 标签管理路由
// pub fn tag_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/tags")
//             // 文档标签操作
//             .route("/tag", web::post().to(tag_document))
//             .route("/untag", web::post().to(untag_document))
//             // 批量操作
//             .route("/tag/batch", web::post().to(batch_tag_documents))
//             .route("/untag/batch", web::post().to(batch_untag_documents))
//             // 智能标签
//             .route("/smart-tag", web::post().to(smart_tag_document))
//             // 标签建议
//             .route("/suggest", web::get().to(suggest_tags_for_document))
//             // 文档查询
//             .route("/documents", web::get().to(get_document_tags))
//             .route("/documents/find", web::post().to(find_documents_by_tags))
//             // 统计信息
//             .route("/statistics", web::get().to(get_tag_usage_statistics)),
//     );
// }

// // 版本控制路由
// pub fn version_routes(cfg: &mut web::ServiceConfig) {
//     cfg.service(
//         web::scope("/versions")
//             // 版本管理
//             .route("", web::post().to(create_document_version))
//             .route("/{document_id}", web::get().to(get_document_versions))
//             .route(
//                 "/{document_id}/{version}",
//                 web::get().to(get_document_version),
//             )
//             // 版本恢复
//             .route("/restore", web::post().to(restore_document_to_version))
//             // 版本比较
//             .route("/compare", web::get().to(compare_document_versions))
//             // 统计和管理
//             .route(
//                 "/statistics",
//                 web::get().to(get_document_version_statistics),
//             )
//             .route("/cleanup", web::post().to(cleanup_document_versions))
//             .route("/storage-info", web::get().to(get_version_storage_info)),
//     );
// }

pub fn audit_routes(cfg: &mut ServiceConfig) {
    cfg.service(
        scope("/audit")
            .route("/logs", web::get().to(audit::query_audit_logs))
            .route(
                "/logs/{event_id}",
                web::get().to(audit::get_audit_log_detail),
            )
            .route("/stats", web::get().to(audit::get_audit_stats))
            .route(
                "/users/{user_id}/activities",
                web::get().to(audit::get_user_activities),
            )
            .route("/monitor", web::get().to(audit::get_system_monitor)),
    );
}
