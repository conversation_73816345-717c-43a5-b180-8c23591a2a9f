[package]
name = "wisdom-vault-auth"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Authentication and authorization for Wisdom Vault"

[dependencies]
# Workspace crates
wisdom-vault-common = { path = "../wisdom-vault-common" }
wisdom-vault-database = { path = "../wisdom-vault-database" }

# External dependencies
jsonwebtoken = { workspace = true }
bcrypt = { workspace = true }
argon2 = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
tokio = { workspace = true }
validator = { workspace = true }
rand = { workspace = true }
actix-web = { workspace = true }
futures-util = { workspace = true }
actix-session = { workspace = true }
utoipa = { workspace = true }