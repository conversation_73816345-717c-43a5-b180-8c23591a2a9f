use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use wisdom_vault_common::{db::next_id, time::current_millis};
use wisdom_vault_database::cache::CacheService;


/// Session管理服务
pub struct SessionManager {
    cache: CacheService,
    default_timeout_minutes: i64,
}

impl SessionManager {
    pub fn new(cache: CacheService, default_timeout_minutes: i64) -> Self {
        Self {
            cache,
            default_timeout_minutes,
        }
    }

    /// 创建会话并存储到Redis
    pub async fn create_session(
        &mut self,
        user_id: String,
        username: String,
        ip_address: String,
        user_agent: Option<String>,
    ) -> Result<String> {
        let session_id = next_id();
        let session = UserSession::new(user_id, username, ip_address, user_agent);

        let key = self.session_key(&session_id);
        let value = serde_json::to_string(&session)?;

        self.cache
            .set(
                key,
                value,
                Some((self.default_timeout_minutes * 60) as usize),
            )
            .await?;

        Ok(session_id)
    }

    /// 获取会话
    pub async fn get_session(&mut self, session_id: &str) -> Result<Option<UserSession>> {
        let key = self.session_key(session_id);

        match self.cache.get::<String, String>(key.clone()).await? {
            Some(value) => {
                let session: UserSession = serde_json::from_str(&value)?;
                if session.is_valid(self.default_timeout_minutes) {
                    Ok(Some(session))
                } else {
                    // 会话过期，删除
                    let _ = self.cache.delete(key).await;
                    Ok(None)
                }
            }
            None => Ok(None),
        }
    }

    /// 刷新会话
    pub async fn refresh_session(&mut self, session_id: &str) -> Result<bool> {
        if let Some(mut session) = self.get_session(session_id).await? {
            session.refresh();
            let key = self.session_key(session_id);
            let value = serde_json::to_string(&session)?;

            self.cache
                .set(
                    key,
                    value,
                    Some((self.default_timeout_minutes * 60) as usize),
                )
                .await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 删除会话
    pub async fn delete_session(&mut self, session_id: &str) -> Result<bool> {
        let key = self.session_key(session_id);
        self.cache.delete(key).await
    }

    /// 验证会话并获取用户信息
    pub async fn validate_session(
        &mut self,
        session_id: &str,
        ip_address: &str,
    ) -> Result<Option<UserSession>> {
        if let Some(session) = self.get_session(session_id).await? {
            if session.validate_ip(ip_address) && session.is_valid(self.default_timeout_minutes) {
                Ok(Some(session))
            } else {
                // 会话无效，删除
                let _ = self.delete_session(session_id).await;
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    /// 获取用户的所有活跃会话
    pub async fn get_user_sessions(&self, _user_id: &str) -> Result<Vec<String>> {
        // 这里需要实现一个能够查找特定用户所有会话的机制
        // 在真实实现中，可能需要维护一个用户ID到会话ID的映射
        // 简化实现，返回空列表
        Ok(vec![])
    }

    /// 删除用户的所有会话（登出所有设备）
    pub async fn delete_user_sessions(&mut self, user_id: &str) -> Result<u32> {
        let session_ids = self.get_user_sessions(user_id).await?;
        let mut deleted = 0;

        for session_id in session_ids {
            if self.delete_session(&session_id).await? {
                deleted += 1;
            }
        }

        Ok(deleted)
    }

    fn session_key(&self, session_id: &str) -> String {
        format!("session:{}", session_id)
    }
}
