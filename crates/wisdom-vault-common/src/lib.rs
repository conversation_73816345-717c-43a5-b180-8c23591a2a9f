use rust_i18n::Backend;

rust_i18n::i18n!("../../locales");

pub struct I18nBackend;

impl Backend for I18nBackend {
    fn available_locales(&self) -> Vec<&str> {
        _RUST_I18N_BACKEND.available_locales()
    }

    fn translate(&self, locale: &str, key: &str) -> Option<&str> {
        let val = _RUST_I18N_BACKEND.translate(locale, key);
        if val.is_none() {
            _RUST_I18N_BACKEND.translate("en", key)
        } else {
            val
        }
    }
}

#[macro_export]
macro_rules! i18n_init {
    () => {
        rust_i18n::i18n!(backend = wisdom_vault_common::I18nBackend);
    };
}

pub use rust_i18n::set_locale;
pub use rust_i18n::t;

/// 时间戳工具函数
pub mod time {
    use chrono::{DateTime, FixedOffset, TimeZone, Utc};

    /// 默认时区: 东八区
    pub const DEFAULT_TZ: FixedOffset = FixedOffset::west_opt(8 * 3600).unwrap();

    pub fn now() -> DateTime<Utc> {
        Utc::now()
    }

    /// 获取当前时间戳
    pub fn current_millis() -> i64 {
        Utc::now().timestamp_millis()
    }

    /// 将时间戳转换为DateTime<Utc>
    pub fn to_datetime(millis: i64) -> DateTime<Utc> {
        DateTime::from_timestamp_millis(millis).unwrap().to_utc()
    }

    /// 将DateTime转换为时间戳
    pub fn from_datetime<Tz: TimeZone>(dt: DateTime<Tz>) -> i64 {
        dt.timestamp_millis()
    }
}

pub mod db {
    use mongodb::bson::oid::ObjectId;

    pub fn next_id() -> String {
        ObjectId::new().to_hex()
    }
}
