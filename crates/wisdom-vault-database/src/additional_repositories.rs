use crate::{connection::DatabaseConnection, models::*};
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use bson::{doc, oid::ObjectId};
use futures::TryStreamExt;
use mongodb::{Collection, options::FindOptions};
use std::collections::HashMap;
use wisdom_vault_common::time::current_millis;

// MongoDB 集合名称常量
pub const CATEGORIES_COLLECTION: &str = "categories";
pub const TAGS_COLLECTION: &str = "tags";
pub const DOCUMENT_TAGS_COLLECTION: &str = "document_tags";
pub const DOCUMENT_CATEGORIES_COLLECTION: &str = "document_categories";
pub const CONVERSATIONS_COLLECTION: &str = "conversations";
pub const MESSAGES_COLLECTION: &str = "messages";
pub const PROCESSING_TASKS_COLLECTION: &str = "processing_tasks";

// ============================================================================
// Category Repository
// ============================================================================

#[async_trait]
pub trait CategoryRepository {
    async fn create(&self, category: &Category) -> Result<Category>;
    async fn find_by_id(&self, id: &str) -> Result<Option<Category>>;
    async fn find_by_knowledge_base(&self, kb_id: &str) -> Result<Vec<Category>>;
    async fn find_by_parent(&self, parent_id: &str) -> Result<Vec<Category>>;
    async fn find_root_categories(&self, kb_id: &str) -> Result<Vec<Category>>;
    async fn update(&self, category: &Category) -> Result<Category>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn update_document_count(&self, id: &str, delta: i64) -> Result<bool>;
}

pub struct MongoCategoryRepository {
    collection: Collection<Category>,
}

impl MongoCategoryRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(CATEGORIES_COLLECTION),
        }
    }
}

#[async_trait]
impl CategoryRepository for MongoCategoryRepository {
    async fn create(&self, category: &Category) -> Result<Category> {
        let mut new_category = category.clone();

        if new_category.id.is_empty() {
            new_category.id = ObjectId::new().to_hex();
        }

        let now = current_millis();
        new_category.created_at = now;
        new_category.updated_at = now;

        self.collection
            .insert_one(&new_category)
            .await
            .map_err(|e| anyhow!("创建分类失败: {}", e))?;

        Ok(new_category)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<Category>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找分类失败: {}", e))
    }

    async fn find_by_knowledge_base(&self, kb_id: &str) -> Result<Vec<Category>> {
        let filter = doc! { "knowledgeBaseId": kb_id };
        let options = FindOptions::builder()
            .sort(doc! { "sortOrder": 1, "name": 1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按知识库查找分类失败: {}", e))?;

        let mut categories = Vec::new();
        while let Some(category) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历分类失败: {}", e))?
        {
            categories.push(category);
        }

        Ok(categories)
    }

    async fn find_by_parent(&self, parent_id: &str) -> Result<Vec<Category>> {
        let filter = doc! { "parentId": parent_id };
        let options = FindOptions::builder()
            .sort(doc! { "sortOrder": 1, "name": 1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按父分类查找子分类失败: {}", e))?;

        let mut categories = Vec::new();
        while let Some(category) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历子分类失败: {}", e))?
        {
            categories.push(category);
        }

        Ok(categories)
    }

    async fn find_root_categories(&self, kb_id: &str) -> Result<Vec<Category>> {
        let filter = doc! {
            "knowledgeBaseId": kb_id,
            "parentId": bson::Bson::Null
        };
        let options = FindOptions::builder()
            .sort(doc! { "sortOrder": 1, "name": 1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找根分类失败: {}", e))?;

        let mut categories = Vec::new();
        while let Some(category) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历根分类失败: {}", e))?
        {
            categories.push(category);
        }

        Ok(categories)
    }

    async fn update(&self, category: &Category) -> Result<Category> {
        let mut updated_category = category.clone();
        updated_category.updated_at = current_millis();

        let filter = doc! { "_id": &category.id };
        let update = doc! { "$set": bson::to_bson(&updated_category)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新分类失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("分类不存在: {}", category.id));
        }

        Ok(updated_category)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除分类失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn update_document_count(&self, id: &str, delta: i64) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$inc": { "documentCount": delta },
            "$set": { "updatedAt": current_millis() as i64 }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新分类文档数量失败: {}", e))?;

        Ok(result.matched_count > 0)
    }
}

// ============================================================================
// DocumentTag Repository
// ============================================================================

#[async_trait]
pub trait DocumentTagRepository {
    async fn assign_tag(
        &self,
        document_id: &str,
        tag_id: &str,
        tagged_by: &str,
    ) -> Result<DocumentTag>;
    async fn remove_tag(&self, document_id: &str, tag_id: &str) -> Result<bool>;
    async fn find_by_document(&self, document_id: &str) -> Result<Vec<DocumentTag>>;
    async fn find_by_tag(&self, tag_id: &str) -> Result<Vec<DocumentTag>>;
    async fn remove_all_by_document(&self, document_id: &str) -> Result<u64>;
    async fn cleanup_orphaned_tags(&self) -> Result<u64>;
}

pub struct MongoDocumentTagRepository {
    collection: Collection<DocumentTag>,
    db_connection: DatabaseConnection,
}

impl MongoDocumentTagRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(DOCUMENT_TAGS_COLLECTION),
            db_connection: db_connection.clone(),
        }
    }
}

#[async_trait]
impl DocumentTagRepository for MongoDocumentTagRepository {
    async fn assign_tag(
        &self,
        document_id: &str,
        tag_id: &str,
        tagged_by: &str,
    ) -> Result<DocumentTag> {
        let document_tag = DocumentTag {
            id: ObjectId::new().to_hex(),
            document_id: document_id.to_string(),
            tag_id: tag_id.to_string(),
            tagged_by: tagged_by.to_string(),
            tagged_at: current_millis(),
        };

        self.collection
            .insert_one(&document_tag)
            .await
            .map_err(|e| anyhow!("分配标签失败: {}", e))?;

        Ok(document_tag)
    }

    async fn remove_tag(&self, document_id: &str, tag_id: &str) -> Result<bool> {
        let filter = doc! {
            "documentId": document_id,
            "tagId": tag_id
        };

        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("移除标签失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn find_by_document(&self, document_id: &str) -> Result<Vec<DocumentTag>> {
        let filter = doc! { "documentId": document_id };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找文档标签失败: {}", e))?;

        let mut document_tags = Vec::new();
        while let Some(doc_tag) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历文档标签失败: {}", e))?
        {
            document_tags.push(doc_tag);
        }

        Ok(document_tags)
    }

    async fn find_by_tag(&self, tag_id: &str) -> Result<Vec<DocumentTag>> {
        let filter = doc! { "tagId": tag_id };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找标签文档失败: {}", e))?;

        let mut document_tags = Vec::new();
        while let Some(doc_tag) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历标签文档失败: {}", e))?
        {
            document_tags.push(doc_tag);
        }

        Ok(document_tags)
    }

    async fn remove_all_by_document(&self, document_id: &str) -> Result<u64> {
        let filter = doc! { "documentId": document_id };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("移除文档所有标签失败: {}", e))?;

        Ok(result.deleted_count)
    }

    async fn cleanup_orphaned_tags(&self) -> Result<u64> {
        let mut total_deleted = 0u64;

        // 获取所有DocumentTag记录
        let mut cursor = self
            .collection
            .find(doc! {})
            .await
            .map_err(|e| anyhow!("获取文档标签关系失败: {}", e))?;

        let mut orphaned_ids = Vec::new();

        while let Some(doc_tag) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历文档标签关系失败: {}", e))?
        {
            let mut is_orphaned = false;

            // 检查文档是否存在
            let doc_filter = doc! { "_id": &doc_tag.document_id };
            let doc_collection: Collection<bson::Document> =
                self.db_connection.collection("documents");
            if doc_collection
                .find_one(doc_filter)
                .await
                .map_err(|e| anyhow!("检查文档存在性失败: {}", e))?
                .is_none()
            {
                is_orphaned = true;
                tracing::debug!(
                    "发现孤儿文档标签关系: 文档ID {} 不存在",
                    doc_tag.document_id
                );
            }

            // 检查标签是否存在
            if !is_orphaned {
                let tag_filter = doc! { "_id": &doc_tag.tag_id };
                let tag_collection: Collection<bson::Document> =
                    self.db_connection.collection("tags");
                if tag_collection
                    .find_one(tag_filter)
                    .await
                    .map_err(|e| anyhow!("检查标签存在性失败: {}", e))?
                    .is_none()
                {
                    is_orphaned = true;
                    tracing::debug!("发现孤儿文档标签关系: 标签ID {} 不存在", doc_tag.tag_id);
                }
            }

            if is_orphaned {
                orphaned_ids.push(doc_tag.id.clone());
            }
        }

        // 批量删除孤儿记录
        if !orphaned_ids.is_empty() {
            let delete_filter = doc! { "_id": { "$in": &orphaned_ids } };
            let result = self
                .collection
                .delete_many(delete_filter)
                .await
                .map_err(|e| anyhow!("删除孤儿标签关系失败: {}", e))?;

            total_deleted = result.deleted_count;
            tracing::info!("清理了 {} 个孤儿文档标签关系", total_deleted);
        }

        Ok(total_deleted)
    }
}

// ============================================================================
// DocumentCategory Repository
// ============================================================================

#[async_trait]
pub trait DocumentCategoryRepository {
    async fn assign_category(
        &self,
        document_id: &str,
        category_id: &str,
        assigned_by: &str,
        confidence_score: Option<f64>,
    ) -> Result<DocumentCategory>;
    async fn remove_category(&self, document_id: &str, category_id: &str) -> Result<bool>;
    async fn find_by_document(&self, document_id: &str) -> Result<Vec<DocumentCategory>>;
    async fn find_by_category(&self, category_id: &str) -> Result<Vec<DocumentCategory>>;
    async fn remove_all_by_document(&self, document_id: &str) -> Result<u64>;
    async fn categorize_document(
        &self,
        document_id: &str,
        category_id: &str,
        assigned_by: &str,
        confidence_score: Option<f64>,
    ) -> Result<DocumentCategory>;
    async fn find_document_categories(&self, document_id: &str) -> Result<Vec<DocumentCategory>>;
    async fn update_category_confidence(
        &self,
        document_id: &str,
        category_id: &str,
        confidence_score: f64,
    ) -> Result<bool>;
    async fn find_documents_by_categories(
        &self,
        category_ids: &[String],
    ) -> Result<Vec<DocumentCategory>>;
    async fn get_category_document_counts(&self, kb_id: &str) -> Result<HashMap<String, i64>>;
}

pub struct MongoDocumentCategoryRepository {
    collection: Collection<DocumentCategory>,
}

impl MongoDocumentCategoryRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(DOCUMENT_CATEGORIES_COLLECTION),
        }
    }
}

#[async_trait]
impl DocumentCategoryRepository for MongoDocumentCategoryRepository {
    async fn assign_category(
        &self,
        document_id: &str,
        category_id: &str,
        assigned_by: &str,
        confidence_score: Option<f64>,
    ) -> Result<DocumentCategory> {
        let document_category = DocumentCategory {
            id: ObjectId::new().to_hex(),
            document_id: document_id.to_string(),
            category_id: category_id.to_string(),
            assigned_by: assigned_by.to_string(),
            assigned_at: current_millis(),
            confidence_score,
        };

        self.collection
            .insert_one(&document_category)
            .await
            .map_err(|e| anyhow!("分配分类失败: {}", e))?;

        Ok(document_category)
    }

    async fn remove_category(&self, document_id: &str, category_id: &str) -> Result<bool> {
        let filter = doc! {
            "documentId": document_id,
            "categoryId": category_id
        };

        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("移除分类失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn find_by_document(&self, document_id: &str) -> Result<Vec<DocumentCategory>> {
        let filter = doc! { "documentId": document_id };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找文档分类失败: {}", e))?;

        let mut document_categories = Vec::new();
        while let Some(doc_cat) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历文档分类失败: {}", e))?
        {
            document_categories.push(doc_cat);
        }

        Ok(document_categories)
    }

    async fn find_by_category(&self, category_id: &str) -> Result<Vec<DocumentCategory>> {
        let filter = doc! { "categoryId": category_id };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找分类文档失败: {}", e))?;

        let mut document_categories = Vec::new();
        while let Some(doc_cat) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历分类文档失败: {}", e))?
        {
            document_categories.push(doc_cat);
        }

        Ok(document_categories)
    }

    async fn remove_all_by_document(&self, document_id: &str) -> Result<u64> {
        let filter = doc! { "documentId": document_id };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("移除文档所有分类失败: {}", e))?;

        Ok(result.deleted_count)
    }

    async fn categorize_document(
        &self,
        document_id: &str,
        category_id: &str,
        assigned_by: &str,
        confidence_score: Option<f64>,
    ) -> Result<DocumentCategory> {
        // 使用 assign_category 的相同逻辑
        self.assign_category(document_id, category_id, assigned_by, confidence_score)
            .await
    }

    async fn find_document_categories(&self, document_id: &str) -> Result<Vec<DocumentCategory>> {
        // 使用 find_by_document 的相同逻辑
        self.find_by_document(document_id).await
    }

    async fn update_category_confidence(
        &self,
        document_id: &str,
        category_id: &str,
        confidence_score: f64,
    ) -> Result<bool> {
        let filter = doc! {
            "documentId": document_id,
            "categoryId": category_id
        };
        let update = doc! {
            "$set": {
                "confidenceScore": confidence_score,
                "assignedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新分类置信度失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn find_documents_by_categories(
        &self,
        category_ids: &[String],
    ) -> Result<Vec<DocumentCategory>> {
        let filter = doc! { "categoryId": { "$in": category_ids } };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("按分类查找文档失败: {}", e))?;

        let mut document_categories = Vec::new();
        while let Some(doc_cat) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历分类文档失败: {}", e))?
        {
            document_categories.push(doc_cat);
        }

        Ok(document_categories)
    }

    async fn get_category_document_counts(&self, kb_id: &str) -> Result<HashMap<String, i64>> {
        let pipeline = vec![
            doc! {
                "$lookup": {
                    "from": "categories",
                    "localField": "categoryId",
                    "foreignField": "_id",
                    "as": "category"
                }
            },
            doc! {
                "$match": {
                    "category.knowledgeBaseId": kb_id
                }
            },
            doc! {
                "$group": {
                    "_id": "$categoryId",
                    "count": { "$sum": 1 }
                }
            },
        ];

        let mut cursor = self
            .collection
            .aggregate(pipeline)
            .await
            .map_err(|e| anyhow!("统计分类文档数量失败: {}", e))?;

        let mut counts = HashMap::new();
        while let Some(result) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历统计结果失败: {}", e))?
        {
            if let (Ok(category_id), Ok(count)) = (result.get_str("_id"), result.get_i64("count")) {
                counts.insert(category_id.to_string(), count);
            }
        }

        Ok(counts)
    }
}

// ============================================================================
// Conversation Repository
// ============================================================================

#[async_trait]
pub trait ConversationRepository {
    async fn create(&self, conversation: &Conversation) -> Result<Conversation>;
    async fn find_by_id(&self, id: &str) -> Result<Option<Conversation>>;
    async fn find_by_user(
        &self,
        user_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>>;
    async fn update(&self, conversation: &Conversation) -> Result<Conversation>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn update_status(&self, id: &str, status: ConversationStatus) -> Result<bool>;
    async fn update_statistics(&self, id: &str, stats: &ConversationStatistics) -> Result<bool>;
    async fn search(
        &self,
        user_id: &str,
        query: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>>;
}

pub struct MongoConversationRepository {
    collection: Collection<Conversation>,
}

impl MongoConversationRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(CONVERSATIONS_COLLECTION),
        }
    }
}

#[async_trait]
impl ConversationRepository for MongoConversationRepository {
    async fn create(&self, conversation: &Conversation) -> Result<Conversation> {
        let mut new_conversation = conversation.clone();

        if new_conversation.id.is_empty() {
            new_conversation.id = ObjectId::new().to_hex();
        }

        let now = current_millis();
        new_conversation.created_at = now;
        new_conversation.updated_at = now;

        self.collection
            .insert_one(&new_conversation)
            .await
            .map_err(|e| anyhow!("创建对话失败: {}", e))?;

        Ok(new_conversation)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<Conversation>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找对话失败: {}", e))
    }

    async fn find_by_user(
        &self,
        user_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>> {
        let filter = doc! { "userId": user_id };
        let mut options = FindOptions::builder()
            .sort(doc! { "lastMessageAt": -1, "updatedAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找用户对话失败: {}", e))?;

        let mut conversations = Vec::new();
        while let Some(conversation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历对话失败: {}", e))?
        {
            conversations.push(conversation);
        }

        Ok(conversations)
    }

    async fn update(&self, conversation: &Conversation) -> Result<Conversation> {
        let mut updated_conversation = conversation.clone();
        updated_conversation.updated_at = current_millis();

        let filter = doc! { "_id": &conversation.id };
        let update = doc! { "$set": bson::to_bson(&updated_conversation)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新对话失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("对话不存在: {}", conversation.id));
        }

        Ok(updated_conversation)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除对话失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn update_status(&self, id: &str, status: ConversationStatus) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": {
                "status": bson::to_bson(&status)?,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新对话状态失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn update_statistics(&self, id: &str, stats: &ConversationStatistics) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": {
                "statistics": bson::to_bson(stats)?,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新对话统计信息失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn search(
        &self,
        user_id: &str,
        query: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>> {
        let filter = doc! {
            "userId": user_id,
            "$or": [
                { "title": { "$regex": query, "$options": "i" } },
                { "summary": { "$regex": query, "$options": "i" } }
            ]
        };

        let mut options = FindOptions::builder()
            .sort(doc! { "lastMessageAt": -1, "updatedAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("搜索对话失败: {}", e))?;

        let mut conversations = Vec::new();
        while let Some(conversation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历搜索结果失败: {}", e))?
        {
            conversations.push(conversation);
        }

        Ok(conversations)
    }
}

// ============================================================================
// Message Repository
// ============================================================================

#[async_trait]
pub trait MessageRepository {
    async fn create(&self, message: &Message) -> Result<Message>;
    async fn find_by_id(&self, id: &str) -> Result<Option<Message>>;
    async fn find_by_conversation(
        &self,
        conversation_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Message>>;
    async fn update(&self, message: &Message) -> Result<Message>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn delete_by_conversation(&self, conversation_id: &str) -> Result<u64>;
    async fn count_by_conversation(&self, conversation_id: &str) -> Result<i64>;
    async fn find_latest_by_conversation(
        &self,
        conversation_id: &str,
        limit: u32,
    ) -> Result<Vec<Message>>;
}

pub struct MongoMessageRepository {
    collection: Collection<Message>,
}

impl MongoMessageRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(MESSAGES_COLLECTION),
        }
    }
}

#[async_trait]
impl MessageRepository for MongoMessageRepository {
    async fn create(&self, message: &Message) -> Result<Message> {
        let mut new_message = message.clone();

        if new_message.id.is_empty() {
            new_message.id = ObjectId::new().to_hex();
        }

        let now = current_millis();
        new_message.created_at = now;
        new_message.updated_at = now;

        self.collection
            .insert_one(&new_message)
            .await
            .map_err(|e| anyhow!("创建消息失败: {}", e))?;

        Ok(new_message)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<Message>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找消息失败: {}", e))
    }

    async fn find_by_conversation(
        &self,
        conversation_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Message>> {
        let filter = doc! { "conversationId": conversation_id };
        let mut options = FindOptions::builder().sort(doc! { "createdAt": 1 }).build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找对话消息失败: {}", e))?;

        let mut messages = Vec::new();
        while let Some(message) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历消息失败: {}", e))?
        {
            messages.push(message);
        }

        Ok(messages)
    }

    async fn update(&self, message: &Message) -> Result<Message> {
        let mut updated_message = message.clone();
        updated_message.updated_at = current_millis();

        let filter = doc! { "_id": &message.id };
        let update = doc! { "$set": bson::to_bson(&updated_message)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新消息失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("消息不存在: {}", message.id));
        }

        Ok(updated_message)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除消息失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn delete_by_conversation(&self, conversation_id: &str) -> Result<u64> {
        let filter = doc! { "conversationId": conversation_id };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("删除对话消息失败: {}", e))?;

        Ok(result.deleted_count)
    }

    async fn count_by_conversation(&self, conversation_id: &str) -> Result<i64> {
        let filter = doc! { "conversationId": conversation_id };
        self.collection
            .count_documents(filter)
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计对话消息数量失败: {}", e))
    }

    async fn find_latest_by_conversation(
        &self,
        conversation_id: &str,
        limit: u32,
    ) -> Result<Vec<Message>> {
        let filter = doc! { "conversationId": conversation_id };
        let options = FindOptions::builder()
            .sort(doc! { "createdAt": -1 })
            .limit(limit as i64)
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找最新消息失败: {}", e))?;

        let mut messages = Vec::new();
        while let Some(message) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历最新消息失败: {}", e))?
        {
            messages.push(message);
        }

        // 按时间正序返回
        messages.reverse();
        Ok(messages)
    }
}

// ============================================================================
// ProcessingTask Repository
// ============================================================================

#[derive(Debug, Clone, Default, serde::Serialize)]
pub struct TaskStatistics {
    pub total_tasks: i64,
    pub queued_tasks: i64,
    pub running_tasks: i64,
    pub completed_tasks: i64,
    pub failed_tasks: i64,
    pub cancelled_tasks: i64,
    pub retrying_tasks: i64,
}

#[async_trait]
pub trait ProcessingTaskRepository {
    async fn create(&self, task: &ProcessingTask) -> Result<ProcessingTask>;
    async fn find_by_id(&self, id: &str) -> Result<Option<ProcessingTask>>;
    async fn update(&self, task: &ProcessingTask) -> Result<ProcessingTask>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn find_by_status(
        &self,
        status: &TaskStatus,
        limit: Option<u32>,
    ) -> Result<Vec<ProcessingTask>>;
    async fn find_by_resource(&self, resource_id: &str) -> Result<Vec<ProcessingTask>>;
    async fn update_status(
        &self,
        id: &str,
        status: TaskStatus,
        error_message: Option<String>,
    ) -> Result<bool>;
    async fn update_progress(&self, id: &str, progress: f64) -> Result<bool>;
    async fn find_pending_tasks(&self, limit: u32) -> Result<Vec<ProcessingTask>>;
    async fn find_retry_tasks(&self) -> Result<Vec<ProcessingTask>>;
    async fn get_statistics(&self) -> Result<TaskStatistics>;
}

pub struct MongoProcessingTaskRepository {
    collection: Collection<ProcessingTask>,
}

impl MongoProcessingTaskRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(PROCESSING_TASKS_COLLECTION),
        }
    }
}

#[async_trait]
impl ProcessingTaskRepository for MongoProcessingTaskRepository {
    async fn create(&self, task: &ProcessingTask) -> Result<ProcessingTask> {
        let mut new_task = task.clone();

        if new_task.id.is_empty() {
            new_task.id = ObjectId::new().to_hex();
        }

        new_task.created_at = current_millis();

        self.collection
            .insert_one(&new_task)
            .await
            .map_err(|e| anyhow!("创建处理任务失败: {}", e))?;

        Ok(new_task)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<ProcessingTask>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找处理任务失败: {}", e))
    }

    async fn update(&self, task: &ProcessingTask) -> Result<ProcessingTask> {
        let filter = doc! { "_id": &task.id };
        let update = doc! { "$set": bson::to_bson(task)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新处理任务失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("处理任务不存在: {}", task.id));
        }

        Ok(task.clone())
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除处理任务失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn find_by_status(
        &self,
        status: &TaskStatus,
        limit: Option<u32>,
    ) -> Result<Vec<ProcessingTask>> {
        let filter = doc! { "status": bson::to_bson(status)? };
        let mut options = FindOptions::builder()
            .sort(doc! { "priority": -1, "createdAt": 1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按状态查找处理任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历处理任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn find_by_resource(&self, resource_id: &str) -> Result<Vec<ProcessingTask>> {
        let filter = doc! { "resourceId": resource_id };
        let options = FindOptions::builder()
            .sort(doc! { "createdAt": -1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按资源查找处理任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历资源任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn update_status(
        &self,
        id: &str,
        status: TaskStatus,
        error_message: Option<String>,
    ) -> Result<bool> {
        let filter = doc! { "_id": id };
        let mut update_doc = doc! { "status": bson::to_bson(&status)? };

        if let Some(error) = error_message {
            update_doc.insert("errorMessage", error);
        }

        // 设置完成时间
        if status == TaskStatus::Completed || status == TaskStatus::Failed {
            update_doc.insert("completedAt", current_millis() as i64);
        }

        let update = doc! { "$set": update_doc };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新任务状态失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn update_progress(&self, id: &str, progress: f64) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! { "$set": { "progress": progress } };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新任务进度失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn find_pending_tasks(&self, limit: u32) -> Result<Vec<ProcessingTask>> {
        let filter = doc! { "status": bson::to_bson(&TaskStatus::Queued)? };
        let options = FindOptions::builder()
            .sort(doc! { "priority": -1, "createdAt": 1 })
            .limit(limit as i64)
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找待处理任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历待处理任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn find_retry_tasks(&self) -> Result<Vec<ProcessingTask>> {
        let now = current_millis() as i64;
        let filter = doc! {
            "status": bson::to_bson(&TaskStatus::Retrying)?,
            "nextRetryAt": { "$lte": now }
        };
        let options = FindOptions::builder()
            .sort(doc! { "priority": -1, "nextRetryAt": 1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找重试任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历重试任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn get_statistics(&self) -> Result<TaskStatistics> {
        let pipeline = vec![doc! {
            "$group": {
                "_id": "$status",
                "count": { "$sum": 1 }
            }
        }];

        let mut cursor = self
            .collection
            .aggregate(pipeline)
            .await
            .map_err(|e| anyhow!("统计任务数据失败: {}", e))?;

        let mut stats = TaskStatistics::default();

        while let Some(result) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历统计结果失败: {}", e))?
        {
            if let (Ok(status), Ok(count)) = (
                bson::from_bson::<TaskStatus>(result.get("_id").unwrap().clone()),
                result.get_i64("count"),
            ) {
                match status {
                    TaskStatus::Queued => stats.queued_tasks = count,
                    TaskStatus::Running => stats.running_tasks = count,
                    TaskStatus::Completed => stats.completed_tasks = count,
                    TaskStatus::Failed => stats.failed_tasks = count,
                    TaskStatus::Cancelled => stats.cancelled_tasks = count,
                    TaskStatus::Retrying => stats.retrying_tasks = count,
                }
                stats.total_tasks += count;
            }
        }

        Ok(stats)
    }
}
