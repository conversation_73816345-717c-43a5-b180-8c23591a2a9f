use crate::connection::DatabaseConnection;
use anyhow::{Result, anyhow};
use bson::doc;
use mongodb::IndexModel;
use mongodb::options::IndexOptions;
use tracing::{error, info};

/// MongoDB 索引管理器
pub struct IndexManager {
    db_connection: DatabaseConnection,
}

impl IndexManager {
    /// 创建新的索引管理器
    pub fn new(db_connection: DatabaseConnection) -> Self {
        Self { db_connection }
    }

    /// 创建所有必要的索引
    pub async fn create_all_indexes(&self) -> Result<()> {
        info!("开始创建MongoDB索引...");

        // 创建各个集合的索引
        self.create_user_indexes().await?;
        self.create_role_indexes().await?;
        self.create_permission_indexes().await?;
        self.create_knowledge_base_indexes().await?;
        self.create_document_indexes().await?;
        self.create_document_chunk_indexes().await?;
        self.create_tag_indexes().await?;
        self.create_category_indexes().await?;
        self.create_document_tag_indexes().await?;
        self.create_document_category_indexes().await?;
        self.create_conversation_indexes().await?;
        self.create_message_indexes().await?;
        self.create_processing_task_indexes().await?;

        info!("所有MongoDB索引创建完成");
        Ok(())
    }

    /// 用户集合索引
    async fn create_user_indexes(&self) -> Result<()> {
        let collection = self.db_connection.collection::<bson::Document>("users");

        let indexes = vec![
            // 邮箱唯一索引
            IndexModel::builder()
                .keys(doc! { "email": 1 })
                .options(
                    IndexOptions::builder()
                        .unique(true)
                        .name("email_unique".to_string())
                        .build(),
                )
                .build(),
            // 用户名唯一索引
            IndexModel::builder()
                .keys(doc! { "username": 1 })
                .options(
                    IndexOptions::builder()
                        .unique(true)
                        .name("username_unique".to_string())
                        .build(),
                )
                .build(),
            // 部门索引
            IndexModel::builder()
                .keys(doc! { "departmentId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("department_id_index".to_string())
                        .build(),
                )
                .build(),
            // 活跃状态索引
            IndexModel::builder()
                .keys(doc! { "isActive": 1 })
                .options(
                    IndexOptions::builder()
                        .name("is_active_index".to_string())
                        .build(),
                )
                .build(),
            // 创建时间索引
            IndexModel::builder()
                .keys(doc! { "createdAt": -1 })
                .options(
                    IndexOptions::builder()
                        .name("created_at_desc_index".to_string())
                        .build(),
                )
                .build(),
            // 全文搜索索引
            IndexModel::builder()
                .keys(doc! {
                    "username": "text",
                    "email": "text",
                    "fullName": "text"
                })
                .options(
                    IndexOptions::builder()
                        .name("user_text_search".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建用户索引失败: {}", e))?;

        info!("用户集合索引创建完成");
        Ok(())
    }

    /// 角色集合索引
    async fn create_role_indexes(&self) -> Result<()> {
        let collection = self.db_connection.collection::<bson::Document>("roles");

        let indexes = vec![
            // 角色名唯一索引
            IndexModel::builder()
                .keys(doc! { "name": 1 })
                .options(
                    IndexOptions::builder()
                        .unique(true)
                        .name("role_name_unique".to_string())
                        .build(),
                )
                .build(),
            // 系统角色索引
            IndexModel::builder()
                .keys(doc! { "isSystem": 1 })
                .options(
                    IndexOptions::builder()
                        .name("is_system_index".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建角色索引失败: {}", e))?;

        info!("角色集合索引创建完成");
        Ok(())
    }

    /// 权限集合索引
    async fn create_permission_indexes(&self) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>("permissions");

        let indexes = vec![
            // 权限名唯一索引
            IndexModel::builder()
                .keys(doc! { "name": 1 })
                .options(
                    IndexOptions::builder()
                        .unique(true)
                        .name("permission_name_unique".to_string())
                        .build(),
                )
                .build(),
            // 资源和操作组合索引
            IndexModel::builder()
                .keys(doc! { "resource": 1, "action": 1 })
                .options(
                    IndexOptions::builder()
                        .name("resource_action_index".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建权限索引失败: {}", e))?;

        info!("权限集合索引创建完成");
        Ok(())
    }

    /// 知识库集合索引
    async fn create_knowledge_base_indexes(&self) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>("knowledge_bases");

        let indexes = vec![
            // 组织ID索引
            IndexModel::builder()
                .keys(doc! { "organizationId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("organization_id_index".to_string())
                        .build(),
                )
                .build(),
            // 拥有者索引
            IndexModel::builder()
                .keys(doc! { "ownerId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("owner_id_index".to_string())
                        .build(),
                )
                .build(),
            // 可见性索引
            IndexModel::builder()
                .keys(doc! { "visibility": 1 })
                .options(
                    IndexOptions::builder()
                        .name("visibility_index".to_string())
                        .build(),
                )
                .build(),
            // 创建时间索引
            IndexModel::builder()
                .keys(doc! { "createdAt": -1 })
                .options(
                    IndexOptions::builder()
                        .name("created_at_desc_index".to_string())
                        .build(),
                )
                .build(),
            // 组织和拥有者组合索引
            IndexModel::builder()
                .keys(doc! { "organizationId": 1, "ownerId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("org_owner_index".to_string())
                        .build(),
                )
                .build(),
            // 全文搜索索引
            IndexModel::builder()
                .keys(doc! {
                    "name": "text",
                    "description": "text"
                })
                .options(
                    IndexOptions::builder()
                        .name("kb_text_search".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建知识库索引失败: {}", e))?;

        info!("知识库集合索引创建完成");
        Ok(())
    }

    /// 文档集合索引
    async fn create_document_indexes(&self) -> Result<()> {
        let collection = self.db_connection.collection::<bson::Document>("documents");

        let indexes = vec![
            // 知识库ID索引
            IndexModel::builder()
                .keys(doc! { "knowledgeBaseId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("knowledge_base_id_index".to_string())
                        .build(),
                )
                .build(),
            // 状态索引
            IndexModel::builder()
                .keys(doc! { "status": 1 })
                .options(
                    IndexOptions::builder()
                        .name("status_index".to_string())
                        .build(),
                )
                .build(),
            // 文件类型索引
            IndexModel::builder()
                .keys(doc! { "fileType": 1 })
                .options(
                    IndexOptions::builder()
                        .name("file_type_index".to_string())
                        .build(),
                )
                .build(),
            // 上传者索引
            IndexModel::builder()
                .keys(doc! { "uploadedBy": 1 })
                .options(
                    IndexOptions::builder()
                        .name("uploaded_by_index".to_string())
                        .build(),
                )
                .build(),
            // 创建时间索引
            IndexModel::builder()
                .keys(doc! { "createdAt": -1 })
                .options(
                    IndexOptions::builder()
                        .name("created_at_desc_index".to_string())
                        .build(),
                )
                .build(),
            // 索引时间索引
            IndexModel::builder()
                .keys(doc! { "indexedAt": -1 })
                .options(
                    IndexOptions::builder()
                        .name("indexed_at_desc_index".to_string())
                        .build(),
                )
                .build(),
            // 知识库和状态组合索引
            IndexModel::builder()
                .keys(doc! { "knowledgeBaseId": 1, "status": 1 })
                .options(
                    IndexOptions::builder()
                        .name("kb_status_index".to_string())
                        .build(),
                )
                .build(),
            // 知识库和创建时间组合索引
            IndexModel::builder()
                .keys(doc! { "knowledgeBaseId": 1, "createdAt": -1 })
                .options(
                    IndexOptions::builder()
                        .name("kb_created_at_index".to_string())
                        .build(),
                )
                .build(),
            // 全文搜索索引
            IndexModel::builder()
                .keys(doc! {
                    "title": "text",
                    "content": "text",
                    "summary": "text"
                })
                .options(
                    IndexOptions::builder()
                        .name("document_text_search".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建文档索引失败: {}", e))?;

        info!("文档集合索引创建完成");
        Ok(())
    }

    /// 文档分块集合索引
    async fn create_document_chunk_indexes(&self) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>("document_chunks");

        let indexes = vec![
            // 文档ID索引
            IndexModel::builder()
                .keys(doc! { "documentId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("document_id_index".to_string())
                        .build(),
                )
                .build(),
            // 分块类型索引
            IndexModel::builder()
                .keys(doc! { "chunkType": 1 })
                .options(
                    IndexOptions::builder()
                        .name("chunk_type_index".to_string())
                        .build(),
                )
                .build(),
            // 文档ID和分块索引组合索引
            IndexModel::builder()
                .keys(doc! { "documentId": 1, "chunkIndex": 1 })
                .options(
                    IndexOptions::builder()
                        .name("document_chunk_index".to_string())
                        .build(),
                )
                .build(),
            // 质量分数索引
            IndexModel::builder()
                .keys(doc! { "metadata.qualityScore": -1 })
                .options(
                    IndexOptions::builder()
                        .name("quality_score_index".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建文档分块索引失败: {}", e))?;

        info!("文档分块集合索引创建完成");
        Ok(())
    }

    /// 标签集合索引
    async fn create_tag_indexes(&self) -> Result<()> {
        let collection = self.db_connection.collection::<bson::Document>("tags");

        let indexes = vec![
            // 知识库ID索引
            IndexModel::builder()
                .keys(doc! { "knowledgeBaseId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("knowledge_base_id_index".to_string())
                        .build(),
                )
                .build(),
            // 知识库和名称组合唯一索引
            IndexModel::builder()
                .keys(doc! { "knowledgeBaseId": 1, "name": 1 })
                .options(
                    IndexOptions::builder()
                        .unique(true)
                        .name("kb_tag_name_unique".to_string())
                        .build(),
                )
                .build(),
            // 使用次数索引
            IndexModel::builder()
                .keys(doc! { "usageCount": -1 })
                .options(
                    IndexOptions::builder()
                        .name("usage_count_desc_index".to_string())
                        .build(),
                )
                .build(),
            // 创建者索引
            IndexModel::builder()
                .keys(doc! { "createdBy": 1 })
                .options(
                    IndexOptions::builder()
                        .name("created_by_index".to_string())
                        .build(),
                )
                .build(),
            // 全文搜索索引
            IndexModel::builder()
                .keys(doc! {
                    "name": "text",
                    "displayName": "text",
                    "description": "text"
                })
                .options(
                    IndexOptions::builder()
                        .name("tag_text_search".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建标签索引失败: {}", e))?;

        info!("标签集合索引创建完成");
        Ok(())
    }

    /// 分类集合索引
    async fn create_category_indexes(&self) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>("categories");

        let indexes = vec![
            // 知识库ID索引
            IndexModel::builder()
                .keys(doc! { "knowledgeBaseId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("knowledge_base_id_index".to_string())
                        .build(),
                )
                .build(),
            // 父分类索引
            IndexModel::builder()
                .keys(doc! { "parentId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("parent_id_index".to_string())
                        .build(),
                )
                .build(),
            // 知识库和父分类组合索引
            IndexModel::builder()
                .keys(doc! { "knowledgeBaseId": 1, "parentId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("kb_parent_index".to_string())
                        .build(),
                )
                .build(),
            // 排序顺序索引
            IndexModel::builder()
                .keys(doc! { "sortOrder": 1 })
                .options(
                    IndexOptions::builder()
                        .name("sort_order_index".to_string())
                        .build(),
                )
                .build(),
            // 文档数量索引
            IndexModel::builder()
                .keys(doc! { "documentCount": -1 })
                .options(
                    IndexOptions::builder()
                        .name("document_count_desc_index".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建分类索引失败: {}", e))?;

        info!("分类集合索引创建完成");
        Ok(())
    }

    /// 文档标签关联集合索引
    async fn create_document_tag_indexes(&self) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>("document_tags");

        let indexes = vec![
            // 文档ID索引
            IndexModel::builder()
                .keys(doc! { "documentId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("document_id_index".to_string())
                        .build(),
                )
                .build(),
            // 标签ID索引
            IndexModel::builder()
                .keys(doc! { "tagId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("tag_id_index".to_string())
                        .build(),
                )
                .build(),
            // 文档和标签组合唯一索引
            IndexModel::builder()
                .keys(doc! { "documentId": 1, "tagId": 1 })
                .options(
                    IndexOptions::builder()
                        .unique(true)
                        .name("document_tag_unique".to_string())
                        .build(),
                )
                .build(),
            // 标记时间索引
            IndexModel::builder()
                .keys(doc! { "taggedAt": -1 })
                .options(
                    IndexOptions::builder()
                        .name("tagged_at_desc_index".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建文档标签索引失败: {}", e))?;

        info!("文档标签集合索引创建完成");
        Ok(())
    }

    /// 文档分类关联集合索引
    async fn create_document_category_indexes(&self) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>("document_categories");

        let indexes = vec![
            // 文档ID索引
            IndexModel::builder()
                .keys(doc! { "documentId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("document_id_index".to_string())
                        .build(),
                )
                .build(),
            // 分类ID索引
            IndexModel::builder()
                .keys(doc! { "categoryId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("category_id_index".to_string())
                        .build(),
                )
                .build(),
            // 文档和分类组合唯一索引
            IndexModel::builder()
                .keys(doc! { "documentId": 1, "categoryId": 1 })
                .options(
                    IndexOptions::builder()
                        .unique(true)
                        .name("document_category_unique".to_string())
                        .build(),
                )
                .build(),
            // 置信度分数索引
            IndexModel::builder()
                .keys(doc! { "confidenceScore": -1 })
                .options(
                    IndexOptions::builder()
                        .name("confidence_score_desc_index".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建文档分类索引失败: {}", e))?;

        info!("文档分类集合索引创建完成");
        Ok(())
    }

    /// 对话集合索引
    async fn create_conversation_indexes(&self) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>("conversations");

        let indexes = vec![
            // 用户ID索引
            IndexModel::builder()
                .keys(doc! { "userId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("user_id_index".to_string())
                        .build(),
                )
                .build(),
            // 知识库ID索引
            IndexModel::builder()
                .keys(doc! { "knowledgeBaseId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("knowledge_base_id_index".to_string())
                        .build(),
                )
                .build(),
            // 状态索引
            IndexModel::builder()
                .keys(doc! { "status": 1 })
                .options(
                    IndexOptions::builder()
                        .name("status_index".to_string())
                        .build(),
                )
                .build(),
            // 最后消息时间索引
            IndexModel::builder()
                .keys(doc! { "lastMessageAt": -1 })
                .options(
                    IndexOptions::builder()
                        .name("last_message_at_desc_index".to_string())
                        .build(),
                )
                .build(),
            // 用户和最后消息时间组合索引
            IndexModel::builder()
                .keys(doc! { "userId": 1, "lastMessageAt": -1 })
                .options(
                    IndexOptions::builder()
                        .name("user_last_message_index".to_string())
                        .build(),
                )
                .build(),
            // 全文搜索索引
            IndexModel::builder()
                .keys(doc! {
                    "title": "text",
                    "summary": "text"
                })
                .options(
                    IndexOptions::builder()
                        .name("conversation_text_search".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建对话索引失败: {}", e))?;

        info!("对话集合索引创建完成");
        Ok(())
    }

    /// 消息集合索引
    async fn create_message_indexes(&self) -> Result<()> {
        let collection = self.db_connection.collection::<bson::Document>("messages");

        let indexes = vec![
            // 对话ID索引
            IndexModel::builder()
                .keys(doc! { "conversationId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("conversation_id_index".to_string())
                        .build(),
                )
                .build(),
            // 角色索引
            IndexModel::builder()
                .keys(doc! { "role": 1 })
                .options(
                    IndexOptions::builder()
                        .name("role_index".to_string())
                        .build(),
                )
                .build(),
            // 消息类型索引
            IndexModel::builder()
                .keys(doc! { "messageType": 1 })
                .options(
                    IndexOptions::builder()
                        .name("message_type_index".to_string())
                        .build(),
                )
                .build(),
            // 创建时间索引
            IndexModel::builder()
                .keys(doc! { "createdAt": 1 })
                .options(
                    IndexOptions::builder()
                        .name("created_at_asc_index".to_string())
                        .build(),
                )
                .build(),
            // 对话和创建时间组合索引
            IndexModel::builder()
                .keys(doc! { "conversationId": 1, "createdAt": 1 })
                .options(
                    IndexOptions::builder()
                        .name("conversation_created_at_index".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建消息索引失败: {}", e))?;

        info!("消息集合索引创建完成");
        Ok(())
    }

    /// 处理任务集合索引
    async fn create_processing_task_indexes(&self) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>("processing_tasks");

        let indexes = vec![
            // 任务类型索引
            IndexModel::builder()
                .keys(doc! { "taskType": 1 })
                .options(
                    IndexOptions::builder()
                        .name("task_type_index".to_string())
                        .build(),
                )
                .build(),
            // 状态索引
            IndexModel::builder()
                .keys(doc! { "status": 1 })
                .options(
                    IndexOptions::builder()
                        .name("status_index".to_string())
                        .build(),
                )
                .build(),
            // 资源ID索引
            IndexModel::builder()
                .keys(doc! { "resourceId": 1 })
                .options(
                    IndexOptions::builder()
                        .name("resource_id_index".to_string())
                        .build(),
                )
                .build(),
            // 优先级索引
            IndexModel::builder()
                .keys(doc! { "priority": -1 })
                .options(
                    IndexOptions::builder()
                        .name("priority_desc_index".to_string())
                        .build(),
                )
                .build(),
            // 状态和优先级组合索引
            IndexModel::builder()
                .keys(doc! { "status": 1, "priority": -1 })
                .options(
                    IndexOptions::builder()
                        .name("status_priority_index".to_string())
                        .build(),
                )
                .build(),
            // 重试时间索引
            IndexModel::builder()
                .keys(doc! { "nextRetryAt": 1 })
                .options(
                    IndexOptions::builder()
                        .name("next_retry_at_index".to_string())
                        .build(),
                )
                .build(),
            // 创建时间索引
            IndexModel::builder()
                .keys(doc! { "createdAt": 1 })
                .options(
                    IndexOptions::builder()
                        .name("created_at_asc_index".to_string())
                        .build(),
                )
                .build(),
        ];

        collection
            .create_indexes(indexes)
            .await
            .map_err(|e| anyhow!("创建处理任务索引失败: {}", e))?;

        info!("处理任务集合索引创建完成");
        Ok(())
    }

    /// 删除所有索引
    pub async fn drop_all_indexes(&self) -> Result<()> {
        info!("开始删除所有MongoDB索引...");

        let collections = vec![
            "users",
            "roles",
            "permissions",
            "knowledge_bases",
            "documents",
            "document_chunks",
            "tags",
            "categories",
            "document_tags",
            "document_categories",
            "conversations",
            "messages",
            "processing_tasks",
        ];

        for collection_name in collections {
            match self.drop_collection_indexes(collection_name).await {
                Ok(_) => info!("删除 {} 集合索引成功", collection_name),
                Err(e) => error!("删除 {} 集合索引失败: {}", collection_name, e),
            }
        }

        info!("所有MongoDB索引删除完成");
        Ok(())
    }

    /// 删除单个集合的索引
    async fn drop_collection_indexes(&self, collection_name: &str) -> Result<()> {
        let collection = self
            .db_connection
            .collection::<bson::Document>(collection_name);

        collection
            .drop_indexes()
            .await
            .map_err(|e| anyhow!("删除 {} 集合索引失败: {}", collection_name, e))?;

        Ok(())
    }

    /// 获取集合索引信息
    pub async fn list_collection_indexes(&self, collection_name: &str) -> Result<Vec<String>> {
        // 简化实现，暂时返回空列表
        // 在实际使用时可以通过数据库管理工具查看索引
        info!("列出 {} 集合的索引信息", collection_name);
        Ok(vec!["_id_".to_string()]) // MongoDB 默认都有 _id 索引
    }

    /// 获取所有集合的索引信息
    pub async fn list_all_indexes(&self) -> Result<std::collections::HashMap<String, Vec<String>>> {
        let collections = vec![
            "users",
            "roles",
            "permissions",
            "knowledge_bases",
            "documents",
            "document_chunks",
            "tags",
            "categories",
            "document_tags",
            "document_categories",
            "conversations",
            "messages",
            "processing_tasks",
        ];

        let mut all_indexes = std::collections::HashMap::new();

        for collection_name in collections {
            match self.list_collection_indexes(collection_name).await {
                Ok(indexes) => {
                    all_indexes.insert(collection_name.to_string(), indexes);
                }
                Err(e) => {
                    error!("获取 {} 集合索引失败: {}", collection_name, e);
                    all_indexes.insert(collection_name.to_string(), Vec::new());
                }
            }
        }

        Ok(all_indexes)
    }
}
