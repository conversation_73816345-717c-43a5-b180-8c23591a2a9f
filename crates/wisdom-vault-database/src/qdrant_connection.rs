use std::sync::Arc;

use anyhow::{Result, anyhow};
use qdrant_client::{
    Qdrant,
    qdrant::{
        CountPoints, DeletePoints, Distance, Filter, PointStruct, ScoredPoint, SearchPoints,
        UpsertPoints,
    },
};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// Qdrant 数据库连接配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QdrantConfig {
    /// Qdrant 连接字符串
    pub url: String,
    /// API Key (可选)
    pub api_key: Option<String>,
    /// 连接超时时间（毫秒）
    pub connect_timeout_ms: Option<u64>,
    /// 请求超时时间（毫秒）
    pub request_timeout_ms: Option<u64>,
    /// 是否启用TLS
    pub enable_tls: bool,
}

impl Default for QdrantConfig {
    fn default() -> Self {
        Self {
            url: "http://localhost:6334".to_string(),
            api_key: None,
            connect_timeout_ms: Some(10_000),
            request_timeout_ms: Some(30_000),
            enable_tls: false,
        }
    }
}

/// Qdrant 数据库连接管理器
#[derive(Clone)]
pub struct QdrantConnection {
    client: Arc<Qdrant>,
}

impl QdrantConnection {
    /// 创建新的 Qdrant 连接
    pub async fn new(config: QdrantConfig) -> Result<Self> {
        info!("正在连接到 Qdrant: {}", config.url);

        // 创建客户端配置
        let client_config = if let Some(api_key) = &config.api_key {
            qdrant_client::config::QdrantConfig::from_url(&config.url).api_key(api_key.clone())
        } else {
            qdrant_client::config::QdrantConfig::from_url(&config.url)
        };

        // 创建客户端
        let client = Arc::new(
            Qdrant::new(client_config).map_err(|e| anyhow!("创建 Qdrant 客户端失败: {}", e))?,
        );

        // 测试连接
        Self::test_connection(&client).await?;

        info!("成功连接到 Qdrant 数据库");

        Ok(Self { client })
    }

    /// 获取 Qdrant 客户端
    pub fn client(&self) -> &Arc<Qdrant> {
        &self.client
    }

    /// 测试数据库连接
    async fn test_connection(client: &Qdrant) -> Result<()> {
        match client.health_check().await {
            Ok(_) => {
                info!("Qdrant 连接测试成功");
                Ok(())
            }
            Err(e) => {
                error!("Qdrant 连接测试失败: {}", e);
                Err(anyhow!("Qdrant 连接测试失败: {}", e))
            }
        }
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<QdrantHealth> {
        let start_time = std::time::Instant::now();

        match self.client.health_check().await {
            Ok(_) => {
                let response_time = start_time.elapsed();
                Ok(QdrantHealth {
                    is_connected: true,
                    response_time_ms: response_time.as_millis() as u64,
                    error_message: None,
                })
            }
            Err(e) => {
                warn!("Qdrant 健康检查失败: {}", e);
                Ok(QdrantHealth {
                    is_connected: false,
                    response_time_ms: start_time.elapsed().as_millis() as u64,
                    error_message: Some(e.to_string()),
                })
            }
        }
    }

    /// 创建集合
    pub async fn create_collection(
        &self,
        collection_name: &str,
        vector_size: u64,
        distance: Distance,
    ) -> Result<()> {
        use qdrant_client::qdrant::{CreateCollection, VectorParams, VectorsConfig};

        let vectors_config = VectorsConfig {
            config: Some(qdrant_client::qdrant::vectors_config::Config::Params(
                VectorParams {
                    size: vector_size,
                    distance: distance.into(),
                    hnsw_config: None,
                    quantization_config: None,
                    on_disk: None,
                    datatype: None,
                    multivector_config: None,
                },
            )),
        };

        let collection_config = CreateCollection {
            collection_name: collection_name.to_string(),
            vectors_config: Some(vectors_config),
            shard_number: None,
            replication_factor: None,
            write_consistency_factor: None,
            on_disk_payload: None,
            timeout: None,
            hnsw_config: None,
            wal_config: None,
            optimizers_config: None,
            quantization_config: None,
            init_from_collection: None,
            sharding_method: None,
            sparse_vectors_config: None,
            strict_mode_config: None,
        };

        self.client
            .create_collection(collection_config)
            .await
            .map_err(|e| anyhow!("创建集合失败: {}", e))?;

        Ok(())
    }

    /// 检查集合是否存在
    pub async fn collection_exists(&self, collection_name: &str) -> Result<bool> {
        match self.client.collection_info(collection_name).await {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }

    /// 删除集合
    pub async fn delete_collection(&self, collection_name: &str) -> Result<()> {
        self.client
            .delete_collection(collection_name)
            .await
            .map_err(|e| anyhow!("删除集合失败: {}", e))?;
        Ok(())
    }

    /// 插入或更新向量点
    pub async fn upsert_points(
        &self,
        collection_name: &str,
        points: Vec<PointStruct>,
    ) -> Result<()> {
        let upsert_points = UpsertPoints {
            collection_name: collection_name.to_string(),
            wait: Some(true),
            points,
            ordering: None,
            shard_key_selector: None,
        };

        self.client
            .upsert_points(upsert_points)
            .await
            .map_err(|e| anyhow!("插入向量点失败: {}", e))?;

        Ok(())
    }

    /// 搜索向量点
    pub async fn search_points(
        &self,
        collection_name: &str,
        query_vector: Vec<f32>,
        limit: u64,
        score_threshold: Option<f32>,
        filter: Option<Filter>,
    ) -> Result<Vec<ScoredPoint>> {
        let search_points = SearchPoints {
            collection_name: collection_name.to_string(),
            vector: query_vector,
            filter,
            limit,
            with_vectors: Some(false.into()),
            with_payload: Some(true.into()),
            params: None,
            score_threshold,
            offset: None,
            vector_name: None,
            read_consistency: None,
            timeout: None,
            shard_key_selector: None,
            sparse_indices: None,
        };

        let response = self
            .client
            .search_points(search_points)
            .await
            .map_err(|e| anyhow!("搜索向量点失败: {}", e))?;

        Ok(response.result)
    }

    /// 删除向量点
    pub async fn delete_points(&self, collection_name: &str, point_ids: Vec<String>) -> Result<()> {
        use qdrant_client::qdrant::{
            PointId, PointsSelector, points_selector::PointsSelectorOneOf,
        };

        let point_ids: Vec<PointId> = point_ids.into_iter().map(|id| PointId::from(id)).collect();

        let points_selector = PointsSelector {
            points_selector_one_of: Some(PointsSelectorOneOf::Points(
                qdrant_client::qdrant::PointsIdsList { ids: point_ids },
            )),
        };

        let delete_points = DeletePoints {
            collection_name: collection_name.to_string(),
            wait: Some(true),
            points: Some(points_selector),
            ordering: None,
            shard_key_selector: None,
        };

        self.client
            .delete_points(delete_points)
            .await
            .map_err(|e| anyhow!("删除向量点失败: {}", e))?;

        Ok(())
    }

    /// 统计向量点数量
    pub async fn count_points(&self, collection_name: &str, filter: Option<Filter>) -> Result<u64> {
        let count_points = CountPoints {
            collection_name: collection_name.to_string(),
            filter,
            exact: Some(false),
            read_consistency: None,
            shard_key_selector: None,
            timeout: None,
        };

        let response = self
            .client
            .count(count_points)
            .await
            .map_err(|e| anyhow!("统计向量点失败: {}", e))?;

        Ok(response.result.map(|r| r.count).unwrap_or(0))
    }
}

/// Qdrant 健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QdrantHealth {
    pub is_connected: bool,
    pub response_time_ms: u64,
    pub error_message: Option<String>,
}

/// Qdrant 连接池管理器
pub struct QdrantManager {
    connection: Arc<RwLock<Option<QdrantConnection>>>,
    config: Arc<RwLock<QdrantConfig>>,
}

impl QdrantManager {
    /// 创建新的 Qdrant 管理器
    pub fn new(config: QdrantConfig) -> Self {
        Self {
            connection: Arc::new(RwLock::new(None)),
            config: Arc::new(RwLock::new(config)),
        }
    }

    /// 初始化 Qdrant 连接
    pub async fn initialize(&self) -> Result<()> {
        let config = self.config.read().await.clone();
        let connection = QdrantConnection::new(config).await?;

        let mut conn_guard = self.connection.write().await;
        *conn_guard = Some(connection);

        info!("Qdrant 管理器初始化完成");
        Ok(())
    }

    /// 获取 Qdrant 连接
    pub async fn get_connection(&self) -> Result<QdrantConnection> {
        let conn_guard = self.connection.read().await;
        match conn_guard.as_ref() {
            Some(connection) => Ok(connection.clone()),
            None => Err(anyhow!("Qdrant 连接未初始化，请先调用 initialize()")),
        }
    }

    /// 重新连接
    pub async fn reconnect(&self) -> Result<()> {
        info!("正在重新连接 Qdrant...");

        let config = self.config.read().await.clone();
        let new_connection = QdrantConnection::new(config).await?;

        let mut conn_guard = self.connection.write().await;
        *conn_guard = Some(new_connection);

        info!("Qdrant 重新连接成功");
        Ok(())
    }

    /// 更新配置并重新连接
    pub async fn update_config(&self, new_config: QdrantConfig) -> Result<()> {
        {
            let mut config_guard = self.config.write().await;
            *config_guard = new_config;
        }

        self.reconnect().await
    }

    /// 关闭连接
    pub async fn close(&self) {
        let mut conn_guard = self.connection.write().await;
        *conn_guard = None;
        info!("Qdrant 连接已关闭");
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<QdrantHealth> {
        match self.get_connection().await {
            Ok(connection) => connection.health_check().await,
            Err(e) => Ok(QdrantHealth {
                is_connected: false,
                response_time_ms: 0,
                error_message: Some(e.to_string()),
            }),
        }
    }
}

/// 向量集合配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorCollectionConfig {
    pub name: String,
    pub vector_size: u64,
    pub distance: i32, // 使用i32代替Distance枚举进行序列化
    pub description: Option<String>,
}

impl VectorCollectionConfig {
    pub fn new(name: impl Into<String>, vector_size: u64, distance: Distance) -> Self {
        Self {
            name: name.into(),
            vector_size,
            distance: distance as i32,
            description: None,
        }
    }

    pub fn with_description(mut self, description: impl Into<String>) -> Self {
        self.description = Some(description.into());
        self
    }

    pub fn get_distance(&self) -> Distance {
        match self.distance {
            0 => Distance::UnknownDistance,
            1 => Distance::Cosine,
            2 => Distance::Euclid,
            3 => Distance::Dot,
            4 => Distance::Manhattan,
            _ => Distance::Cosine, // 默认使用余弦距离
        }
    }
}

/// 预定义的向量集合配置
pub struct VectorCollections;

impl VectorCollections {
    /// 文档嵌入集合
    pub fn document_embeddings() -> VectorCollectionConfig {
        VectorCollectionConfig::new("document_embeddings", 1536, Distance::Cosine)
            .with_description("文档级向量嵌入")
    }

    /// 文档分块嵌入集合
    pub fn chunk_embeddings() -> VectorCollectionConfig {
        VectorCollectionConfig::new("chunk_embeddings", 1536, Distance::Cosine)
            .with_description("文档分块级向量嵌入")
    }

    /// 知识图谱嵌入集合
    pub fn knowledge_graph_embeddings() -> VectorCollectionConfig {
        VectorCollectionConfig::new("knowledge_graph_embeddings", 1536, Distance::Cosine)
            .with_description("知识图谱实体向量嵌入")
    }
}

// 测试模块
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_qdrant_config_default() {
        let config = QdrantConfig::default();
        assert_eq!(config.url, "http://localhost:6334");
        assert_eq!(config.api_key, None);
        assert_eq!(config.connect_timeout_ms, Some(10_000));
        assert!(!config.enable_tls);
    }

    #[test]
    fn test_vector_collection_config() {
        let config = VectorCollections::document_embeddings();
        assert_eq!(config.name, "document_embeddings");
        assert_eq!(config.vector_size, 1536);
        assert_eq!(config.get_distance(), Distance::Cosine);
        assert!(config.description.is_some());
    }

    #[tokio::test]
    async fn test_qdrant_manager_creation() {
        let config = QdrantConfig::default();
        let manager = QdrantManager::new(config);

        // 测试连接获取在未初始化时应该失败
        assert!(manager.get_connection().await.is_err());
    }
}
