use crate::{
    data_cache::{<PERSON><PERSON><PERSON>anager, DataCache},
    models::User,
    repositories::UserRepository,
};
use anyhow::Result;
use async_trait::async_trait;
use std::{sync::Arc, time::Duration};

/// Cached trait for UserRepository that supports mutable operations
#[async_trait]
pub trait CachedUserRepositoryTrait {
    async fn create(&mut self, user: &User) -> Result<User>;
    async fn find_by_id(&mut self, id: &str) -> Result<Option<User>>;
    async fn find_by_email(&mut self, email: &str) -> Result<Option<User>>;
    async fn find_by_username(&mut self, username: &str) -> Result<Option<User>>;
    async fn find_by_username_or_email(&mut self, identifier: &str) -> Result<Option<User>>;
    async fn update(&mut self, user: &User) -> Result<User>;
    async fn delete(&mut self, id: &str) -> Result<bool>;
    async fn list(&mut self, limit: Option<u32>, offset: Option<u32>) -> Result<Vec<User>>;
    async fn count(&mut self) -> Result<u64>;
    async fn search(
        &mut self,
        query: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<User>>;
    async fn set_active_status(&mut self, id: &str, is_active: bool) -> Result<bool>;
}

/// Cached implementation of UserRepository
pub struct CachedUserRepository<R> {
    repository: Arc<R>,
    cache: CacheManager,
    user_ttl: Duration,
    list_ttl: Duration,
}

impl<R: UserRepository> CachedUserRepository<R> {
    pub fn new(repository: Arc<R>, cache: DataCache) -> Self {
        Self {
            repository,
            cache: CacheManager::new(cache),
            user_ttl: Duration::from_secs(600), // 10 minutes for user data
            list_ttl: Duration::from_secs(300), // 5 minutes for lists
        }
    }

    pub fn with_ttl(
        repository: Arc<R>,
        cache: DataCache,
        user_ttl: Duration,
        list_ttl: Duration,
    ) -> Self {
        Self {
            repository,
            cache: CacheManager::new(cache),
            user_ttl,
            list_ttl,
        }
    }

    pub fn get_cache_metrics(&self) -> &crate::data_cache::CacheMetrics {
        self.cache.get_metrics()
    }

    pub fn reset_cache_metrics(&mut self) {
        self.cache.reset_metrics();
    }

    async fn invalidate_user_cache(&mut self, user_id: &str) -> Result<()> {
        let keys = [
            DataCache::user_key(user_id),
            format!("user:email:{}", user_id), // We'll need to store email->id mapping
            format!("user:username:{}", user_id), // We'll need to store username->id mapping
        ];

        for key in &keys {
            self.cache.cache.delete(key).await?;
        }

        // Also invalidate list caches (in a real implementation, you might want to be more
        // selective)
        self.cache.cache.delete("users:list").await?;
        self.cache.cache.delete("users:count").await?;

        Ok(())
    }
}

#[async_trait]
impl<R: UserRepository + Send + Sync> CachedUserRepositoryTrait for CachedUserRepository<R> {
    async fn create(&mut self, user: &User) -> Result<User> {
        let created_user = self.repository.create(user).await?;

        // Cache the created user
        let user_key = DataCache::user_key(&created_user.id);
        let _ = self
            .cache
            .cache
            .set(&user_key, &created_user, Some(self.user_ttl))
            .await;

        // Invalidate list caches
        let _ = self.cache.cache.delete("users:list").await;
        let _ = self.cache.cache.delete("users:count").await;

        Ok(created_user)
    }

    async fn find_by_id(&mut self, id: &str) -> Result<Option<User>> {
        let user_key = DataCache::user_key(id);

        // Try cache first
        if let Some(user) = self.cache.get_with_metrics::<User>(&user_key).await? {
            return Ok(Some(user));
        }

        // Not in cache, query database
        let user = self.repository.find_by_id(id).await?;

        // Cache the result if found
        if let Some(ref user) = user {
            let _ = self
                .cache
                .set_with_metrics(&user_key, user, Some(self.user_ttl))
                .await;
        }

        Ok(user)
    }

    async fn find_by_email(&mut self, email: &str) -> Result<Option<User>> {
        let email_key = format!("user:email:{}", email);

        // Try to get user ID from email cache first
        if let Some(user_id) = self.cache.get_with_metrics::<String>(&email_key).await? {
            return self.find_by_id(&user_id).await;
        }

        // Not in cache, query database
        let user = self.repository.find_by_email(email).await?;

        // Cache the email->id mapping if found
        if let Some(ref user) = user {
            let _ = self
                .cache
                .set_with_metrics(&email_key, &user.id, Some(self.user_ttl))
                .await;

            // Also cache the user data
            let user_key = DataCache::user_key(&user.id);
            let _ = self
                .cache
                .set_with_metrics(&user_key, user, Some(self.user_ttl))
                .await;
        }

        Ok(user)
    }

    async fn find_by_username(&mut self, username: &str) -> Result<Option<User>> {
        let username_key = format!("user:username:{}", username);

        // Try to get user ID from username cache first
        if let Some(user_id) = self.cache.get_with_metrics::<String>(&username_key).await? {
            return self.find_by_id(&user_id).await;
        }

        // Not in cache, query database
        let user = self.repository.find_by_username(username).await?;

        // Cache the username->id mapping if found
        if let Some(ref user) = user {
            let _ = self
                .cache
                .set_with_metrics(&username_key, &user.id, Some(self.user_ttl))
                .await;

            // Also cache the user data
            let user_key = DataCache::user_key(&user.id);
            let _ = self
                .cache
                .set_with_metrics(&user_key, user, Some(self.user_ttl))
                .await;
        }

        Ok(user)
    }

    async fn find_by_username_or_email(&mut self, identifier: &str) -> Result<Option<User>> {
        // Try username first
        if let Some(user) = self.find_by_username(identifier).await? {
            return Ok(Some(user));
        }

        // Then try email
        self.find_by_email(identifier).await
    }

    async fn update(&mut self, user: &User) -> Result<User> {
        let updated_user = self.repository.update(user).await?;

        // Update cache
        let user_key = DataCache::user_key(&updated_user.id);
        let _ = self
            .cache
            .set_with_metrics(&user_key, &updated_user, Some(self.user_ttl))
            .await;

        // Invalidate related caches
        let email_key = format!("user:email:{}", updated_user.email);
        let username_key = format!("user:username:{}", updated_user.username);
        let _ = self.cache.cache.delete(&email_key).await;
        let _ = self.cache.cache.delete(&username_key).await;

        // Set new mappings
        let _ = self
            .cache
            .set_with_metrics(&email_key, &updated_user.id, Some(self.user_ttl))
            .await;
        let _ = self
            .cache
            .set_with_metrics(&username_key, &updated_user.id, Some(self.user_ttl))
            .await;

        Ok(updated_user)
    }

    async fn delete(&mut self, id: &str) -> Result<bool> {
        // Get user first to clean up caches
        if let Some(user) = self.find_by_id(id).await? {
            let result = self.repository.delete(id).await?;

            if result {
                // Clean up all related cache entries
                let user_key = DataCache::user_key(id);
                let email_key = format!("user:email:{}", user.email);
                let username_key = format!("user:username:{}", user.username);

                let _ = self.cache.cache.delete(&user_key).await;
                let _ = self.cache.cache.delete(&email_key).await;
                let _ = self.cache.cache.delete(&username_key).await;

                // Invalidate list caches
                let _ = self.cache.cache.delete("users:list").await;
                let _ = self.cache.cache.delete("users:count").await;
            }

            Ok(result)
        } else {
            self.repository.delete(id).await
        }
    }

    async fn list(&mut self, limit: Option<u32>, offset: Option<u32>) -> Result<Vec<User>> {
        let list_key = format!("users:list:{}:{}", limit.unwrap_or(0), offset.unwrap_or(0));

        // Try cache first
        if let Some(users) = self.cache.get_with_metrics::<Vec<User>>(&list_key).await? {
            return Ok(users);
        }

        // Not in cache, query database
        let users = self
            .repository
            .list(None, None, None, limit, offset)
            .await?;

        // Cache the result
        let _ = self
            .cache
            .set_with_metrics(&list_key, &users, Some(self.list_ttl))
            .await;

        Ok(users)
    }

    async fn count(&mut self) -> Result<u64> {
        let count_key = "users:count";

        // Try cache first
        if let Some(count) = self.cache.get_with_metrics::<u64>(count_key).await? {
            return Ok(count);
        }

        // Not in cache, query database
        let count = self.repository.count().await?;

        // Cache the result
        let _ = self
            .cache
            .set_with_metrics(count_key, &count, Some(self.list_ttl))
            .await;

        Ok(count)
    }

    async fn search(
        &mut self,
        query: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<User>> {
        let search_key = format!(
            "users:search:{}:{}:{}",
            query,
            limit.unwrap_or(0),
            offset.unwrap_or(0)
        );

        // Try cache first
        if let Some(users) = self
            .cache
            .get_with_metrics::<Vec<User>>(&search_key)
            .await?
        {
            return Ok(users);
        }

        // Not in cache, query database
        let users = self
            .repository
            .list(Some(query), None, None, limit, offset)
            .await?;

        // Cache the result with shorter TTL for search results
        let search_ttl = Duration::from_secs(60); // 1 minute for search results
        let _ = self
            .cache
            .set_with_metrics(&search_key, &users, Some(search_ttl))
            .await;

        Ok(users)
    }

    async fn set_active_status(&mut self, id: &str, is_active: bool) -> Result<bool> {
        let result = self.repository.set_active_status(id, is_active).await?;

        if result {
            // Invalidate user cache to force refresh
            let user_key = DataCache::user_key(id);
            let _ = self.cache.cache.delete(&user_key).await;
        }

        Ok(result)
    }
}
