use anyhow::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::{RwLock, broadcast};

use crate::cache::CacheService;

/// Cache invalidation event types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum CacheEvent {
    UserCreated { user_id: String },
    UserUpdated { user_id: String },
    UserDeleted { user_id: String },
    UserRoleChanged { user_id: String, new_role: String },

    DocumentCreated { doc_id: String, kb_id: String },
    DocumentUpdated { doc_id: String, kb_id: String },
    DocumentDeleted { doc_id: String, kb_id: String },

    KnowledgeBaseCreated { kb_id: String },
    KnowledgeBaseUpdated { kb_id: String },
    KnowledgeBaseDeleted { kb_id: String },

    SearchIndexUpdated { kb_id: Option<String> },
    ConversationEnded { session_id: String },

    // Bulk operations
    BulkUserUpdate { user_ids: Vec<String> },
    BulkDocumentUpdate { doc_ids: Vec<String> },

    // System events
    CacheClearAll,
    CacheClearPattern { pattern: String },
}

/// Cache key patterns for different data types
pub struct CacheKeyPatterns;

impl CacheKeyPatterns {
    pub fn user_patterns(user_id: &str) -> Vec<String> {
        vec![
            format!("user:{}", user_id),
            format!("user:{}:*", user_id),
            format!("user:email:*"), // Will need more specific invalidation
            format!("user:username:*"), // Will need more specific invalidation
            "users:list*".to_string(),
            "users:count".to_string(),
            "users:search:*".to_string(),
        ]
    }

    pub fn document_patterns(doc_id: &str, kb_id: &str) -> Vec<String> {
        vec![
            format!("doc:{}", doc_id),
            format!("kb:{}", kb_id),
            format!("kb:{}:*", kb_id),
            "search:*".to_string(), // Invalidate all search results
            "documents:list*".to_string(),
            "documents:count*".to_string(),
        ]
    }

    pub fn knowledge_base_patterns(kb_id: &str) -> Vec<String> {
        vec![
            format!("kb:{}", kb_id),
            format!("kb:{}:*", kb_id),
            format!("documents:{}:*", kb_id),
            "kb:list*".to_string(),
            "kb:count".to_string(),
            "search:*".to_string(),
        ]
    }

    pub fn search_patterns() -> Vec<String> {
        vec!["search:*".to_string()]
    }

    pub fn conversation_patterns(session_id: &str) -> Vec<String> {
        vec![
            format!("conversation:{}", session_id),
            format!("session:{}", session_id),
        ]
    }
}

/// Handler for cache invalidation events
#[async_trait]
pub trait CacheEventHandler: Send + Sync {
    async fn handle_event(&mut self, event: &CacheEvent) -> Result<()>;
}

/// Redis-based cache invalidation handler
pub struct RedisCacheEventHandler {
    cache: CacheService,
}

impl RedisCacheEventHandler {
    pub fn new(cache: CacheService) -> Self {
        Self { cache }
    }

    async fn invalidate_patterns(&mut self, patterns: Vec<String>) -> Result<()> {
        for pattern in patterns {
            if pattern.contains('*') {
                // In a real implementation, you'd use Redis SCAN or maintain
                // a registry of keys to avoid the performance hit of pattern matching
                // For now, we'll just log the pattern
                tracing::debug!("Would invalidate pattern: {}", pattern);
            } else {
                self.cache.delete(&pattern).await?;
            }
        }
        Ok(())
    }
}

#[async_trait]
impl CacheEventHandler for RedisCacheEventHandler {
    async fn handle_event(&mut self, event: &CacheEvent) -> Result<()> {
        match event {
            CacheEvent::UserCreated { user_id: _ } => {
                // Invalidate list caches only
                self.invalidate_patterns(vec![
                    "users:list*".to_string(),
                    "users:count".to_string(),
                ])
                .await?;
            }

            CacheEvent::UserUpdated { user_id } => {
                self.invalidate_patterns(CacheKeyPatterns::user_patterns(user_id))
                    .await?;
            }

            CacheEvent::UserDeleted { user_id } => {
                self.invalidate_patterns(CacheKeyPatterns::user_patterns(user_id))
                    .await?;
            }

            CacheEvent::UserRoleChanged { user_id, .. } => {
                // Invalidate user cache and permissions
                self.invalidate_patterns(vec![
                    format!("user:{}", user_id),
                    format!("user:{}:permissions", user_id),
                ])
                .await?;
            }

            CacheEvent::DocumentCreated { doc_id: _, kb_id } => {
                self.invalidate_patterns(vec![
                    format!("kb:{}", kb_id),
                    "documents:list*".to_string(),
                    "documents:count*".to_string(),
                    "search:*".to_string(),
                ])
                .await?;
            }

            CacheEvent::DocumentUpdated { doc_id, kb_id } => {
                self.invalidate_patterns(CacheKeyPatterns::document_patterns(doc_id, kb_id))
                    .await?;
            }

            CacheEvent::DocumentDeleted { doc_id, kb_id } => {
                self.invalidate_patterns(CacheKeyPatterns::document_patterns(doc_id, kb_id))
                    .await?;
            }

            CacheEvent::KnowledgeBaseCreated { kb_id: _ } => {
                self.invalidate_patterns(vec!["kb:list*".to_string(), "kb:count".to_string()])
                    .await?;
            }

            CacheEvent::KnowledgeBaseUpdated { kb_id } => {
                self.invalidate_patterns(CacheKeyPatterns::knowledge_base_patterns(kb_id))
                    .await?;
            }

            CacheEvent::KnowledgeBaseDeleted { kb_id } => {
                self.invalidate_patterns(CacheKeyPatterns::knowledge_base_patterns(kb_id))
                    .await?;
            }

            CacheEvent::SearchIndexUpdated { kb_id } => {
                if let Some(kb_id) = kb_id {
                    self.invalidate_patterns(vec![format!("search:*{}*", kb_id)])
                        .await?;
                } else {
                    self.invalidate_patterns(CacheKeyPatterns::search_patterns())
                        .await?;
                }
            }

            CacheEvent::ConversationEnded { session_id } => {
                self.invalidate_patterns(CacheKeyPatterns::conversation_patterns(session_id))
                    .await?;
            }

            CacheEvent::BulkUserUpdate { user_ids } => {
                for user_id in user_ids {
                    self.invalidate_patterns(CacheKeyPatterns::user_patterns(user_id))
                        .await?;
                }
            }

            CacheEvent::BulkDocumentUpdate { doc_ids: _ } => {
                // For bulk operations, we might want to be less granular
                self.invalidate_patterns(vec![
                    "documents:*".to_string(),
                    "kb:*".to_string(),
                    "search:*".to_string(),
                ])
                .await?;
            }

            CacheEvent::CacheClearAll => {
                // This would require a Redis FLUSHDB in a real implementation
                tracing::warn!("Cache clear all requested - would flush entire cache");
            }

            CacheEvent::CacheClearPattern { pattern } => {
                self.invalidate_patterns(vec![pattern.clone()]).await?;
            }
        }

        Ok(())
    }
}

/// Event-driven cache invalidation system
pub struct CacheInvalidationSystem {
    event_sender: broadcast::Sender<CacheEvent>,
    handlers: Arc<RwLock<Vec<Box<dyn CacheEventHandler>>>>,
}

impl CacheInvalidationSystem {
    pub fn new() -> Self {
        let (event_sender, _) = broadcast::channel(1000);

        Self {
            event_sender,
            handlers: Arc::new(RwLock::new(Vec::new())),
        }
    }

    pub async fn add_handler(&self, handler: Box<dyn CacheEventHandler>) {
        let mut handlers = self.handlers.write().await;
        handlers.push(handler);
    }

    pub fn get_event_sender(&self) -> broadcast::Sender<CacheEvent> {
        self.event_sender.clone()
    }

    pub async fn emit_event(&self, event: CacheEvent) -> Result<()> {
        // Send event to all subscribers
        if let Err(e) = self.event_sender.send(event.clone()) {
            tracing::warn!("Failed to send cache event: {}", e);
        }

        // Also handle immediately with registered handlers
        let mut handlers = self.handlers.write().await;
        for handler in handlers.iter_mut() {
            if let Err(e) = handler.handle_event(&event).await {
                tracing::error!("Cache event handler failed: {}", e);
            }
        }

        Ok(())
    }

    pub async fn start_event_loop(&self) {
        let mut receiver = self.event_sender.subscribe();
        let handlers = self.handlers.clone();

        tokio::spawn(async move {
            while let Ok(event) = receiver.recv().await {
                let mut handlers = handlers.write().await;
                for handler in handlers.iter_mut() {
                    if let Err(e) = handler.handle_event(&event).await {
                        tracing::error!("Cache event handler failed in loop: {}", e);
                    }
                }
            }
        });
    }
}

/// Cache key registry for tracking dependencies
pub struct CacheKeyRegistry {
    dependencies: Arc<RwLock<HashMap<String, Vec<String>>>>,
}

impl CacheKeyRegistry {
    pub fn new() -> Self {
        Self {
            dependencies: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Register a cache key with its dependencies
    pub async fn register_dependency(&self, cache_key: String, depends_on: Vec<String>) {
        let mut deps = self.dependencies.write().await;
        deps.insert(cache_key, depends_on);
    }

    /// Get all cache keys that depend on a given resource
    pub async fn get_dependent_keys(&self, resource: &str) -> Vec<String> {
        let deps = self.dependencies.read().await;
        deps.iter()
            .filter(|(_, dependencies)| dependencies.contains(&resource.to_string()))
            .map(|(key, _)| key.clone())
            .collect()
    }

    /// Remove dependencies for a cache key
    pub async fn remove_dependency(&self, cache_key: &str) {
        let mut deps = self.dependencies.write().await;
        deps.remove(cache_key);
    }
}

/// Batch cache operations for efficiency
pub struct BatchCacheOperations {
    operations: Vec<CacheOperation>,
}

#[derive(Debug, Clone)]
pub enum CacheOperation {
    Set {
        key: String,
        value: String,
        ttl: Option<usize>,
    },
    Delete {
        key: String,
    },
    InvalidatePattern {
        pattern: String,
    },
}

impl BatchCacheOperations {
    pub fn new() -> Self {
        Self {
            operations: Vec::new(),
        }
    }

    pub fn add_set(&mut self, key: String, value: String, ttl: Option<usize>) {
        self.operations
            .push(CacheOperation::Set { key, value, ttl });
    }

    pub fn add_delete(&mut self, key: String) {
        self.operations.push(CacheOperation::Delete { key });
    }

    pub fn add_invalidate_pattern(&mut self, pattern: String) {
        self.operations
            .push(CacheOperation::InvalidatePattern { pattern });
    }

    pub async fn execute(&self, cache: &mut CacheService) -> Result<()> {
        for operation in &self.operations {
            match operation {
                CacheOperation::Set { key, value, ttl } => {
                    cache.set(key, value, *ttl).await?;
                }
                CacheOperation::Delete { key } => {
                    cache.delete(key).await?;
                }
                CacheOperation::InvalidatePattern { pattern } => {
                    // In a real implementation, you'd handle pattern matching
                    tracing::debug!("Would invalidate pattern: {}", pattern);
                }
            }
        }
        Ok(())
    }

    pub fn len(&self) -> usize {
        self.operations.len()
    }

    pub fn is_empty(&self) -> bool {
        self.operations.is_empty()
    }
}

#[cfg(test)]
mod tests {
    use wisdom_vault_common::db::next_id;

    use super::*;
    use crate::cache::establish_cache_connection;

    #[tokio::test]
    async fn test_cache_invalidation_system() {
        if std::env::var("SKIP_CACHE_TESTS").is_ok() {
            return;
        }

        let connection = establish_cache_connection("redis://127.0.0.1:6379")
            .await
            .expect("Failed to connect to Redis");

        let cache_service = CacheService::new(connection);
        let handler = RedisCacheEventHandler::new(cache_service);

        let system = CacheInvalidationSystem::new();
        system.add_handler(Box::new(handler)).await;

        let user_id = next_id();
        let event = CacheEvent::UserUpdated { user_id };

        // This should not panic
        system.emit_event(event).await.unwrap();
    }

    #[tokio::test]
    async fn test_cache_key_registry() {
        let registry = CacheKeyRegistry::new();

        registry
            .register_dependency(
                "user:123".to_string(),
                vec!["user:123".to_string(), "users:list".to_string()],
            )
            .await;

        let dependent_keys = registry.get_dependent_keys("user:123").await;
        assert!(dependent_keys.contains(&"user:123".to_string()));
    }

    #[tokio::test]
    async fn test_batch_cache_operations() {
        let mut batch = BatchCacheOperations::new();

        batch.add_set("key1".to_string(), "value1".to_string(), None);
        batch.add_delete("key2".to_string());
        batch.add_invalidate_pattern("pattern:*".to_string());

        assert_eq!(batch.len(), 3);
        assert!(!batch.is_empty());
    }
}
