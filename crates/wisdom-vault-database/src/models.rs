use bson::oid::ObjectId;
use serde::{Deserialize, Serialize};
use wisdom_vault_common::db::next_id;

// MongoDB 辅助函数
pub fn generate_object_id() -> String {
    ObjectId::new().to_hex()
}

pub fn default_string_id() -> String {
    generate_object_id()
}

// ============================================================================
// 关系型数据模型 (Relational Data Models)
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct User {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub username: String,
    pub email: String,
    pub password_hash: String,
    pub full_name: Option<String>,
    pub avatar_id: Option<String>,
    pub phone: Option<String>,
    pub organization_id: Option<String>,
    pub is_active: bool,
    pub last_login_at: Option<i64>,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Role {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub is_system: bool,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Permission {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub name: String,
    pub resource: String,
    pub action: PermissionAction,
    pub description: Option<String>,
    pub created_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PermissionAction {
    Create,
    Read,
    Update,
    Delete,
    Manage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct UserRole {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub user_id: String,
    pub role_id: String,
    pub assigned_by: String,
    pub assigned_at: i64,
    pub expires_at: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RolePermission {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub role_id: String,
    pub permission_id: String,
    pub granted_by: String,
    pub granted_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Organization {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub name: String,
    pub code: String,
    pub description: Option<String>,
    pub logo_url: Option<String>,
    pub settings: serde_json::Value,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Department {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub organization_id: String,
    pub parent_id: Option<String>,
    pub name: String,
    pub code: String,
    pub description: Option<String>,
    pub manager_id: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

// ============================================================================
// 文档型数据模型 (Document Data Models)
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct KnowledgeBase {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub organization_id: String,
    pub name: String,
    pub description: Option<String>,
    pub owner_id: String,
    pub visibility: KnowledgeBaseVisibility,
    pub settings: KnowledgeBaseSettings,
    pub statistics: KnowledgeBaseStatistics,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum KnowledgeBaseVisibility {
    Public,
    Private,
    Organization,
    Department,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct KnowledgeBaseSettings {
    pub allow_anonymous_access: bool,
    pub auto_categorize: bool,
    pub enable_versioning: bool,
    pub max_file_size: i64,
    pub allowed_file_types: Vec<String>,
    pub language: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct KnowledgeBaseStatistics {
    pub document_count: i64,
    pub total_size: i64,
    pub last_indexed_at: Option<u64>,
    pub view_count: i64,
    pub search_count: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Document {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub knowledge_base_id: String,
    pub title: String,
    pub content: String,
    pub summary: Option<String>,
    pub file_type: String,
    pub file_size: i64,
    pub file_path: Option<String>,
    pub original_filename: Option<String>,
    pub mime_type: String,
    pub language: Option<String>,
    pub metadata: DocumentMetadata,
    pub processing_metadata: DocumentProcessingMetadata,
    pub status: DocumentStatus,
    pub uploaded_by: String,
    pub indexed_at: Option<i64>,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DocumentMetadata {
    // Basic document metadata
    pub author: Option<String>,
    pub subject: Option<String>,
    pub creator: Option<String>,
    pub producer: Option<String>,
    pub keywords: Vec<String>,
    pub source_url: Option<String>,

    // Document properties
    pub page_count: Option<i32>,
    pub word_count: Option<i32>,
    pub character_count: Option<i32>,
    pub creation_date: Option<u64>,
    pub modification_date: Option<u64>,

    // Tika extraction metadata
    pub content_type: String,
    pub content_encoding: Option<String>,
    pub content_language: Option<String>,

    // Custom fields for additional metadata
    pub custom_fields: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DocumentProcessingMetadata {
    pub extraction_method: String,
    pub extraction_quality: f32,
    pub processing_time_ms: u64,
    pub parsing_errors: Vec<String>,
    pub parsing_warnings: Vec<String>,
    pub file_checksum: String,
    pub structured_content: Option<String>, // HTML/XML representation
    pub processing_attempts: u32,
    pub last_processing_attempt: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum DocumentStatus {
    Uploading,
    Uploaded,   // File uploaded but not processed
    Processing, // Document being parsed by Tika
    Parsed,     // Successfully parsed, ready for indexing
    Indexing,   // Being indexed for search
    Indexed,    // Fully processed and searchable
    Failed,     // Processing failed
    Archived,   // Archived but still accessible
    Deleted,    // Soft deleted
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DocumentVersion {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub document_id: String,
    pub version_number: i32,
    pub content: String,
    pub summary: Option<String>,
    pub changes: Vec<String>,
    pub created_by: String,
    pub created_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Tag {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub knowledge_base_id: String,
    pub name: String,
    pub display_name: String,
    pub color: Option<String>,
    pub description: Option<String>,
    pub usage_count: i64,
    pub created_by: String,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Category {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub knowledge_base_id: String,
    pub parent_id: Option<String>,
    pub name: String,
    pub description: Option<String>,
    pub icon: Option<String>,
    pub sort_order: i32,
    pub document_count: i64,
    pub created_by: String,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DocumentTag {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub document_id: String,
    pub tag_id: String,
    pub tagged_by: String,
    pub tagged_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DocumentCategory {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub document_id: String,
    pub category_id: String,
    pub assigned_by: String,
    pub assigned_at: i64,
    pub confidence_score: Option<f64>,
}

// ============================================================================
// 向量型数据模型 (Vector Data Models) - 这些保持不变，继续使用向量数据库
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DocumentChunk {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub document_id: String,
    pub content: String,
    pub chunk_index: i32,
    pub chunk_type: ChunkType,
    pub token_count: i32,
    pub char_count: i32,
    pub start_offset: i32,
    pub end_offset: i32,
    pub metadata: ChunkMetadata,
    pub created_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ChunkType {
    Paragraph,
    Sentence,
    Section,
    Table,
    Code,
    List,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ChunkMetadata {
    pub heading: Option<String>,
    pub section_level: Option<i32>,
    pub language: Option<String>,
    pub quality_score: Option<f64>,
    pub extraction_confidence: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DocumentEmbedding {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub chunk_id: String,
    pub embedding: Vec<f32>,
    pub model_name: String,
    pub model_version: String,
    pub dimension: i32,
    pub embedding_type: EmbeddingType,
    pub processing_metadata: EmbeddingMetadata,
    pub created_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum EmbeddingType {
    Dense,
    Sparse,
    Hybrid,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct EmbeddingMetadata {
    pub processing_time_ms: i32,
    pub normalization_method: String,
    pub chunk_preprocessing: Vec<String>,
    pub similarity_threshold: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchQuery {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub user_id: String,
    pub knowledge_base_id: Option<String>,
    pub query_text: String,
    pub query_type: SearchQueryType,
    pub filters: SearchFilters,
    pub parameters: SearchParameters,
    pub created_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SearchQueryType {
    Keyword,
    Semantic,
    Hybrid,
    Advanced,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchFilters {
    pub document_types: Vec<String>,
    pub tags: Vec<String>,
    pub categories: Vec<String>,
    pub date_range: Option<DateRange>,
    pub authors: Vec<String>,
    pub languages: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DateRange {
    pub start: i64,
    pub end: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchParameters {
    pub limit: i32,
    pub offset: i32,
    pub similarity_threshold: f64,
    pub keyword_weight: f64,
    pub semantic_weight: f64,
    pub enable_reranking: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchResult {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub query_id: String,
    pub document_id: String,
    pub chunk_id: String,
    pub relevance_score: f64,
    pub keyword_score: f64,
    pub semantic_score: f64,
    pub final_score: f64,
    pub rank_position: i32,
    pub highlight_snippets: Vec<String>,
    pub metadata: SearchResultMetadata,
    pub created_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchResultMetadata {
    pub matched_terms: Vec<String>,
    pub similarity_distance: f64,
    pub chunk_context: String,
    pub relevance_explanation: Option<String>,
}

// ============================================================================
// 图数据模型 (Graph Data Models)
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct DocumentRelation {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub source_document_id: String,
    pub target_document_id: String,
    pub relation_type: DocumentRelationType,
    pub strength: f64,
    pub confidence: f64,
    pub description: Option<String>,
    pub metadata: RelationMetadata,
    pub created_by: Option<String>,
    pub created_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DocumentRelationType {
    References,
    Supersedes,
    DerivedFrom,
    Similar,
    Contradicts,
    Complements,
    Prerequisite,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RelationMetadata {
    pub extraction_method: String,
    pub evidence_snippets: Vec<String>,
    pub semantic_similarity: Option<f64>,
    pub manual_verified: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct KnowledgeGraphNode {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub knowledge_base_id: String,
    pub entity_type: EntityType,
    pub name: String,
    pub description: Option<String>,
    pub properties: serde_json::Value,
    pub importance_score: f64,
    pub occurrence_count: i32,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EntityType {
    Person,
    Organization,
    Location,
    Concept,
    Event,
    Product,
    Technology,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct KnowledgeGraphEdge {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub source_node_id: String,
    pub target_node_id: String,
    pub relation_type: String,
    pub weight: f64,
    pub confidence: f64,
    pub properties: serde_json::Value,
    pub source_documents: Vec<String>,
    pub created_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Conversation {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub user_id: String,
    pub knowledge_base_id: Option<String>,
    pub title: String,
    pub summary: Option<String>,
    pub status: ConversationStatus,
    pub context: ConversationContext,
    pub settings: ConversationSettings,
    pub statistics: ConversationStatistics,
    pub created_at: i64,
    pub updated_at: i64,
    pub last_message_at: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConversationStatus {
    Active,
    Archived,
    Deleted,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ConversationContext {
    pub session_id: String,
    pub previous_queries: Vec<String>,
    pub topic_focus: Option<String>,
    pub mentioned_documents: Vec<String>,
    pub context_window_size: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ConversationSettings {
    pub model_name: String,
    pub temperature: f64,
    pub max_tokens: i32,
    pub enable_citations: bool,
    pub response_language: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ConversationStatistics {
    pub message_count: i32,
    pub total_tokens_used: i32,
    pub average_response_time: f64,
    pub user_satisfaction: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Message {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub conversation_id: String,
    pub role: MessageRole,
    pub content: String,
    pub message_type: MessageType,
    pub metadata: MessageMetadata,
    pub status: MessageStatus,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageType {
    Text,
    Document,
    Image,
    Code,
    Error,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MessageMetadata {
    pub tokens_used: Option<i32>,
    pub processing_time_ms: Option<i32>,
    pub model_used: Option<String>,
    pub confidence_score: Option<f64>,
    pub sources: Vec<MessageSource>,
    pub attachments: Vec<MessageAttachment>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MessageSource {
    pub document_id: String,
    pub chunk_id: String,
    pub relevance_score: f64,
    pub snippet: String,
    pub page_number: Option<i32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MessageAttachment {
    pub file_id: String,
    pub filename: String,
    pub file_type: String,
    pub file_size: i64,
    pub upload_status: AttachmentStatus,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AttachmentStatus {
    Uploading,
    Processing,
    Ready,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageStatus {
    Pending,
    Sending,
    Sent,
    Failed,
    Edited,
}

// ============================================================================
// 辅助结构和常量 (Helper Structures and Constants)
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ProcessingTask {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub task_type: TaskType,
    pub status: TaskStatus,
    pub resource_id: String,
    pub progress: f64,
    pub error_message: Option<String>,
    pub started_at: i64,
    pub completed_at: Option<i64>,
    pub context: Option<TaskContext>,
    pub priority: TaskPriority,
    pub retry_count: u32,
    pub max_retries: u32,
    pub error_details: Option<String>,
    pub created_at: i64,
    pub next_retry_at: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskType {
    DocumentIndexing,
    VectorGeneration,
    KnowledgeGraphExtraction,
    DocumentClassification,
    DocumentParsing,
    MetadataExtraction,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TaskContext {
    pub gridfs_file_id: Option<ObjectId>, // GridFS文件的ObjectID
    pub original_filename: Option<String>,
    pub mime_type: Option<String>,
    pub file_size: Option<i64>,
    pub knowledge_base_id: Option<String>,
    pub user_id: Option<String>,
    pub additional_params: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, PartialOrd, Eq, Hash, Ord)]
pub enum TaskPriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskStatus {
    Queued,
    Running,
    Completed,
    Failed,
    Cancelled,
    Retrying, // 添加重试状态
}

// Vector search constants
pub const DEFAULT_VECTOR_DIMENSION: usize = 1536;
pub const DEFAULT_SIMILARITY_THRESHOLD: f64 = 0.7;
pub const MAX_SEARCH_RESULTS: i32 = 100;
pub const DEFAULT_CHUNK_SIZE: usize = 512;

// ============================================================================
// 向量化处理模型 (Vectorization Models)
// ============================================================================

/// 向量化任务
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct EmbeddingTask {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub task_type: EmbeddingTaskType,
    pub resource_id: String, // 文档ID或分块ID
    pub model_id: String,
    pub status: EmbeddingTaskStatus,
    pub priority: TaskPriority,
    pub progress: f64, // 0.0 - 1.0
    pub batch_id: Option<String>,
    pub retry_count: u32,
    pub max_retries: u32,
    pub error_message: Option<String>,
    pub processing_metadata: EmbeddingTaskMetadata,
    pub created_at: i64,
    pub started_at: Option<i64>,
    pub completed_at: Option<i64>,
    pub updated_at: i64,
}

/// 向量化任务类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EmbeddingTaskType {
    Document,    // 整个文档向量化
    Chunk,       // 单个分块向量化
    Batch,       // 批量向量化
    Incremental, // 增量更新
    Recompute,   // 重新计算
}

/// 向量化任务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EmbeddingTaskStatus {
    Pending,    // 待处理
    Queued,     // 队列中
    Processing, // 处理中
    Completed,  // 已完成
    Failed,     // 失败
    Cancelled,  // 已取消
    Retrying,   // 重试中
}

/// 向量化任务元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct EmbeddingTaskMetadata {
    pub text_length: Option<i32>,
    pub token_count: Option<i32>,
    pub preprocessing_steps: Vec<String>,
    pub model_config: serde_json::Value,
    pub quality_score: Option<f64>,
    pub processing_time_ms: Option<i64>,
    pub memory_usage_mb: Option<f64>,
    pub gpu_used: bool,
}

/// 嵌入模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct EmbeddingModel {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub name: String,
    pub display_name: String,
    pub model_type: EmbeddingModelType,
    pub model_path: String,
    pub tokenizer_path: Option<String>,
    pub dimension: i32,
    pub max_sequence_length: i32,
    pub normalization: NormalizationMethod,
    pub language_support: Vec<String>,
    pub model_config: EmbeddingModelConfig,
    pub performance_metrics: ModelPerformanceMetrics,
    pub is_active: bool,
    pub is_default: bool,
    pub version: String,
    pub created_at: i64,
    pub updated_at: i64,
}

/// 嵌入模型类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EmbeddingModelType {
    SentenceTransformers, // sentence-transformers模型
    BERT,                 // BERT类模型
    Custom,               // 自定义模型
}

/// 标准化方法
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum NormalizationMethod {
    L2,     // L2标准化
    L1,     // L1标准化
    None,   // 不标准化
    MinMax, // Min-Max标准化
}

/// 嵌入模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct EmbeddingModelConfig {
    pub batch_size: i32,
    pub device: String,    // "cpu", "cuda", "mps"
    pub precision: String, // "fp32", "fp16", "bf16"
    pub use_cache: bool,
    pub cache_size_mb: i32,
    pub preprocessing_config: PreprocessingConfig,
    pub inference_config: InferenceConfig,
}

/// 预处理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PreprocessingConfig {
    pub lowercase: bool,
    pub remove_punctuation: bool,
    pub remove_stopwords: bool,
    pub stem_words: bool,
    pub max_length: i32,
    pub truncation_strategy: TruncationStrategy,
    pub padding_strategy: PaddingStrategy,
}

/// 截断策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TruncationStrategy {
    Head,   // 保留开头
    Tail,   // 保留结尾
    Middle, // 保留中间
}

/// 填充策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PaddingStrategy {
    None,      // 不填充
    MaxLength, // 填充到最大长度
    Batch,     // 填充到批次最大长度
}

/// 推理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InferenceConfig {
    pub temperature: f32,
    pub top_k: Option<i32>,
    pub top_p: Option<f32>,
    pub repetition_penalty: f32,
    pub attention_dropout: f32,
    pub hidden_dropout: f32,
}

/// 模型性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ModelPerformanceMetrics {
    pub avg_processing_time_ms: f64,
    pub throughput_tokens_per_sec: f64,
    pub memory_usage_mb: f64,
    pub accuracy_score: Option<f64>,
    pub f1_score: Option<f64>,
    pub benchmark_results: serde_json::Value,
    pub last_evaluated_at: i64,
}

/// 向量质量指标
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct VectorQualityMetrics {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub embedding_id: String,
    pub dimension_consistency: bool,
    pub magnitude: f64,
    pub sparsity_ratio: f64,
    pub norm_type: NormalizationMethod,
    pub quality_score: f64,
    pub anomaly_score: Option<f64>,
    pub similarity_distribution: SimilarityDistribution,
    pub computed_at: i64,
}

/// 相似度分布统计
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SimilarityDistribution {
    pub mean: f64,
    pub std_dev: f64,
    pub min: f64,
    pub max: f64,
    pub percentile_25: f64,
    pub percentile_50: f64,
    pub percentile_75: f64,
    pub percentile_95: f64,
}

/// 向量化批次
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct EmbeddingBatch {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub model_id: String,
    pub total_items: i32,
    pub processed_items: i32,
    pub failed_items: i32,
    pub status: EmbeddingTaskStatus,
    pub batch_config: BatchConfig,
    pub started_at: Option<i64>,
    pub completed_at: Option<i64>,
    pub created_by: String,
    pub created_at: i64,
    pub updated_at: i64,
}

/// 批次配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct BatchConfig {
    pub batch_size: i32,
    pub max_parallel_tasks: i32,
    pub retry_failed: bool,
    pub quality_threshold: f64,
    pub auto_recompute: bool,
    pub notification_settings: NotificationSettings,
}

impl Default for BatchConfig {
    fn default() -> Self {
        Self {
            batch_size: 32,
            max_parallel_tasks: 4,
            retry_failed: true,
            quality_threshold: 0.8,
            auto_recompute: false,
            notification_settings: NotificationSettings {
                notify_on_completion: true,
                notify_on_failure: true,
                notification_threshold: 90.0,
                notification_channels: vec!["email".to_string()],
            },
        }
    }
}

/// 通知设置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct NotificationSettings {
    pub notify_on_completion: bool,
    pub notify_on_failure: bool,
    pub notification_threshold: f64, // 完成百分比阈值
    pub notification_channels: Vec<String>,
}

/// 向量索引配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct VectorIndex {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    pub name: String,
    pub index_type: VectorIndexType,
    pub dimension: i32,
    pub metric: DistanceMetric,
    pub index_config: VectorIndexConfig,
    pub statistics: IndexStatistics,
    pub is_active: bool,
    pub created_at: i64,
    pub updated_at: i64,
}

/// 向量索引类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum VectorIndexType {
    HNSW, // Hierarchical Navigable Small World
    IVF,  // Inverted File
    Flat, // 暴力搜索
    LSH,  // Locality Sensitive Hashing
}

/// 距离度量
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DistanceMetric {
    Cosine,    // 余弦相似度
    Euclidean, // 欧几里得距离
    Manhattan, // 曼哈顿距离
    Dot,       // 点积
}

/// 向量索引配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct VectorIndexConfig {
    pub m: Option<i32>,               // HNSW参数
    pub ef_construction: Option<i32>, // HNSW构建参数
    pub ef_search: Option<i32>,       // HNSW搜索参数
    pub nlist: Option<i32>,           // IVF聚类数量
    pub nprobe: Option<i32>,          // IVF搜索聚类数
    pub hash_bits: Option<i32>,       // LSH哈希位数
}

/// 索引统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct IndexStatistics {
    pub total_vectors: i64,
    pub index_size_mb: f64,
    pub avg_search_time_ms: f64,
    pub recall_at_k: f64,
    pub last_updated: i64,
}

// ============================================================================
// RAG 对话数据模型 (RAG Conversation Data Models)
// ============================================================================

/// 对话类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConversationType {
    /// 问答对话
    QnA,
    /// 知识检索
    Search,
    /// 多轮对话
    MultiTurn,
    /// 文档分析
    DocumentAnalysis,
}

/// 对话元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ConversationMetadata {
    /// 对话标签
    pub tags: Vec<String>,
    /// 用户偏好设置
    pub user_preferences: serde_json::Value,
    /// 对话上下文配置
    pub context_settings: ContextSettings,
    /// 自定义属性
    pub custom_attributes: serde_json::Value,
}

/// 上下文设置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ContextSettings {
    /// 上下文窗口大小
    pub context_window_size: u32,
    /// 历史消息保留数量
    pub history_retention_count: u32,
    /// 启用上下文压缩
    pub enable_context_compression: bool,
    /// 搜索参数
    pub search_params: serde_json::Value,
}

/// Token 使用情况
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenUsage {
    /// 输入 tokens
    pub prompt_tokens: u32,
    /// 输出 tokens
    pub completion_tokens: u32,
    /// 总 tokens
    pub total_tokens: u32,
}

/// 搜索统计
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SearchStatistics {
    /// 搜索耗时（毫秒）
    pub search_time_ms: u64,
    /// 匹配文档数
    pub documents_matched: u32,
    /// 平均相关性分数
    pub avg_relevance_score: f64,
    /// 搜索策略
    pub search_strategy: String,
}

/// 用户反馈
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct UserFeedback {
    /// 评分（1-5）
    pub rating: Option<u8>,
    /// 是否有用
    pub helpful: Option<bool>,
    /// 反馈评论
    pub comment: Option<String>,
    /// 反馈时间
    pub feedback_at: i64,
}

/// 对话摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ConversationSummary {
    #[serde(rename = "_id", default = "default_string_id")]
    pub id: String,
    /// 对话 ID
    pub conversation_id: String,
    /// 摘要内容
    pub summary: String,
    /// 关键主题
    pub key_topics: Vec<String>,
    /// 摘要生成时间
    pub generated_at: i64,
    /// 包含的消息时间范围
    pub message_range: MessageRange,
}

/// 消息时间范围
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MessageRange {
    /// 起始时间
    pub start_time: i64,
    /// 结束时间
    pub end_time: i64,
    /// 消息数量
    pub message_count: u32,
}

// ============================================================================
// 默认实现 (Default Implementations)
// ============================================================================

impl Default for ConversationContext {
    fn default() -> Self {
        Self {
            session_id: next_id(),
            previous_queries: Vec::new(),
            topic_focus: None,
            mentioned_documents: Vec::new(),
            context_window_size: 8000,
        }
    }
}

impl Default for ConversationSettings {
    fn default() -> Self {
        Self {
            model_name: "gpt-4o-mini".to_string(),
            temperature: 0.7,
            max_tokens: 4000,
            enable_citations: true,
            response_language: "zh-CN".to_string(),
        }
    }
}

impl Default for ConversationStatistics {
    fn default() -> Self {
        Self {
            message_count: 0,
            total_tokens_used: 0,
            average_response_time: 0.0,
            user_satisfaction: None,
        }
    }
}

impl Default for ConversationMetadata {
    fn default() -> Self {
        Self {
            tags: Vec::new(),
            user_preferences: serde_json::Value::Object(serde_json::Map::new()),
            context_settings: ContextSettings::default(),
            custom_attributes: serde_json::Value::Object(serde_json::Map::new()),
        }
    }
}

impl Default for ContextSettings {
    fn default() -> Self {
        Self {
            context_window_size: 8000,
            history_retention_count: 10,
            enable_context_compression: true,
            search_params: serde_json::Value::Object(serde_json::Map::new()),
        }
    }
}
