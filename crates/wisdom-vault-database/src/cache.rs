use anyhow::{Result, anyhow};
use redis::{AsyncCommands, Client, RedisResult, aio::MultiplexedConnection};
use tracing::info;

pub type CacheConnection = MultiplexedConnection;

pub async fn establish_cache_connection(url: &str) -> Result<CacheConnection> {
    info!("Connecting to Redis at: {}", url);

    let client = Client::open(url).map_err(|e| anyhow!("Failed to create Redis client: {}", e))?;

    let connection = client
        .get_multiplexed_async_connection()
        .await
        .map_err(|e| anyhow!("Failed to connect to Redis: {}", e))?;

    info!("Successfully connected to Redis");
    Ok(connection)
}

#[derive(Clone)]
pub struct CacheService {
    connection: CacheConnection,
}

impl CacheService {
    pub fn new(connection: CacheConnection) -> Self {
        Self { connection }
    }

    /// Set a key-value pair with optional expiration in seconds
    pub async fn set<K, V>(&mut self, key: K, value: V, expire_seconds: Option<usize>) -> Result<()>
    where
        K: redis::ToRedisArgs + Send + Sync,
        V: redis::ToRedisArgs + Send + Sync,
    {
        if let Some(expire) = expire_seconds {
            self.connection
                .set_ex::<_, _, ()>(key, value, expire as u64)
                .await
                .map_err(|e| anyhow!("Failed to set key with expiration: {}", e))?;
        } else {
            self.connection
                .set::<_, _, ()>(key, value)
                .await
                .map_err(|e| anyhow!("Failed to set key: {}", e))?;
        }
        Ok(())
    }

    /// Get a value by key
    pub async fn get<K, V>(&mut self, key: K) -> Result<Option<V>>
    where
        K: redis::ToRedisArgs + Send + Sync,
        V: redis::FromRedisValue,
    {
        let result: RedisResult<V> = self.connection.get(key).await;
        match result {
            Ok(value) => Ok(Some(value)),
            Err(e) => {
                // Handle the case where key doesn't exist
                if e.to_string().contains("nil") {
                    Ok(None)
                } else {
                    Err(anyhow!("Failed to get key: {}", e))
                }
            }
        }
    }

    /// Delete a key
    pub async fn delete<K>(&mut self, key: K) -> Result<bool>
    where
        K: redis::ToRedisArgs + Send + Sync,
    {
        let deleted: i32 = self
            .connection
            .del(key)
            .await
            .map_err(|e| anyhow!("Failed to delete key: {}", e))?;
        Ok(deleted > 0)
    }

    /// Check if a key exists
    pub async fn exists<K>(&mut self, key: K) -> Result<bool>
    where
        K: redis::ToRedisArgs + Send + Sync,
    {
        let exists: bool = self
            .connection
            .exists(key)
            .await
            .map_err(|e| anyhow!("Failed to check key existence: {}", e))?;
        Ok(exists)
    }

    /// Set expiration for a key
    pub async fn expire<K>(&mut self, key: K, seconds: usize) -> Result<bool>
    where
        K: redis::ToRedisArgs + Send + Sync,
    {
        let result: bool = self
            .connection
            .expire(key, seconds as i64)
            .await
            .map_err(|e| anyhow!("Failed to set expiration: {}", e))?;
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_connection() {
        // This test requires a running Redis instance
        // Skip in CI unless Redis is available
        if std::env::var("SKIP_CACHE_TESTS").is_ok() {
            return;
        }

        let result = establish_cache_connection("redis://127.0.0.1:6379").await;

        match result {
            Ok(_) => println!("Cache connection successful"),
            Err(e) => println!("Cache connection failed (expected in test): {}", e),
        }
    }
}
