use anyhow::Result;
use serde::{Serialize, de::DeserializeOwned};
use std::time::Duration;

use crate::cache::CacheService;

pub trait Cacheable: Serialize + DeserializeOwned + Clone {}
impl<T> Cacheable for T where T: Serialize + DeserializeOwned + Clone {}

#[derive(Clone)]
pub struct DataCache {
    cache: CacheService,
    default_ttl: Duration,
}

impl DataCache {
    pub fn new(cache: CacheService) -> Self {
        Self {
            cache,
            default_ttl: Duration::from_secs(300), // 5 minutes default
        }
    }

    pub fn with_default_ttl(cache: CacheService, default_ttl: Duration) -> Self {
        Self { cache, default_ttl }
    }

    /// Cache a value with a key and optional TTL
    pub async fn set<T>(&mut self, key: &str, value: &T, ttl: Option<Duration>) -> Result<()>
    where
        T: Cacheable,
    {
        let json = serde_json::to_string(value)?;
        let ttl_seconds = ttl.unwrap_or(self.default_ttl).as_secs() as usize;

        self.cache.set(key, json, Some(ttl_seconds)).await
    }

    /// Get a cached value by key
    pub async fn get<T>(&mut self, key: &str) -> Result<Option<T>>
    where
        T: Cacheable,
    {
        let json: Option<String> = self.cache.get(key).await?;

        match json {
            Some(json_str) => {
                let value: T = serde_json::from_str(&json_str)?;
                Ok(Some(value))
            }
            None => Ok(None),
        }
    }

    /// Delete a cached value
    pub async fn delete(&mut self, key: &str) -> Result<bool> {
        self.cache.delete(key).await
    }

    /// Check if a key exists
    pub async fn exists(&mut self, key: &str) -> Result<bool> {
        self.cache.exists(key).await
    }

    /// Set expiration for an existing key
    pub async fn expire(&mut self, key: &str, ttl: Duration) -> Result<bool> {
        self.cache.expire(key, ttl.as_secs() as usize).await
    }

    /// Get or compute a value (cache-aside pattern)
    pub async fn get_or_compute<T, F, Fut>(
        &mut self,
        key: &str,
        compute_fn: F,
        ttl: Option<Duration>,
    ) -> Result<T>
    where
        T: Cacheable,
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<T>>,
    {
        // Try to get from cache first
        if let Some(cached_value) = self.get::<T>(key).await? {
            return Ok(cached_value);
        }

        // Not in cache, compute the value
        let computed_value = compute_fn().await?;

        // Store in cache for next time
        self.set(key, &computed_value, ttl).await?;

        Ok(computed_value)
    }

    /// Warm up cache with multiple key-value pairs
    pub async fn warm_up<T>(&mut self, entries: Vec<(String, T, Option<Duration>)>) -> Result<()>
    where
        T: Cacheable,
    {
        for (key, value, ttl) in entries {
            self.set(&key, &value, ttl).await?;
        }
        Ok(())
    }

    /// Generate cache keys for different data types
    pub fn user_key(user_id: &str) -> String {
        format!("user:{}", user_id)
    }

    pub fn user_permissions_key(user_id: &str) -> String {
        format!("user:{}:permissions", user_id)
    }

    pub fn user_profile_key(user_id: &str) -> String {
        format!("user:{}:profile", user_id)
    }

    pub fn knowledge_base_key(kb_id: &str) -> String {
        format!("kb:{}", kb_id)
    }

    pub fn document_key(doc_id: &str) -> String {
        format!("doc:{}", doc_id)
    }

    pub fn search_results_key(query: &str, filters: &str) -> String {
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{Hash, Hasher},
        };

        let mut hasher = DefaultHasher::new();
        query.hash(&mut hasher);
        filters.hash(&mut hasher);
        let hash = hasher.finish();

        format!(
            "search:{}:{:x}",
            query.chars().take(20).collect::<String>(),
            hash
        )
    }

    pub fn conversation_key(session_id: &str) -> String {
        format!("conversation:{}", session_id)
    }
}

/// Decorator for caching repository results
pub struct CachedRepository<R> {
    repository: R,
    cache: DataCache,
}

impl<R> CachedRepository<R> {
    pub fn new(repository: R, cache: DataCache) -> Self {
        Self { repository, cache }
    }

    pub fn into_inner(self) -> R {
        self.repository
    }

    pub async fn invalidate_user_cache(&mut self, user_id: &str) -> Result<()> {
        let user_id_str = user_id.to_string();
        let keys = [
            DataCache::user_key(&user_id_str),
            DataCache::user_permissions_key(&user_id_str),
            DataCache::user_profile_key(&user_id_str),
        ];

        for key in &keys {
            self.cache.delete(key).await?;
        }

        Ok(())
    }

    pub async fn invalidate_document_cache(&mut self, doc_id: &str) -> Result<()> {
        self.cache
            .delete(&DataCache::document_key(&doc_id.to_string()))
            .await?;
        Ok(())
    }

    pub async fn invalidate_knowledge_base_cache(&mut self, kb_id: &str) -> Result<()> {
        self.cache
            .delete(&DataCache::knowledge_base_key(&kb_id.to_string()))
            .await?;
        Ok(())
    }

    /// Invalidate search cache (you might want to use patterns here in real implementation)
    pub async fn invalidate_search_cache(&mut self) -> Result<()> {
        // In a real implementation, you'd want to use Redis patterns or maintain
        // a list of search cache keys to invalidate
        Ok(())
    }
}

/// Cache metrics and monitoring
#[derive(Debug, Clone)]
pub struct CacheMetrics {
    pub hits: u64,
    pub misses: u64,
    pub errors: u64,
}

impl CacheMetrics {
    pub fn new() -> Self {
        Self {
            hits: 0,
            misses: 0,
            errors: 0,
        }
    }

    pub fn hit_rate(&self) -> f64 {
        if self.hits + self.misses == 0 {
            0.0
        } else {
            self.hits as f64 / (self.hits + self.misses) as f64
        }
    }

    pub fn record_hit(&mut self) {
        self.hits += 1;
    }

    pub fn record_miss(&mut self) {
        self.misses += 1;
    }

    pub fn record_error(&mut self) {
        self.errors += 1;
    }
}

/// Cache manager with metrics
pub struct CacheManager {
    pub cache: DataCache,
    metrics: CacheMetrics,
}

impl CacheManager {
    pub fn new(cache: DataCache) -> Self {
        Self {
            cache,
            metrics: CacheMetrics::new(),
        }
    }

    pub async fn get_with_metrics<T>(&mut self, key: &str) -> Result<Option<T>>
    where
        T: Cacheable,
    {
        match self.cache.get::<T>(key).await {
            Ok(Some(value)) => {
                self.metrics.record_hit();
                Ok(Some(value))
            }
            Ok(None) => {
                self.metrics.record_miss();
                Ok(None)
            }
            Err(e) => {
                self.metrics.record_error();
                Err(e)
            }
        }
    }

    pub async fn set_with_metrics<T>(
        &mut self,
        key: &str,
        value: &T,
        ttl: Option<Duration>,
    ) -> Result<()>
    where
        T: Cacheable,
    {
        match self.cache.set(key, value, ttl).await {
            Ok(()) => Ok(()),
            Err(e) => {
                self.metrics.record_error();
                Err(e)
            }
        }
    }

    pub fn get_metrics(&self) -> &CacheMetrics {
        &self.metrics
    }

    pub fn reset_metrics(&mut self) {
        self.metrics = CacheMetrics::new();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::cache::establish_cache_connection;

    #[derive(Debug, Clone, serde::Serialize, serde::Deserialize, PartialEq)]
    struct TestData {
        id: u32,
        name: String,
    }

    #[tokio::test]
    async fn test_data_cache() {
        if std::env::var("SKIP_CACHE_TESTS").is_ok() {
            return;
        }

        let connection = establish_cache_connection("redis://127.0.0.1:6379")
            .await
            .expect("Failed to connect to Redis");

        let cache_service = CacheService::new(connection);
        let mut data_cache = DataCache::new(cache_service);

        let test_data = TestData {
            id: 1,
            name: "Test".to_string(),
        };

        // Set and get
        data_cache.set("test_key", &test_data, None).await.unwrap();
        let retrieved = data_cache.get::<TestData>("test_key").await.unwrap();

        assert_eq!(retrieved, Some(test_data));

        // Test cache miss
        let missing = data_cache.get::<TestData>("missing_key").await.unwrap();
        assert_eq!(missing, None);
    }

    #[tokio::test]
    async fn test_get_or_compute() {
        if std::env::var("SKIP_CACHE_TESTS").is_ok() {
            return;
        }

        let connection = establish_cache_connection("redis://127.0.0.1:6379")
            .await
            .expect("Failed to connect to Redis");

        let cache_service = CacheService::new(connection);
        let mut data_cache = DataCache::new(cache_service);

        let test_data = TestData {
            id: 2,
            name: "Computed".to_string(),
        };

        let result = data_cache
            .get_or_compute("compute_key", || async { Ok(test_data.clone()) }, None)
            .await
            .unwrap();

        assert_eq!(result, test_data);

        // Second call should return cached value
        let cached_result = data_cache
            .get::<TestData>("compute_key")
            .await
            .unwrap()
            .unwrap();

        assert_eq!(cached_result, test_data);
    }
}
