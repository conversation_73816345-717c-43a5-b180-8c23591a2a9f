use std::sync::Arc;
use std::time::Duration;

use anyhow::{Result, anyhow};
use mongodb::{Client, ClientSession, Database, bson::Document, options::ClientOptions};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// MongoDB 数据库连接配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MongoDBConfig {
    /// MongoDB 连接字符串
    pub url: String,
    /// 连接池最大大小
    pub max_pool_size: Option<u32>,
    /// 连接池最小大小
    pub min_pool_size: Option<u32>,
    /// 最大空闲时间（毫秒）
    pub max_idle_time_ms: Option<u64>,
    /// 连接超时时间（毫秒）
    pub connect_timeout_ms: Option<u64>,
    /// 服务器选择超时时间（毫秒）
    pub server_selection_timeout_ms: Option<u64>,
}

impl Default for MongoDBConfig {
    fn default() -> Self {
        Self {
            url: "mongodb://localhost:27017/wisdom_vault".to_string(),
            max_pool_size: Some(10),
            min_pool_size: Some(2),
            max_idle_time_ms: Some(30_000),
            connect_timeout_ms: Some(10_000),
            server_selection_timeout_ms: Some(30_000),
        }
    }
}

/// MongoDB 数据库连接管理器
#[derive(Debug, Clone)]
pub struct DatabaseConnection {
    client: Client,
    database: Database,
}

impl DatabaseConnection {
    /// 创建新的数据库连接
    pub async fn new(config: MongoDBConfig) -> Result<Self> {
        info!("正在连接到 MongoDB: {}", config.url);

        // 解析连接选项
        let mut client_options = ClientOptions::parse(&config.url)
            .await
            .map_err(|e| anyhow!("解析 MongoDB 连接字符串失败: {}", e))?;

        // 设置连接池选项
        if let Some(max_pool_size) = config.max_pool_size {
            client_options.max_pool_size = Some(max_pool_size);
        }

        if let Some(min_pool_size) = config.min_pool_size {
            client_options.min_pool_size = Some(min_pool_size);
        }

        if let Some(max_idle_time_ms) = config.max_idle_time_ms {
            client_options.max_idle_time = Some(Duration::from_millis(max_idle_time_ms));
        }

        if let Some(connect_timeout_ms) = config.connect_timeout_ms {
            client_options.connect_timeout = Some(Duration::from_millis(connect_timeout_ms));
        }

        if let Some(server_selection_timeout_ms) = config.server_selection_timeout_ms {
            client_options.server_selection_timeout =
                Some(Duration::from_millis(server_selection_timeout_ms));
        }

        // 创建客户端
        let client = Client::with_options(client_options)
            .map_err(|e| anyhow!("创建 MongoDB 客户端失败: {}", e))?;

        // 获取数据库名称
        let database_name = extract_database_name(&config.url)?;
        let database = client.database(&database_name);

        // 测试连接
        Self::test_connection(&database).await?;

        info!("成功连接到 MongoDB 数据库: {}", database_name);

        Ok(Self { client, database })
    }

    /// 获取 MongoDB 客户端
    pub fn client(&self) -> &Client {
        &self.client
    }

    /// 获取数据库实例
    pub fn database(&self) -> &Database {
        &self.database
    }

    /// 获取指定集合
    pub fn collection<T: Send + Sync>(&self, name: &str) -> mongodb::Collection<T> {
        self.database.collection(name)
    }

    /// 创建新会话
    pub async fn start_session(&self) -> Result<ClientSession> {
        self.client
            .start_session()
            .await
            .map_err(|e| anyhow!("创建 MongoDB 会话失败: {}", e))
    }

    /// 创建带事务选项的会话
    pub async fn start_session_with_transaction(&self) -> Result<ClientSession> {
        let mut session = self.start_session().await?;
        session
            .start_transaction()
            .await
            .map_err(|e| anyhow!("启动事务失败: {}", e))?;
        Ok(session)
    }

    /// 测试数据库连接
    async fn test_connection(database: &Database) -> Result<()> {
        match database.run_command(mongodb::bson::doc! {"ping": 1}).await {
            Ok(_) => {
                info!("MongoDB 连接测试成功");
                Ok(())
            }
            Err(e) => {
                error!("MongoDB 连接测试失败: {}", e);
                Err(anyhow!("MongoDB 连接测试失败: {}", e))
            }
        }
    }

    /// 获取数据库统计信息
    pub async fn get_stats(&self) -> Result<mongodb::bson::Document> {
        self.database
            .run_command(mongodb::bson::doc! {"dbStats": 1})
            .await
            .map_err(|e| anyhow!("获取数据库统计信息失败: {}", e))
    }

    /// 创建索引
    pub async fn create_index(
        &self,
        collection_name: &str,
        keys: Document,
        options: Option<mongodb::options::IndexOptions>,
    ) -> Result<String> {
        let collection: mongodb::Collection<Document> = self.collection(collection_name);
        let index_model = mongodb::IndexModel::builder()
            .keys(keys)
            .options(options)
            .build();

        let result = collection
            .create_index(index_model)
            .await
            .map_err(|e| anyhow!("创建索引失败: {}", e))?;

        Ok(result.index_name)
    }

    /// 删除索引
    pub async fn drop_index(&self, collection_name: &str, index_name: &str) -> Result<()> {
        let collection: mongodb::Collection<Document> = self.collection(collection_name);
        collection
            .drop_index(index_name)
            .await
            .map_err(|e| anyhow!("删除索引失败: {}", e))
    }

    /// 列出所有集合
    pub async fn list_collections(&self) -> Result<Vec<String>> {
        self.database
            .list_collection_names()
            .await
            .map_err(|e| anyhow!("列出集合失败: {}", e))
    }

    /// 检查集合是否存在
    pub async fn collection_exists(&self, collection_name: &str) -> Result<bool> {
        let collections = self.list_collections().await?;
        Ok(collections.contains(&collection_name.to_string()))
    }

    /// 创建集合
    pub async fn create_collection(&self, collection_name: &str) -> Result<()> {
        self.database
            .create_collection(collection_name)
            .await
            .map_err(|e| anyhow!("创建集合失败: {}", e))
    }

    /// 删除集合
    pub async fn drop_collection(&self, collection_name: &str) -> Result<()> {
        let collection: mongodb::Collection<Document> = self.collection(collection_name);
        collection
            .drop()
            .await
            .map_err(|e| anyhow!("删除集合失败: {}", e))
    }

    /// 获取集合统计信息
    pub async fn get_collection_stats(&self, collection_name: &str) -> Result<Document> {
        self.database
            .run_command(mongodb::bson::doc! {"collStats": collection_name})
            .await
            .map_err(|e| anyhow!("获取集合统计信息失败: {}", e))
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<DatabaseHealth> {
        let start_time = std::time::Instant::now();

        // 测试基本连接
        match Self::test_connection(&self.database).await {
            Ok(_) => {
                let response_time = start_time.elapsed();

                // 获取服务器状态
                let server_status = self
                    .database
                    .run_command(mongodb::bson::doc! {"serverStatus": 1})
                    .await
                    .ok();

                // 获取数据库统计信息
                let db_stats = self.get_stats().await.ok();

                Ok(DatabaseHealth {
                    is_connected: true,
                    response_time_ms: response_time.as_millis() as u64,
                    server_status,
                    db_stats,
                    error_message: None,
                })
            }
            Err(e) => {
                warn!("数据库健康检查失败: {}", e);
                Ok(DatabaseHealth {
                    is_connected: false,
                    response_time_ms: start_time.elapsed().as_millis() as u64,
                    server_status: None,
                    db_stats: None,
                    error_message: Some(e.to_string()),
                })
            }
        }
    }
}

/// 数据库健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseHealth {
    pub is_connected: bool,
    pub response_time_ms: u64,
    pub server_status: Option<Document>,
    pub db_stats: Option<Document>,
    pub error_message: Option<String>,
}

/// 从连接字符串中提取数据库名称
fn extract_database_name(url: &str) -> Result<String> {
    // 使用简单的字符串解析来提取数据库名称
    // 格式: mongodb://[username:password@]host:port[/database][?options]

    let url = url.trim();

    // 移除协议前缀
    let url = url
        .strip_prefix("mongodb://")
        .ok_or_else(|| anyhow!("无效的 MongoDB 连接字符串: 缺少 mongodb:// 前缀"))?;

    // 查找数据库名称部分
    if let Some(db_part) = url.split('/').nth(1) {
        // 移除查询参数
        let db_name = db_part.split('?').next().unwrap_or(db_part);
        if db_name.is_empty() {
            return Err(anyhow!("连接字符串中未指定数据库名称"));
        }
        Ok(db_name.to_string())
    } else {
        Err(anyhow!("连接字符串中未指定数据库名称"))
    }
}

/// 数据库连接池管理器
#[derive(Debug)]
pub struct DatabaseManager {
    connection: Arc<RwLock<Option<DatabaseConnection>>>,
    config: Arc<RwLock<MongoDBConfig>>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub fn new(config: MongoDBConfig) -> Self {
        Self {
            connection: Arc::new(RwLock::new(None)),
            config: Arc::new(RwLock::new(config)),
        }
    }

    /// 初始化数据库连接
    pub async fn initialize(&self) -> Result<()> {
        let config = self.config.read().await.clone();
        let connection = DatabaseConnection::new(config).await?;

        let mut conn_guard = self.connection.write().await;
        *conn_guard = Some(connection);

        info!("数据库管理器初始化完成");
        Ok(())
    }

    /// 获取数据库连接
    pub async fn get_connection(&self) -> Result<DatabaseConnection> {
        let conn_guard = self.connection.read().await;
        match conn_guard.as_ref() {
            Some(connection) => Ok(connection.clone()),
            None => Err(anyhow!("数据库连接未初始化，请先调用 initialize()")),
        }
    }

    /// 重新连接数据库
    pub async fn reconnect(&self) -> Result<()> {
        info!("正在重新连接数据库...");

        let config = self.config.read().await.clone();
        let new_connection = DatabaseConnection::new(config).await?;

        let mut conn_guard = self.connection.write().await;
        *conn_guard = Some(new_connection);

        info!("数据库重新连接成功");
        Ok(())
    }

    /// 更新配置并重新连接
    pub async fn update_config(&self, new_config: MongoDBConfig) -> Result<()> {
        {
            let mut config_guard = self.config.write().await;
            *config_guard = new_config;
        }

        self.reconnect().await
    }

    /// 关闭连接
    pub async fn close(&self) {
        let mut conn_guard = self.connection.write().await;
        *conn_guard = None;
        info!("数据库连接已关闭");
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<DatabaseHealth> {
        match self.get_connection().await {
            Ok(connection) => connection.health_check().await,
            Err(e) => Ok(DatabaseHealth {
                is_connected: false,
                response_time_ms: 0,
                server_status: None,
                db_stats: None,
                error_message: Some(e.to_string()),
            }),
        }
    }
}

// 测试模块
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_database_name() {
        assert_eq!(
            extract_database_name("mongodb://localhost:27017/test_db").unwrap(),
            "test_db"
        );

        assert_eq!(
            extract_database_name(
                "****************************************************************"
            )
            .unwrap(),
            "my_database"
        );

        assert!(extract_database_name("mongodb://localhost:27017/").is_err());
        assert!(extract_database_name("mongodb://localhost:27017").is_err());
        assert!(extract_database_name("invalid_url").is_err());
    }

    #[tokio::test]
    async fn test_database_config_default() {
        let config = MongoDBConfig::default();
        assert_eq!(config.url, "mongodb://localhost:27017/wisdom_vault");
        assert_eq!(config.max_pool_size, Some(10));
        assert_eq!(config.min_pool_size, Some(2));
    }
}
