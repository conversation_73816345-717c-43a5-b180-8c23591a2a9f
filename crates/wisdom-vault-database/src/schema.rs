// ============================================================================
// MongoDB Schema Definitions and Index Management
// ============================================================================

use anyhow::Result;
use bson::doc;
use crate::connection::DatabaseConnection;
use crate::repositories::*;

/// Initialize the MongoDB database with collections and indexes
pub async fn initialize_schema(db_connection: &DatabaseConnection) -> Result<()> {
    // Create all indexes for better performance
    create_indexes(db_connection).await?;

    Ok(())
}

async fn create_indexes(db_connection: &DatabaseConnection) -> Result<()> {
    // ========================================================================
    // 用户相关索引 (User-related Indexes)
    // ========================================================================
    
    // Users collection indexes
    db_connection.create_index(
        USERS_COLLECTION,
        doc! { "username": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .unique(true)
            .name("unique_username".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        USERS_COLLECTION,
        doc! { "email": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .unique(true)
            .name("unique_email".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        USERS_COLLECTION,
        doc! { "isActive": 1, "createdAt": -1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_users_active_created".to_string())
            .build())
    ).await?;
    
    // Roles collection indexes
    db_connection.create_index(
        ROLES_COLLECTION,
        doc! { "name": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .unique(true)
            .name("unique_role_name".to_string())
            .build())
    ).await?;
    
    // User roles collection indexes
    db_connection.create_index(
        USER_ROLES_COLLECTION,
        doc! { "userId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_user_roles_user".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        USER_ROLES_COLLECTION,
        doc! { "roleId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_user_roles_role".to_string())
            .build())
    ).await?;
    
    // Role permissions collection indexes
    db_connection.create_index(
        ROLE_PERMISSIONS_COLLECTION,
        doc! { "roleId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_role_permissions_role".to_string())
            .build())
    ).await?;
    
    // ========================================================================
    // 组织相关索引 (Organization-related Indexes)
    // ========================================================================
    
    // Organizations collection indexes
    db_connection.create_index(
        ORGANIZATIONS_COLLECTION,
        doc! { "code": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .unique(true)
            .name("unique_org_code".to_string())
            .build())
    ).await?;
    
    // Departments collection indexes
    db_connection.create_index(
        DEPARTMENTS_COLLECTION,
        doc! { "organizationId": 1, "parentId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_departments_org_parent".to_string())
            .build())
    ).await?;
    
    // ========================================================================
    // 知识库相关索引 (Knowledge Base-related Indexes)
    // ========================================================================
    
    // Knowledge bases collection indexes
    db_connection.create_index(
        KNOWLEDGE_BASES_COLLECTION,
        doc! { "organizationId": 1, "visibility": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_kb_org_visibility".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        KNOWLEDGE_BASES_COLLECTION,
        doc! { "ownerId": 1, "createdAt": -1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_kb_owner_created".to_string())
            .build())
    ).await?;
    
    // Knowledge bases text search index
    db_connection.create_index(
        KNOWLEDGE_BASES_COLLECTION,
        doc! {
            "name": "text",
            "description": "text"
        },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_kb_text_search".to_string())
            .default_language("english".to_string())
            .build())
    ).await?;
    
    // ========================================================================
    // 文档相关索引 (Document-related Indexes)
    // ========================================================================
    
    // Documents collection indexes
    db_connection.create_index(
        DOCUMENTS_COLLECTION,
        doc! { "knowledgeBaseId": 1, "status": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_documents_kb_status".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        DOCUMENTS_COLLECTION,
        doc! { "uploadedBy": 1, "createdAt": -1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_documents_uploader_created".to_string())
            .build())
    ).await?;
    
    // Documents text search index
    db_connection.create_index(
        DOCUMENTS_COLLECTION,
        doc! {
            "title": "text",
            "content": "text",
            "summary": "text"
        },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_documents_text_search".to_string())
            .default_language("english".to_string())
            .weights(doc! {
                "title": 10,
                "summary": 5,
                "content": 1
            })
            .build())
    ).await?;
    
    // Categories collection indexes
    db_connection.create_index(
        CATEGORIES_COLLECTION,
        doc! { "knowledgeBaseId": 1, "parentId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_categories_kb_parent".to_string())
            .build())
    ).await?;
    
    // Tags collection indexes
    db_connection.create_index(
        TAGS_COLLECTION,
        doc! { "knowledgeBaseId": 1, "usageCount": -1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_tags_kb_usage".to_string())
            .build())
    ).await?;
    
    // Document tags collection indexes
    db_connection.create_index(
        DOCUMENT_TAGS_COLLECTION,
        doc! { "documentId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_document_tags_doc".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        DOCUMENT_TAGS_COLLECTION,
        doc! { "tagId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_document_tags_tag".to_string())
            .build())
    ).await?;
    
    // Document categories collection indexes
    db_connection.create_index(
        DOCUMENT_CATEGORIES_COLLECTION,
        doc! { "documentId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_document_categories_doc".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        DOCUMENT_CATEGORIES_COLLECTION,
        doc! { "categoryId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_document_categories_cat".to_string())
            .build())
    ).await?;
    
    // ========================================================================
    // 任务处理相关索引 (Task Processing-related Indexes)
    // ========================================================================
    
    // Processing tasks collection indexes
    db_connection.create_index(
        PROCESSING_TASKS_COLLECTION,
        doc! { "status": 1, "priority": -1, "createdAt": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_tasks_status_priority_created".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        PROCESSING_TASKS_COLLECTION,
        doc! { "taskType": 1, "status": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_tasks_type_status".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        PROCESSING_TASKS_COLLECTION,
        doc! { "resourceId": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_tasks_resource".to_string())
            .build())
    ).await?;
    
    // ========================================================================
    // 对话相关索引 (Conversation-related Indexes)
    // ========================================================================
    
    // Conversations collection indexes
    db_connection.create_index(
        CONVERSATIONS_COLLECTION,
        doc! { "userId": 1, "lastMessageAt": -1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_conversations_user_last_message".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        CONVERSATIONS_COLLECTION,
        doc! { "knowledgeBaseId": 1, "status": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_conversations_kb_status".to_string())
            .build())
    ).await?;
    
    // Messages collection indexes
    db_connection.create_index(
        MESSAGES_COLLECTION,
        doc! { "conversationId": 1, "createdAt": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_messages_conversation_created".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        MESSAGES_COLLECTION,
        doc! { "role": 1, "messageType": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_messages_role_type".to_string())
            .build())
    ).await?;
    
    // ========================================================================
    // 关系图相关索引 (Graph-related Indexes)
    // ========================================================================
    
    // Document relations collection indexes
    db_connection.create_index(
        DOCUMENT_RELATIONS_COLLECTION,
        doc! { "sourceDocumentId": 1, "relationType": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_doc_relations_source_type".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        DOCUMENT_RELATIONS_COLLECTION,
        doc! { "targetDocumentId": 1, "relationType": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_doc_relations_target_type".to_string())
            .build())
    ).await?;
    
    // Knowledge graph nodes collection indexes
    db_connection.create_index(
        KNOWLEDGE_GRAPH_NODES_COLLECTION,
        doc! { "knowledgeBaseId": 1, "entityType": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_kg_nodes_kb_type".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        KNOWLEDGE_GRAPH_NODES_COLLECTION,
        doc! { "name": "text", "description": "text" },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_kg_nodes_text_search".to_string())
            .default_language("english".to_string())
            .build())
    ).await?;
    
    // Knowledge graph edges collection indexes
    db_connection.create_index(
        KNOWLEDGE_GRAPH_EDGES_COLLECTION,
        doc! { "sourceNodeId": 1, "relationType": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_kg_edges_source_type".to_string())
            .build())
    ).await?;
    
    db_connection.create_index(
        KNOWLEDGE_GRAPH_EDGES_COLLECTION,
        doc! { "targetNodeId": 1, "relationType": 1 },
        Some(mongodb::options::IndexOptions::builder()
            .name("idx_kg_edges_target_type".to_string())
            .build())
    ).await?;
    
    Ok(())
}

/// Vector search configuration constants
pub mod vector_config {
    pub const DEFAULT_DIMENSION: usize = 1536;
    pub const SIMILARITY_THRESHOLD: f64 = 0.7;
    pub const MAX_RESULTS: usize = 100;
    pub const CHUNK_SIZE: usize = 512;
    pub const CHUNK_OVERLAP: usize = 50;

    // Enhanced vector configuration constants
    pub const HIGH_QUALITY_THRESHOLD: f64 = 0.85;
    pub const LOW_QUALITY_THRESHOLD: f64 = 0.5;
    pub const VECTOR_SEARCH_TIMEOUT_MS: u64 = 30000;
    pub const MAX_BATCH_SIZE: usize = 1000;

    // Distance function configurations
    pub const COSINE_SIMILARITY_THRESHOLD: f64 = 0.7;
    pub const EUCLIDEAN_DISTANCE_THRESHOLD: f64 = 1.0;
    pub const MANHATTAN_DISTANCE_THRESHOLD: f64 = 2.0;
}

/// Common MongoDB queries and aggregation pipelines
pub mod queries {
    /// User search aggregation pipeline
    pub fn user_search_pipeline(query: &str, limit: i64, skip: u64) -> Vec<bson::Document> {
        vec![
            bson::doc! {
                "$match": {
                    "$or": [
                        { "username": { "$regex": query, "$options": "i" } },
                        { "email": { "$regex": query, "$options": "i" } },
                        { "fullName": { "$regex": query, "$options": "i" } }
                    ]
                }
            },
            bson::doc! { "$sort": { "createdAt": -1 } },
            bson::doc! { "$skip": skip as i64 },
            bson::doc! { "$limit": limit }
        ]
    }
    
    /// Knowledge base search aggregation pipeline  
    pub fn knowledge_base_search_pipeline(query: &str, limit: i64, skip: u64) -> Vec<bson::Document> {
        vec![
            bson::doc! {
                "$match": {
                    "$or": [
                        { "name": { "$regex": query, "$options": "i" } },
                        { "description": { "$regex": query, "$options": "i" } }
                    ]
                }
            },
            bson::doc! { "$sort": { "createdAt": -1 } },
            bson::doc! { "$skip": skip as i64 },
            bson::doc! { "$limit": limit }
        ]
    }
    
    /// Document with categories and tags aggregation pipeline
    pub fn document_with_metadata_pipeline(document_id: &str) -> Vec<bson::Document> {
        vec![
            bson::doc! { "$match": { "_id": document_id } },
            bson::doc! {
                "$lookup": {
                    "from": "document_categories",
                    "localField": "_id",
                    "foreignField": "documentId", 
                    "as": "documentCategories"
                }
            },
            bson::doc! {
                "$lookup": {
                    "from": "categories",
                    "localField": "documentCategories.categoryId",
                    "foreignField": "_id",
                    "as": "categories"
                }
            },
            bson::doc! {
                "$lookup": {
                    "from": "document_tags",
                    "localField": "_id",
                    "foreignField": "documentId",
                    "as": "documentTags"
                }
            },
            bson::doc! {
                "$lookup": {
                    "from": "tags",
                    "localField": "documentTags.tagId",
                    "foreignField": "_id",
                    "as": "tags"
                }
            }
        ]
    }
    
    /// Task statistics aggregation pipeline
    pub fn task_statistics_pipeline() -> Vec<bson::Document> {
        vec![
            bson::doc! {
                "$group": {
                    "_id": bson::Bson::Null,
                    "totalTasks": { "$sum": 1 },
                    "queuedTasks": {
                        "$sum": { "$cond": [{ "$eq": ["$status", "Queued"] }, 1, 0] }
                    },
                    "runningTasks": {
                        "$sum": { "$cond": [{ "$eq": ["$status", "Running"] }, 1, 0] }
                    },
                    "completedTasks": {
                        "$sum": { "$cond": [{ "$eq": ["$status", "Completed"] }, 1, 0] }
                    },
                    "failedTasks": {
                        "$sum": { "$cond": [{ "$eq": ["$status", "Failed"] }, 1, 0] }
                    },
                    "cancelledTasks": {
                        "$sum": { "$cond": [{ "$eq": ["$status", "Cancelled"] }, 1, 0] }
                    },
                    "retryingTasks": {
                        "$sum": { "$cond": [{ "$eq": ["$status", "Retrying"] }, 1, 0] }
                    }
                }
            }
        ]
    }
}