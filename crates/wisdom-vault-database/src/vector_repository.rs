use std::collections::HashMap;

use anyhow::{Result, anyhow};
use async_trait::async_trait;
use qdrant_client::{
    qdrant::{
        Distance, PointStruct, Filter, PointId, Vectors, Value as QdrantValue,
    },
};
use serde::{Deserialize, Serialize};

use crate::{
    models::{DocumentEmbedding, DocumentChunk},
    qdrant_connection::QdrantConnection,
    repositories::VectorStatistics,
};

/// 向量搜索结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorSearchResult {
    pub point_id: String,
    pub score: f32,
    pub payload: HashMap<String, QdrantValue>,
}

/// 向量Repository trait
#[async_trait]
pub trait VectorRepository {
    /// 创建集合
    async fn create_collection(&self, collection_name: &str, vector_size: u64, distance: Distance) -> Result<()>;
    
    /// 检查集合是否存在
    async fn collection_exists(&self, collection_name: &str) -> Result<bool>;
    
    /// 删除集合
    async fn delete_collection(&self, collection_name: &str) -> Result<()>;
    
    /// 插入或更新向量点
    async fn upsert_vectors(&self, collection_name: &str, vectors: Vec<VectorPoint>) -> Result<()>;
    
    /// 搜索相似向量
    async fn search_vectors(
        &self,
        collection_name: &str,
        query_vector: Vec<f32>,
        limit: u64,
        score_threshold: Option<f32>,
        filter: Option<VectorFilter>,
    ) -> Result<Vec<VectorSearchResult>>;
    
    /// 删除向量点
    async fn delete_vectors(&self, collection_name: &str, point_ids: Vec<String>) -> Result<()>;
    
    /// 获取向量点数量
    async fn count_vectors(&self, collection_name: &str, filter: Option<VectorFilter>) -> Result<u64>;
    
    /// 批量插入文档嵌入
    async fn batch_insert_document_embeddings(&self, embeddings: Vec<DocumentEmbedding>) -> Result<()>;
    
    /// 批量插入分块嵌入
    async fn batch_insert_chunk_embeddings(&self, chunks: Vec<DocumentChunk>, embeddings: Vec<Vec<f32>>) -> Result<()>;
    
    /// 批量更新文档嵌入
    async fn batch_update_document_embeddings(&self, embeddings: Vec<DocumentEmbedding>) -> Result<()>;
    
    /// 批量更新分块嵌入
    async fn batch_update_chunk_embeddings(&self, chunks: Vec<DocumentChunk>, embeddings: Vec<Vec<f32>>) -> Result<()>;
    
    /// 删除文档的所有嵌入
    async fn delete_document_embeddings(&self, document_id: &str) -> Result<()>;
    
    /// 搜索文档相似向量
    async fn search_document_embeddings(
        &self,
        query_vector: Vec<f32>,
        limit: u64,
        knowledge_base_id: Option<&str>,
    ) -> Result<Vec<VectorSearchResult>>;
    
    /// 搜索分块相似向量
    async fn search_chunk_embeddings(
        &self,
        query_vector: Vec<f32>,
        limit: u64,
        knowledge_base_id: Option<&str>,
    ) -> Result<Vec<VectorSearchResult>>;
    
    /// 混合搜索 - 结合关键词和向量搜索
    async fn hybrid_search(
        &self,
        query_vector: Vec<f32>,
        keyword_filter: Option<&str>,
        knowledge_base_id: Option<&str>,
        limit: u64,
    ) -> Result<Vec<VectorSearchResult>>;
    
    /// 获取向量集合统计信息
    async fn get_collection_stats(&self, collection_name: &str) -> Result<VectorStatistics>;
    
    /// 初始化向量集合
    async fn initialize_collections(&self) -> Result<()>;
}

/// 向量点数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorPoint {
    pub id: String,
    pub vector: Vec<f32>,
    pub payload: HashMap<String, QdrantValue>,
}

/// 向量过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorFilter {
    pub conditions: HashMap<String, FilterCondition>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterCondition {
    Equals(String),
    In(Vec<String>),
    Range { gte: Option<f64>, lte: Option<f64> },
}

/// Qdrant向量Repository实现
pub struct QdrantVectorRepository {
    connection: QdrantConnection,
    document_collection: String,
    chunk_collection: String,
}

impl QdrantVectorRepository {
    pub fn new(connection: QdrantConnection) -> Self {
        Self {
            connection,
            document_collection: "document_embeddings".to_string(),
            chunk_collection: "chunk_embeddings".to_string(),
        }
    }
    
    /// 将VectorFilter转换为Qdrant的Filter
    fn convert_filter(filter: Option<VectorFilter>) -> Option<Filter> {
        filter.map(|f| {
            let mut conditions = Vec::new();
            
            for (key, condition) in f.conditions {
                match condition {
                    FilterCondition::Equals(value) => {
                        conditions.push(qdrant_client::qdrant::Condition {
                            condition_one_of: Some(
                                qdrant_client::qdrant::condition::ConditionOneOf::Field(
                                    qdrant_client::qdrant::FieldCondition {
                                        key,
                                        r#match: Some(qdrant_client::qdrant::Match {
                                            match_value: Some(
                                                qdrant_client::qdrant::r#match::MatchValue::Keyword(value)
                                            ),
                                        }),
                                        range: None,
                                        geo_bounding_box: None,
                                        geo_radius: None,
                                        values_count: None,
                                        geo_polygon: None,
                                        datetime_range: None,
                                        is_empty: None,
                                        is_null: None,
                                    }
                                )
                            ),
                        });
                    }
                    FilterCondition::In(values) => {
                        conditions.push(qdrant_client::qdrant::Condition {
                            condition_one_of: Some(
                                qdrant_client::qdrant::condition::ConditionOneOf::Field(
                                    qdrant_client::qdrant::FieldCondition {
                                        key,
                                        r#match: Some(qdrant_client::qdrant::Match {
                                            match_value: Some(
                                                qdrant_client::qdrant::r#match::MatchValue::Keywords(
                                                    qdrant_client::qdrant::RepeatedStrings { strings: values }
                                                )
                                            ),
                                        }),
                                        range: None,
                                        geo_bounding_box: None,  
                                        geo_radius: None,
                                        values_count: None,
                                        geo_polygon: None,
                                        datetime_range: None,
                                        is_empty: None,
                                        is_null: None,
                                    }
                                )
                            ),
                        });
                    }
                    FilterCondition::Range { gte, lte } => {
                        conditions.push(qdrant_client::qdrant::Condition {
                            condition_one_of: Some(
                                qdrant_client::qdrant::condition::ConditionOneOf::Field(
                                    qdrant_client::qdrant::FieldCondition {
                                        key,
                                        r#match: None,
                                        range: Some(qdrant_client::qdrant::Range {
                                            gte,
                                            gt: None,
                                            lte,
                                            lt: None,
                                        }),
                                        geo_bounding_box: None,
                                        geo_radius: None,
                                        values_count: None,
                                        geo_polygon: None,
                                        datetime_range: None,
                                        is_empty: None,
                                        is_null: None,
                                    }
                                )
                            ),
                        });
                    }
                }
            }
            
            Filter {
                should: Vec::new(),
                must: conditions,
                must_not: Vec::new(),
                min_should: None,
            }
        })
    }
    
    /// 创建文档嵌入向量点
    fn create_document_vector_point(embedding: &DocumentEmbedding) -> VectorPoint {
        let mut payload = HashMap::new();
        payload.insert("chunk_id".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::StringValue(embedding.chunk_id.clone()))
        });
        payload.insert("model_name".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::StringValue(embedding.model_name.clone()))
        });
        payload.insert("embedding_type".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::StringValue(
                format!("{:?}", embedding.embedding_type)
            ))
        });
        payload.insert("created_at".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::IntegerValue(embedding.created_at as i64))
        });
        
        VectorPoint {
            id: embedding.id.clone(),
            vector: embedding.embedding.clone(),
            payload,
        }
    }
    
    /// 创建分块向量点
    fn create_chunk_vector_point(chunk: &DocumentChunk, embedding: &[f32]) -> VectorPoint {
        let mut payload = HashMap::new();
        payload.insert("document_id".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::StringValue(chunk.document_id.clone()))
        });
        payload.insert("content".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::StringValue(chunk.content.clone()))
        });
        payload.insert("chunk_index".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::IntegerValue(chunk.chunk_index as i64))
        });
        payload.insert("chunk_type".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::StringValue(
                format!("{:?}", chunk.chunk_type)
            ))
        });
        payload.insert("created_at".to_string(), QdrantValue {
            kind: Some(qdrant_client::qdrant::value::Kind::IntegerValue(chunk.created_at as i64))
        });
        
        VectorPoint {
            id: chunk.id.clone(),
            vector: embedding.to_vec(),
            payload,
        }
    }
}

#[async_trait]
impl VectorRepository for QdrantVectorRepository {
    async fn create_collection(&self, collection_name: &str, vector_size: u64, distance: Distance) -> Result<()> {
        self.connection.create_collection(collection_name, vector_size, distance).await?;
        Ok(())
    }
    
    async fn collection_exists(&self, collection_name: &str) -> Result<bool> {
        self.connection.collection_exists(collection_name).await
    }
    
    async fn delete_collection(&self, collection_name: &str) -> Result<()> {
        self.connection.delete_collection(collection_name).await?;
        Ok(())
    }
    
    async fn upsert_vectors(&self, collection_name: &str, vectors: Vec<VectorPoint>) -> Result<()> {
        let points: Vec<PointStruct> = vectors.into_iter().map(|v| {
            PointStruct {
                id: Some(PointId::from(v.id)),
                vectors: Some(Vectors::from(v.vector)),
                payload: v.payload,
            }
        }).collect();
        
        self.connection.upsert_points(collection_name, points).await?;
        Ok(())
    }
    
    async fn search_vectors(
        &self,
        collection_name: &str,
        query_vector: Vec<f32>,
        limit: u64,
        score_threshold: Option<f32>,
        filter: Option<VectorFilter>,
    ) -> Result<Vec<VectorSearchResult>> {
        let qdrant_filter = Self::convert_filter(filter);
        
        let results = self.connection.search_points(
            collection_name,
            query_vector,
            limit,
            score_threshold,
            qdrant_filter,
        ).await?;
        
        let search_results = results.into_iter().map(|scored_point| {
            VectorSearchResult {
                point_id: match scored_point.id {
                    Some(id) => match id.point_id_options {
                        Some(qdrant_client::qdrant::point_id::PointIdOptions::Uuid(uuid)) => uuid,
                        Some(qdrant_client::qdrant::point_id::PointIdOptions::Num(num)) => num.to_string(),
                        None => String::new(),
                    },
                    None => String::new(),
                },
                score: scored_point.score,
                payload: scored_point.payload,
            }
        }).collect();
        
        Ok(search_results)
    }
    
    async fn delete_vectors(&self, collection_name: &str, point_ids: Vec<String>) -> Result<()> {
        self.connection.delete_points(collection_name, point_ids).await?;
        Ok(())
    }
    
    async fn count_vectors(&self, collection_name: &str, filter: Option<VectorFilter>) -> Result<u64> {
        let qdrant_filter = Self::convert_filter(filter);
        self.connection.count_points(collection_name, qdrant_filter).await
    }
    
    /// 批量插入文档嵌入
    async fn batch_insert_document_embeddings(&self, embeddings: Vec<DocumentEmbedding>) -> Result<()> {
        if embeddings.is_empty() {
            return Ok(());
        }

        let vector_points: Vec<VectorPoint> = embeddings.iter()
            .map(Self::create_document_vector_point)
            .collect();

        self.upsert_vectors(&self.document_collection, vector_points).await
    }

    /// 批量插入分块嵌入
    async fn batch_insert_chunk_embeddings(&self, chunks: Vec<DocumentChunk>, embeddings: Vec<Vec<f32>>) -> Result<()> {
        if chunks.len() != embeddings.len() {
            return Err(anyhow!("分块数量与嵌入向量数量不匹配"));
        }

        if chunks.is_empty() {
            return Ok(());
        }

        let vector_points: Vec<VectorPoint> = chunks.iter()
            .zip(embeddings.iter())
            .map(|(chunk, embedding)| Self::create_chunk_vector_point(chunk, embedding))
            .collect();

        self.upsert_vectors(&self.chunk_collection, vector_points).await
    }

    /// 批量更新文档嵌入
    async fn batch_update_document_embeddings(&self, embeddings: Vec<DocumentEmbedding>) -> Result<()> {
        // 批量更新就是批量插入，Qdrant会自动处理重复ID
        self.batch_insert_document_embeddings(embeddings).await
    }

    /// 批量更新分块嵌入  
    async fn batch_update_chunk_embeddings(&self, chunks: Vec<DocumentChunk>, embeddings: Vec<Vec<f32>>) -> Result<()> {
        // 批量更新就是批量插入，Qdrant会自动处理重复ID
        self.batch_insert_chunk_embeddings(chunks, embeddings).await
    }

    /// 删除文档的所有嵌入
    async fn delete_document_embeddings(&self, document_id: &str) -> Result<()> {
        // 首先搜索该文档的所有嵌入点
        let filter = VectorFilter {
            conditions: {
                let mut conditions = HashMap::new();
                conditions.insert("document_id".to_string(), FilterCondition::Equals(document_id.to_string()));
                conditions
            }
        };

        let search_results = self.search_vectors(
            &self.chunk_collection,
            vec![0.0; 1536], // 占位向量
            1000, // 最多删除1000个点
            None,
            Some(filter),
        ).await?;

        if !search_results.is_empty() {
            let point_ids: Vec<String> = search_results.into_iter()
                .map(|result| result.point_id)
                .collect();
            
            self.delete_vectors(&self.chunk_collection, point_ids).await?;
        }

        Ok(())
    }

    /// 搜索文档相似向量
    async fn search_document_embeddings(
        &self,
        query_vector: Vec<f32>,
        limit: u64,
        knowledge_base_id: Option<&str>,
    ) -> Result<Vec<VectorSearchResult>> {
        let filter = knowledge_base_id.map(|kb_id| {
            VectorFilter {
                conditions: {
                    let mut conditions = HashMap::new();
                    conditions.insert(
                        "knowledge_base_id".to_string(),
                        FilterCondition::Equals(kb_id.to_string())
                    );
                    conditions
                }
            }
        });

        self.search_vectors(
            &self.document_collection,
            query_vector,
            limit,
            Some(0.5), // 设置相似度阈值
            filter,
        ).await
    }

    /// 搜索分块相似向量
    async fn search_chunk_embeddings(
        &self,
        query_vector: Vec<f32>,
        limit: u64,
        knowledge_base_id: Option<&str>,
    ) -> Result<Vec<VectorSearchResult>> {
        let filter = knowledge_base_id.map(|kb_id| {
            VectorFilter {
                conditions: {
                    let mut conditions = HashMap::new();
                    conditions.insert(
                        "knowledge_base_id".to_string(),
                        FilterCondition::Equals(kb_id.to_string())
                    );
                    conditions
                }
            }
        });

        self.search_vectors(
            &self.chunk_collection,
            query_vector,
            limit,
            Some(0.6), // 分块搜索使用稍高的相似度阈值
            filter,
        ).await
    }

    /// 混合搜索 - 结合关键词和向量搜索
    async fn hybrid_search(
        &self,
        query_vector: Vec<f32>,
        keyword_filter: Option<&str>,
        knowledge_base_id: Option<&str>,
        limit: u64,
    ) -> Result<Vec<VectorSearchResult>> {
        let mut filter_conditions = HashMap::new();
        
        if let Some(kb_id) = knowledge_base_id {
            filter_conditions.insert(
                "knowledge_base_id".to_string(),
                FilterCondition::Equals(kb_id.to_string())
            );
        }
        
        // 如果有关键词过滤，添加到过滤条件
        if let Some(keyword) = keyword_filter {
            filter_conditions.insert(
                "content".to_string(),
                FilterCondition::Equals(keyword.to_string())
            );
        }
        
        let filter = if !filter_conditions.is_empty() {
            Some(VectorFilter { conditions: filter_conditions })
        } else {
            None
        };

        self.search_vectors(
            &self.chunk_collection,
            query_vector,
            limit,
            Some(0.65), // 混合搜索使用中等相似度阈值
            filter,
        ).await
    }

    /// 获取向量集合统计信息
    async fn get_collection_stats(&self, collection_name: &str) -> Result<VectorStatistics> {
        let total_vectors = self.count_vectors(collection_name, None).await?;
        
        Ok(VectorStatistics {
            total_vectors: total_vectors as i64,
            total_documents: 0, // 这里可以根据需要实现文档统计
            total_chunks: if collection_name == &self.chunk_collection { total_vectors as i64 } else { 0 },
            average_vector_size: 1536.0, // 假设使用OpenAI embeddings
        })
    }

    /// 初始化向量集合
    async fn initialize_collections(&self) -> Result<()> {
        // 初始化文档嵌入集合
        if !self.collection_exists(&self.document_collection).await? {
            self.create_collection(&self.document_collection, 1536, Distance::Cosine).await?;
        }
        
        // 初始化分块嵌入集合
        if !self.collection_exists(&self.chunk_collection).await? {
            self.create_collection(&self.chunk_collection, 1536, Distance::Cosine).await?;
        }
        
        Ok(())
    }
}