use crate::{connection::DatabaseConnection, models::*};
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use bson::{doc, oid::ObjectId};
use futures::TryStreamExt;
use mongodb::{Collection, options::FindOptions};
use wisdom_vault_common::time::current_millis;

// MongoDB 集合名称常量
pub const USERS_COLLECTION: &str = "users";
pub const ROLES_COLLECTION: &str = "roles";
pub const PERMISSIONS_COLLECTION: &str = "permissions";
pub const USER_ROLES_COLLECTION: &str = "user_roles";
pub const ROLE_PERMISSIONS_COLLECTION: &str = "role_permissions";
pub const ORGANIZATIONS_COLLECTION: &str = "organizations";
pub const DEPARTMENTS_COLLECTION: &str = "departments";
pub const KNOWLEDGE_BASES_COLLECTION: &str = "knowledge_bases";
pub const CATEGORIES_COLLECTION: &str = "categories";
pub const TAGS_COLLECTION: &str = "tags";
pub const DOCUMENTS_COLLECTION: &str = "documents";
pub const DOCUMENT_VERSIONS_COLLECTION: &str = "document_versions";
pub const DOCUMENT_TAGS_COLLECTION: &str = "document_tags";
pub const DOCUMENT_CATEGORIES_COLLECTION: &str = "document_categories";
pub const PROCESSING_TASKS_COLLECTION: &str = "processing_tasks";
pub const CONVERSATIONS_COLLECTION: &str = "conversations";
pub const MESSAGES_COLLECTION: &str = "messages";
pub const DOCUMENT_RELATIONS_COLLECTION: &str = "document_relations";
pub const KNOWLEDGE_GRAPH_NODES_COLLECTION: &str = "knowledge_graph_nodes";
pub const KNOWLEDGE_GRAPH_EDGES_COLLECTION: &str = "knowledge_graph_edges";

#[derive(Debug, Clone, Default, serde::Serialize, serde::Deserialize)]
pub struct TaskStatistics {
    pub total_tasks: i64,
    pub queued_tasks: i64,
    pub running_tasks: i64,
    pub completed_tasks: i64,
    pub failed_tasks: i64,
    pub cancelled_tasks: i64,
    pub retrying_tasks: i64,
}

/// 向量统计信息
#[derive(Debug, Clone, Default, serde::Serialize)]
pub struct VectorStatistics {
    pub total_vectors: i64,
    pub total_documents: i64,
    pub total_chunks: i64,
    pub average_vector_size: f64,
}

// MongoDB 辅助函数
fn ensure_object_id(id: &str) -> String {
    if ObjectId::parse_str(id).is_ok() {
        id.to_string()
    } else {
        ObjectId::new().to_hex()
    }
}

fn build_sort_doc(sort_field: Option<&str>, ascending: bool) -> bson::Document {
    let sort_value = if ascending { 1 } else { -1 };
    match sort_field {
        Some(field) => doc! { field: sort_value },
        None => doc! { "createdAt": -1 }, // 默认按创建时间降序
    }
}

// Repository trait definitions
#[async_trait]
pub trait UserRepository {
    async fn create(&self, user: &User) -> Result<User>;
    async fn find_by_id(&self, id: &str) -> Result<Option<User>>;
    async fn find_by_email(&self, email: &str) -> Result<Option<User>>;
    async fn find_by_username(&self, username: &str) -> Result<Option<User>>;
    async fn find_by_username_or_email(&self, identifier: &str) -> Result<Option<User>>;
    async fn update(&self, user: &User) -> Result<User>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn list(
        &self,
        query: Option<&str>,
        organization_id: Option<&str>,
        is_active: Option<bool>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<User>>;
    async fn count(&self) -> Result<u64>;
    async fn search(
        &self,
        query: Option<&str>,
        organization_id: Option<&str>,
        is_active: Option<bool>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<(Vec<User>, u64)>;
    async fn set_active_status(&self, id: &str, is_active: bool) -> Result<bool>;
}

// MongoDB User Repository 实现
pub struct MongoUserRepository {
    collection: Collection<User>,
}

impl MongoUserRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(USERS_COLLECTION),
        }
    }

    fn build_filter(
        query: Option<&str>,
        organization_id: Option<&str>,
        is_active: Option<bool>,
    ) -> bson::Document {
        let mut filter = doc! {};
        if let Some(organization_id) = organization_id {
            filter.insert("organizationId", organization_id);
        }
        if let Some(query) = query {
            filter.insert(
                "$or",
                vec![
                    doc! { "username": { "$regex": query, "$options": "i" } },
                    doc! { "email": { "$regex": query, "$options": "i" } },
                    doc! { "fullName": { "$regex": query, "$options": "i" } },
                ],
            );
        }
        if let Some(is_active) = is_active {
            filter.insert("isActive", is_active);
        }
        filter
    }
}

#[async_trait]
impl UserRepository for MongoUserRepository {
    async fn create(&self, user: &User) -> Result<User> {
        let mut new_user = user.clone();

        // 确保有有效的ID
        if new_user.id.is_empty() {
            new_user.id = ObjectId::new().to_hex();
        }

        // 设置时间戳
        let now = current_millis();
        new_user.created_at = now;
        new_user.updated_at = now;

        self.collection
            .insert_one(&new_user)
            .await
            .map_err(|e| anyhow!("创建用户失败: {}", e))?;

        Ok(new_user)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<User>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找用户失败: {}", e))
    }

    async fn find_by_email(&self, email: &str) -> Result<Option<User>> {
        let filter = doc! { "email": email };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("通过邮箱查找用户失败: {}", e))
    }

    async fn find_by_username(&self, username: &str) -> Result<Option<User>> {
        let filter = doc! { "username": username };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("通过用户名查找用户失败: {}", e))
    }

    async fn find_by_username_or_email(&self, identifier: &str) -> Result<Option<User>> {
        let filter = doc! {
            "$or": [
                { "username": identifier },
                { "email": identifier }
            ]
        };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("通过用户名或邮箱查找用户失败: {}", e))
    }

    async fn update(&self, user: &User) -> Result<User> {
        let mut updated_user = user.clone();
        updated_user.updated_at = current_millis();

        let filter = doc! { "_id": &user.id };
        let update = doc! { "$set": bson::to_document(&updated_user)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新用户失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("用户不存在: {}", user.id));
        }

        Ok(updated_user)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除用户失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn list(
        &self,
        query: Option<&str>,
        organization_id: Option<&str>,
        is_active: Option<bool>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<User>> {
        let filter = Self::build_filter(query, organization_id, is_active);

        let mut options = FindOptions::builder()
            .sort(build_sort_doc(Some("createdAt"), false))
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("获取用户列表失败: {}", e))?;

        let mut users = Vec::new();
        while let Some(user) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历用户失败: {}", e))?
        {
            users.push(user);
        }

        Ok(users)
    }

    async fn count(&self) -> Result<u64> {
        self.collection
            .count_documents(doc! {})
            .await
            .map_err(|e| anyhow!("统计用户数量失败: {}", e))
    }

    async fn search(
        &self,
        query: Option<&str>,
        organization_id: Option<&str>,
        is_active: Option<bool>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<(Vec<User>, u64)> {
        let filter = Self::build_filter(query, organization_id, is_active);

        let count = self
            .collection
            .count_documents(filter.clone())
            .await
            .map_err(|e| anyhow!("统计用户数量失败: {}", e))?;

        let mut options = FindOptions::builder()
            .sort(build_sort_doc(Some("createdAt"), false))
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("搜索用户失败: {}", e))?;

        let mut users = Vec::new();
        while let Some(user) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历搜索结果失败: {}", e))?
        {
            users.push(user);
        }

        Ok((users, count))
    }

    async fn set_active_status(&self, id: &str, is_active: bool) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": {
                "isActive": is_active,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新用户状态失败: {}", e))?;

        Ok(result.matched_count > 0)
    }
}

// Role Repository
#[async_trait]
pub trait RoleRepository {
    async fn create(&self, role: &Role) -> Result<Role>;
    async fn find_by_id(&self, id: &str) -> Result<Option<Role>>;
    async fn find_by_name(&self, name: &str) -> Result<Option<Role>>;
    async fn update(&self, role: &Role) -> Result<Role>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn list(&self) -> Result<Vec<Role>>;
    async fn count(&self) -> Result<u64>;
}

pub struct MongoRoleRepository {
    collection: Collection<Role>,
}

impl MongoRoleRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(ROLES_COLLECTION),
        }
    }
}

#[async_trait]
impl RoleRepository for MongoRoleRepository {
    async fn create(&self, role: &Role) -> Result<Role> {
        let mut new_role = role.clone();

        if new_role.id.is_empty() {
            new_role.id = ObjectId::new().to_hex();
        }

        let now = current_millis();
        new_role.created_at = now;
        new_role.updated_at = now;

        self.collection
            .insert_one(&new_role)
            .await
            .map_err(|e| anyhow!("创建角色失败: {}", e))?;

        Ok(new_role)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<Role>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找角色失败: {}", e))
    }

    async fn find_by_name(&self, name: &str) -> Result<Option<Role>> {
        let filter = doc! { "name": name };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("通过名称查找角色失败: {}", e))
    }

    async fn update(&self, role: &Role) -> Result<Role> {
        let mut updated_role = role.clone();
        updated_role.updated_at = current_millis();

        let filter = doc! { "_id": &role.id };
        let update = doc! { "$set": bson::to_document(&updated_role)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新角色失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("角色不存在: {}", role.id));
        }

        Ok(updated_role)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除角色失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn list(&self) -> Result<Vec<Role>> {
        let mut cursor = self
            .collection
            .find(doc! {})
            .sort(doc! { "name": 1 })
            .await
            .map_err(|e| anyhow!("获取角色列表失败: {}", e))?;

        let mut roles = Vec::new();
        while let Some(role) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历角色失败: {}", e))?
        {
            roles.push(role);
        }

        Ok(roles)
    }

    async fn count(&self) -> Result<u64> {
        self.collection
            .count_documents(doc! {})
            .await
            .map_err(|e| anyhow!("统计角色数量失败: {}", e))
    }
}

// Permission Repository
#[async_trait]
pub trait PermissionRepository {
    async fn create(&self, permission: &Permission) -> Result<Permission>;
    async fn find_by_id(&self, id: &str) -> Result<Option<Permission>>;
    async fn find_by_name(&self, name: &str) -> Result<Option<Permission>>;
    async fn list(&self) -> Result<Vec<Permission>>;
    async fn find_by_role(&self, role_id: &str) -> Result<Vec<Permission>>;
    async fn count(&self) -> Result<u64>;
}

pub struct MongoPermissionRepository {
    collection: Collection<Permission>,
    role_permission_collection: Collection<RolePermission>,
}

impl MongoPermissionRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(PERMISSIONS_COLLECTION),
            role_permission_collection: db_connection.collection(ROLE_PERMISSIONS_COLLECTION),
        }
    }
}

#[async_trait]
impl PermissionRepository for MongoPermissionRepository {
    async fn create(&self, permission: &Permission) -> Result<Permission> {
        let mut new_permission = permission.clone();

        if new_permission.id.is_empty() {
            new_permission.id = ObjectId::new().to_hex();
        }

        new_permission.created_at = current_millis();

        self.collection
            .insert_one(&new_permission)
            .await
            .map_err(|e| anyhow!("创建权限失败: {}", e))?;

        Ok(new_permission)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<Permission>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找权限失败: {}", e))
    }

    async fn find_by_name(&self, name: &str) -> Result<Option<Permission>> {
        let filter = doc! { "name": name };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("通过名称查找权限失败: {}", e))
    }

    async fn list(&self) -> Result<Vec<Permission>> {
        let mut cursor = self
            .collection
            .find(doc! {})
            .sort(doc! { "name": 1 })
            .await
            .map_err(|e| anyhow!("获取权限列表失败: {}", e))?;

        let mut permissions = Vec::new();
        while let Some(permission) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历权限失败: {}", e))?
        {
            permissions.push(permission);
        }

        Ok(permissions)
    }

    async fn find_by_role(&self, role_id: &str) -> Result<Vec<Permission>> {
        // 使用聚合管道进行关联查询
        let pipeline = vec![
            doc! {
                "$match": { "roleId": role_id }
            },
            doc! {
                "$lookup": {
                    "from": PERMISSIONS_COLLECTION,
                    "localField": "permissionId",
                    "foreignField": "_id",
                    "as": "permission"
                }
            },
            doc! {
                "$unwind": "$permission"
            },
            doc! {
                "$replaceRoot": { "newRoot": "$permission" }
            },
        ];

        let mut cursor = self
            .role_permission_collection
            .aggregate(pipeline)
            .await
            .map_err(|e| anyhow!("查找角色权限失败: {}", e))?;

        let mut permissions = Vec::new();
        while let Some(result) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历角色权限失败: {}", e))?
        {
            if let Ok(permission) = bson::from_document::<Permission>(result) {
                permissions.push(permission);
            }
        }

        Ok(permissions)
    }

    async fn count(&self) -> Result<u64> {
        self.collection
            .count_documents(doc! {})
            .await
            .map_err(|e| anyhow!("统计权限数量失败: {}", e))
    }
}

// UserRole Repository
#[async_trait]
pub trait UserRoleRepository {
    async fn assign_role(
        &self,
        user_id: &str,
        role_id: &str,
        assigned_by: &str,
    ) -> Result<UserRole>;
    async fn remove_role(&self, user_id: &str, role_id: &str) -> Result<bool>;
    async fn find_by_user(&self, user_id: &str) -> Result<Vec<UserRole>>;
    async fn find_by_role(&self, role_id: &str) -> Result<Vec<UserRole>>;
}

pub struct MongoUserRoleRepository {
    collection: Collection<UserRole>,
}

impl MongoUserRoleRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(USER_ROLES_COLLECTION),
        }
    }
}

#[async_trait]
impl UserRoleRepository for MongoUserRoleRepository {
    async fn assign_role(
        &self,
        user_id: &str,
        role_id: &str,
        assigned_by: &str,
    ) -> Result<UserRole> {
        let user_role = UserRole {
            id: ObjectId::new().to_hex(),
            user_id: user_id.to_string(),
            role_id: role_id.to_string(),
            assigned_by: assigned_by.to_string(),
            assigned_at: current_millis(),
            expires_at: None,
        };

        self.collection
            .insert_one(&user_role)
            .await
            .map_err(|e| anyhow!("分配角色失败: {}", e))?;

        Ok(user_role)
    }

    async fn remove_role(&self, user_id: &str, role_id: &str) -> Result<bool> {
        let filter = doc! {
            "userId": user_id,
            "roleId": role_id
        };

        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("移除角色失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn find_by_user(&self, user_id: &str) -> Result<Vec<UserRole>> {
        let filter = doc! { "userId": user_id };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找用户角色失败: {}", e))?;

        let mut user_roles = Vec::new();
        while let Some(user_role) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历用户角色失败: {}", e))?
        {
            user_roles.push(user_role);
        }

        Ok(user_roles)
    }

    async fn find_by_role(&self, role_id: &str) -> Result<Vec<UserRole>> {
        let filter = doc! { "roleId": role_id };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找角色用户失败: {}", e))?;

        let mut user_roles = Vec::new();
        while let Some(user_role) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历角色用户失败: {}", e))?
        {
            user_roles.push(user_role);
        }

        Ok(user_roles)
    }
}

// RolePermission Repository
#[async_trait]
pub trait RolePermissionRepository {
    async fn create(&self, role_permission: &RolePermission) -> Result<RolePermission>;
    async fn find_by_role(&self, role_id: &str) -> Result<Vec<RolePermission>>;
    async fn find_by_permission(&self, permission_id: &str) -> Result<Vec<RolePermission>>;
    async fn find_by_role_and_permission(
        &self,
        role_id: &str,
        permission_id: &str,
    ) -> Result<Option<RolePermission>>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn delete_by_role(&self, role_id: &str) -> Result<u64>;
    async fn delete_by_permission(&self, permission_id: &str) -> Result<u64>;
    async fn delete_by_role_and_permission(
        &self,
        role_id: &str,
        permission_id: &str,
    ) -> Result<bool>;
    async fn count(&self) -> Result<i64>;
}

pub struct MongoRolePermissionRepository {
    collection: Collection<RolePermission>,
}

impl MongoRolePermissionRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(ROLE_PERMISSIONS_COLLECTION),
        }
    }
}

#[async_trait]
impl RolePermissionRepository for MongoRolePermissionRepository {
    async fn create(&self, role_permission: &RolePermission) -> Result<RolePermission> {
        let mut new_role_permission = role_permission.clone();
        if new_role_permission.id.is_empty() {
            new_role_permission.id = ObjectId::new().to_hex();
        }

        self.collection
            .insert_one(&new_role_permission)
            .await
            .map_err(|e| anyhow!("创建角色权限关联失败: {}", e))?;
        Ok(new_role_permission)
    }

    async fn find_by_role(&self, role_id: &str) -> Result<Vec<RolePermission>> {
        let filter = doc! { "roleId": role_id };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找角色权限失败: {}", e))?;

        let mut role_permissions = Vec::new();
        while let Some(role_permission) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历角色权限失败: {}", e))?
        {
            role_permissions.push(role_permission);
        }
        Ok(role_permissions)
    }

    async fn find_by_permission(&self, permission_id: &str) -> Result<Vec<RolePermission>> {
        let filter = doc! { "permissionId": permission_id };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找权限角色失败: {}", e))?;

        let mut role_permissions = Vec::new();
        while let Some(role_permission) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历权限角色失败: {}", e))?
        {
            role_permissions.push(role_permission);
        }
        Ok(role_permissions)
    }

    async fn find_by_role_and_permission(
        &self,
        role_id: &str,
        permission_id: &str,
    ) -> Result<Option<RolePermission>> {
        let filter = doc! {
            "roleId": role_id,
            "permissionId": permission_id
        };

        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找角色权限关联失败: {}", e))
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除角色权限关联失败: {}", e))?;
        Ok(result.deleted_count > 0)
    }

    async fn delete_by_role(&self, role_id: &str) -> Result<u64> {
        let filter = doc! { "roleId": role_id };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("删除角色相关权限失败: {}", e))?;
        Ok(result.deleted_count)
    }

    async fn delete_by_permission(&self, permission_id: &str) -> Result<u64> {
        let filter = doc! { "permissionId": permission_id };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("删除权限相关角色失败: {}", e))?;
        Ok(result.deleted_count)
    }

    async fn delete_by_role_and_permission(
        &self,
        role_id: &str,
        permission_id: &str,
    ) -> Result<bool> {
        let filter = doc! {
            "roleId": role_id,
            "permissionId": permission_id
        };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除角色权限关联失败: {}", e))?;
        Ok(result.deleted_count > 0)
    }

    async fn count(&self) -> Result<i64> {
        self.collection
            .count_documents(doc! {})
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计角色权限数量失败: {}", e))
    }
}

// KnowledgeBase Repository
#[async_trait]
pub trait KnowledgeBaseRepository {
    async fn create(&self, kb: &KnowledgeBase) -> Result<KnowledgeBase>;
    async fn find_by_id(&self, id: &str) -> Result<Option<KnowledgeBase>>;
    async fn find_by_owner(&self, owner_id: &str) -> Result<Vec<KnowledgeBase>>;
    async fn find_by_organization(&self, org_id: &str) -> Result<Vec<KnowledgeBase>>;
    async fn update(&self, kb: &KnowledgeBase) -> Result<KnowledgeBase>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn list(&self, limit: Option<u32>, offset: Option<u32>) -> Result<Vec<KnowledgeBase>>;
    async fn search(
        &self,
        query: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<KnowledgeBase>>;
    async fn count(&self) -> Result<i64>;
    async fn update_statistics(&self, id: &str, stats: &KnowledgeBaseStatistics) -> Result<bool>;
}

pub struct MongoKnowledgeBaseRepository {
    collection: Collection<KnowledgeBase>,
}

impl MongoKnowledgeBaseRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(KNOWLEDGE_BASES_COLLECTION),
        }
    }
}

#[async_trait]
impl KnowledgeBaseRepository for MongoKnowledgeBaseRepository {
    async fn create(&self, kb: &KnowledgeBase) -> Result<KnowledgeBase> {
        let mut new_kb = kb.clone();

        if new_kb.id.is_empty() {
            new_kb.id = ObjectId::new().to_hex();
        }

        let now = current_millis();
        new_kb.created_at = now;
        new_kb.updated_at = now;

        self.collection
            .insert_one(&new_kb)
            .await
            .map_err(|e| anyhow!("创建知识库失败: {}", e))?;

        Ok(new_kb)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<KnowledgeBase>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找知识库失败: {}", e))
    }

    async fn find_by_owner(&self, owner_id: &str) -> Result<Vec<KnowledgeBase>> {
        let filter = doc! { "ownerId": owner_id };
        let mut cursor = self
            .collection
            .find(filter)
            .sort(doc! { "createdAt": -1 })
            .await
            .map_err(|e| anyhow!("查找用户知识库失败: {}", e))?;

        let mut knowledge_bases = Vec::new();
        while let Some(kb) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历知识库失败: {}", e))?
        {
            knowledge_bases.push(kb);
        }

        Ok(knowledge_bases)
    }

    async fn find_by_organization(&self, org_id: &str) -> Result<Vec<KnowledgeBase>> {
        let filter = doc! { "organizationId": org_id };
        let mut cursor = self
            .collection
            .find(filter)
            .sort(doc! { "createdAt": -1 })
            .await
            .map_err(|e| anyhow!("查找组织知识库失败: {}", e))?;

        let mut knowledge_bases = Vec::new();
        while let Some(kb) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历知识库失败: {}", e))?
        {
            knowledge_bases.push(kb);
        }

        Ok(knowledge_bases)
    }

    async fn update(&self, kb: &KnowledgeBase) -> Result<KnowledgeBase> {
        let mut updated_kb = kb.clone();
        updated_kb.updated_at = current_millis();

        let filter = doc! { "_id": &kb.id };
        let update = doc! { "$set": bson::to_document(&updated_kb)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新知识库失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("知识库不存在: {}", kb.id));
        }

        Ok(updated_kb)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除知识库失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn list(&self, limit: Option<u32>, offset: Option<u32>) -> Result<Vec<KnowledgeBase>> {
        let mut options = FindOptions::builder()
            .sort(build_sort_doc(Some("createdAt"), false))
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(doc! {})
            .with_options(options)
            .await
            .map_err(|e| anyhow!("获取知识库列表失败: {}", e))?;

        let mut knowledge_bases = Vec::new();
        while let Some(kb) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历知识库失败: {}", e))?
        {
            knowledge_bases.push(kb);
        }

        Ok(knowledge_bases)
    }

    async fn search(
        &self,
        query: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<KnowledgeBase>> {
        let filter = doc! {
            "$or": [
                { "name": { "$regex": query, "$options": "i" } },
                { "description": { "$regex": query, "$options": "i" } }
            ]
        };

        let mut options = FindOptions::builder()
            .sort(build_sort_doc(Some("createdAt"), false))
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("搜索知识库失败: {}", e))?;

        let mut knowledge_bases = Vec::new();
        while let Some(kb) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历搜索结果失败: {}", e))?
        {
            knowledge_bases.push(kb);
        }

        Ok(knowledge_bases)
    }

    async fn count(&self) -> Result<i64> {
        self.collection
            .count_documents(doc! {})
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计知识库数量失败: {}", e))
    }

    async fn update_statistics(&self, id: &str, stats: &KnowledgeBaseStatistics) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": {
                "statistics": bson::to_bson(stats)?,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新知识库统计信息失败: {}", e))?;

        Ok(result.matched_count > 0)
    }
}

// ============================================================================
// Document Repository (新增)
// ============================================================================

#[async_trait]
pub trait DocumentRepository {
    async fn create(&self, document: &crate::models::Document) -> Result<crate::models::Document>;
    async fn find_by_id(&self, id: &str) -> Result<Option<crate::models::Document>>;
    async fn update(&self, document: &crate::models::Document) -> Result<crate::models::Document>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn list(
        &self,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<crate::models::Document>>;
    async fn find_by_knowledge_base(
        &self,
        kb_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<crate::models::Document>>;
    async fn find_by_status(
        &self,
        status: &DocumentStatus,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<crate::models::Document>>;
    async fn update_status(&self, id: &str, status: DocumentStatus) -> Result<bool>;
    async fn search(
        &self,
        query: &str,
        kb_id: Option<&str>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<crate::models::Document>>;
    async fn count(&self) -> Result<i64>;
    async fn count_by_status(&self, status: &DocumentStatus) -> Result<i64>;
    async fn update_processing_metadata(
        &self,
        id: &str,
        metadata: &crate::models::DocumentProcessingMetadata,
    ) -> Result<bool>;
    async fn find_by_checksum(&self, checksum: &str) -> Result<Vec<crate::models::Document>>;
    async fn batch_update_status(&self, ids: Vec<String>, status: DocumentStatus) -> Result<u64>;
    async fn find_failed_documents(
        &self,
        kb_id: Option<&str>,
    ) -> Result<Vec<crate::models::Document>>;
    async fn find_processing_documents(
        &self,
        kb_id: Option<&str>,
    ) -> Result<Vec<crate::models::Document>>;
    async fn find_by_file_type(
        &self,
        file_type: &str,
        kb_id: Option<&str>,
    ) -> Result<Vec<crate::models::Document>>;
}

pub struct MongoDocumentRepository {
    collection: Collection<crate::models::Document>,
}

impl MongoDocumentRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(DOCUMENTS_COLLECTION),
        }
    }
}

#[async_trait]
impl DocumentRepository for MongoDocumentRepository {
    async fn create(&self, document: &crate::models::Document) -> Result<crate::models::Document> {
        let mut new_document = document.clone();

        if new_document.id.is_empty() {
            new_document.id = ObjectId::new().to_hex();
        }

        new_document.created_at = current_millis();
        new_document.updated_at = current_millis();

        self.collection
            .insert_one(&new_document)
            .await
            .map_err(|e| anyhow!("创建文档失败: {}", e))?;

        Ok(new_document)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<crate::models::Document>> {
        let filter = doc! { "_id": id };

        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找文档失败: {}", e))
    }

    async fn update(&self, document: &crate::models::Document) -> Result<crate::models::Document> {
        let mut updated_document = document.clone();
        updated_document.updated_at = current_millis();

        let filter = doc! { "_id": &document.id };
        let update = doc! {
            "$set": bson::to_bson(&updated_document)?
        };

        self.collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新文档失败: {}", e))?;

        Ok(updated_document)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };

        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除文档失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn list(
        &self,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<crate::models::Document>> {
        let mut options = FindOptions::builder()
            .sort(build_sort_doc(Some("createdAt"), false))
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(doc! {})
            .with_options(options)
            .await
            .map_err(|e| anyhow!("列出文档失败: {}", e))?;

        let mut documents = Vec::new();
        while let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历文档失败: {}", e))?
        {
            documents.push(doc);
        }

        Ok(documents)
    }

    async fn find_by_knowledge_base(
        &self,
        kb_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<crate::models::Document>> {
        let filter = doc! { "knowledgeBaseId": kb_id };

        let mut options = FindOptions::builder()
            .sort(build_sort_doc(Some("createdAt"), false))
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按知识库查找文档失败: {}", e))?;

        let mut documents = Vec::new();
        while let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历文档失败: {}", e))?
        {
            documents.push(doc);
        }

        Ok(documents)
    }

    async fn find_by_status(
        &self,
        status: &DocumentStatus,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<crate::models::Document>> {
        let filter = doc! { "status": bson::to_bson(status)? };

        let mut options = FindOptions::builder()
            .sort(build_sort_doc(Some("createdAt"), false))
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按状态查找文档失败: {}", e))?;

        let mut documents = Vec::new();
        while let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历文档失败: {}", e))?
        {
            documents.push(doc);
        }

        Ok(documents)
    }

    async fn update_status(&self, id: &str, status: DocumentStatus) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": {
                "status": bson::to_bson(&status)?,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新文档状态失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn search(
        &self,
        query: &str,
        kb_id: Option<&str>,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<crate::models::Document>> {
        let mut filter = doc! {
            "$or": [
                { "title": { "$regex": query, "$options": "i" } },
                { "content": { "$regex": query, "$options": "i" } }
            ]
        };

        if let Some(kb_id) = kb_id {
            filter.insert("knowledgeBaseId", kb_id);
        }

        let mut options = FindOptions::builder()
            .sort(build_sort_doc(Some("createdAt"), false))
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("搜索文档失败: {}", e))?;

        let mut documents = Vec::new();
        while let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历搜索结果失败: {}", e))?
        {
            documents.push(doc);
        }

        Ok(documents)
    }

    async fn count(&self) -> Result<i64> {
        self.collection
            .count_documents(doc! {})
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计文档数量失败: {}", e))
    }

    async fn count_by_status(&self, status: &DocumentStatus) -> Result<i64> {
        let filter = doc! { "status": bson::to_bson(status)? };

        self.collection
            .count_documents(filter)
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("按状态统计文档数量失败: {}", e))
    }

    async fn update_processing_metadata(
        &self,
        id: &str,
        metadata: &crate::models::DocumentProcessingMetadata,
    ) -> Result<bool> {
        let object_id = ensure_object_id(id);
        let filter = doc! { "_id": object_id };
        let update = doc! {
            "$set": {
                "processingMetadata": bson::to_bson(metadata)?,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新文档处理元数据失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn find_by_checksum(&self, checksum: &str) -> Result<Vec<crate::models::Document>> {
        let filter = doc! { "checksum": checksum };
        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("按校验和查找文档失败: {}", e))?;

        let mut documents = Vec::new();
        while let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("读取文档失败: {}", e))?
        {
            documents.push(doc);
        }

        Ok(documents)
    }

    async fn batch_update_status(&self, ids: Vec<String>, status: DocumentStatus) -> Result<u64> {
        if ids.is_empty() {
            return Ok(0);
        }

        let object_ids: Vec<String> = ids.iter().map(|id| ensure_object_id(id)).collect();

        let filter = doc! { "_id": { "$in": object_ids } };
        let update = doc! {
            "$set": {
                "status": bson::to_bson(&status)?,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_many(filter, update)
            .await
            .map_err(|e| anyhow!("批量更新文档状态失败: {}", e))?;

        Ok(result.modified_count)
    }

    async fn find_failed_documents(
        &self,
        kb_id: Option<&str>,
    ) -> Result<Vec<crate::models::Document>> {
        let mut filter = doc! { "status": bson::to_bson(&DocumentStatus::Failed)? };

        if let Some(kb_id) = kb_id {
            filter.insert("knowledgeBaseId", kb_id);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找失败文档失败: {}", e))?;

        let mut documents = Vec::new();
        while let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("读取文档失败: {}", e))?
        {
            documents.push(doc);
        }

        Ok(documents)
    }

    async fn find_processing_documents(
        &self,
        kb_id: Option<&str>,
    ) -> Result<Vec<crate::models::Document>> {
        let mut filter = doc! { "status": bson::to_bson(&DocumentStatus::Processing)? };

        if let Some(kb_id) = kb_id {
            filter.insert("knowledgeBaseId", kb_id);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("查找处理中文档失败: {}", e))?;

        let mut documents = Vec::new();
        while let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("读取文档失败: {}", e))?
        {
            documents.push(doc);
        }

        Ok(documents)
    }

    async fn find_by_file_type(
        &self,
        file_type: &str,
        kb_id: Option<&str>,
    ) -> Result<Vec<crate::models::Document>> {
        let mut filter = doc! { "fileType": { "$regex": file_type, "$options": "i" } };

        if let Some(kb_id) = kb_id {
            filter.insert("knowledgeBaseId", kb_id);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .await
            .map_err(|e| anyhow!("按文件类型查找文档失败: {}", e))?;

        let mut documents = Vec::new();
        while let Some(doc) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("读取文档失败: {}", e))?
        {
            documents.push(doc);
        }

        Ok(documents)
    }
}

// ============================================================================
// DocumentChunk Repository
// ============================================================================

#[async_trait]
pub trait DocumentChunkRepository {
    async fn create(&self, chunk: &DocumentChunk) -> Result<DocumentChunk>;
    async fn find_by_id(&self, id: &str) -> Result<Option<DocumentChunk>>;
    async fn find_by_document_id(&self, document_id: &str) -> Result<Vec<DocumentChunk>>;
    async fn update(&self, chunk: &DocumentChunk) -> Result<DocumentChunk>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn delete_by_document_id(&self, document_id: &str) -> Result<u64>;
    async fn count_by_document(&self, document_id: &str) -> Result<i64>;
}

pub struct MongoDocumentChunkRepository {
    collection: Collection<DocumentChunk>,
}

impl MongoDocumentChunkRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection("document_chunks"),
        }
    }
}

#[async_trait]
impl DocumentChunkRepository for MongoDocumentChunkRepository {
    async fn create(&self, chunk: &DocumentChunk) -> Result<DocumentChunk> {
        let mut new_chunk = chunk.clone();

        if new_chunk.id.is_empty() {
            new_chunk.id = ObjectId::new().to_hex();
        }

        new_chunk.created_at = current_millis();

        self.collection
            .insert_one(&new_chunk)
            .await
            .map_err(|e| anyhow!("创建文档分块失败: {}", e))?;

        Ok(new_chunk)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<DocumentChunk>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找文档分块失败: {}", e))
    }

    async fn find_by_document_id(&self, document_id: &str) -> Result<Vec<DocumentChunk>> {
        let filter = doc! { "documentId": document_id };
        let options = FindOptions::builder()
            .sort(doc! { "chunkIndex": 1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按文档ID查找分块失败: {}", e))?;

        let mut chunks = Vec::new();
        while let Some(chunk) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历分块失败: {}", e))?
        {
            chunks.push(chunk);
        }

        Ok(chunks)
    }

    async fn update(&self, chunk: &DocumentChunk) -> Result<DocumentChunk> {
        let filter = doc! { "_id": &chunk.id };
        let update = doc! { "$set": bson::to_bson(chunk)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新文档分块失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("文档分块不存在: {}", chunk.id));
        }

        Ok(chunk.clone())
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除文档分块失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn delete_by_document_id(&self, document_id: &str) -> Result<u64> {
        let filter = doc! { "documentId": document_id };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("按文档ID删除分块失败: {}", e))?;

        Ok(result.deleted_count)
    }

    async fn count_by_document(&self, document_id: &str) -> Result<i64> {
        let filter = doc! { "documentId": document_id };
        self.collection
            .count_documents(filter)
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计文档分块数量失败: {}", e))
    }
}

// ============================================================================
// Tag Repository
// ============================================================================

#[async_trait]
pub trait TagRepository {
    async fn create(&self, tag: &Tag) -> Result<Tag>;
    async fn find_by_id(&self, id: &str) -> Result<Option<Tag>>;
    async fn find_by_knowledge_base(&self, kb_id: &str) -> Result<Vec<Tag>>;
    async fn find_by_name(&self, kb_id: &str, name: &str) -> Result<Option<Tag>>;
    async fn update(&self, tag: &Tag) -> Result<Tag>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn search(&self, kb_id: &str, query: &str) -> Result<Vec<Tag>>;
    async fn update_usage_count(&self, id: &str, delta: i64) -> Result<bool>;
}

pub struct MongoTagRepository {
    collection: Collection<Tag>,
}

impl MongoTagRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(TAGS_COLLECTION),
        }
    }
}

#[async_trait]
impl TagRepository for MongoTagRepository {
    async fn create(&self, tag: &Tag) -> Result<Tag> {
        let mut new_tag = tag.clone();

        if new_tag.id.is_empty() {
            new_tag.id = ObjectId::new().to_hex();
        }

        let now = current_millis();
        new_tag.created_at = now;
        new_tag.updated_at = now;

        self.collection
            .insert_one(&new_tag)
            .await
            .map_err(|e| anyhow!("创建标签失败: {}", e))?;

        Ok(new_tag)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<Tag>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找标签失败: {}", e))
    }

    async fn find_by_knowledge_base(&self, kb_id: &str) -> Result<Vec<Tag>> {
        let filter = doc! { "knowledgeBaseId": kb_id };
        let options = FindOptions::builder().sort(doc! { "name": 1 }).build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按知识库查找标签失败: {}", e))?;

        let mut tags = Vec::new();
        while let Some(tag) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历标签失败: {}", e))?
        {
            tags.push(tag);
        }

        Ok(tags)
    }

    async fn find_by_name(&self, kb_id: &str, name: &str) -> Result<Option<Tag>> {
        let filter = doc! {
            "knowledgeBaseId": kb_id,
            "name": name
        };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("通过名称查找标签失败: {}", e))
    }

    async fn update(&self, tag: &Tag) -> Result<Tag> {
        let mut updated_tag = tag.clone();
        updated_tag.updated_at = current_millis();

        let filter = doc! { "_id": &tag.id };
        let update = doc! { "$set": bson::to_bson(&updated_tag)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新标签失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("标签不存在: {}", tag.id));
        }

        Ok(updated_tag)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除标签失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn search(&self, kb_id: &str, query: &str) -> Result<Vec<Tag>> {
        let filter = doc! {
            "knowledgeBaseId": kb_id,
            "$or": [
                { "name": { "$regex": query, "$options": "i" } },
                { "displayName": { "$regex": query, "$options": "i" } },
                { "description": { "$regex": query, "$options": "i" } }
            ]
        };

        let options = FindOptions::builder()
            .sort(doc! { "usageCount": -1, "name": 1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("搜索标签失败: {}", e))?;

        let mut tags = Vec::new();
        while let Some(tag) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历搜索结果失败: {}", e))?
        {
            tags.push(tag);
        }

        Ok(tags)
    }

    async fn update_usage_count(&self, id: &str, delta: i64) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$inc": { "usageCount": delta },
            "$set": { "updatedAt": current_millis() as i64 }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新标签使用次数失败: {}", e))?;

        Ok(result.matched_count > 0)
    }
}

// ============================================================================
// Conversation Repository
// ============================================================================

#[async_trait]
pub trait ConversationRepository {
    async fn create(&self, conversation: &Conversation) -> Result<Conversation>;
    async fn find_by_id(&self, id: &str) -> Result<Option<Conversation>>;
    async fn find_by_user(
        &self,
        user_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>>;
    async fn find_by_knowledge_base(
        &self,
        kb_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>>;
    async fn update(&self, conversation: &Conversation) -> Result<Conversation>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn update_last_message_time(&self, id: &str, last_message_at: u64) -> Result<bool>;
    async fn update_statistics(&self, id: &str, stats: &ConversationStatistics) -> Result<bool>;
    async fn search(
        &self,
        user_id: &str,
        query: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>>;
    async fn count_by_user(&self, user_id: &str) -> Result<i64>;
}

pub struct MongoConversationRepository {
    collection: Collection<Conversation>,
}

impl MongoConversationRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(CONVERSATIONS_COLLECTION),
        }
    }
}

#[async_trait]
impl ConversationRepository for MongoConversationRepository {
    async fn create(&self, conversation: &Conversation) -> Result<Conversation> {
        let mut new_conversation = conversation.clone();

        if new_conversation.id.is_empty() {
            new_conversation.id = ObjectId::new().to_hex();
        }

        let now = current_millis();
        new_conversation.created_at = now;
        new_conversation.updated_at = now;

        self.collection
            .insert_one(&new_conversation)
            .await
            .map_err(|e| anyhow!("创建对话失败: {}", e))?;

        Ok(new_conversation)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<Conversation>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找对话失败: {}", e))
    }

    async fn find_by_user(
        &self,
        user_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>> {
        let filter = doc! { "userId": user_id };

        let mut options = FindOptions::builder()
            .sort(doc! { "lastMessageAt": -1, "updatedAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按用户查找对话失败: {}", e))?;

        let mut conversations = Vec::new();
        while let Some(conversation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历对话失败: {}", e))?
        {
            conversations.push(conversation);
        }

        Ok(conversations)
    }

    async fn find_by_knowledge_base(
        &self,
        kb_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>> {
        let filter = doc! { "knowledgeBaseId": kb_id };

        let mut options = FindOptions::builder()
            .sort(doc! { "lastMessageAt": -1, "updatedAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按知识库查找对话失败: {}", e))?;

        let mut conversations = Vec::new();
        while let Some(conversation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历对话失败: {}", e))?
        {
            conversations.push(conversation);
        }

        Ok(conversations)
    }

    async fn update(&self, conversation: &Conversation) -> Result<Conversation> {
        let mut updated_conversation = conversation.clone();
        updated_conversation.updated_at = current_millis();

        let filter = doc! { "_id": &conversation.id };
        let update = doc! { "$set": bson::to_bson(&updated_conversation)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新对话失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("对话不存在: {}", conversation.id));
        }

        Ok(updated_conversation)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除对话失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn update_last_message_time(&self, id: &str, last_message_at: u64) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": {
                "lastMessageAt": last_message_at as i64,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新对话最后消息时间失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn update_statistics(&self, id: &str, stats: &ConversationStatistics) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": {
                "statistics": bson::to_bson(stats)?,
                "updatedAt": current_millis() as i64
            }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新对话统计信息失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn search(
        &self,
        user_id: &str,
        query: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Conversation>> {
        let filter = doc! {
            "userId": user_id,
            "$or": [
                { "title": { "$regex": query, "$options": "i" } },
                { "summary": { "$regex": query, "$options": "i" } }
            ]
        };

        let mut options = FindOptions::builder()
            .sort(doc! { "lastMessageAt": -1, "updatedAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("搜索对话失败: {}", e))?;

        let mut conversations = Vec::new();
        while let Some(conversation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历搜索结果失败: {}", e))?
        {
            conversations.push(conversation);
        }

        Ok(conversations)
    }

    async fn count_by_user(&self, user_id: &str) -> Result<i64> {
        let filter = doc! { "userId": user_id };
        self.collection
            .count_documents(filter)
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计用户对话数量失败: {}", e))
    }
}

// ============================================================================
// Message Repository
// ============================================================================

#[async_trait]
pub trait MessageRepository {
    async fn create(&self, message: &Message) -> Result<Message>;
    async fn find_by_id(&self, id: &str) -> Result<Option<Message>>;
    async fn find_by_conversation(
        &self,
        conversation_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Message>>;
    async fn update(&self, message: &Message) -> Result<Message>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn delete_by_conversation(&self, conversation_id: &str) -> Result<u64>;
    async fn count_by_conversation(&self, conversation_id: &str) -> Result<i64>;
    async fn find_latest_by_conversation(
        &self,
        conversation_id: &str,
        limit: u32,
    ) -> Result<Vec<Message>>;
}

pub struct MongoMessageRepository {
    collection: Collection<Message>,
}

impl MongoMessageRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(MESSAGES_COLLECTION),
        }
    }
}

#[async_trait]
impl MessageRepository for MongoMessageRepository {
    async fn create(&self, message: &Message) -> Result<Message> {
        let mut new_message = message.clone();

        if new_message.id.is_empty() {
            new_message.id = ObjectId::new().to_hex();
        }

        let now = current_millis();
        new_message.created_at = now;
        new_message.updated_at = now;

        self.collection
            .insert_one(&new_message)
            .await
            .map_err(|e| anyhow!("创建消息失败: {}", e))?;

        Ok(new_message)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<Message>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找消息失败: {}", e))
    }

    async fn find_by_conversation(
        &self,
        conversation_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<Message>> {
        let filter = doc! { "conversationId": conversation_id };

        let mut options = FindOptions::builder().sort(doc! { "createdAt": 1 }).build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按对话查找消息失败: {}", e))?;

        let mut messages = Vec::new();
        while let Some(message) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历消息失败: {}", e))?
        {
            messages.push(message);
        }

        Ok(messages)
    }

    async fn update(&self, message: &Message) -> Result<Message> {
        let mut updated_message = message.clone();
        updated_message.updated_at = current_millis();

        let filter = doc! { "_id": &message.id };
        let update = doc! { "$set": bson::to_bson(&updated_message)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新消息失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("消息不存在: {}", message.id));
        }

        Ok(updated_message)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除消息失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn delete_by_conversation(&self, conversation_id: &str) -> Result<u64> {
        let filter = doc! { "conversationId": conversation_id };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("按对话删除消息失败: {}", e))?;

        Ok(result.deleted_count)
    }

    async fn count_by_conversation(&self, conversation_id: &str) -> Result<i64> {
        let filter = doc! { "conversationId": conversation_id };
        self.collection
            .count_documents(filter)
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计对话消息数量失败: {}", e))
    }

    async fn find_latest_by_conversation(
        &self,
        conversation_id: &str,
        limit: u32,
    ) -> Result<Vec<Message>> {
        let filter = doc! { "conversationId": conversation_id };
        let options = FindOptions::builder()
            .sort(doc! { "createdAt": -1 })
            .limit(limit as i64)
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找最新消息失败: {}", e))?;

        let mut messages = Vec::new();
        while let Some(message) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历最新消息失败: {}", e))?
        {
            messages.push(message);
        }

        // 恢复时间顺序
        messages.reverse();
        Ok(messages)
    }
}

// ============================================================================
// ProcessingTask Repository
// ============================================================================

#[async_trait]
pub trait ProcessingTaskRepository {
    async fn create(&self, task: &ProcessingTask) -> Result<ProcessingTask>;
    async fn find_by_id(&self, id: &str) -> Result<Option<ProcessingTask>>;
    async fn update(&self, task: &ProcessingTask) -> Result<ProcessingTask>;
    async fn update_status(&self, id: &str, status: TaskStatus) -> Result<bool>;
    async fn update_progress(&self, id: &str, progress: f64) -> Result<bool>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn list_by_status(
        &self,
        status: &TaskStatus,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<ProcessingTask>>;
    async fn list_by_type(
        &self,
        task_type: &TaskType,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<ProcessingTask>>;
    async fn find_by_resource(&self, resource_id: &str) -> Result<Vec<ProcessingTask>>;
    async fn find_pending_tasks(&self, limit: Option<u32>) -> Result<Vec<ProcessingTask>>;
    async fn find_failed_tasks(&self, limit: Option<u32>) -> Result<Vec<ProcessingTask>>;
    async fn find_retry_tasks(&self) -> Result<Vec<ProcessingTask>>;
    async fn increment_retry_count(&self, id: &str) -> Result<bool>;
    async fn set_next_retry_time(&self, id: &str, next_retry_at: u64) -> Result<bool>;
    async fn get_statistics(&self) -> Result<TaskStatistics>;
    async fn cleanup_completed_tasks(&self, older_than: i64) -> Result<u64>;
}

pub struct MongoProcessingTaskRepository {
    collection: Collection<ProcessingTask>,
}

impl MongoProcessingTaskRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(PROCESSING_TASKS_COLLECTION),
        }
    }
}

#[async_trait]
impl ProcessingTaskRepository for MongoProcessingTaskRepository {
    async fn create(&self, task: &ProcessingTask) -> Result<ProcessingTask> {
        let mut new_task = task.clone();

        if new_task.id.is_empty() {
            new_task.id = ObjectId::new().to_hex();
        }

        new_task.created_at = current_millis();
        new_task.started_at = current_millis();

        self.collection
            .insert_one(&new_task)
            .await
            .map_err(|e| anyhow!("创建处理任务失败: {}", e))?;

        Ok(new_task)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<ProcessingTask>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找处理任务失败: {}", e))
    }

    async fn update(&self, task: &ProcessingTask) -> Result<ProcessingTask> {
        let filter = doc! { "_id": &task.id };
        let update = doc! { "$set": bson::to_bson(task)? };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新处理任务失败: {}", e))?;

        if result.matched_count == 0 {
            return Err(anyhow!("处理任务不存在: {}", task.id));
        }

        Ok(task.clone())
    }

    async fn update_status(&self, id: &str, status: TaskStatus) -> Result<bool> {
        let filter = doc! { "_id": id };
        let mut update = doc! {
            "$set": { "status": bson::to_bson(&status)? }
        };

        // 如果任务完成或失败，设置完成时间
        if status == TaskStatus::Completed
            || status == TaskStatus::Failed
            || status == TaskStatus::Cancelled
        {
            update
                .get_document_mut("$set")
                .unwrap()
                .insert("completedAt", current_millis() as i64);
        }

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新任务状态失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn update_progress(&self, id: &str, progress: f64) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": { "progress": progress }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("更新任务进度失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除处理任务失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn list_by_status(
        &self,
        status: &TaskStatus,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<ProcessingTask>> {
        let filter = doc! { "status": bson::to_bson(status)? };

        let mut options = FindOptions::builder()
            .sort(doc! { "priority": -1, "createdAt": 1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按状态查找任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn list_by_type(
        &self,
        task_type: &TaskType,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<ProcessingTask>> {
        let filter = doc! { "taskType": bson::to_bson(task_type)? };

        let mut options = FindOptions::builder()
            .sort(doc! { "createdAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按类型查找任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn find_by_resource(&self, resource_id: &str) -> Result<Vec<ProcessingTask>> {
        let filter = doc! { "resourceId": resource_id };
        let options = FindOptions::builder()
            .sort(doc! { "createdAt": -1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按资源ID查找任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn find_pending_tasks(&self, limit: Option<u32>) -> Result<Vec<ProcessingTask>> {
        let filter = doc! { "status": "Queued" };
        let mut options = FindOptions::builder()
            .sort(doc! { "priority": -1, "createdAt": 1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找待处理任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历待处理任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn find_failed_tasks(&self, limit: Option<u32>) -> Result<Vec<ProcessingTask>> {
        let filter = doc! { "status": "Failed" };
        let mut options = FindOptions::builder()
            .sort(doc! { "createdAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找失败任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历失败任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn find_retry_tasks(&self) -> Result<Vec<ProcessingTask>> {
        let now = current_millis() as i64;
        let filter = doc! {
            "status": "Failed",
            "retryCount": { "$lt": { "$toInt": "$maxRetries" } },
            "$or": [
                { "nextRetryAt": { "$exists": false } },
                { "nextRetryAt": { "$lte": now } }
            ]
        };

        let options = FindOptions::builder()
            .sort(doc! { "priority": -1, "createdAt": 1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找重试任务失败: {}", e))?;

        let mut tasks = Vec::new();
        while let Some(task) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历重试任务失败: {}", e))?
        {
            tasks.push(task);
        }

        Ok(tasks)
    }

    async fn increment_retry_count(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$inc": { "retryCount": 1 },
            "$set": { "status": "Retrying" }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("增加重试次数失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn set_next_retry_time(&self, id: &str, next_retry_at: u64) -> Result<bool> {
        let filter = doc! { "_id": id };
        let update = doc! {
            "$set": { "nextRetryAt": next_retry_at as i64 }
        };

        let result = self
            .collection
            .update_one(filter, update)
            .await
            .map_err(|e| anyhow!("设置下次重试时间失败: {}", e))?;

        Ok(result.matched_count > 0)
    }

    async fn get_statistics(&self) -> Result<TaskStatistics> {
        use crate::schema::queries::task_statistics_pipeline;

        let pipeline = task_statistics_pipeline();
        let mut cursor = self
            .collection
            .aggregate(pipeline)
            .await
            .map_err(|e| anyhow!("获取任务统计失败: {}", e))?;

        if let Some(result) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("读取统计结果失败: {}", e))?
        {
            let stats: TaskStatistics =
                bson::from_document(result).map_err(|e| anyhow!("解析统计结果失败: {}", e))?;
            Ok(stats)
        } else {
            Ok(TaskStatistics::default())
        }
    }

    async fn cleanup_completed_tasks(&self, older_than: i64) -> Result<u64> {
        let filter = doc! {
            "status": { "$in": ["Completed", "Cancelled"] },
            "completedAt": { "$lt": older_than }
        };

        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("清理已完成任务失败: {}", e))?;

        Ok(result.deleted_count)
    }
}

// ============================================================================
// DocumentVersion Repository
// ============================================================================

#[async_trait]
pub trait DocumentVersionRepository {
    async fn create(&self, version: &DocumentVersion) -> Result<DocumentVersion>;
    async fn find_by_id(&self, id: &str) -> Result<Option<DocumentVersion>>;
    async fn find_by_document(
        &self,
        document_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<DocumentVersion>>;
    async fn find_latest_by_document(&self, document_id: &str) -> Result<Option<DocumentVersion>>;
    async fn find_by_version_number(
        &self,
        document_id: &str,
        version_number: i32,
    ) -> Result<Option<DocumentVersion>>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn delete_by_document(&self, document_id: &str) -> Result<u64>;
    async fn delete_old_versions(&self, document_id: &str, keep_latest: i32) -> Result<u64>;
    async fn count_by_document(&self, document_id: &str) -> Result<i64>;
    async fn get_next_version_number(&self, document_id: &str) -> Result<i32>;
}

pub struct MongoDocumentVersionRepository {
    collection: Collection<DocumentVersion>,
}

impl MongoDocumentVersionRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(DOCUMENT_VERSIONS_COLLECTION),
        }
    }
}

#[async_trait]
impl DocumentVersionRepository for MongoDocumentVersionRepository {
    async fn create(&self, version: &DocumentVersion) -> Result<DocumentVersion> {
        let mut new_version = version.clone();

        if new_version.id.is_empty() {
            new_version.id = ObjectId::new().to_hex();
        }

        new_version.created_at = current_millis();

        self.collection
            .insert_one(&new_version)
            .await
            .map_err(|e| anyhow!("创建文档版本失败: {}", e))?;

        Ok(new_version)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<DocumentVersion>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找文档版本失败: {}", e))
    }

    async fn find_by_document(
        &self,
        document_id: &str,
        limit: Option<u32>,
        offset: Option<u32>,
    ) -> Result<Vec<DocumentVersion>> {
        let filter = doc! { "documentId": document_id };

        let mut options = FindOptions::builder()
            .sort(doc! { "versionNumber": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        if let Some(offset) = offset {
            options.skip = Some(offset as u64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按文档查找版本失败: {}", e))?;

        let mut versions = Vec::new();
        while let Some(version) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历文档版本失败: {}", e))?
        {
            versions.push(version);
        }

        Ok(versions)
    }

    async fn find_latest_by_document(&self, document_id: &str) -> Result<Option<DocumentVersion>> {
        let filter = doc! { "documentId": document_id };
        let options = mongodb::options::FindOneOptions::builder()
            .sort(doc! { "versionNumber": -1 })
            .build();

        self.collection
            .find_one(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找最新文档版本失败: {}", e))
    }

    async fn find_by_version_number(
        &self,
        document_id: &str,
        version_number: i32,
    ) -> Result<Option<DocumentVersion>> {
        let filter = doc! {
            "documentId": document_id,
            "versionNumber": version_number
        };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找指定版本号的文档版本失败: {}", e))
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除文档版本失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn delete_by_document(&self, document_id: &str) -> Result<u64> {
        let filter = doc! { "documentId": document_id };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("按文档删除版本失败: {}", e))?;

        Ok(result.deleted_count)
    }

    async fn delete_old_versions(&self, document_id: &str, keep_latest: i32) -> Result<u64> {
        // 先查找该文档的所有版本，按版本号降序排序
        let filter = doc! { "documentId": document_id };
        let options = FindOptions::builder()
            .sort(doc! { "versionNumber": -1 })
            .skip(keep_latest as u64)
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找要删除的版本失败: {}", e))?;

        let mut version_ids = Vec::new();
        while let Some(version) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历要删除的版本失败: {}", e))?
        {
            version_ids.push(version.id);
        }

        if version_ids.is_empty() {
            return Ok(0);
        }

        // 批量删除旧版本
        let delete_filter = doc! { "_id": { "$in": &version_ids } };
        let result = self
            .collection
            .delete_many(delete_filter)
            .await
            .map_err(|e| anyhow!("批量删除旧版本失败: {}", e))?;

        Ok(result.deleted_count)
    }

    async fn count_by_document(&self, document_id: &str) -> Result<i64> {
        let filter = doc! { "documentId": document_id };
        self.collection
            .count_documents(filter)
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计文档版本数量失败: {}", e))
    }

    async fn get_next_version_number(&self, document_id: &str) -> Result<i32> {
        let filter = doc! { "documentId": document_id };
        let options = mongodb::options::FindOneOptions::builder()
            .sort(doc! { "versionNumber": -1 })
            .projection(doc! { "versionNumber": 1 })
            .build();

        match self.collection.find_one(filter).with_options(options).await {
            Ok(Some(version)) => Ok(version.version_number + 1),
            Ok(None) => Ok(1), // 第一个版本
            Err(e) => Err(anyhow!("获取下一个版本号失败: {}", e)),
        }
    }
}

// ============================================================================
// DocumentRelation Repository
// ============================================================================

#[async_trait]
pub trait DocumentRelationRepository {
    async fn create(&self, relation: &DocumentRelation) -> Result<DocumentRelation>;
    async fn find_by_id(&self, id: &str) -> Result<Option<DocumentRelation>>;
    async fn delete(&self, id: &str) -> Result<bool>;
    async fn find_outbound_relations(&self, document_id: &str) -> Result<Vec<DocumentRelation>>;
    async fn find_inbound_relations(&self, document_id: &str) -> Result<Vec<DocumentRelation>>;
    async fn find_by_type(
        &self,
        relation_type: &DocumentRelationType,
        limit: Option<u32>,
    ) -> Result<Vec<DocumentRelation>>;
    async fn find_by_strength_range(
        &self,
        min_strength: f64,
        max_strength: f64,
        limit: Option<u32>,
    ) -> Result<Vec<DocumentRelation>>;
    async fn find_by_documents(
        &self,
        source_id: &str,
        target_id: &str,
    ) -> Result<Option<DocumentRelation>>;
    async fn count_by_document(&self, document_id: &str) -> Result<i64>;
    async fn delete_by_document(&self, document_id: &str) -> Result<u64>;
    async fn batch_create(&self, relations: Vec<DocumentRelation>)
    -> Result<Vec<DocumentRelation>>;
}

pub struct MongoDocumentRelationRepository {
    collection: Collection<DocumentRelation>,
}

impl MongoDocumentRelationRepository {
    pub fn new(db_connection: &DatabaseConnection) -> Self {
        Self {
            collection: db_connection.collection(DOCUMENT_RELATIONS_COLLECTION),
        }
    }
}

#[async_trait]
impl DocumentRelationRepository for MongoDocumentRelationRepository {
    async fn create(&self, relation: &DocumentRelation) -> Result<DocumentRelation> {
        let mut new_relation = relation.clone();

        if new_relation.id.is_empty() {
            new_relation.id = ObjectId::new().to_hex();
        }

        self.collection
            .insert_one(&new_relation)
            .await
            .map_err(|e| anyhow!("创建文档关系失败: {}", e))?;

        Ok(new_relation)
    }

    async fn find_by_id(&self, id: &str) -> Result<Option<DocumentRelation>> {
        let filter = doc! { "_id": id };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("查找文档关系失败: {}", e))
    }

    async fn delete(&self, id: &str) -> Result<bool> {
        let filter = doc! { "_id": id };
        let result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| anyhow!("删除文档关系失败: {}", e))?;

        Ok(result.deleted_count > 0)
    }

    async fn find_outbound_relations(&self, document_id: &str) -> Result<Vec<DocumentRelation>> {
        let filter = doc! { "sourceDocumentId": document_id };
        let options = FindOptions::builder()
            .sort(doc! { "strength": -1, "createdAt": -1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找出向关系失败: {}", e))?;

        let mut relations = Vec::new();
        while let Some(relation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历出向关系失败: {}", e))?
        {
            relations.push(relation);
        }

        Ok(relations)
    }

    async fn find_inbound_relations(&self, document_id: &str) -> Result<Vec<DocumentRelation>> {
        let filter = doc! { "targetDocumentId": document_id };
        let options = FindOptions::builder()
            .sort(doc! { "strength": -1, "createdAt": -1 })
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("查找入向关系失败: {}", e))?;

        let mut relations = Vec::new();
        while let Some(relation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历入向关系失败: {}", e))?
        {
            relations.push(relation);
        }

        Ok(relations)
    }

    async fn find_by_type(
        &self,
        relation_type: &DocumentRelationType,
        limit: Option<u32>,
    ) -> Result<Vec<DocumentRelation>> {
        let filter = doc! { "relationType": bson::to_bson(relation_type)? };
        let mut options = FindOptions::builder()
            .sort(doc! { "strength": -1, "createdAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按类型查找关系失败: {}", e))?;

        let mut relations = Vec::new();
        while let Some(relation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历关系失败: {}", e))?
        {
            relations.push(relation);
        }

        Ok(relations)
    }

    async fn find_by_strength_range(
        &self,
        min_strength: f64,
        max_strength: f64,
        limit: Option<u32>,
    ) -> Result<Vec<DocumentRelation>> {
        let filter = doc! {
            "strength": { "$gte": min_strength, "$lte": max_strength }
        };
        let mut options = FindOptions::builder()
            .sort(doc! { "strength": -1, "createdAt": -1 })
            .build();

        if let Some(limit) = limit {
            options.limit = Some(limit as i64);
        }

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(options)
            .await
            .map_err(|e| anyhow!("按强度范围查找关系失败: {}", e))?;

        let mut relations = Vec::new();
        while let Some(relation) = cursor
            .try_next()
            .await
            .map_err(|e| anyhow!("遍历关系失败: {}", e))?
        {
            relations.push(relation);
        }

        Ok(relations)
    }

    async fn find_by_documents(
        &self,
        source_id: &str,
        target_id: &str,
    ) -> Result<Option<DocumentRelation>> {
        let filter = doc! {
            "sourceDocumentId": source_id,
            "targetDocumentId": target_id
        };
        self.collection
            .find_one(filter)
            .await
            .map_err(|e| anyhow!("按文档查找关系失败: {}", e))
    }

    async fn count_by_document(&self, document_id: &str) -> Result<i64> {
        let filter = doc! {
            "$or": [
                { "sourceDocumentId": document_id },
                { "targetDocumentId": document_id }
            ]
        };
        self.collection
            .count_documents(filter)
            .await
            .map(|count| count as i64)
            .map_err(|e| anyhow!("统计文档关系数量失败: {}", e))
    }

    async fn delete_by_document(&self, document_id: &str) -> Result<u64> {
        let filter = doc! {
            "$or": [
                { "sourceDocumentId": document_id },
                { "targetDocumentId": document_id }
            ]
        };
        let result = self
            .collection
            .delete_many(filter)
            .await
            .map_err(|e| anyhow!("删除文档相关关系失败: {}", e))?;

        Ok(result.deleted_count)
    }

    async fn batch_create(
        &self,
        relations: Vec<DocumentRelation>,
    ) -> Result<Vec<DocumentRelation>> {
        if relations.is_empty() {
            return Ok(vec![]);
        }

        let mut new_relations = relations;
        for relation in &mut new_relations {
            if relation.id.is_empty() {
                relation.id = ObjectId::new().to_hex();
            }
        }

        self.collection
            .insert_many(&new_relations)
            .await
            .map_err(|e| anyhow!("批量创建文档关系失败: {}", e))?;

        Ok(new_relations)
    }
}

// Re-export additional repositories
pub use crate::additional_repositories::*;
