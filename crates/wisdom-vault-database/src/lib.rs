pub mod cache;
pub mod cache_invalidation;
pub mod cached_repositories;
pub mod connection;
pub mod data_cache;
pub mod models;
pub mod qdrant_connection;
pub mod repositories;
pub mod additional_repositories;
pub mod schema;
pub mod vector_repository;
pub mod indexes;

// Re-export qdrant_client for use in other crates
pub use qdrant_client;

pub use cache::{CacheConnection, CacheService, establish_cache_connection};
pub use cache_invalidation::{
    BatchCacheOperations, CacheEvent, CacheEventHandler, CacheInvalidationSystem, CacheKeyRegistry,
    RedisCacheEventHandler,
};
pub use cached_repositories::*;
pub use connection::{DatabaseConnection, MongoDBConfig, DatabaseManager, DatabaseHealth};
pub use data_cache::{CacheManager, CacheMetrics, CachedRepository, DataCache};
pub use qdrant_connection::{
    QdrantConnection, QdrantConfig, QdrantManager, QdrantHealth,
    VectorCollectionConfig, VectorCollections,
};
pub use schema::{initialize_schema, queries, vector_config};
pub use vector_repository::{
    VectorRepository, QdrantVectorRepository, VectorPoint, VectorFilter,
    VectorSearchResult, FilterCondition,
};
pub use indexes::IndexManager;

// Re-export commonly used types
pub use models::*;
pub use repositories::*;
