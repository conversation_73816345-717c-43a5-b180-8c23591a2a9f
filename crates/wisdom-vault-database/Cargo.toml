[package]
name = "wisdom-vault-database"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Database models and repository layer for Wisdom Vault"

[dependencies]
# Workspace crates
wisdom-vault-common = { path = "../wisdom-vault-common" }

# External dependencies
mongodb = { workspace = true }
bson = { workspace = true }
futures = { workspace = true }
redis = { workspace = true, features = ["tokio-comp"] }
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
tokio = { workspace = true }
async-trait = { workspace = true }
qdrant-client = { workspace = true }
