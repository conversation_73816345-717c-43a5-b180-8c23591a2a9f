[workspace]
members = [
    "crates/wisdom-vault-api",
    "crates/wisdom-vault-core",
    "crates/wisdom-vault-database",
    # "crates/wisdom-vault-auth",
    "crates/wisdom-vault-common",
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2024"
authors = ["Wisdom Vault Team"]
license = "MIT"
description = "Enterprise AI-powered knowledge base system"

[workspace.dependencies]
# Web framework and async
actix-web = "4.4"
actix-multipart = "0.7"
tokio = { version = "1.35", features = ["full"] }
actix-cors = "0.7"
futures-util = "0.3"
futures = "0.3"

# API documentation and OpenAPI
utoipa = { version = "5", features = ["actix_extras"] }
utoipa-actix-web = "0.1"
utoipa-swagger-ui = { version = "9", features = ["actix-web"] }

# Localization and internationalization
rust-i18n = "3"

# File handling and storage
tokio-util = { version = "0.7", features = ["io"] }
mime = "0.3"
tempfile = "3.8"

# HTTP client for external services
reqwest = { version = "0.12", features = ["json", "multipart"] }
mime_guess = "2.0"

# Database
redis = { version = "0.32", features = ["tokio-comp"] }
mongodb = "3"
bson = "2"
qdrant-client = "1"

# Serialization and data formats
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }

# Authentication and security
jsonwebtoken = "9.2"
bcrypt = "0.17"
argon2 = { version = "0.5", features = ["std"] }
actix-session = { version = "0.10", features = ["redis-session"] }
hex = "0.4"
actix-web-httpauth = "0.8"

# Error handling and logging
anyhow = "1.0"
thiserror = "2"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = [
    "env-filter",
    "fmt",
    "json",
    "registry",
] }
tracing-actix-web = "0.7"
derive_more = "2"

# Configuration
config = "0.15.13"
dotenvy = "0.15"

# Validation
validator = { version = "0.20", features = ["derive"] }
uuid = { version = "1.0", features = ["v4", "serde"] }

# Text processing for document chunking
regex = "1.10"
unicode-segmentation = "1.12"

# AI and vectorization dependencies
candle-core = "0.9"
candle-transformers = "0.9"
candle-nn = "0.9"
tokenizers = "0.21"
ndarray = "0.15"
hf-hub = "0.4"
safetensors = "0.6"
async-trait = "0.1"

# Mathematical operations for vectors
approx = "0.5"
rand = "0.9"

# OpenAI and LLM integration
openai-api-rs = "6.0.6"


# Keep workspace members' reqwest in sync
