#!/bin/bash

# Start the server in background
# cargo run --bin wisdom-vault &
# SERVER_PID=$!

# # Wait for server to start
# sleep 3

# Test health endpoint
echo "Testing health endpoint..."
curl -s http://127.0.0.1:5141/health | head -c 200

echo ""
echo "Testing API endpoint..."
curl -X POST -s http://127.0.0.1:5141/api/v1/auth/register | head -c 200

# Kill the server
kill $SERVER_PID
wait $SERVER_PID 2>/dev/null